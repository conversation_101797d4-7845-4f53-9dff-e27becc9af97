/* 全局样式 */
body {
    background-color: #f8f9fa;
}

/* 卡片样式 */
.card {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: none;
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

/* 表单样式 */
.form-group {
    margin-bottom: 1rem;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 按钮样式 */
.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-secondary:hover {
    background-color: #5a6268;
    border-color: #545b62;
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table-sm td, .table-sm th {
    padding: 0.3rem;
}

/* 图表容器样式 */
#temperature-chart, #composition-chart {
    min-height: 300px;
    width: 100%;
}

/* 响应式布局调整 */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    #temperature-chart, #composition-chart {
        min-height: 250px;
    }
} 