version: 2
updates:
  - package-ecosystem: npm
    directory: "/"
    schedule:
      interval: daily
      time: "23:00"
    commit-message:
      prefix: "chore"
      include: "scope"
    open-pull-requests-limit: 3
    ignore:
      # node-fetch must be synced manually
      - dependency-name: "node-fetch"

  - package-ecosystem: "docker"
    # Look for a `Dockerfile` in the `root` directory
    directory: "/"
    # Check for updates once a week
    schedule:
      interval: "weekly"
      time: "23:00"

  - package-ecosystem: "github-actions"
    target-branch: "master"
    directory: "/"
    schedule:
      interval: "daily"
      time: "23:00"
    commit-message:
      prefix: "chore"
      include: "scope"
    open-pull-requests-limit: 3


