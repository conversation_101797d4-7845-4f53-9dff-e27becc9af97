"""
CADWorx等级库(PRJ)文件解析器

此模块提供了解析CADWorx等级库(PRJ)文件的功能，包括：
- 管道规格信息
- 组件配置
- 材料规格
- 等级规格
- 端部处理规格
- 壁厚规格
"""

import os
from pathlib import Path
from typing import Dict, List, Optional, Any
import xml.etree.ElementTree as ET
from datetime import datetime
import yaml


class CADWorxPrjParser:
    """CADWorx等级库(PRJ)文件解析器"""

    def __init__(self, data_dir: str = None):
        """
        初始化解析器
        
        Args:
            data_dir: 数据目录路径，默认为当前目录下的dataset/pipe_standards
        """
        if data_dir is None:
            data_dir = os.path.join(os.path.dirname(__file__),
                                    'dataset', 'pipe_standards')
        self.data_dir = Path(data_dir)
        self.output_dir = self.data_dir / "parsed"

    def _convert_timestamp(self, timestamp: str) -> str:
        """
        将Unix时间戳转换为可读的日期时间格式
        
        Args:
            timestamp: Unix时间戳字符串
            
        Returns:
            str: 格式化的日期时间字符串 (YYYY-MM-DD HH:MM:SS)
        """
        try:
            # 将时间戳转换为整数
            ts = int(timestamp)
            # 转换为datetime对象
            dt = datetime.fromtimestamp(ts)
            # 格式化为字符串
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        except (ValueError, TypeError):
            # 如果转换失败，返回原始时间戳
            return timestamp

    def parse(self, file_path: str) -> Dict[str, Any]:
        """
        解析PRJ文件
        
        Args:
            file_path: PRJ文件路径
            
        Returns:
            Dict[str, Any]: 解析后的数据
        """
        tree = ET.parse(file_path)
        root = tree.getroot()

        # 解析项目信息
        project = {
            'name': root.get('Name', ''),
            'version': root.get('Version', ''),
            'description': root.get('Desc', ''),
            'created_time': self._convert_timestamp(root.get('CreatedTime', '')),
            'modified_time': self._convert_timestamp(root.get('ModifiedTime', '')),
            'guid': root.get('GUID', ''),
            'specs': []  # 管道规格列表
        }

        # 解析管道规格
        specs = root.find('Specs')
        if specs is not None:
            for spec in specs.findall('Spec'):
                spec_data = {
                    'name': spec.get('Name', ''),
                    'code': spec.get('Code', ''),
                    'version': spec.get('Version', ''),
                    'description': spec.get('Desc', ''),
                    'created_time': self._convert_timestamp(spec.get('CreatedTime', '')),
                    'modified_time': self._convert_timestamp(spec.get('ModifiedTime', '')),
                    'components': []
                }

                # 解析组件
                components = spec.find('Components')
                if components is not None:
                    for comp in components.findall('Component'):
                        comp_data = {
                            'type': comp.get('Type', ''),
                            'size_range': comp.get('SizeRange', ''),
                            'material': comp.get('Material', ''),
                            'rating': comp.get('Rating', ''),
                            'end_preparation': comp.get('EndPreparation', ''),
                            'schedule': comp.get('Schedule', '')
                        }
                        spec_data['components'].append(comp_data)

                project['specs'].append(spec_data)

        return project

    def save_as_yaml(self, data: Dict[str, Any], output_path: Path) -> None:
        """
        将解析后的数据保存为YAML格式
        
        Args:
            data: 解析后的数据
            output_path: 输出文件路径
        """
        # 确保输出目录存在
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # 保存为YAML文件
        with open(output_path, 'w', encoding='utf-8') as f:
            yaml.dump(data, f, allow_unicode=True, sort_keys=False)

    def parse_and_save(self, prj_file: str, output_name: Optional[str] = None) -> str:
        """
        解析文件并保存为YAML格式
        
        Args:
            prj_file: PRJ文件路径
            output_name: 输出文件名（不含扩展名），如果为None则使用PRJ文件的name属性
            
        Returns:
            str: 生成的输出文件路径
        """
        # 解析PRJ文件
        prj_data = self.parse(prj_file)

        # 生成输出文件名
        if output_name is None:
            # 使用PRJ文件中的name属性，转换为小写
            output_name = prj_data['name'].lower()
            # 添加版本信息（如果有）
            if prj_data['version']:
                output_name = f"{output_name}_{prj_data['version'].replace('.', '_')}"

        # 添加prj后缀和yaml扩展名
        output_path = self.output_dir / f"{output_name}_prj.yaml"

        # 保存数据
        self.save_as_yaml(prj_data, output_path)
        print(f"已解析并保存到：{output_path}")
        return str(output_path)


def main():
    """主函数"""
    # 获取当前目录
    current_dir = Path(__file__).parent

    # 设置输入文件路径
    prj_file = current_dir / "dataset" / "GB-B.PRJ"

    # 创建解析器
    parser = CADWorxPrjParser()

    # 解析并保存（使用默认输出名）
    output_path = parser.parse_and_save(str(prj_file))
    print(f"默认输出：{output_path}")  # 例如：gb_b_2015_01_prj.yaml

    # 解析并保存（指定输出名）
    output_path = parser.parse_and_save(str(prj_file), "gb_b_2015")
    print(f"指定输出：{output_path}")  # 例如：gb_b_2015_prj.yaml


if __name__ == "__main__":
    main()
