{"hooks": {"before:bump": ["./release/check-for-breaking-changes.sh ${latestVersion} ${version}", "npm update swagger-client", "npm test"], "after:bump": ["npm run build"], "after:release": "export GIT_TAG=v${version} && echo GIT_TAG=v${version} > release/.version"}, "git": {"requireUpstream": false, "changelog": "./release/get-changelog.sh", "commitMessage": "chore(release): cut the v${version} release", "tagName": "v${version}", "push": false}, "github": {"release": true, "releaseName": "Swagger UI v${version} Released!", "draft": true}, "plugins": {"@release-it/conventional-changelog": {"preset": "angular"}}}