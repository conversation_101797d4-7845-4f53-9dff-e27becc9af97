from sqlalchemy import Column, Integer, String, Float, DateTime, JSON
from sqlalchemy.sql import func
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.ext.declarative import declarative_base
import json
from datetime import datetime
from typing import Dict, Any, Optional

Base = declarative_base()

class BaseModel(Base):
    """所有模型的基类，包含共享字段和方法"""
    __abstract__ = True
    
    id = Column(Integer, primary_key=True, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    @declared_attr
    def __tablename__(cls) -> str:
        """自动生成表名"""
        return cls.__name__.lower() + "s"
    
    def to_dict(self) -> Dict[str, Any]:
        """将模型实例转换为字典"""
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            
            # 处理日期时间
            if isinstance(value, datetime):
                value = value.isoformat()
            
            # 处理JSON字段
            elif isinstance(value, str) and column.type.python_type == dict:
                try:
                    value = json.loads(value)
                except json.JSONDecodeError:
                    pass
            
            result[column.name] = value
            
        return result
    
    def __repr__(self) -> str:
        """模型实例的字符串表示"""
        attrs = []
        for column in self.__table__.columns:
            if column.primary_key or column.name == 'name' or column.name == 'filename':
                value = getattr(self, column.name)
                attrs.append(f"{column.name}={repr(value)}")
        
        return f"<{self.__class__.__name__}({', '.join(attrs)})>" 