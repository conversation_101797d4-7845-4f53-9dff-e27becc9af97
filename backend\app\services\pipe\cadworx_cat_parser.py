"""
CADWorx元件库(CAT)文件解析器

此模块提供了解析CADWorx元件库(CAT)文件的功能，包括：
- 尺寸列表
- 材料列表
- 等级列表
- 连接列表
- 端部处理列表
- 壁厚列表
- 类型列表
"""

import os
from pathlib import Path
from typing import Dict, List, Optional, Any
import xml.etree.ElementTree as ET
from datetime import datetime
import yaml
from pydantic import BaseModel, Field, field_validator
import re
import uuid


# 字段名称英文说明映射
FIELD_DESCRIPTIONS_EN = {
    'NOM': 'Nominal Pipe Size',
    'OD': 'Outside Diameter',
    'DESCRIPTION': 'Description',
    'PTN': 'Part Number',
    'ID': 'Inside Diameter',
    'WT': 'Wall Thickness',
    'SCH': 'Schedule',
    'TYPE': 'Type',
    'MATERIAL': 'Material',
    'RATING': 'Pressure Rating',
    'CONNECTION': 'Connection Type',
    'END_PREP': 'End Preparation',
    'LENGTH': 'Length',
    'WEIGHT': 'Weight',
    'CODE': 'Standard Code',
    'SPEC': 'Specification',
    'CLASS': 'Class',
    'SIZE': 'Size',
    'UNIT': 'Unit',
    'STATUS': 'Status',
    'VERSION': 'Version',
    'GUID': 'Global Unique Identifier',
    'CREATED_TIME': 'Created Time',
    'MODIFIED_TIME': 'Modified Time',
    'CREATED_BY': 'Created By',
    'EDITED_BY': 'Edited By',
    'BASE_ID': 'Base ID',
    'DEFAULT_PART_NUMBER': 'Default Part Number',
    'MAX_ROW_ID': 'Max Row ID',
    'TYPE': 'Type',
    'INDEX': 'Index',
    'ORDER': 'Order',
    'TITLE': 'Title',
    'DESC': 'Description',
    'DATA_TYPE': 'Data Type',
    'REQUIRED': 'Required'
}

# 字段名称中文说明映射
FIELD_DESCRIPTIONS_CN = {
    'NOM': '公称直径',
    'OD': '外径',
    'DESCRIPTION': '描述',
    'PTN': '零件编号',
    'ID': '内径',
    'WT': '壁厚',
    'SCH': '管表号',
    'TYPE': '类型',
    'MATERIAL': '材料',
    'RATING': '压力等级',
    'CONNECTION': '连接方式',
    'END_PREP': '端部处理',
    'LENGTH': '长度',
    'WEIGHT': '重量',
    'CODE': '标准代号',
    'SPEC': '规格',
    'CLASS': '等级',
    'SIZE': '尺寸',
    'UNIT': '单位',
    'STATUS': '状态',
    'VERSION': '版本',
    'GUID': '全局唯一标识符',
    'CREATED_TIME': '创建时间',
    'MODIFIED_TIME': '修改时间',
    'CREATED_BY': '创建者',
    'EDITED_BY': '编辑者',
    'BASE_ID': '基础ID',
    'DEFAULT_PART_NUMBER': '默认零件编号',
    'MAX_ROW_ID': '最大行ID',
    'TYPE': '类型',
    'INDEX': '索引',
    'ORDER': '顺序',
    'TITLE': '标题',
    'DESC': '描述',
    'DATA_TYPE': '数据类型',
    'REQUIRED': '是否必填'
}


class PipeField(BaseModel):
    """管道字段定义"""
    index: int = Field(..., description="字段索引")
    order: int = Field(..., description="字段顺序")
    type: int = Field(..., description="字段类型")
    name: str = Field(..., description="字段名称")
    title: str = Field(..., description="字段标题")
    description: str = Field("", description="字段描述")
    data_type: str = Field(..., description="数据类型")
    required: bool = Field(False, description="是否必填")

    @field_validator('description', mode='before')
    @classmethod
    def set_description(cls, v: str, info) -> str:
        """设置字段描述"""
        if not v and 'name' in info.data:
            field_name = info.data['name']
            if field_name in FIELD_DESCRIPTIONS_EN:
                return f"{FIELD_DESCRIPTIONS_CN.get(field_name, '')} ({FIELD_DESCRIPTIONS_EN.get(field_name, '')})"
        return v


class BaseItem(BaseModel):
    """基础数据项"""
    id: int = Field(..., description="ID")
    code: str = Field(..., description="代码")
    name: str = Field(..., description="名称")
    description: str = Field("", description="描述")
    status: Optional[str] = Field(None, description="状态")


class PipeSize(BaseItem):
    """管道尺寸数据类"""
    nom: float = Field(..., description="公称直径")
    od: float = Field(..., description="外径")
    part_number: str = Field(..., description="零件编号")
    id: Optional[float] = Field(None, description="内径")
    wt: Optional[float] = Field(None, description="壁厚")
    sch: Optional[str] = Field(None, description="管表号")
    length: Optional[float] = Field(None, description="长度")
    weight: Optional[float] = Field(None, description="重量")

    @field_validator('description', mode='before')
    @classmethod
    def set_description(cls, v: str, info) -> str:
        """设置尺寸描述"""
        if not v and 'nom' in info.data:
            return f"DN{info.data['nom']}"
        return v


class MaterialItem(BaseItem):
    """材料数据类"""
    spec: Optional[str] = Field(None, description="规格")
    grade: Optional[str] = Field(None, description="等级")
    type: Optional[str] = Field(None, description="类型")


class RatingItem(BaseItem):
    """压力等级数据类"""
    pressure: Optional[float] = Field(None, description="压力值")
    temperature: Optional[float] = Field(None, description="温度值")
    unit: Optional[str] = Field(None, description="单位")


class ConnectionItem(BaseItem):
    """连接方式数据类"""
    type: Optional[str] = Field(None, description="类型")
    standard: Optional[str] = Field(None, description="标准")


class EndPrepItem(BaseItem):
    """端部处理数据类"""
    type: Optional[str] = Field(None, description="类型")
    standard: Optional[str] = Field(None, description="标准")


class WallThicknessItem(BaseItem):
    """壁厚数据类"""
    value: Optional[float] = Field(None, description="壁厚值")
    unit: Optional[str] = Field(None, description="单位")


class TypeItem(BaseItem):
    """类型数据类"""
    category: Optional[str] = Field(None, description="类别")


class ScheduleItem(BaseItem):
    """管表号数据类"""
    value: str = Field(..., description="管表号值")
    description: str = Field("", description="描述")


class ThicknessItem(BaseItem):
    """壁厚数据类"""
    value: float = Field(..., description="壁厚值")
    unit: str = Field("mm", description="单位")


class BaseTable(BaseModel):
    """基础表格数据类"""
    _next_id: int = 1  # 类变量，用于跟踪下一个可用的ID

    id: int = Field(
        default_factory=lambda: BaseTable._get_next_id(), description="表ID")
    name: str = Field(..., description="表名")
    version: str = Field(..., description="版本号")
    description: str = Field("", description="描述")
    created_time: str = Field(..., description="创建时间")
    modified_time: str = Field(..., description="修改时间")
    created_by: str = Field("", description="创建者")
    edited_by: str = Field("", description="编辑者")
    status: str = Field("", description="状态")
    base_id: int = Field(-1, description="基础ID")
    unit: str = Field("", description="单位")
    default_part_number: str = Field("", description="默认零件编号")
    max_row_id: int = Field(0, description="最大行ID")
    type: int = Field(0, description="类型")
    fields: List[PipeField] = Field(default_factory=list, description="字段定义")

    @classmethod
    def _get_next_id(cls) -> int:
        """获取下一个可用的ID并递增计数器"""
        current_id = cls._next_id
        cls._next_id += 1
        return current_id

    @classmethod
    def reset_id_counter(cls):
        """重置ID计数器"""
        cls._next_id = 1


class ItemTable(BaseTable):
    """带项目列表的表格基类"""
    items: Dict[str, BaseItem] = Field(
        default_factory=dict, description="项目数据")

    def add_item(self, item: BaseItem) -> None:
        """添加项目"""
        self.items[item.code] = item

    def update_item(self, item: BaseItem) -> None:
        """更新项目"""
        if item.code not in self.items:
            raise ValueError(f"项目 {item.code} 不存在")
        self.items[item.code] = item

    def delete_item(self, code: str) -> None:
        """删除项目"""
        if code not in self.items:
            raise ValueError(f"项目 {code} 不存在")
        del self.items[code]

    def get_item(self, code: str) -> Optional[BaseItem]:
        """获取项目"""
        return self.items.get(code)

    def get_all_items(self) -> List[BaseItem]:
        """获取所有项目"""
        return list(self.items.values())


class SizeTable(BaseTable):
    """尺寸表数据类"""
    sizes: List[List[str]] = Field(
        default_factory=list, description="尺寸数据列表，每个元素为[ID, NOM, OD, DESCRIPTION, PTN]")

    def add_size(self, size: PipeSize) -> None:
        """添加尺寸数据"""
        self.sizes.append([
            str(size.id),
            str(size.nom),
            str(size.od),
            size.description,
            size.part_number
        ])

    def update_size(self, size: PipeSize) -> None:
        """更新尺寸数据"""
        for i, existing_size in enumerate(self.sizes):
            if float(existing_size[1]) == size.nom:  # 比较NOM值
                self.sizes[i] = [
                    str(size.id),
                    str(size.nom),
                    str(size.od),
                    size.description,
                    size.part_number
                ]
                return
        raise ValueError(f"尺寸 {size.nom} 不存在")

    def delete_size(self, nom: float) -> None:
        """删除尺寸数据"""
        for i, size in enumerate(self.sizes):
            if float(size[1]) == nom:  # 比较NOM值
                del self.sizes[i]
                return
        raise ValueError(f"尺寸 {nom} 不存在")

    def get_size(self, nom: float) -> Optional[List[str]]:
        """获取尺寸数据"""
        for size in self.sizes:
            if float(size[1]) == nom:  # 比较NOM值
                return size
        return None

    def get_all_sizes(self) -> List[List[str]]:
        """获取所有尺寸数据"""
        return self.sizes

    def get_size_range(self, min_nom: float, max_nom: float) -> List[List[str]]:
        """获取指定范围内的尺寸数据"""
        return [size for size in self.sizes if min_nom <= float(size[1]) <= max_nom]


class MaterialTable(ItemTable):
    """材料表数据类"""
    items: Dict[str, MaterialItem] = Field(
        default_factory=dict, description="材料数据")


class RatingTable(ItemTable):
    """压力等级表数据类"""
    items: Dict[str, RatingItem] = Field(
        default_factory=dict, description="压力等级数据")


class ConnectionTable(ItemTable):
    """连接方式表数据类"""
    items: Dict[str, ConnectionItem] = Field(
        default_factory=dict, description="连接方式数据")


class EndPrepTable(ItemTable):
    """端部处理表数据类"""
    items: Dict[str, EndPrepItem] = Field(
        default_factory=dict, description="端部处理数据")


class WallThicknessTable(ItemTable):
    """壁厚表数据类"""
    items: Dict[str, WallThicknessItem] = Field(
        default_factory=dict, description="壁厚数据")


class TypeTable(ItemTable):
    """类型表数据类"""
    items: Dict[str, TypeItem] = Field(
        default_factory=dict, description="类型数据")


class ScheduleTable(ItemTable):
    """管表号表数据类"""
    items: Dict[str, ScheduleItem] = Field(
        default_factory=dict, description="管表号数据")


class ThicknessTable(ItemTable):
    """壁厚表数据类"""
    items: Dict[str, ThicknessItem] = Field(
        default_factory=dict, description="壁厚数据")


class PipeStandard(BaseModel):
    """管道标准数据类"""
    name: str = Field(..., description="标准名称")
    version: str = Field(..., description="版本号")
    description: str = Field("", description="描述")
    created_time: str = Field(..., description="创建时间")
    modified_time: str = Field(..., description="修改时间")
    guid: str = Field("", description="唯一标识符")
    size_tables: Dict[str, SizeTable] = Field(
        default_factory=dict, description="尺寸表")
    material_tables: Dict[str, MaterialTable] = Field(
        default_factory=dict, description="材料表")
    rating_tables: Dict[str, RatingTable] = Field(
        default_factory=dict, description="压力等级表")
    connection_tables: Dict[str, ConnectionTable] = Field(
        default_factory=dict, description="连接方式表")
    end_prep_tables: Dict[str, EndPrepTable] = Field(
        default_factory=dict, description="端部处理表")
    wall_thickness_tables: Dict[str, WallThicknessTable] = Field(
        default_factory=dict, description="壁厚表")
    type_tables: Dict[str, TypeTable] = Field(
        default_factory=dict, description="类型表")
    schedule_tables: Dict[str, ScheduleTable] = Field(
        default_factory=dict, description="管表号表")
    thickness_tables: Dict[str, ThicknessTable] = Field(
        default_factory=dict, description="壁厚表")


class CADWorxCatParser:
    """CADWorx元件库(CAT)文件解析器"""

    def __init__(self, data_dir: str = None):
        """
        初始化解析器
        
        Args:
            data_dir: 数据目录路径，默认为当前目录下的dataset
        """
        if data_dir is None:
            data_dir = os.path.join(os.path.dirname(__file__), 'dataset')
        self.data_dir = Path(data_dir)
        self.output_dir = self.data_dir / "pipe_standards" / "parsed"
        # 确保输出目录存在
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.current_standard: Optional[PipeStandard] = None
        self.current_table: Optional[SizeTable] = None
        self.current_size: Optional[PipeSize] = None

    def _convert_timestamp(self, timestamp: str) -> str:
        """
        将Unix时间戳转换为可读的日期时间格式
        
        Args:
            timestamp: Unix时间戳字符串
            
        Returns:
            str: 格式化的日期时间字符串 (YYYY-MM-DD HH:MM:SS)
        """
        try:
            # 将时间戳转换为整数
            ts = int(timestamp)
            # 转换为datetime对象
            dt = datetime.fromtimestamp(ts)
            # 格式化为字符串
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        except (ValueError, TypeError):
            # 如果转换失败，返回原始时间戳
            return timestamp

    def _parse_size_table(self, table_elem: ET.Element) -> SizeTable:
        """解析尺寸表数据
        
        Args:
            table_elem: 尺寸表XML元素
            
        Returns:
            SizeTable: 解析后的尺寸表数据
        """
        # 解析表属性
        table_data = {
            'id': int(table_elem.get('ID', '0')),
            'name': table_elem.get('Name', ''),
            'version': table_elem.get('Version', ''),
            'description': table_elem.get('Desc', ''),
            'created_time': self._convert_timestamp(table_elem.get('CreatedTime', '')),
            'modified_time': self._convert_timestamp(table_elem.get('ModifiedTime', '')),
            'created_by': table_elem.get('CreatedBy', ''),
            'edited_by': table_elem.get('EditedBy', ''),
            'status': table_elem.get('Status', ''),
            'base_id': int(table_elem.get('BaseID', '-1')),
            'unit': table_elem.get('Unit', ''),
            'default_part_number': table_elem.get('DefaultPartNumber', ''),
            'max_row_id': int(table_elem.get('MaxRowId', '0')),
            'type': int(table_elem.get('Type', '0')),
            'fields': [],
            'sizes': []
        }

        # 解析字段定义
        fields_elem = table_elem.find('Fields')
        if fields_elem is not None:
            for field_elem in fields_elem.findall('Field'):
                field_data = {
                    'index': int(field_elem.get('Index', '0')),
                    'order': int(field_elem.get('Order', '0')),
                    'type': int(field_elem.get('Type', '0')),
                    'name': field_elem.get('Name', ''),
                    'title': field_elem.get('Title', ''),
                    'description': field_elem.get('Desc', ''),
                    'data_type': field_elem.get('DataType', ''),
                    'required': field_elem.get('Required', '0') == '1'
                }
                table_data['fields'].append(PipeField(**field_data))

        # 解析尺寸数据
        print(f"开始解析尺寸数据，表名: {table_data['name']}")
        for size_elem in table_elem.findall('SizeItem'):
            try:
                nom = float(size_elem.get('NOM', '0'))
                size_data = [
                    size_elem.get('ID', '0'),
                    str(nom),
                    size_elem.get('OD', '0'),
                    size_elem.get('DESCRIPTION', f"DN{nom}"),
                    size_elem.get('PTN', '')
                ]
                table_data['sizes'].append(size_data)
                print(f"成功解析尺寸: DN{nom}")
            except Exception as e:
                print(f"解析尺寸数据时出错: {str(e)}")
                print(f"问题数据: {size_elem.attrib}")
                raise

        print(f"尺寸表解析完成，共解析 {len(table_data['sizes'])} 个尺寸")
        return SizeTable(**table_data)

    def _parse_material_table(self, table_elem: ET.Element) -> MaterialTable:
        """解析材料表数据
        
        Args:
            table_elem: 材料表XML元素
            
        Returns:
            MaterialTable: 解析后的材料表数据
        """
        # 解析表属性
        table_data = {
            'id': int(table_elem.get('ID', '0')),
            'name': table_elem.get('Name', ''),
            'version': table_elem.get('Version', ''),
            'description': table_elem.get('Desc', ''),
            'created_time': self._convert_timestamp(table_elem.get('CreatedTime', '')),
            'modified_time': self._convert_timestamp(table_elem.get('ModifiedTime', '')),
            'created_by': table_elem.get('CreatedBy', ''),
            'edited_by': table_elem.get('EditedBy', ''),
            'status': table_elem.get('Status', ''),
            'base_id': int(table_elem.get('BaseID', '-1')),
            'unit': table_elem.get('Unit', ''),
            'default_part_number': table_elem.get('DefaultPartNumber', ''),
            'max_row_id': int(table_elem.get('MaxRowId', '0')),
            'type': int(table_elem.get('Type', '0')),
            'fields': [],
            'items': {}
        }

        # 解析字段定义
        fields_elem = table_elem.find('Fields')
        if fields_elem is not None:
            for field_elem in fields_elem.findall('Field'):
                field_data = {
                    'index': int(field_elem.get('Index', '0')),
                    'order': int(field_elem.get('Order', '0')),
                    'type': int(field_elem.get('Type', '0')),
                    'name': field_elem.get('Name', ''),
                    'title': field_elem.get('Title', ''),
                    'description': field_elem.get('Desc', ''),
                    'data_type': field_elem.get('DataType', ''),
                    'required': field_elem.get('Required', '0') == '1'
                }
                table_data['fields'].append(PipeField(**field_data))

        # 解析材料数据
        for item_elem in table_elem.findall('MaterialItem'):
            item_data = {
                'id': int(item_elem.get('ID', '0')),
                'code': item_elem.get('CODE', ''),
                'name': item_elem.get('NAME', ''),
                'description': item_elem.get('DESCRIPTION', ''),
                'status': item_elem.get('STATUS', ''),
                'spec': item_elem.get('SPEC', ''),
                'grade': item_elem.get('GRADE', ''),
                'type': item_elem.get('TYPE', '')
            }
            item = MaterialItem(**item_data)
            table_data['items'][item.code] = item

        return MaterialTable(**table_data)

    def _parse_rating_table(self, table_elem: ET.Element) -> RatingTable:
        """解析压力等级表数据
        
        Args:
            table_elem: 压力等级表XML元素
            
        Returns:
            RatingTable: 解析后的压力等级表数据
        """
        # 解析表属性
        table_data = {
            'id': int(table_elem.get('ID', '0')),
            'name': table_elem.get('Name', ''),
            'version': table_elem.get('Version', ''),
            'description': table_elem.get('Desc', ''),
            'created_time': self._convert_timestamp(table_elem.get('CreatedTime', '')),
            'modified_time': self._convert_timestamp(table_elem.get('ModifiedTime', '')),
            'created_by': table_elem.get('CreatedBy', ''),
            'edited_by': table_elem.get('EditedBy', ''),
            'status': table_elem.get('Status', ''),
            'base_id': int(table_elem.get('BaseID', '-1')),
            'unit': table_elem.get('Unit', ''),
            'default_part_number': table_elem.get('DefaultPartNumber', ''),
            'max_row_id': int(table_elem.get('MaxRowId', '0')),
            'type': int(table_elem.get('Type', '0')),
            'fields': [],
            'items': {}
        }

        # 解析字段定义
        fields_elem = table_elem.find('Fields')
        if fields_elem is not None:
            for field_elem in fields_elem.findall('Field'):
                field_data = {
                    'index': int(field_elem.get('Index', '0')),
                    'order': int(field_elem.get('Order', '0')),
                    'type': int(field_elem.get('Type', '0')),
                    'name': field_elem.get('Name', ''),
                    'title': field_elem.get('Title', ''),
                    'description': field_elem.get('Desc', ''),
                    'data_type': field_elem.get('DataType', ''),
                    'required': field_elem.get('Required', '0') == '1'
                }
                table_data['fields'].append(PipeField(**field_data))

        # 解析压力等级数据
        for item_elem in table_elem.findall('RatingItem'):
            item_data = {
                'id': int(item_elem.get('ID', '0')),
                'code': item_elem.get('CODE', ''),
                'name': item_elem.get('NAME', ''),
                'description': item_elem.get('DESCRIPTION', ''),
                'status': item_elem.get('STATUS', ''),
                'pressure': float(item_elem.get('PRESSURE', '0')),
                'temperature': float(item_elem.get('TEMPERATURE', '0')),
                'unit': item_elem.get('UNIT', '')
            }
            item = RatingItem(**item_data)
            table_data['items'][item.code] = item

        return RatingTable(**table_data)

    def _parse_connection_table(self, table_elem: ET.Element) -> ConnectionTable:
        """解析连接方式表数据
        
        Args:
            table_elem: 连接方式表XML元素
            
        Returns:
            ConnectionTable: 解析后的连接方式表数据
        """
        # 解析表属性
        table_data = {
            'id': int(table_elem.get('ID', '0')),
            'name': table_elem.get('Name', ''),
            'version': table_elem.get('Version', ''),
            'description': table_elem.get('Desc', ''),
            'created_time': self._convert_timestamp(table_elem.get('CreatedTime', '')),
            'modified_time': self._convert_timestamp(table_elem.get('ModifiedTime', '')),
            'created_by': table_elem.get('CreatedBy', ''),
            'edited_by': table_elem.get('EditedBy', ''),
            'status': table_elem.get('Status', ''),
            'base_id': int(table_elem.get('BaseID', '-1')),
            'unit': table_elem.get('Unit', ''),
            'default_part_number': table_elem.get('DefaultPartNumber', ''),
            'max_row_id': int(table_elem.get('MaxRowId', '0')),
            'type': int(table_elem.get('Type', '0')),
            'fields': [],
            'items': {}
        }

        # 解析字段定义
        fields_elem = table_elem.find('Fields')
        if fields_elem is not None:
            for field_elem in fields_elem.findall('Field'):
                field_data = {
                    'index': int(field_elem.get('Index', '0')),
                    'order': int(field_elem.get('Order', '0')),
                    'type': int(field_elem.get('Type', '0')),
                    'name': field_elem.get('Name', ''),
                    'title': field_elem.get('Title', ''),
                    'description': field_elem.get('Desc', ''),
                    'data_type': field_elem.get('DataType', ''),
                    'required': field_elem.get('Required', '0') == '1'
                }
                table_data['fields'].append(PipeField(**field_data))

        # 解析连接方式数据
        for item_elem in table_elem.findall('ConnectionItem'):
            item_data = {
                'id': int(item_elem.get('ID', '0')),
                'code': item_elem.get('CODE', ''),
                'name': item_elem.get('NAME', ''),
                'description': item_elem.get('DESCRIPTION', ''),
                'status': item_elem.get('STATUS', ''),
                'type': item_elem.get('TYPE', ''),
                'standard': item_elem.get('STANDARD', '')
            }
            item = ConnectionItem(**item_data)
            table_data['items'][item.code] = item

        return ConnectionTable(**table_data)

    def _parse_end_prep_table(self, table_elem: ET.Element) -> EndPrepTable:
        """解析端部处理表数据
        
        Args:
            table_elem: 端部处理表XML元素
            
        Returns:
            EndPrepTable: 解析后的端部处理表数据
        """
        # 解析表属性
        table_data = {
            'id': int(table_elem.get('ID', '0')),
            'name': table_elem.get('Name', ''),
            'version': table_elem.get('Version', ''),
            'description': table_elem.get('Desc', ''),
            'created_time': self._convert_timestamp(table_elem.get('CreatedTime', '')),
            'modified_time': self._convert_timestamp(table_elem.get('ModifiedTime', '')),
            'created_by': table_elem.get('CreatedBy', ''),
            'edited_by': table_elem.get('EditedBy', ''),
            'status': table_elem.get('Status', ''),
            'base_id': int(table_elem.get('BaseID', '-1')),
            'unit': table_elem.get('Unit', ''),
            'default_part_number': table_elem.get('DefaultPartNumber', ''),
            'max_row_id': int(table_elem.get('MaxRowId', '0')),
            'type': int(table_elem.get('Type', '0')),
            'fields': [],
            'items': {}
        }

        # 解析字段定义
        fields_elem = table_elem.find('Fields')
        if fields_elem is not None:
            for field_elem in fields_elem.findall('Field'):
                field_data = {
                    'index': int(field_elem.get('Index', '0')),
                    'order': int(field_elem.get('Order', '0')),
                    'type': int(field_elem.get('Type', '0')),
                    'name': field_elem.get('Name', ''),
                    'title': field_elem.get('Title', ''),
                    'description': field_elem.get('Desc', ''),
                    'data_type': field_elem.get('DataType', ''),
                    'required': field_elem.get('Required', '0') == '1'
                }
                table_data['fields'].append(PipeField(**field_data))

        # 解析端部处理数据
        for item_elem in table_elem.findall('EndPrepItem'):
            item_data = {
                'id': int(item_elem.get('ID', '0')),
                'code': item_elem.get('CODE', ''),
                'name': item_elem.get('NAME', ''),
                'description': item_elem.get('DESCRIPTION', ''),
                'status': item_elem.get('STATUS', ''),
                'type': item_elem.get('TYPE', ''),
                'standard': item_elem.get('STANDARD', '')
            }
            item = EndPrepItem(**item_data)
            table_data['items'][item.code] = item

        return EndPrepTable(**table_data)

    def _parse_wall_thickness_table(self, table_elem: ET.Element) -> WallThicknessTable:
        """解析壁厚表数据
        
        Args:
            table_elem: 壁厚表XML元素
            
        Returns:
            WallThicknessTable: 解析后的壁厚表数据
        """
        # 解析表属性
        table_data = {
            'id': int(table_elem.get('ID', '0')),
            'name': table_elem.get('Name', ''),
            'version': table_elem.get('Version', ''),
            'description': table_elem.get('Desc', ''),
            'created_time': self._convert_timestamp(table_elem.get('CreatedTime', '')),
            'modified_time': self._convert_timestamp(table_elem.get('ModifiedTime', '')),
            'created_by': table_elem.get('CreatedBy', ''),
            'edited_by': table_elem.get('EditedBy', ''),
            'status': table_elem.get('Status', ''),
            'base_id': int(table_elem.get('BaseID', '-1')),
            'unit': table_elem.get('Unit', ''),
            'default_part_number': table_elem.get('DefaultPartNumber', ''),
            'max_row_id': int(table_elem.get('MaxRowId', '0')),
            'type': int(table_elem.get('Type', '0')),
            'fields': [],
            'items': {}
        }

        # 解析字段定义
        fields_elem = table_elem.find('Fields')
        if fields_elem is not None:
            for field_elem in fields_elem.findall('Field'):
                field_data = {
                    'index': int(field_elem.get('Index', '0')),
                    'order': int(field_elem.get('Order', '0')),
                    'type': int(field_elem.get('Type', '0')),
                    'name': field_elem.get('Name', ''),
                    'title': field_elem.get('Title', ''),
                    'description': field_elem.get('Desc', ''),
                    'data_type': field_elem.get('DataType', ''),
                    'required': field_elem.get('Required', '0') == '1'
                }
                table_data['fields'].append(PipeField(**field_data))

        # 解析壁厚数据
        for item_elem in table_elem.findall('WallThicknessItem'):
            item_data = {
                'id': int(item_elem.get('ID', '0')),
                'code': item_elem.get('CODE', ''),
                'name': item_elem.get('NAME', ''),
                'description': item_elem.get('DESCRIPTION', ''),
                'status': item_elem.get('STATUS', ''),
                'value': float(item_elem.get('VALUE', '0')),
                'unit': item_elem.get('UNIT', '')
            }
            item = WallThicknessItem(**item_data)
            table_data['items'][item.code] = item

        return WallThicknessTable(**table_data)

    def _parse_type_table(self, table_elem: ET.Element) -> TypeTable:
        """解析类型表数据
        
        Args:
            table_elem: 类型表XML元素
            
        Returns:
            TypeTable: 解析后的类型表数据
        """
        # 解析表属性
        table_data = {
            'id': int(table_elem.get('ID', '0')),
            'name': table_elem.get('Name', ''),
            'version': table_elem.get('Version', ''),
            'description': table_elem.get('Desc', ''),
            'created_time': self._convert_timestamp(table_elem.get('CreatedTime', '')),
            'modified_time': self._convert_timestamp(table_elem.get('ModifiedTime', '')),
            'created_by': table_elem.get('CreatedBy', ''),
            'edited_by': table_elem.get('EditedBy', ''),
            'status': table_elem.get('Status', ''),
            'base_id': int(table_elem.get('BaseID', '-1')),
            'unit': table_elem.get('Unit', ''),
            'default_part_number': table_elem.get('DefaultPartNumber', ''),
            'max_row_id': int(table_elem.get('MaxRowId', '0')),
            'type': int(table_elem.get('Type', '0')),
            'fields': [],
            'items': {}
        }

        # 解析字段定义
        fields_elem = table_elem.find('Fields')
        if fields_elem is not None:
            for field_elem in fields_elem.findall('Field'):
                field_data = {
                    'index': int(field_elem.get('Index', '0')),
                    'order': int(field_elem.get('Order', '0')),
                    'type': int(field_elem.get('Type', '0')),
                    'name': field_elem.get('Name', ''),
                    'title': field_elem.get('Title', ''),
                    'description': field_elem.get('Desc', ''),
                    'data_type': field_elem.get('DataType', ''),
                    'required': field_elem.get('Required', '0') == '1'
                }
                table_data['fields'].append(PipeField(**field_data))

        # 解析类型数据
        for item_elem in table_elem.findall('TypeItem'):
            item_data = {
                'id': int(item_elem.get('ID', '0')),
                'code': item_elem.get('CODE', ''),
                'name': item_elem.get('NAME', ''),
                'description': item_elem.get('DESCRIPTION', ''),
                'status': item_elem.get('STATUS', ''),
                'category': item_elem.get('CATEGORY', '')
            }
            item = TypeItem(**item_data)
            table_data['items'][item.code] = item

        return TypeTable(**table_data)

    def _parse_schedule_table(self, table_elem: ET.Element) -> ScheduleTable:
        """解析管表号表数据
        
        Args:
            table_elem: 管表号表XML元素
            
        Returns:
            ScheduleTable: 解析后的管表号表数据
        """
        # 解析表属性
        table_data = {
            'id': int(table_elem.get('ID', '0')),
            'name': table_elem.get('Name', ''),
            'version': table_elem.get('Version', ''),
            'description': table_elem.get('Desc', ''),
            'created_time': self._convert_timestamp(table_elem.get('CreatedTime', '')),
            'modified_time': self._convert_timestamp(table_elem.get('ModifiedTime', '')),
            'created_by': table_elem.get('CreatedBy', ''),
            'edited_by': table_elem.get('EditedBy', ''),
            'status': table_elem.get('Status', ''),
            'base_id': int(table_elem.get('BaseID', '-1')),
            'unit': table_elem.get('Unit', ''),
            'default_part_number': table_elem.get('DefaultPartNumber', ''),
            'max_row_id': int(table_elem.get('MaxRowId', '0')),
            'type': int(table_elem.get('Type', '0')),
            'fields': [],
            'items': {}
        }

        # 解析字段定义
        fields_elem = table_elem.find('Fields')
        if fields_elem is not None:
            for field_elem in fields_elem.findall('Field'):
                field_data = {
                    'index': int(field_elem.get('Index', '0')),
                    'order': int(field_elem.get('Order', '0')),
                    'type': int(field_elem.get('Type', '0')),
                    'name': field_elem.get('Name', ''),
                    'title': field_elem.get('Title', ''),
                    'description': field_elem.get('Desc', ''),
                    'data_type': field_elem.get('DataType', ''),
                    'required': field_elem.get('Required', '0') == '1'
                }
                table_data['fields'].append(PipeField(**field_data))

        # 解析管表号数据
        print(f"开始解析管表号数据，表名: {table_data['name']}")
        for item_elem in table_elem.findall('ScheduleItem'):
            try:
                item_data = {
                    'id': int(item_elem.get('ID', '0')),
                    'code': item_elem.get('CODE', ''),
                    'name': item_elem.get('NAME', ''),
                    'description': item_elem.get('DESCRIPTION', ''),
                    'status': item_elem.get('STATUS', 'active'),
                    'value': item_elem.get('VALUE', ''),
                }
                item = ScheduleItem(**item_data)
                table_data['items'][item.code] = item
                print(f"成功解析管表号: {item.code}")
            except Exception as e:
                print(f"解析管表号数据时出错: {str(e)}")
                print(f"问题数据: {item_elem.attrib}")
                raise

        print(f"管表号表解析完成，共解析 {len(table_data['items'])} 个管表号")
        return ScheduleTable(**table_data)

    def _parse_thickness_table(self, table_elem: ET.Element) -> ThicknessTable:
        """解析壁厚表数据
        
        Args:
            table_elem: 壁厚表XML元素
            
        Returns:
            ThicknessTable: 解析后的壁厚表数据
        """
        # 解析表属性
        table_data = {
            'id': int(table_elem.get('ID', '0')),
            'name': table_elem.get('Name', ''),
            'version': table_elem.get('Version', ''),
            'description': table_elem.get('Desc', ''),
            'created_time': self._convert_timestamp(table_elem.get('CreatedTime', '')),
            'modified_time': self._convert_timestamp(table_elem.get('ModifiedTime', '')),
            'created_by': table_elem.get('CreatedBy', ''),
            'edited_by': table_elem.get('EditedBy', ''),
            'status': table_elem.get('Status', ''),
            'base_id': int(table_elem.get('BaseID', '-1')),
            'unit': table_elem.get('Unit', ''),
            'default_part_number': table_elem.get('DefaultPartNumber', ''),
            'max_row_id': int(table_elem.get('MaxRowId', '0')),
            'type': int(table_elem.get('Type', '0')),
            'fields': [],
            'items': {}
        }

        # 解析字段定义
        fields_elem = table_elem.find('Fields')
        if fields_elem is not None:
            for field_elem in fields_elem.findall('Field'):
                field_data = {
                    'index': int(field_elem.get('Index', '0')),
                    'order': int(field_elem.get('Order', '0')),
                    'type': int(field_elem.get('Type', '0')),
                    'name': field_elem.get('Name', ''),
                    'title': field_elem.get('Title', ''),
                    'description': field_elem.get('Desc', ''),
                    'data_type': field_elem.get('DataType', ''),
                    'required': field_elem.get('Required', '0') == '1'
                }
                table_data['fields'].append(PipeField(**field_data))

        # 解析壁厚数据
        print(f"开始解析壁厚数据，表名: {table_data['name']}")
        for item_elem in table_elem.findall('ThicknessItem'):
            try:
                item_data = {
                    'id': int(item_elem.get('ID', '0')),
                    'code': item_elem.get('CODE', ''),
                    'name': item_elem.get('NAME', ''),
                    'description': item_elem.get('DESCRIPTION', ''),
                    'status': item_elem.get('STATUS', 'active'),
                    'value': float(item_elem.get('VALUE', '0')),
                    'unit': item_elem.get('UNIT', 'mm')
                }
                item = ThicknessItem(**item_data)
                table_data['items'][item.code] = item
                print(f"成功解析壁厚: {item.code}")
            except Exception as e:
                print(f"解析壁厚数据时出错: {str(e)}")
                print(f"问题数据: {item_elem.attrib}")
                raise

        print(f"壁厚表解析完成，共解析 {len(table_data['items'])} 个壁厚")
        return ThicknessTable(**table_data)

    def parse(self, file_path: str) -> Dict[str, Any]:
        """
        解析CAT文件
        
        Args:
            file_path: CAT文件路径
            
        Returns:
            Dict[str, Any]: 解析后的数据
        """
        file_path = Path(file_path)
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")

        return self._parse_xml(file_path)

    def _parse_xml(self, file_path: Path) -> Dict[str, Any]:
        """解析XML格式的CAT文件
        
        Args:
            file_path: CAT文件路径
            
        Returns:
            Dict[str, Any]: 解析后的数据
        """
        try:
            # 重置ID计数器
            BaseTable.reset_id_counter()

            tree = ET.parse(file_path)
            root = tree.getroot()
            print(f"正在解析XML文件: {file_path}")
            print(f"根节点标签: {root.tag}")
            print(f"根节点属性: {root.attrib}")

            # 解析目录信息
            catalog_data = {
                'name': root.get('Name', ''),
                'version': root.get('Version', ''),
                'description': root.get('Desc', ''),
                'created_time': self._convert_timestamp(root.get('CreatedTime', '')),
                'modified_time': self._convert_timestamp(root.get('ModifiedTime', '')),
                'guid': root.get('GUID', ''),
                'size_tables': {},
                'material_tables': {},
                'rating_tables': {},
                'connection_tables': {},
                'end_prep_tables': {},
                'wall_thickness_tables': {},
                'type_tables': {},
                'schedule_tables': {},
                'thickness_tables': {}
            }

            # 解析尺寸表
            size_tables = root.find('SizeTables')
            if size_tables is not None:
                print(f"找到尺寸表节点: {size_tables.tag}")
                for table_elem in size_tables.findall('SizeTable'):
                    print(f"处理尺寸表: {table_elem.get('Name', '')}")
                    table = self._parse_size_table(table_elem)
                    catalog_data['size_tables'][table.name] = table
            else:
                print("未找到尺寸表节点")

            # 解析管表号表
            schedule_tables = root.find('ScheduleTables')
            if schedule_tables is not None:
                print(f"找到管表号表节点: {schedule_tables.tag}")
                for table_elem in schedule_tables.findall('ScheduleTable'):
                    print(f"处理管表号表: {table_elem.get('Name', '')}")
                    table = self._parse_schedule_table(table_elem)
                    catalog_data['schedule_tables'][table.name] = table
            else:
                print("未找到管表号表节点")

            # 解析壁厚表
            thickness_tables = root.find('ThicknessTables')
            if thickness_tables is not None:
                print(f"找到壁厚表节点: {thickness_tables.tag}")
                for table_elem in thickness_tables.findall('ThicknessTable'):
                    print(f"处理壁厚表: {table_elem.get('Name', '')}")
                    table = self._parse_thickness_table(table_elem)
                    catalog_data['thickness_tables'][table.name] = table
            else:
                print("未找到壁厚表节点")

            # 解析材料表
            material_tables = root.find('MaterialTables')
            if material_tables is not None:
                for table_elem in material_tables.findall('MaterialTable'):
                    table = self._parse_material_table(table_elem)
                    catalog_data['material_tables'][table.name] = table

            # 解析压力等级表
            rating_tables = root.find('RatingTables')
            if rating_tables is not None:
                for table_elem in rating_tables.findall('RatingTable'):
                    table = self._parse_rating_table(table_elem)
                    catalog_data['rating_tables'][table.name] = table

            # 解析连接方式表
            connection_tables = root.find('ConnectionTables')
            if connection_tables is not None:
                for table_elem in connection_tables.findall('ConnectionTable'):
                    table = self._parse_connection_table(table_elem)
                    catalog_data['connection_tables'][table.name] = table

            # 解析端部处理表
            end_prep_tables = root.find('EndPrepTables')
            if end_prep_tables is not None:
                for table_elem in end_prep_tables.findall('EndPrepTable'):
                    table = self._parse_end_prep_table(table_elem)
                    catalog_data['end_prep_tables'][table.name] = table

            # 解析类型表
            type_tables = root.find('TypeTables')
            if type_tables is not None:
                for table_elem in type_tables.findall('TypeTable'):
                    table = self._parse_type_table(table_elem)
                    catalog_data['type_tables'][table.name] = table

            return catalog_data
        except ET.ParseError as e:
            print(f"XML解析错误: {str(e)}")
            raise
        except Exception as e:
            print(f"解析过程中出现错误: {str(e)}")
            raise

    def _parse_text(self, file_path: Path) -> Dict[str, Any]:
        """解析文本格式的CAT文件
        
        Args:
            file_path: CAT文件路径
            
        Returns:
            Dict[str, Any]: 解析后的数据
        """
        # 初始化标准数据
        self.current_standard = PipeStandard(
            name="",
            version="",
            description="",
            created_time="",
            modified_time="",
            guid="",
            size_tables={},
            material_tables={},
            rating_tables={},
            connection_tables={},
            end_prep_tables={},
            wall_thickness_tables={},
            type_tables={},
            schedule_tables={},
            thickness_tables={}
        )

        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 解析文件内容
        self._parse_content(content)

        # 转换为字典格式
        return self.current_standard.model_dump()

    def _parse_content(self, content: str):
        """解析文件内容
        
        Args:
            content: 文件内容
        """
        # 分割成行
        lines = content.split('\n')
        current_section = None

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 解析标准信息
            if line.startswith('STANDARD'):
                match = re.match(r'STANDARD\s+(\w+)\s+(\d+)', line)
                if match:
                    self.current_standard.name = match.group(1)
                    self.current_standard.version = match.group(2)
                    self.current_standard.description = f"{match.group(1)} {match.group(2)}标准"
                    self.current_standard.created_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    self.current_standard.modified_time = self.current_standard.created_time
                    self.current_standard.guid = str(uuid.uuid4())
            # 解析尺寸表
            elif line.startswith('TABLE'):
                match = re.match(r'TABLE\s+(\w+)', line)
                if match:
                    table_name = match.group(1)
                    self.current_table = SizeTable(
                        id=len(self.current_standard.size_tables) + 1,
                        name=table_name,
                        version="1.0",
                        description=f"{self.current_standard.name} {table_name}尺寸表",
                        created_time=self.current_standard.created_time,
                        modified_time=self.current_standard.modified_time,
                        created_by="system",
                        edited_by="system",
                        status="active",
                        base_id=-1,
                        unit="mm",
                        default_part_number="",
                        max_row_id=0,
                        type=0,
                        fields=[],
                        sizes=[]
                    )
                    self.current_standard.size_tables[table_name] = self.current_table
            # 解析尺寸数据
            elif line.startswith('SIZE') and self.current_table:
                match = re.match(r'SIZE\s+(\d+)\s+([\d.]+)\s+(\w+)', line)
                if match:
                    nom = float(match.group(1))
                    od = float(match.group(2))
                    part_number = match.group(3)

                    size = PipeSize(
                        id=len(self.current_table.sizes) + 1,
                        code=f"DN{nom}",
                        name=f"DN{nom}",
                        description=f"DN{nom}",
                        status="active",
                        nom=nom,
                        od=od,
                        part_number=part_number
                    )

                    self.current_table.sizes.append([
                        str(size.id),
                        str(size.nom),
                        str(size.od),
                        size.description,
                        size.part_number
                    ])

    def save_to_yaml(self, data: Dict[str, Any], output_path: str):
        """保存为YAML文件
        
        Args:
            data: 解析后的数据
            output_path: 输出文件路径
        """
        # 确保输出目录存在
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # 自定义YAML格式
        class CompactListDumper(yaml.Dumper):
            def increase_indent(self, flow=False, indentless=False):
                return super().increase_indent(flow, False)

            def represent_sequence(self, data, flow_style=None):
                # 处理None值
                if data is None:
                    return self.represent_none(None)
                # 对于尺寸列表使用紧凑格式
                if isinstance(data, list) and len(data) > 0 and isinstance(data[0], list):
                    flow_style = True
                # 使用父类的represent_sequence方法，但传入正确的参数
                node = yaml.representer.SafeRepresenter.represent_sequence(
                    self, 'tag:yaml.org,2002:seq', data, flow_style=flow_style)
                # 添加换行
                if flow_style:
                    node.style = 'flow'
                    node.flow_style = True
                return node

        # 注册自定义dumper
        CompactListDumper.add_representer(
            list, CompactListDumper.represent_sequence)

        # 保存为YAML文件
        with open(output_path, 'w', encoding='utf-8') as f:
            # 先转换为字符串
            yaml_str = yaml.dump(data, allow_unicode=True,
                                 sort_keys=False, Dumper=CompactListDumper,
                                 default_flow_style=False)

            # 处理列表项之间的换行和格式
            lines = yaml_str.split('\n')
            formatted_lines = []
            in_list = False

            for line in lines:
                # 检查是否是列表项
                if '[' in line and ']' in line:
                    # 提取列表项内容并确保在一行内
                    item_content = line.strip()
                    # 移除可能的换行符
                    item_content = item_content.replace('\n', '')
                    # 格式化列表项
                    formatted_item = f"  - {item_content}"
                    formatted_lines.append(formatted_item)
                    in_list = True
                elif in_list and line.strip() == '':
                    # 列表项之间的空行
                    formatted_lines.append('')
                else:
                    # 其他行保持不变
                    formatted_lines.append(line)

            # 写入文件
            f.write('\n'.join(formatted_lines))

    def parse_and_save(self, file_path: str, output_name: Optional[str] = None) -> str:
        """解析CAT文件并保存为YAML
        
        Args:
            file_path: CAT文件路径
            output_name: 输出文件名（不含扩展名），如果为None则使用CAT文件的name属性
            
        Returns:
            str: 保存的YAML文件路径
        """
        # 解析文件
        data = self.parse(file_path)

        # 生成输出文件名
        if output_name is None:
            # 使用CAT文件中的name属性，转换为小写
            output_name = data['name'].lower()
            # 添加版本信息（如果有）
            if data['version']:
                output_name = f"{output_name}_{data['version'].replace('.', '_')}"

        # 添加cat后缀和yaml扩展名
        output_path = self.output_dir / f"{output_name}_cat.yaml"

        # 保存数据
        self.save_to_yaml(data, str(output_path))
        print(f"已解析并保存到：{output_path}")

        return str(output_path)

    def get_catalog_statistics(self, file_path: str) -> Dict[str, Any]:
        """
        获取CAT文件的统计信息
        
        Args:
            file_path: CAT文件路径
            
        Returns:
            Dict[str, Any]: 统计信息，包含各种表格的数量和详细信息
        """
        # 解析文件
        data = self.parse(file_path)

        # 初始化统计信息
        stats = {
            'catalog': {
                'name': data['name'],
                'version': data['version'],
                'description': data['description'],
                'created_time': data['created_time'],
                'modified_time': data['modified_time'],
                'guid': data['guid']
            },
            'tables': {
                'size_tables': {
                    'count': len(data['size_tables']),
                    'details': []
                },
                'material_tables': {
                    'count': len(data.get('material_tables', {})),
                    'details': []
                },
                'rating_tables': {
                    'count': len(data.get('rating_tables', {})),
                    'details': []
                },
                'connection_tables': {
                    'count': len(data.get('connection_tables', {})),
                    'details': []
                },
                'end_prep_tables': {
                    'count': len(data.get('end_prep_tables', {})),
                    'details': []
                },
                'wall_thickness_tables': {
                    'count': len(data.get('wall_thickness_tables', {})),
                    'details': []
                },
                'type_tables': {
                    'count': len(data.get('type_tables', {})),
                    'details': []
                },
                'schedule_tables': {
                    'count': len(data.get('schedule_tables', {})),
                    'details': []
                },
                'thickness_tables': {
                    'count': len(data.get('thickness_tables', {})),
                    'details': []
                }
            }
        }

        # 统计尺寸表信息
        for table_name, table in data['size_tables'].items():
            # 从列表格式中提取尺寸值
            size_values = [float(size[1]) for size in table.sizes]
            table_stats = {
                'name': table_name,
                'version': table.version,
                'description': table.description,
                'created_time': table.created_time,
                'modified_time': table.modified_time,
                'size_count': len(table.sizes),
                'fields': [field.name for field in table.fields],
                'size_range': {
                    'min': min(size_values) if size_values else None,
                    'max': max(size_values) if size_values else None
                }
            }
            stats['tables']['size_tables']['details'].append(table_stats)

        # 统计管表号表信息
        for table_name, table in data.get('schedule_tables', {}).items():
            table_stats = {
                'name': table_name,
                'version': table.version,
                'description': table.description,
                'created_time': table.created_time,
                'modified_time': table.modified_time,
                'item_count': len(table.items),
                'fields': [field.name for field in table.fields]
            }
            stats['tables']['schedule_tables']['details'].append(table_stats)

        # 统计壁厚表信息
        for table_name, table in data.get('thickness_tables', {}).items():
            table_stats = {
                'name': table_name,
                'version': table.version,
                'description': table.description,
                'created_time': table.created_time,
                'modified_time': table.modified_time,
                'item_count': len(table.items),
                'fields': [field.name for field in table.fields]
            }
            stats['tables']['thickness_tables']['details'].append(table_stats)

        # 统计其他表格信息（如果有）
        for table_type in ['material_tables', 'rating_tables', 'connection_tables',
                           'end_prep_tables', 'wall_thickness_tables', 'type_tables']:
            if table_type in data:
                for table_name, table in data[table_type].items():
                    table_stats = {
                        'name': table_name,
                        'version': table.version,
                        'description': table.description,
                        'created_time': table.created_time,
                        'modified_time': table.modified_time,
                        'item_count': len(table.items) if hasattr(table, 'items') else 0,
                        'fields': [field.name for field in table.fields] if hasattr(table, 'fields') else []
                    }
                    stats['tables'][table_type]['details'].append(table_stats)

        return stats

    def print_catalog_statistics(self, file_path: str):
        """
        打印CAT文件的统计信息
        
        Args:
            file_path: CAT文件路径
        """
        stats = self.get_catalog_statistics(file_path)

        print("\n=== CAT文件统计信息 ===")
        print(f"\n目录信息:")
        print(f"  名称: {stats['catalog']['name']}")
        print(f"  版本: {stats['catalog']['version']}")
        print(f"  描述: {stats['catalog']['description']}")
        print(f"  创建时间: {stats['catalog']['created_time']}")
        print(f"  修改时间: {stats['catalog']['modified_time']}")
        print(f"  GUID: {stats['catalog']['guid']}")

        print("\n表格统计:")
        for table_type, table_info in stats['tables'].items():
            print(f"\n{table_type}:")
            print(f"  数量: {table_info['count']}")
            if table_info['details']:
                print("  详细信息:")
                for table in table_info['details']:
                    print(f"    - {table['name']}:")
                    print(f"      版本: {table['version']}")
                    print(f"      描述: {table['description']}")
                    if 'size_count' in table:
                        print(f"      尺寸数量: {table['size_count']}")
                    if 'item_count' in table:
                        print(f"      项目数量: {table['item_count']}")
                    if 'size_range' in table and table['size_range']['min'] is not None:
                        print(
                            f"      尺寸范围: {table['size_range']['min']} - {table['size_range']['max']}")
                    if table['fields']:
                        print(f"      字段: {', '.join(table['fields'])}")


class CatalogReporter:
    """CAT文件报告生成器"""

    def __init__(self, stats: Dict[str, Any]):
        """
        初始化报告生成器
        
        Args:
            stats: 统计信息
        """
        self.stats = stats
        self.catalog_info = self._get_catalog_info()
        self.table_stats = self._get_table_stats()
        self.report_lines = []

    def _get_catalog_info(self) -> Dict[str, str]:
        """获取目录信息"""
        catalog = self.stats['catalog']
        return {
            'name': catalog['name'],
            'version': catalog['version'],
            'description': catalog['description'],
            'created_time': catalog['created_time'],
            'modified_time': catalog['modified_time'],
            'guid': catalog['guid']
        }

    def _get_table_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取表格统计信息"""
        table_stats = {}
        for table_type, table_info in self.stats['tables'].items():
            if table_info['count'] > 0:  # 只统计有数据的表格
                table_stats[table_type] = {
                    'count': table_info['count'],
                    'details': []
                }
                for table in table_info['details']:
                    detail = {
                        'name': table['name'],
                        'version': table['version'],
                        'description': table['description']
                    }
                    if 'size_count' in table:
                        detail['size_count'] = table['size_count']
                    if 'item_count' in table:
                        detail['item_count'] = table['item_count']
                    if 'size_range' in table:
                        detail['size_range'] = table['size_range']
                    if table['fields']:
                        detail['fields'] = table['fields']
                    table_stats[table_type]['details'].append(detail)
        return table_stats

    def _add_line(self, line: str = "", indent: int = 0):
        """添加一行输出"""
        if line:
            self.report_lines.append("  " * indent + line)
        else:
            self.report_lines.append("")

    def _format_catalog_info(self):
        """格式化目录信息"""
        self._add_line("=== CAT文件统计信息 ===")
        self._add_line()
        self._add_line("目录信息:")
        self._add_line(f"名称: {self.catalog_info['name']}", 1)
        self._add_line(f"版本: {self.catalog_info['version']}", 1)
        self._add_line(f"描述: {self.catalog_info['description']}", 1)
        self._add_line(f"创建时间: {self.catalog_info['created_time']}", 1)
        self._add_line(f"修改时间: {self.catalog_info['modified_time']}", 1)
        self._add_line(f"GUID: {self.catalog_info['guid']}", 1)

    def _format_table_info(self, table: Dict[str, Any], indent: int = 0):
        """格式化表格信息"""
        self._add_line(f"版本: {table['version']}", indent)
        self._add_line(f"描述: {table['description']}", indent)

        if 'size_count' in table:
            self._add_line(f"尺寸数量: {table['size_count']}", indent)
        if 'item_count' in table:
            self._add_line(f"项目数量: {table['item_count']}", indent)
        if 'size_range' in table:
            self._add_line(
                f"尺寸范围: {table['size_range']['min']} - {table['size_range']['max']}", indent)
        if 'fields' in table:
            self._add_line(f"字段: {', '.join(table['fields'])}", indent)

    def _format_table_stats(self):
        """格式化表格统计信息"""
        self._add_line()
        self._add_line("表格统计:")
        for table_type, table_info in self.table_stats.items():
            self._add_line()
            self._add_line(f"{table_type}:")
            self._add_line(f"数量: {table_info['count']}", 1)
            if table_info['details']:
                self._add_line("详细信息:", 1)
                for table in table_info['details']:
                    self._add_line(f"- {table['name']}:", 2)
                    self._format_table_info(table, 3)

    def generate_report(self) -> str:
        """生成报告
        
        Returns:
            str: 格式化的报告文本
        """
        self.report_lines = []  # 清空之前的输出
        self._format_catalog_info()
        self._format_table_stats()
        return "\n".join(self.report_lines)


def main():
    """主函数"""
    # 获取当前目录
    current_dir = Path(__file__).parent

    # 设置输入文件路径
    cat_file = current_dir / "dataset" / "GB-B.cat"  # 修正文件名大小写

    # 创建解析器
    parser = CADWorxCatParser()

    try:
        # 解析并保存（指定输出名）
        output_path = parser.parse_and_save(str(cat_file), "gb_b_2015")
        print(f"指定输出：{output_path}")  # 例如：gb_b_2015_cat.yaml

        # 获取统计信息并生成报告
        stats = parser.get_catalog_statistics(str(cat_file))
        reporter = CatalogReporter(stats)
        print(reporter.generate_report())
    except FileNotFoundError as e:
        print(f"错误：找不到文件 {cat_file}")
        print("请确保文件路径正确：")
        print(f"当前目录: {current_dir}")
        print(f"期望文件: {cat_file}")
    except Exception as e:
        print(f"错误：{str(e)}")
        print("详细错误信息：")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
