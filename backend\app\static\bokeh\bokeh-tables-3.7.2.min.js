'use strict';
/*!
 * Copyright (c) Anaconda, Inc., and Bokeh Contributors
 * All rights reserved.
 * 
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 * 
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * 
 * Redistributions in binary form must reproduce the above copyright notice,
 * this list of conditions and the following disclaimer in the documentation
 * and/or other materials provided with the distribution.
 * 
 * Neither the name of <PERSON><PERSON><PERSON> nor the names of any contributors
 * may be used to endorse or promote products derived from this software
 * without specific prior written permission.
 * 
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENT<PERSON>, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
 * THE POSSIBILITY OF SUCH DAMAGE.
 */
(function(root, factory) {
  factory(root["Bokeh"], "3.7.2");
})(this, function(Bokeh, version) {
  let define;
  return (function(modules, entry, aliases, externals) {
    const bokeh = typeof Bokeh !== "undefined" ? (version != null ? Bokeh[version] : Bokeh) : null;
    if (bokeh != null) {
      return bokeh.register_plugin(modules, entry, aliases);
    } else {
      throw new Error("Cannot find Bokeh" + (version != null ? " " + version : "") + ". You have to load it prior to loading plugins.");
    }
  })
({
694: function _(t,e,o,r,s){r();const _=t(1).__importStar(t(695));o.Tables=_;(0,t(7).register_models)(_)},
695: function _(g,a,r,e,t){e();const o=g(1);o.__exportStar(g(696),r),o.__exportStar(g(699),r),t("DataTable",g(702).DataTable),t("TableColumn",g(720).TableColumn),t("TableWidget",g(719).TableWidget);var n=g(722);t("AvgAggregator",n.AvgAggregator),t("MinAggregator",n.MinAggregator),t("MaxAggregator",n.MaxAggregator),t("SumAggregator",n.SumAggregator);var A=g(723);t("GroupingInfo",A.GroupingInfo),t("DataCube",A.DataCube)},
696: function _(e,t,i,s,r){var a,l,n,u,d,o,p,_,c;s();const h=e(1),E=e(63),V=e(8),m=e(57),f=e(51),w=e(697),g=h.__importStar(e(698));class x extends m.DOMComponentView{get emptyValue(){return null}constructor(e){const{model:t,parent:i}=e.column;super({model:t,parent:i,...e}),this.args=e,this.initialize(),this.render()}initialize(){super.initialize(),this.inputEl=this._createInput(),this.defaultValue=null}async lazy_initialize(){throw new Error("unsupported")}css_classes(){return super.css_classes().concat(g.cell_editor)}render(){this.args.container.append(this.el),this.shadow_el.appendChild(this.inputEl),this.renderEditor(),this.disableNavigation()}renderEditor(){}disableNavigation(){this.inputEl.addEventListener("keydown",(e=>{switch(e.key){case"ArrowLeft":case"ArrowRight":case"ArrowUp":case"ArrowDown":case"PageUp":case"PageDown":e.stopImmediatePropagation()}}))}destroy(){this.remove()}focus(){this.inputEl.focus()}show(){}hide(){}position(){}getValue(){return this.inputEl.value}setValue(e){this.inputEl.value=e}serializeValue(){return this.getValue()}isValueChanged(){return!(""==this.getValue()&&null==this.defaultValue)&&this.getValue()!==this.defaultValue}applyValue(e,t){const i=this.args.grid.getData(),s=i.index.indexOf(e[w.DTINDEX_NAME]);i.setField(s,this.args.column.field,t)}loadValue(e){const t=e[this.args.column.field];this.defaultValue=null!=t?t:this.emptyValue,this.setValue(this.defaultValue)}validateValue(e){if(this.args.column.validator){const t=this.args.column.validator(e);if(!t.valid)return t}return{valid:!0,msg:null}}validate(){return this.validateValue(this.getValue())}}i.CellEditorView=x,x.__name__="CellEditorView";class v extends f.Model{}i.CellEditor=v,v.__name__="CellEditor";class y extends x{get emptyValue(){return""}_createInput(){return(0,E.input)({type:"text"})}renderEditor(){this.inputEl.focus(),this.inputEl.select()}loadValue(e){super.loadValue(e),this.inputEl.defaultValue=this.defaultValue,this.inputEl.select()}}i.StringEditorView=y,y.__name__="StringEditorView";class I extends v{}i.StringEditor=I,a=I,I.__name__="StringEditor",a.prototype.default_view=y,a.define((({Str:e,List:t})=>({completions:[t(e),[]]})));class N extends x{_createInput(){return(0,E.textarea)()}renderEditor(){this.inputEl.focus(),this.inputEl.select()}}i.TextEditorView=N,N.__name__="TextEditorView";class b extends v{}i.TextEditor=b,l=b,b.__name__="TextEditor",l.prototype.default_view=N;class C extends x{_createInput(){return(0,E.select)()}renderEditor(){for(const e of this.model.options)this.inputEl.appendChild((0,E.option)({value:e},e));this.focus()}}i.SelectEditorView=C,C.__name__="SelectEditorView";class S extends v{}i.SelectEditor=S,n=S,S.__name__="SelectEditor",n.prototype.default_view=C,n.define((({Str:e,List:t})=>({options:[t(e),[]]})));class D extends x{_createInput(){return(0,E.input)({type:"text"})}}i.PercentEditorView=D,D.__name__="PercentEditorView";class k extends v{}i.PercentEditor=k,u=k,k.__name__="PercentEditor",u.prototype.default_view=D;class z extends x{_createInput(){return(0,E.input)({type:"checkbox"})}renderEditor(){this.focus()}loadValue(e){this.defaultValue=!!e[this.args.column.field],this.inputEl.checked=this.defaultValue}serializeValue(){return this.inputEl.checked}}i.CheckboxEditorView=z,z.__name__="CheckboxEditorView";class P extends v{}i.CheckboxEditor=P,d=P,P.__name__="CheckboxEditor",d.prototype.default_view=z;class T extends x{_createInput(){return(0,E.input)({type:"text"})}renderEditor(){this.inputEl.focus(),this.inputEl.select()}remove(){super.remove()}serializeValue(){const e=parseInt(this.getValue(),10);return isNaN(e)?0:e}loadValue(e){super.loadValue(e),this.inputEl.defaultValue=this.defaultValue,this.inputEl.select()}validateValue(e){return(0,V.isString)(e)&&(e=Number(e)),(0,V.isInteger)(e)?super.validateValue(e):{valid:!1,msg:"Please enter a valid integer"}}}i.IntEditorView=T,T.__name__="IntEditorView";class A extends v{}i.IntEditor=A,o=A,A.__name__="IntEditor",o.prototype.default_view=T,o.define((({Int:e})=>({step:[e,1]})));class L extends x{_createInput(){return(0,E.input)({type:"text"})}renderEditor(){this.inputEl.focus(),this.inputEl.select()}remove(){super.remove()}serializeValue(){const e=parseFloat(this.getValue());return isNaN(e)?0:e}loadValue(e){super.loadValue(e),this.inputEl.defaultValue=this.defaultValue,this.inputEl.select()}validateValue(e){return isNaN(e)?{valid:!1,msg:"Please enter a valid number"}:super.validateValue(e)}}i.NumberEditorView=L,L.__name__="NumberEditorView";class F extends v{}i.NumberEditor=F,p=F,F.__name__="NumberEditor",p.prototype.default_view=L,p.define((({Float:e})=>({step:[e,.01]})));class M extends x{_createInput(){return(0,E.input)({type:"text"})}}i.TimeEditorView=M,M.__name__="TimeEditorView";class O extends v{}i.TimeEditor=O,_=O,O.__name__="TimeEditor",_.prototype.default_view=M;class U extends x{_createInput(){return(0,E.input)({type:"text"})}get emptyValue(){return new Date}renderEditor(){this.inputEl.focus(),this.inputEl.select()}destroy(){super.destroy()}show(){super.show()}hide(){super.hide()}position(){return super.position()}getValue(){}setValue(e){}}i.DateEditorView=U,U.__name__="DateEditorView";class R extends v{}i.DateEditor=R,c=R,R.__name__="DateEditor",c.prototype.default_view=U},
697: function _(_,n,i,t,d){t(),i.DTINDEX_NAME="__bkdt_internal_index__"},
698: function _(e,l,t,i,r){i(),t.data_table="bk-data-table",t.cell_special_defaults="bk-cell-special-defaults",t.cell_select="bk-cell-select",t.cell_index="bk-cell-index",t.header_index="bk-header-index",t.cell_editor="bk-cell-editor",t.cell_editor_completion="bk-cell-editor-completion",t.default=':host{--data-table-font-size:var(--font-size);}.bk-data-table{box-sizing:content-box;width:100%;height:100%;font-size:var(--data-table-font-size);}.bk-data-table input[type="checkbox"]{margin-left:4px;margin-right:4px;}.bk-cell-special-defaults{border-right-color:silver;border-right-style:solid;background:#f5f5f5;}.bk-cell-select{border-right-color:silver;border-right-style:solid;background:#f5f5f5;}.slick-cell.bk-cell-index{border-right-color:silver;border-right-style:solid;background:#f5f5f5;text-align:right;background:#f0f0f0;color:#909090;}.bk-header-index .slick-column-name{float:right;}.slick-row.selected .bk-cell-index{background-color:transparent;}.slick-row.odd{background:#f0f0f0;}.slick-cell{padding-left:4px;padding-right:4px;border-right-color:transparent;border:0.25px solid transparent;}.slick-cell .bk{line-height:inherit;}.slick-cell.active{border-style:dashed;}.slick-cell.selected{background-color:#F0F8FF;}.slick-cell.editable{padding-left:0;padding-right:0;}.bk-cell-editor{display:contents;}.bk-cell-editor input,.bk-cell-editor select{width:100%;height:100%;border:0;margin:0;padding:0;outline:0;background:transparent;vertical-align:baseline;}.bk-cell-editor input{padding-left:4px;padding-right:4px;}.bk-cell-editor-completion{font-size:var(--data-table-font-size);}'},
699: function _(e,t,r,o,a){var l,n,s,i,c,u;o();const m=e(1),_=m.__importDefault(e(248)),f=m.__importStar(e(246)),d=e(700),p=m.__importStar(e(19)),h=e(63),g=e(29),F=e(21),b=e(8),S=e(40),x=e(23),N=e(51),y=e(216),v=e(12);class C extends N.Model{constructor(e){super(e)}doFormat(e,t,r,o,a){return null==r?"":`${r}`.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}}r.CellFormatter=C,C.__name__="CellFormatter";class M extends C{constructor(e){super(e)}doFormat(e,t,r,o,a){const{font_style:l,text_align:n,text_color:s,background_color:i}=this;Number.isNaN(r)?r=this.nan_format:null==r&&(r=this.null_format);const c=(0,h.div)(null==r?"":`${r}`);let u;switch((0,g.isValue)(l)?u=l.value:(0,g.isField)(l)?u=a[l.field]:(0,g.isExpr)(l)||(0,v.unreachable)(),u){case"normal":break;case"italic":c.style.fontStyle="italic";break;case"bold":c.style.fontWeight="bold";break;case"bold italic":c.style.fontStyle="italic",c.style.fontWeight="bold"}if((0,g.isValue)(n)?c.style.textAlign=n.value:(0,g.isField)(n)?c.style.textAlign=a[n.field]:(0,g.isExpr)(n)||(0,v.unreachable)(),(0,g.isValue)(s))null!=s.value&&(c.style.color=(0,x.color2css)(s.value));else if((0,g.isField)(s))if(null!=s.transform&&s.transform instanceof y.ColorMapper){const e=s.transform.rgba_mapper.v_compute([a[s.field]]),[t,r,o,l]=e;c.style.color=(0,x.rgba2css)([t,r,o,l])}else c.style.color=(0,x.color2css)(a[s.field]);else(0,g.isExpr)(s)||(0,v.unreachable)();if((0,g.isValue)(i))null!=i.value&&(c.style.backgroundColor=(0,x.color2css)(i.value));else if((0,g.isField)(i))if(null!=i.transform&&i.transform instanceof y.ColorMapper){const e=i.transform.rgba_mapper.v_compute([a[i.field]]),[t,r,o,l]=e;c.style.backgroundColor=(0,x.rgba2css)([t,r,o,l])}else c.style.backgroundColor=(0,x.color2css)(a[i.field]);else(0,g.isExpr)(i)||(0,v.unreachable)();return c.outerHTML}}r.StringFormatter=M,l=M,M.__name__="StringFormatter",l.define((({Str:e})=>({font_style:[p.FontStyleSpec,{value:"normal"}],text_align:[p.TextAlignSpec,{value:"left"}],text_color:[p.ColorSpec,null],background_color:[p.ColorSpec,null],nan_format:[e,"NaN"],null_format:[e,"(null)"]})));class w extends M{constructor(e){super(e)}get scientific_limit_low(){return 10**this.power_limit_low}get scientific_limit_high(){return 10**this.power_limit_high}doFormat(e,t,r,o,a){const l=Math.abs(r)<=this.scientific_limit_low||Math.abs(r)>=this.scientific_limit_high;let n=this.precision;return n<1&&(n=1),r=Number.isNaN(r)?this.nan_format:null==r?this.null_format:0==r?(0,S.to_fixed)(r,1):l?r.toExponential(n):(0,S.to_fixed)(r,n),super.doFormat(e,t,r,o,a)}}r.ScientificFormatter=w,n=w,w.__name__="ScientificFormatter",n.define((({Float:e})=>({precision:[e,10],power_limit_high:[e,5],power_limit_low:[e,-3]}))),n.override({nan_format:"-",null_format:"-"});class T extends M{constructor(e){super(e)}doFormat(e,t,r,o,a){const{format:l,language:n,nan_format:s,null_format:i}=this,c=(()=>{switch(this.rounding){case"round":case"nearest":return Math.round;case"floor":case"rounddown":return Math.floor;case"ceil":case"roundup":return Math.ceil}})();return r=Number.isNaN(r)?s:null==r?i:f.format(r,l,n,c),super.doFormat(e,t,r,o,a)}}r.NumberFormatter=T,s=T,T.__name__="NumberFormatter",s.define((({Str:e})=>({format:[e,"0,0"],language:[e,"en"],rounding:[F.RoundingFunction,"round"]}))),s.override({nan_format:"-",null_format:"-"});class k extends C{constructor(e){super(e)}doFormat(e,t,r,o,a){return r?(0,h.i)({class:this.icon}).outerHTML:""}}r.BooleanFormatter=k,i=k,k.__name__="BooleanFormatter",i.define((({Str:e})=>({icon:[e,"check"]})));class R extends M{constructor(e){super(e)}getFormat(){switch(this.format){case"ATOM":case"W3C":case"RFC-3339":case"ISO-8601":return"%Y-%m-%d";case"COOKIE":return"%a, %d %b %Y";case"RFC-850":return"%A, %d-%b-%y";case"RFC-1123":case"RFC-2822":return"%a, %e %b %Y";case"RSS":case"RFC-822":case"RFC-1036":return"%a, %e %b %y";case"TIMESTAMP":return;default:return this.format}}doFormat(e,t,r,o,a){const l=(()=>{if(null==r||(0,b.isNumber)(r))return r;if((0,b.isString)(r)){const e=Number(r);return isNaN(e)?function(e){const t=/Z$|[+-]\d\d((:?)\d\d)?$/.test(e);return new Date(t?e:`${e}Z`).getTime()}(r):e}return r instanceof Date?r.valueOf():Number(r)})(),n=(()=>Number.isNaN(l)||-9223372036854776==l?this.nan_format:null==r?this.null_format:(0,_.default)(l,this.getFormat()))();return super.doFormat(e,t,n,o,a)}}r.DateFormatter=R,c=R,R.__name__="DateFormatter",c.define((({Str:e})=>({format:[e,"ISO-8601"]}))),c.override({nan_format:"-",null_format:"-"});class E extends C{constructor(e){super(e)}doFormat(e,t,r,o,a){const{template:l}=this;if(null==r)return"";return d._.template(l)({...a,value:r})}}r.HTMLTemplateFormatter=E,u=E,E.__name__="HTMLTemplateFormatter",u.define((({Str:e})=>({template:[e,"<%= value %>"]})))},
700: function _(e,n,t,f,i){var o=e(701),d=o.template;function r(e,n,t){return d(e,n,t)}r._=o,n.exports=r,"function"==typeof define&&define.amd?define((function(){return r})):"undefined"==typeof window&&"undefined"==typeof navigator||(window.UnderscoreTemplate=r)},
701: function _(r,e,n,t,a){
//     (c) 2009-2013 Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
//     Underscore may be freely distributed under the MIT license.
var u={},c=Array.prototype,o=Object.prototype,l=c.slice,i=o.toString,f=o.hasOwnProperty,s=c.forEach,p=Object.keys,_=Array.isArray,h=function(){},v=h.each=h.forEach=function(r,e,n){if(null!=r)if(s&&r.forEach===s)r.forEach(e,n);else if(r.length===+r.length){for(var t=0,a=r.length;t<a;t++)if(e.call(n,r[t],t,r)===u)return}else{var c=h.keys(r);for(t=0,a=c.length;t<a;t++)if(e.call(n,r[c[t]],c[t],r)===u)return}};h.keys=p||function(r){if(r!==Object(r))throw new TypeError("Invalid object");var e=[];for(var n in r)h.has(r,n)&&e.push(n);return e},h.defaults=function(r){return v(l.call(arguments,1),(function(e){if(e)for(var n in e)void 0===r[n]&&(r[n]=e[n])})),r},h.isArray=_||function(r){return"[object Array]"===i.call(r)},h.has=function(r,e){if(!h.isArray(e))return null!=r&&f.call(r,e);for(var n=e.length,t=0;t<n;t++){var a=e[t];if(null==r||!f.call(r,a))return!1;r=r[a]}return!!n};var g={escape:{"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;"}},y={escape:new RegExp("["+h.keys(g.escape).join("")+"]","g")};h.each(["escape"],(function(r){h[r]=function(e){return null==e?"":(""+e).replace(y[r],(function(e){return g[r][e]}))}})),h.templateSettings={evaluate:/<%([\s\S]+?)%>/g,interpolate:/<%=([\s\S]+?)%>/g,escape:/<%-([\s\S]+?)%>/g};var j=/(.)^/,b={"'":"'","\\":"\\","\r":"r","\n":"n","\t":"t","\u2028":"u2028","\u2029":"u2029"},w=/\\|'|\r|\n|\t|\u2028|\u2029/g;h.template=function(r,e,n){var t;n=h.defaults({},n,h.templateSettings);var a=new RegExp([(n.escape||j).source,(n.interpolate||j).source,(n.evaluate||j).source].join("|")+"|$","g"),u=0,c="__p+='";r.replace(a,(function(e,n,t,a,o){return c+=r.slice(u,o).replace(w,(function(r){return"\\"+b[r]})),n&&(c+="'+\n((__t=("+n+"))==null?'':_.escape(__t))+\n'"),t&&(c+="'+\n((__t=("+t+"))==null?'':__t)+\n'"),a&&(c+="';\n"+a+"\n__p+='"),u=o+e.length,e})),c+="';\n",n.variable||(c="with(obj||{}){\n"+c+"}\n"),c="var __t,__p='',__j=Array.prototype.join,print=function(){__p+=__j.call(arguments,'');};\n"+c+"return __p;\n";try{t=new Function(n.variable||"obj","_",c)}catch(r){throw r.source=c,r}if(e)return t(e,h);var o=function(r){return t.call(this,r,h)};return o.source="function("+(n.variable||"obj")+"){\n"+c+"}",o},e.exports=h},
702: function _(e,t,i,s,o){var r;s();const l=e(1),n=e(703),d=e(707),a=e(708),h=e(709),c=e(63),u=e(9),_=e(40),m=e(8),g=e(10),f=e(13),p=e(31),w=e(20),b=e(718),v=e(697),z=e(719),C=e(720),x=e(56),y=l.__importStar(e(698)),A=y,D=l.__importDefault(e(721));i.AutosizeModes={fit_columns:"FCV",fit_viewport:"FVC",force_fit:"LFF",none:"NOA"};let M=!1;class N{constructor(e,t){this.init(e,t)}init(e,t){if(v.DTINDEX_NAME in e.data)throw new Error(`special name ${v.DTINDEX_NAME} cannot be used as a data table column`);this.source=e,this.view=t,this.index=[...this.view.indices]}getLength(){return this.index.length}getItem(e){const t={},i=(0,u.dict)(this.source.data);for(const[s,o]of i){const i=this.index[e],r=(0,p.is_NDArray)(o)?o.get(i):o[i];t[s]=r}return t[v.DTINDEX_NAME]=this.index[e],t}getField(e,t){if(t==v.DTINDEX_NAME)return this.index[e];{const i=(0,u.dict)(this.source.data).get(t)??[],s=this.index[e];return(0,p.is_NDArray)(i)?i.get(s):i[s]}}setField(e,t,i){const s=this.index[e],o=new Map([[t,[[s,i]]]]);this.source.patch(o)}getRecords(){return(0,g.range)(0,this.getLength()).map((e=>this.getItem(e)))}getItems(){return this.getRecords()}slice(e,t,i=1){return t=t??this.getLength(),(0,g.range)(e,t,i).map((e=>this.getItem(e)))}sort(e){let t=e.map((e=>[e.sortCol,e.sortAsc?1:-1]));0==t.length&&(t=[[{field:v.DTINDEX_NAME},1]]);const i=this.getRecords(),s={};this.index.forEach(((e,t)=>s[e]=t)),this.index.sort(((e,o)=>{for(const[r,l]of t){const t=r.field,n=i[s[e]][t],d=i[s[o]][t];if(null!=r.sorter)return l*r.sorter.compute(n,d);if(n!==d){if((0,m.isNumber)(n)&&(0,m.isNumber)(d))return l*(n-d||+isNaN(n)-+isNaN(d));{const e=`${n}`.localeCompare(`${d}`);if(0==e)continue;return l*e}}}return 0}))}}i.TableDataProvider=N,N.__name__="TableDataProvider";class S extends b.WidgetView{constructor(){super(...arguments),this._in_selection_update=!1,this._width=null,this._filtered_selection=[]}get data_source(){return this.model.properties.source}*children(){yield*super.children(),yield this.cds_view}async lazy_initialize(){await super.lazy_initialize(),this.cds_view=await(0,x.build_view)(this.model.view,{parent:this})}remove(){this.cds_view.remove(),this.grid.destroy(),super.remove()}connect_signals(){super.connect_signals(),this.connect(this.model.change,(()=>this.rerender()));for(const e of this.model.columns)this.connect(e.change,(()=>this.rerender()));this.connect(this.model.view.change,(()=>this.updateGrid())),this.connect(this.model.source.selected.change,(()=>this.updateSelection())),this.connect(this.model.source.selected.properties.indices.change,(()=>this.updateSelection()))}stylesheets(){return[...super.stylesheets(),D.default,y.default]}_after_resize(){super._after_resize(),this.grid.resizeCanvas(),this.updateLayout(!0,!1)}_after_layout(){super._after_layout(),this.grid.resizeCanvas(),this.updateLayout(!0,!1)}box_sizing(){const e=super.box_sizing();return"fit_viewport"===this.model.autosize_mode&&null!=this._width&&(e.width=this._width),e}updateLayout(e,t){const s=this.autosize;s===i.AutosizeModes.fit_columns||s===i.AutosizeModes.force_fit?(e||this.grid.resizeCanvas(),this.grid.autosizeColumns()):e&&t&&s===i.AutosizeModes.fit_viewport&&this.invalidate_layout()}updateGrid(){if(this.data.init(this.model.source,this.model.view),this.model.sortable){const e=this.grid.getColumns(),t=this.grid.getSortColumns().map((t=>({sortCol:{field:e[this.grid.getColumnIndex(t.columnId)].field},sortAsc:t.sortAsc})));this.data.sort(t)}this._sync_selected_with_view(),this.updateSelection(),this.grid.invalidate(),this.updateLayout(!0,!0)}updateSelection(){if(!1===this.model.selectable||this._in_selection_update)return;const{indices:e}=this.model.source.selected,t={};this.data.index.forEach(((e,i)=>t[e]=i));const i=(0,g.sort_by)((0,g.map)(e,(e=>t[e])),(e=>e));this._in_selection_update=!0;try{this.grid.setSelectedRows([...i])}finally{this._in_selection_update=!1}const s=this.grid.getViewport(),o=this.model.get_scroll_index(s,i);null!=o&&this.grid.scrollRowToTop(o)}newIndexColumn(){return{id:(0,_.unique_id)(),name:this.model.index_header,field:v.DTINDEX_NAME,width:this.model.index_width,behavior:"select",cannotTriggerInsert:!0,resizable:!1,selectable:!1,sortable:!0,cssClass:A.cell_index,headerCssClass:A.header_index}}get autosize(){let e;return e=!0===this.model.fit_columns?i.AutosizeModes.force_fit:!1===this.model.fit_columns?i.AutosizeModes.none:i.AutosizeModes[this.model.autosize_mode],e}render(){super.render(),this.wrapper_el=(0,c.div)({class:A.data_table}),this.shadow_el.appendChild(this.wrapper_el)}_render_table(){const e=this.model.columns.filter((e=>e.visible)).map((e=>({...e.toColumn(),parent:this})));let t=null;if("checkbox"==this.model.selectable&&(t=new d.CheckboxSelectColumn({cssClass:A.cell_select}),e.unshift(t.getColumnDefinition())),null!=this.model.index_position){const t=this.model.index_position,i=this.newIndexColumn();-1==t?e.push(i):t<-1?e.splice(t+1,0,i):e.splice(t,0,i)}let{reorderable:s}=this.model;!s||"undefined"!=typeof $&&void 0!==$.fn&&"sortable"in $.fn||(M||(w.logger.warn("jquery-ui is required to enable DataTable.reorderable"),M=!0),s=!1);let o=-1,r=!1;const{frozen_rows:l,frozen_columns:c}=this.model,u=null==c?-1:c-1;null!=l&&(r=l<0,o=Math.abs(l));const _={enableCellNavigation:!1!==this.model.selectable,enableColumnReorder:s,autosizeColsMode:this.autosize,multiColumnSort:this.model.sortable,editable:this.model.editable,autoEdit:this.model.auto_edit,autoHeight:!1,rowHeight:this.model.row_height,frozenColumn:u,frozenRow:o,frozenBottom:r,explicitInitialization:!1};if(this.data=new N(this.model.source,this.model.view),this.grid=new h.Grid(this.wrapper_el,this.data,e,_),this.autosize==i.AutosizeModes.fit_viewport){this.grid.autosizeColumns();let t=0;for(const i of e)t+=i.width??0;this._width=Math.ceil(t)}if(this.grid.onSort.subscribe(((e,t)=>{if(!this.model.sortable)return;const i=t.sortCols;null!=i&&(this.data.sort(i),this.grid.invalidate(),this.updateSelection(),this.grid.render(),this.model.header_row||this._hide_header(),this.model.update_sort_columns(i))})),!1!==this.model.selectable){this.grid.setSelectionModel(new n.RowSelectionModel({selectActiveRow:null==t})),null!=t&&this.grid.registerPlugin(t);const e={dataItemColumnValueExtractor(e,t){let i=e[t.field];return(0,m.isString)(i)&&(i=i.replace(/\n/g,"\\n")),i},includeHeaderWhenCopying:!1};this.grid.registerPlugin(new a.CellExternalCopyManager(e)),this.grid.onSelectedRowsChanged.subscribe(((e,t)=>{this._in_selection_update||(this.model.source.selected.indices=t.rows.map((e=>this.data.index[e])))})),this.updateSelection(),this.model.header_row||this._hide_header()}}_after_render(){const e=void 0!==this.grid;this._render_table(),this.updateLayout(e,!1),super._after_render()}_hide_header(){for(const e of this.shadow_el.querySelectorAll(".slick-header-columns"))e.style.height="0px";this.grid.resizeCanvas()}get_selected_rows(){return this.grid.getSelectedRows()}_sync_selected_with_view(){const e=this.data.view.indices,{source:t}=this.data,i=(0,f.filter)(t.selected.indices,(t=>e.get(t))),s=new Set((0,f.filter)(this._filtered_selection,(t=>e.get(t))));this._filtered_selection=[...(0,f.filter)(this._filtered_selection,(e=>!s.has(e))),...(0,f.filter)(t.selected.indices,(t=>!e.get(t)))],t.selected.indices=[...s,...i]}}i.DataTableView=S,S.__name__="DataTableView";class E extends z.TableWidget{get sort_columns(){return this._sort_columns}constructor(e){super(e),this._sort_columns=[]}update_sort_columns(e){this._sort_columns=e.map((({sortCol:e,sortAsc:t})=>({field:e.field,sortAsc:t})))}get_scroll_index(e,t){return this.scroll_to_selection&&0!=t.length?(0,g.some)(t,(t=>e.top<=t&&t<=e.bottom))?null:Math.max(0,Math.min(...t)-1):null}}i.DataTable=E,r=E,E.__name__="DataTable",r.prototype.default_view=S,r.define((({List:e,Bool:t,Int:i,Ref:s,Str:o,Enum:r,Or:l,Nullable:n})=>({autosize_mode:[r("fit_columns","fit_viewport","none","force_fit"),"force_fit"],auto_edit:[t,!1],columns:[e(s(C.TableColumn)),[]],fit_columns:[n(t),null],frozen_columns:[n(i),null],frozen_rows:[n(i),null],sortable:[t,!0],reorderable:[t,!0],editable:[t,!1],selectable:[l(t,r("checkbox")),!0],index_position:[n(i),0],index_header:[o,"#"],index_width:[i,40],scroll_to_selection:[t,!0],header_row:[t,!0],row_height:[i,25]}))),r.override({width:600,height:400})},
703: function _(e,t,n,o,r){var l=e(704),i=e(706);t.exports={RowSelectionModel:function(e){var t,n,o,r=[],c=this,u=new i.EventHandler,s={selectActiveRow:!0};function a(e){return function(){n||(n=!0,e.apply(this,arguments),n=!1)}}function f(e){for(var t=[],n=0;n<e.length;n++)for(var o=e[n].fromRow;o<=e[n].toRow;o++)t.push(o);return t}function h(e){for(var n=[],o=t.getColumns().length-1,r=0;r<e.length;r++)n.push(new i.Range(e[r],0,e[r],o));return n}function w(){return f(r)}function g(e){(r&&0!==r.length||e&&0!==e.length)&&(r=e,c.onSelectedRangesChanged.notify(r))}function v(e,n){o.selectActiveRow&&null!=n.row&&g([new i.Range(n.row,0,n.row,t.getColumns().length-1)])}function p(e){var n=t.getActiveCell();if(t.getOptions().multiSelect&&n&&e.shiftKey&&!e.ctrlKey&&!e.altKey&&!e.metaKey&&(e.which==i.keyCode.UP||e.which==i.keyCode.DOWN)){var o=w();o.sort((function(e,t){return e-t})),o.length||(o=[n.row]);var r,l=o[0],c=o[o.length-1];if((r=e.which==i.keyCode.DOWN?n.row<c||l==c?++c:++l:n.row<c?--c:--l)>=0&&r<t.getDataLength())t.scrollRowIntoView(r),g(h(function(e,t){var n,o=[];for(n=e;n<=t;n++)o.push(n);for(n=t;n<e;n++)o.push(n);return o}(l,c)));e.preventDefault(),e.stopPropagation()}}function y(e){var n=t.getCellFromEvent(e);if(!n||!t.canCellBeActive(n.row,n.cell))return!1;if(!t.getOptions().multiSelect||!e.ctrlKey&&!e.shiftKey&&!e.metaKey)return!1;var o=f(r),i=l.inArray(n.row,o);if(-1===i&&(e.ctrlKey||e.metaKey))o.push(n.row),t.setActiveCell(n.row,n.cell);else if(-1!==i&&(e.ctrlKey||e.metaKey))o=l.grep(o,(function(e,t){return e!==n.row})),t.setActiveCell(n.row,n.cell);else if(o.length&&e.shiftKey){var c=o.pop(),u=Math.min(n.row,c),s=Math.max(n.row,c);o=[];for(var a=u;a<=s;a++)a!==c&&o.push(a);o.push(c),t.setActiveCell(n.row,n.cell)}return g(h(o)),e.stopImmediatePropagation(),!0}l.extend(this,{getSelectedRows:w,setSelectedRows:function(e){g(h(e))},getSelectedRanges:function(){return r},setSelectedRanges:g,init:function(n){o=l.extend(!0,{},s,e),t=n,u.subscribe(t.onActiveCellChanged,a(v)),u.subscribe(t.onKeyDown,a(p)),u.subscribe(t.onClick,a(y))},destroy:function(){u.unsubscribeAll()},pluginName:"RowSelectionModel",onSelectedRangesChanged:new i.Event})}}},
704: function _(e,n,f,o,t){n.exports="undefined"!=typeof $?$:e(705)},
705: function _(e,t,n,r,i){
/*!
     * jQuery JavaScript Library v3.7.1
     * https://jquery.com/
     *
     * Copyright OpenJS Foundation and other contributors
     * Released under the MIT license
     * https://jquery.org/license
     *
     * Date: 2023-08-28T13:37Z
     */
!function(e,n){"use strict";"object"==typeof t&&"object"==typeof t.exports?t.exports=e.document?n(e,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return n(e)}:n(e)}("undefined"!=typeof window?window:this,(function(e,t){"use strict";var n=[],r=Object.getPrototypeOf,i=n.slice,o=n.flat?function(e){return n.flat.call(e)}:function(e){return n.concat.apply([],e)},a=n.push,s=n.indexOf,u={},l=u.toString,c=u.hasOwnProperty,f=c.toString,p=f.call(Object),d={},h=function(e){return"function"==typeof e&&"number"!=typeof e.nodeType&&"function"!=typeof e.item},g=function(e){return null!=e&&e===e.window},v=e.document,y={type:!0,src:!0,nonce:!0,noModule:!0};function m(e,t,n){var r,i,o=(n=n||v).createElement("script");if(o.text=e,t)for(r in y)(i=t[r]||t.getAttribute&&t.getAttribute(r))&&o.setAttribute(r,i);n.head.appendChild(o).parentNode.removeChild(o)}function x(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?u[l.call(e)]||"object":typeof e}var b="3.7.1",w=/HTML$/i,T=function(e,t){return new T.fn.init(e,t)};function C(e){var t=!!e&&"length"in e&&e.length,n=x(e);return!h(e)&&!g(e)&&("array"===n||0===t||"number"==typeof t&&t>0&&t-1 in e)}function S(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}T.fn=T.prototype={jquery:b,constructor:T,length:0,toArray:function(){return i.call(this)},get:function(e){return null==e?i.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){var t=T.merge(this.constructor(),e);return t.prevObject=this,t},each:function(e){return T.each(this,e)},map:function(e){return this.pushStack(T.map(this,(function(t,n){return e.call(t,n,t)})))},slice:function(){return this.pushStack(i.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(T.grep(this,(function(e,t){return(t+1)%2})))},odd:function(){return this.pushStack(T.grep(this,(function(e,t){return t%2})))},eq:function(e){var t=this.length,n=+e+(e<0?t:0);return this.pushStack(n>=0&&n<t?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:a,sort:n.sort,splice:n.splice},T.extend=T.fn.extend=function(){var e,t,n,r,i,o,a=arguments[0]||{},s=1,u=arguments.length,l=!1;for("boolean"==typeof a&&(l=a,a=arguments[s]||{},s++),"object"==typeof a||h(a)||(a={}),s===u&&(a=this,s--);s<u;s++)if(null!=(e=arguments[s]))for(t in e)r=e[t],"__proto__"!==t&&a!==r&&(l&&r&&(T.isPlainObject(r)||(i=Array.isArray(r)))?(n=a[t],o=i&&!Array.isArray(n)?[]:i||T.isPlainObject(n)?n:{},i=!1,a[t]=T.extend(l,o,r)):void 0!==r&&(a[t]=r));return a},T.extend({expando:"jQuery"+(b+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){var t,n;return!(!e||"[object Object]"!==l.call(e))&&(!(t=r(e))||"function"==typeof(n=c.call(t,"constructor")&&t.constructor)&&f.call(n)===p)},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},globalEval:function(e,t,n){m(e,{nonce:t&&t.nonce},n)},each:function(e,t){var n,r=0;if(C(e))for(n=e.length;r<n&&!1!==t.call(e[r],r,e[r]);r++);else for(r in e)if(!1===t.call(e[r],r,e[r]))break;return e},text:function(e){var t,n="",r=0,i=e.nodeType;if(!i)for(;t=e[r++];)n+=T.text(t);return 1===i||11===i?e.textContent:9===i?e.documentElement.textContent:3===i||4===i?e.nodeValue:n},makeArray:function(e,t){var n=t||[];return null!=e&&(C(Object(e))?T.merge(n,"string"==typeof e?[e]:e):a.call(n,e)),n},inArray:function(e,t,n){return null==t?-1:s.call(t,e,n)},isXMLDoc:function(e){var t=e&&e.namespaceURI,n=e&&(e.ownerDocument||e).documentElement;return!w.test(t||n&&n.nodeName||"HTML")},merge:function(e,t){for(var n=+t.length,r=0,i=e.length;r<n;r++)e[i++]=t[r];return e.length=i,e},grep:function(e,t,n){for(var r=[],i=0,o=e.length,a=!n;i<o;i++)!t(e[i],i)!==a&&r.push(e[i]);return r},map:function(e,t,n){var r,i,a=0,s=[];if(C(e))for(r=e.length;a<r;a++)null!=(i=t(e[a],a,n))&&s.push(i);else for(a in e)null!=(i=t(e[a],a,n))&&s.push(i);return o(s)},guid:1,support:d}),"function"==typeof Symbol&&(T.fn[Symbol.iterator]=n[Symbol.iterator]),T.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),(function(e,t){u["[object "+t+"]"]=t.toLowerCase()}));var E=n.pop,k=n.sort,j=n.splice,A="[\\x20\\t\\r\\n\\f]",D=new RegExp("^"+A+"+|((?:^|[^\\\\])(?:\\\\.)*)"+A+"+$","g");T.contains=function(e,t){var n=t&&t.parentNode;return e===n||!(!n||1!==n.nodeType||!(e.contains?e.contains(n):e.compareDocumentPosition&&16&e.compareDocumentPosition(n)))};var N=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g;function q(e,t){return t?"\0"===e?"\ufffd":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e}T.escapeSelector=function(e){return(e+"").replace(N,q)};var L=v,H=a;!function(){var t,r,o,a,u,l,f,p,h,g,v=H,y=T.expando,m=0,x=0,b=ee(),w=ee(),C=ee(),N=ee(),q=function(e,t){return e===t&&(u=!0),0},O="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",P="(?:\\\\[\\da-fA-F]{1,6}"+A+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",M="\\["+A+"*("+P+")(?:"+A+"*([*^$|!~]?=)"+A+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+P+"))|)"+A+"*\\]",R=":("+P+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+M+")*)|.*)\\)|)",I=new RegExp(A+"+","g"),W=new RegExp("^"+A+"*,"+A+"*"),F=new RegExp("^"+A+"*([>+~]|"+A+")"+A+"*"),$=new RegExp(A+"|>"),B=new RegExp(R),_=new RegExp("^"+P+"$"),z={ID:new RegExp("^#("+P+")"),CLASS:new RegExp("^\\.("+P+")"),TAG:new RegExp("^("+P+"|[*])"),ATTR:new RegExp("^"+M),PSEUDO:new RegExp("^"+R),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+A+"*(even|odd|(([+-]|)(\\d*)n|)"+A+"*(?:([+-]|)"+A+"*(\\d+)|))"+A+"*\\)|)","i"),bool:new RegExp("^(?:"+O+")$","i"),needsContext:new RegExp("^"+A+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+A+"*((?:-\\d)?\\d*)"+A+"*\\)|)(?=[^-]|$)","i")},X=/^(?:input|select|textarea|button)$/i,U=/^h\d$/i,V=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,G=/[+~]/,Y=new RegExp("\\\\[\\da-fA-F]{1,6}"+A+"?|\\\\([^\\r\\n\\f])","g"),Q=function(e,t){var n="0x"+e.slice(1)-65536;return t||(n<0?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,1023&n|56320))},J=function(){ue()},K=pe((function(e){return!0===e.disabled&&S(e,"fieldset")}),{dir:"parentNode",next:"legend"});try{v.apply(n=i.call(L.childNodes),L.childNodes),n[L.childNodes.length].nodeType}catch(e){v={apply:function(e,t){H.apply(e,i.call(t))},call:function(e){H.apply(e,i.call(arguments,1))}}}function Z(e,t,n,r){var i,o,a,s,u,c,f,g=t&&t.ownerDocument,m=t?t.nodeType:9;if(n=n||[],"string"!=typeof e||!e||1!==m&&9!==m&&11!==m)return n;if(!r&&(ue(t),t=t||l,p)){if(11!==m&&(u=V.exec(e)))if(i=u[1]){if(9===m){if(!(a=t.getElementById(i)))return n;if(a.id===i)return v.call(n,a),n}else if(g&&(a=g.getElementById(i))&&Z.contains(t,a)&&a.id===i)return v.call(n,a),n}else{if(u[2])return v.apply(n,t.getElementsByTagName(e)),n;if((i=u[3])&&t.getElementsByClassName)return v.apply(n,t.getElementsByClassName(i)),n}if(!(N[e+" "]||h&&h.test(e))){if(f=e,g=t,1===m&&($.test(e)||F.test(e))){for((g=G.test(e)&&se(t.parentNode)||t)==t&&d.scope||((s=t.getAttribute("id"))?s=T.escapeSelector(s):t.setAttribute("id",s=y)),o=(c=ce(e)).length;o--;)c[o]=(s?"#"+s:":scope")+" "+fe(c[o]);f=c.join(",")}try{return v.apply(n,g.querySelectorAll(f)),n}catch(t){N(e,!0)}finally{s===y&&t.removeAttribute("id")}}}return me(e.replace(D,"$1"),t,n,r)}function ee(){var e=[];return function t(n,i){return e.push(n+" ")>r.cacheLength&&delete t[e.shift()],t[n+" "]=i}}function te(e){return e[y]=!0,e}function ne(e){var t=l.createElement("fieldset");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function re(e){return function(t){return S(t,"input")&&t.type===e}}function ie(e){return function(t){return(S(t,"input")||S(t,"button"))&&t.type===e}}function oe(e){return function(t){return"form"in t?t.parentNode&&!1===t.disabled?"label"in t?"label"in t.parentNode?t.parentNode.disabled===e:t.disabled===e:t.isDisabled===e||t.isDisabled!==!e&&K(t)===e:t.disabled===e:"label"in t&&t.disabled===e}}function ae(e){return te((function(t){return t=+t,te((function(n,r){for(var i,o=e([],n.length,t),a=o.length;a--;)n[i=o[a]]&&(n[i]=!(r[i]=n[i]))}))}))}function se(e){return e&&void 0!==e.getElementsByTagName&&e}function ue(e){var t,n=e?e.ownerDocument||e:L;return n!=l&&9===n.nodeType&&n.documentElement?(f=(l=n).documentElement,p=!T.isXMLDoc(l),g=f.matches||f.webkitMatchesSelector||f.msMatchesSelector,f.msMatchesSelector&&L!=l&&(t=l.defaultView)&&t.top!==t&&t.addEventListener("unload",J),d.getById=ne((function(e){return f.appendChild(e).id=T.expando,!l.getElementsByName||!l.getElementsByName(T.expando).length})),d.disconnectedMatch=ne((function(e){return g.call(e,"*")})),d.scope=ne((function(){return l.querySelectorAll(":scope")})),d.cssHas=ne((function(){try{return l.querySelector(":has(*,:jqfake)"),!1}catch(e){return!0}})),d.getById?(r.filter.ID=function(e){var t=e.replace(Y,Q);return function(e){return e.getAttribute("id")===t}},r.find.ID=function(e,t){if(void 0!==t.getElementById&&p){var n=t.getElementById(e);return n?[n]:[]}}):(r.filter.ID=function(e){var t=e.replace(Y,Q);return function(e){var n=void 0!==e.getAttributeNode&&e.getAttributeNode("id");return n&&n.value===t}},r.find.ID=function(e,t){if(void 0!==t.getElementById&&p){var n,r,i,o=t.getElementById(e);if(o){if((n=o.getAttributeNode("id"))&&n.value===e)return[o];for(i=t.getElementsByName(e),r=0;o=i[r++];)if((n=o.getAttributeNode("id"))&&n.value===e)return[o]}return[]}}),r.find.TAG=function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):t.querySelectorAll(e)},r.find.CLASS=function(e,t){if(void 0!==t.getElementsByClassName&&p)return t.getElementsByClassName(e)},h=[],ne((function(e){var t;f.appendChild(e).innerHTML="<a id='"+y+"' href='' disabled='disabled'></a><select id='"+y+"-\r\\' disabled='disabled'><option selected=''></option></select>",e.querySelectorAll("[selected]").length||h.push("\\["+A+"*(?:value|"+O+")"),e.querySelectorAll("[id~="+y+"-]").length||h.push("~="),e.querySelectorAll("a#"+y+"+*").length||h.push(".#.+[+~]"),e.querySelectorAll(":checked").length||h.push(":checked"),(t=l.createElement("input")).setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),f.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&h.push(":enabled",":disabled"),(t=l.createElement("input")).setAttribute("name",""),e.appendChild(t),e.querySelectorAll("[name='']").length||h.push("\\["+A+"*name"+A+"*="+A+"*(?:''|\"\")")})),d.cssHas||h.push(":has"),h=h.length&&new RegExp(h.join("|")),q=function(e,t){if(e===t)return u=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n||(1&(n=(e.ownerDocument||e)==(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!d.sortDetached&&t.compareDocumentPosition(e)===n?e===l||e.ownerDocument==L&&Z.contains(L,e)?-1:t===l||t.ownerDocument==L&&Z.contains(L,t)?1:a?s.call(a,e)-s.call(a,t):0:4&n?-1:1)},l):l}for(t in Z.matches=function(e,t){return Z(e,null,null,t)},Z.matchesSelector=function(e,t){if(ue(e),p&&!N[t+" "]&&(!h||!h.test(t)))try{var n=g.call(e,t);if(n||d.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(e){N(t,!0)}return Z(t,l,null,[e]).length>0},Z.contains=function(e,t){return(e.ownerDocument||e)!=l&&ue(e),T.contains(e,t)},Z.attr=function(e,t){(e.ownerDocument||e)!=l&&ue(e);var n=r.attrHandle[t.toLowerCase()],i=n&&c.call(r.attrHandle,t.toLowerCase())?n(e,t,!p):void 0;return void 0!==i?i:e.getAttribute(t)},Z.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},T.uniqueSort=function(e){var t,n=[],r=0,o=0;if(u=!d.sortStable,a=!d.sortStable&&i.call(e,0),k.call(e,q),u){for(;t=e[o++];)t===e[o]&&(r=n.push(o));for(;r--;)j.call(e,n[r],1)}return a=null,e},T.fn.uniqueSort=function(){return this.pushStack(T.uniqueSort(i.apply(this)))},r=T.expr={cacheLength:50,createPseudo:te,match:z,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(Y,Q),e[3]=(e[3]||e[4]||e[5]||"").replace(Y,Q),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||Z.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&Z.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return z.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&B.test(n)&&(t=ce(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(Y,Q).toLowerCase();return"*"===e?function(){return!0}:function(e){return S(e,t)}},CLASS:function(e){var t=b[e+" "];return t||(t=new RegExp("(^|"+A+")"+e+"("+A+"|$)"))&&b(e,(function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")}))},ATTR:function(e,t,n){return function(r){var i=Z.attr(r,e);return null==i?"!="===t:!t||(i+="","="===t?i===n:"!="===t?i!==n:"^="===t?n&&0===i.indexOf(n):"*="===t?n&&i.indexOf(n)>-1:"$="===t?n&&i.slice(-n.length)===n:"~="===t?(" "+i.replace(I," ")+" ").indexOf(n)>-1:"|="===t&&(i===n||i.slice(0,n.length+1)===n+"-"))}},CHILD:function(e,t,n,r,i){var o="nth"!==e.slice(0,3),a="last"!==e.slice(-4),s="of-type"===t;return 1===r&&0===i?function(e){return!!e.parentNode}:function(t,n,u){var l,c,f,p,d,h=o!==a?"nextSibling":"previousSibling",g=t.parentNode,v=s&&t.nodeName.toLowerCase(),x=!u&&!s,b=!1;if(g){if(o){for(;h;){for(f=t;f=f[h];)if(s?S(f,v):1===f.nodeType)return!1;d=h="only"===e&&!d&&"nextSibling"}return!0}if(d=[a?g.firstChild:g.lastChild],a&&x){for(b=(p=(l=(c=g[y]||(g[y]={}))[e]||[])[0]===m&&l[1])&&l[2],f=p&&g.childNodes[p];f=++p&&f&&f[h]||(b=p=0)||d.pop();)if(1===f.nodeType&&++b&&f===t){c[e]=[m,p,b];break}}else if(x&&(b=p=(l=(c=t[y]||(t[y]={}))[e]||[])[0]===m&&l[1]),!1===b)for(;(f=++p&&f&&f[h]||(b=p=0)||d.pop())&&(!(s?S(f,v):1===f.nodeType)||!++b||(x&&((c=f[y]||(f[y]={}))[e]=[m,b]),f!==t)););return(b-=i)===r||b%r==0&&b/r>=0}}},PSEUDO:function(e,t){var n,i=r.pseudos[e]||r.setFilters[e.toLowerCase()]||Z.error("unsupported pseudo: "+e);return i[y]?i(t):i.length>1?(n=[e,e,"",t],r.setFilters.hasOwnProperty(e.toLowerCase())?te((function(e,n){for(var r,o=i(e,t),a=o.length;a--;)e[r=s.call(e,o[a])]=!(n[r]=o[a])})):function(e){return i(e,0,n)}):i}},pseudos:{not:te((function(e){var t=[],n=[],r=ye(e.replace(D,"$1"));return r[y]?te((function(e,t,n,i){for(var o,a=r(e,null,i,[]),s=e.length;s--;)(o=a[s])&&(e[s]=!(t[s]=o))})):function(e,i,o){return t[0]=e,r(t,null,o,n),t[0]=null,!n.pop()}})),has:te((function(e){return function(t){return Z(e,t).length>0}})),contains:te((function(e){return e=e.replace(Y,Q),function(t){return(t.textContent||T.text(t)).indexOf(e)>-1}})),lang:te((function(e){return _.test(e||"")||Z.error("unsupported lang: "+e),e=e.replace(Y,Q).toLowerCase(),function(t){var n;do{if(n=p?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return(n=n.toLowerCase())===e||0===n.indexOf(e+"-")}while((t=t.parentNode)&&1===t.nodeType);return!1}})),target:function(t){var n=e.location&&e.location.hash;return n&&n.slice(1)===t.id},root:function(e){return e===f},focus:function(e){return e===function(){try{return l.activeElement}catch(e){}}()&&l.hasFocus()&&!!(e.type||e.href||~e.tabIndex)},enabled:oe(!1),disabled:oe(!0),checked:function(e){return S(e,"input")&&!!e.checked||S(e,"option")&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!r.pseudos.empty(e)},header:function(e){return U.test(e.nodeName)},input:function(e){return X.test(e.nodeName)},button:function(e){return S(e,"input")&&"button"===e.type||S(e,"button")},text:function(e){var t;return S(e,"input")&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:ae((function(){return[0]})),last:ae((function(e,t){return[t-1]})),eq:ae((function(e,t,n){return[n<0?n+t:n]})),even:ae((function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e})),odd:ae((function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e})),lt:ae((function(e,t,n){var r;for(r=n<0?n+t:n>t?t:n;--r>=0;)e.push(r);return e})),gt:ae((function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e}))}},r.pseudos.nth=r.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})r.pseudos[t]=re(t);for(t in{submit:!0,reset:!0})r.pseudos[t]=ie(t);function le(){}function ce(e,t){var n,i,o,a,s,u,l,c=w[e+" "];if(c)return t?0:c.slice(0);for(s=e,u=[],l=r.preFilter;s;){for(a in n&&!(i=W.exec(s))||(i&&(s=s.slice(i[0].length)||s),u.push(o=[])),n=!1,(i=F.exec(s))&&(n=i.shift(),o.push({value:n,type:i[0].replace(D," ")}),s=s.slice(n.length)),r.filter)!(i=z[a].exec(s))||l[a]&&!(i=l[a](i))||(n=i.shift(),o.push({value:n,type:a,matches:i}),s=s.slice(n.length));if(!n)break}return t?s.length:s?Z.error(e):w(e,u).slice(0)}function fe(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function pe(e,t,n){var r=t.dir,i=t.next,o=i||r,a=n&&"parentNode"===o,s=x++;return t.first?function(t,n,i){for(;t=t[r];)if(1===t.nodeType||a)return e(t,n,i);return!1}:function(t,n,u){var l,c,f=[m,s];if(u){for(;t=t[r];)if((1===t.nodeType||a)&&e(t,n,u))return!0}else for(;t=t[r];)if(1===t.nodeType||a)if(c=t[y]||(t[y]={}),i&&S(t,i))t=t[r]||t;else{if((l=c[o])&&l[0]===m&&l[1]===s)return f[2]=l[2];if(c[o]=f,f[2]=e(t,n,u))return!0}return!1}}function de(e){return e.length>1?function(t,n,r){for(var i=e.length;i--;)if(!e[i](t,n,r))return!1;return!0}:e[0]}function he(e,t,n,r,i){for(var o,a=[],s=0,u=e.length,l=null!=t;s<u;s++)(o=e[s])&&(n&&!n(o,r,i)||(a.push(o),l&&t.push(s)));return a}function ge(e,t,n,r,i,o){return r&&!r[y]&&(r=ge(r)),i&&!i[y]&&(i=ge(i,o)),te((function(o,a,u,l){var c,f,p,d,h=[],g=[],y=a.length,m=o||function(e,t,n){for(var r=0,i=t.length;r<i;r++)Z(e,t[r],n);return n}(t||"*",u.nodeType?[u]:u,[]),x=!e||!o&&t?m:he(m,h,e,u,l);if(n?n(x,d=i||(o?e:y||r)?[]:a,u,l):d=x,r)for(c=he(d,g),r(c,[],u,l),f=c.length;f--;)(p=c[f])&&(d[g[f]]=!(x[g[f]]=p));if(o){if(i||e){if(i){for(c=[],f=d.length;f--;)(p=d[f])&&c.push(x[f]=p);i(null,d=[],c,l)}for(f=d.length;f--;)(p=d[f])&&(c=i?s.call(o,p):h[f])>-1&&(o[c]=!(a[c]=p))}}else d=he(d===a?d.splice(y,d.length):d),i?i(null,a,d,l):v.apply(a,d)}))}function ve(e){for(var t,n,i,a=e.length,u=r.relative[e[0].type],l=u||r.relative[" "],c=u?1:0,f=pe((function(e){return e===t}),l,!0),p=pe((function(e){return s.call(t,e)>-1}),l,!0),d=[function(e,n,r){var i=!u&&(r||n!=o)||((t=n).nodeType?f(e,n,r):p(e,n,r));return t=null,i}];c<a;c++)if(n=r.relative[e[c].type])d=[pe(de(d),n)];else{if((n=r.filter[e[c].type].apply(null,e[c].matches))[y]){for(i=++c;i<a&&!r.relative[e[i].type];i++);return ge(c>1&&de(d),c>1&&fe(e.slice(0,c-1).concat({value:" "===e[c-2].type?"*":""})).replace(D,"$1"),n,c<i&&ve(e.slice(c,i)),i<a&&ve(e=e.slice(i)),i<a&&fe(e))}d.push(n)}return de(d)}function ye(e,t){var n,i=[],a=[],s=C[e+" "];if(!s){for(t||(t=ce(e)),n=t.length;n--;)(s=ve(t[n]))[y]?i.push(s):a.push(s);s=C(e,function(e,t){var n=t.length>0,i=e.length>0,a=function(a,s,u,c,f){var d,h,g,y=0,x="0",b=a&&[],w=[],C=o,S=a||i&&r.find.TAG("*",f),k=m+=null==C?1:Math.random()||.1,j=S.length;for(f&&(o=s==l||s||f);x!==j&&null!=(d=S[x]);x++){if(i&&d){for(h=0,s||d.ownerDocument==l||(ue(d),u=!p);g=e[h++];)if(g(d,s||l,u)){v.call(c,d);break}f&&(m=k)}n&&((d=!g&&d)&&y--,a&&b.push(d))}if(y+=x,n&&x!==y){for(h=0;g=t[h++];)g(b,w,s,u);if(a){if(y>0)for(;x--;)b[x]||w[x]||(w[x]=E.call(c));w=he(w)}v.apply(c,w),f&&!a&&w.length>0&&y+t.length>1&&T.uniqueSort(c)}return f&&(m=k,o=C),b};return n?te(a):a}(a,i)),s.selector=e}return s}function me(e,t,n,i){var o,a,s,u,l,c="function"==typeof e&&e,f=!i&&ce(e=c.selector||e);if(n=n||[],1===f.length){if((a=f[0]=f[0].slice(0)).length>2&&"ID"===(s=a[0]).type&&9===t.nodeType&&p&&r.relative[a[1].type]){if(!(t=(r.find.ID(s.matches[0].replace(Y,Q),t)||[])[0]))return n;c&&(t=t.parentNode),e=e.slice(a.shift().value.length)}for(o=z.needsContext.test(e)?0:a.length;o--&&(s=a[o],!r.relative[u=s.type]);)if((l=r.find[u])&&(i=l(s.matches[0].replace(Y,Q),G.test(a[0].type)&&se(t.parentNode)||t))){if(a.splice(o,1),!(e=i.length&&fe(a)))return v.apply(n,i),n;break}}return(c||ye(e,f))(i,t,!p,n,!t||G.test(e)&&se(t.parentNode)||t),n}le.prototype=r.filters=r.pseudos,r.setFilters=new le,d.sortStable=y.split("").sort(q).join("")===y,ue(),d.sortDetached=ne((function(e){return 1&e.compareDocumentPosition(l.createElement("fieldset"))})),T.find=Z,T.expr[":"]=T.expr.pseudos,T.unique=T.uniqueSort,Z.compile=ye,Z.select=me,Z.setDocument=ue,Z.tokenize=ce,Z.escape=T.escapeSelector,Z.getText=T.text,Z.isXML=T.isXMLDoc,Z.selectors=T.expr,Z.support=T.support,Z.uniqueSort=T.uniqueSort}();var O=function(e,t,n){for(var r=[],i=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(i&&T(e).is(n))break;r.push(e)}return r},P=function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n},M=T.expr.match.needsContext,R=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function I(e,t,n){return h(t)?T.grep(e,(function(e,r){return!!t.call(e,r,e)!==n})):t.nodeType?T.grep(e,(function(e){return e===t!==n})):"string"!=typeof t?T.grep(e,(function(e){return s.call(t,e)>-1!==n})):T.filter(t,e,n)}T.filter=function(e,t,n){var r=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===r.nodeType?T.find.matchesSelector(r,e)?[r]:[]:T.find.matches(e,T.grep(t,(function(e){return 1===e.nodeType})))},T.fn.extend({find:function(e){var t,n,r=this.length,i=this;if("string"!=typeof e)return this.pushStack(T(e).filter((function(){for(t=0;t<r;t++)if(T.contains(i[t],this))return!0})));for(n=this.pushStack([]),t=0;t<r;t++)T.find(e,i[t],n);return r>1?T.uniqueSort(n):n},filter:function(e){return this.pushStack(I(this,e||[],!1))},not:function(e){return this.pushStack(I(this,e||[],!0))},is:function(e){return!!I(this,"string"==typeof e&&M.test(e)?T(e):e||[],!1).length}});var W,F=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(T.fn.init=function(e,t,n){var r,i;if(!e)return this;if(n=n||W,"string"==typeof e){if(!(r="<"===e[0]&&">"===e[e.length-1]&&e.length>=3?[null,e,null]:F.exec(e))||!r[1]&&t)return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);if(r[1]){if(t=t instanceof T?t[0]:t,T.merge(this,T.parseHTML(r[1],t&&t.nodeType?t.ownerDocument||t:v,!0)),R.test(r[1])&&T.isPlainObject(t))for(r in t)h(this[r])?this[r](t[r]):this.attr(r,t[r]);return this}return(i=v.getElementById(r[2]))&&(this[0]=i,this.length=1),this}return e.nodeType?(this[0]=e,this.length=1,this):h(e)?void 0!==n.ready?n.ready(e):e(T):T.makeArray(e,this)}).prototype=T.fn,W=T(v);var $=/^(?:parents|prev(?:Until|All))/,B={children:!0,contents:!0,next:!0,prev:!0};function _(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}T.fn.extend({has:function(e){var t=T(e,this),n=t.length;return this.filter((function(){for(var e=0;e<n;e++)if(T.contains(this,t[e]))return!0}))},closest:function(e,t){var n,r=0,i=this.length,o=[],a="string"!=typeof e&&T(e);if(!M.test(e))for(;r<i;r++)for(n=this[r];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(a?a.index(n)>-1:1===n.nodeType&&T.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(o.length>1?T.uniqueSort(o):o)},index:function(e){return e?"string"==typeof e?s.call(T(e),this[0]):s.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(T.uniqueSort(T.merge(this.get(),T(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),T.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return O(e,"parentNode")},parentsUntil:function(e,t,n){return O(e,"parentNode",n)},next:function(e){return _(e,"nextSibling")},prev:function(e){return _(e,"previousSibling")},nextAll:function(e){return O(e,"nextSibling")},prevAll:function(e){return O(e,"previousSibling")},nextUntil:function(e,t,n){return O(e,"nextSibling",n)},prevUntil:function(e,t,n){return O(e,"previousSibling",n)},siblings:function(e){return P((e.parentNode||{}).firstChild,e)},children:function(e){return P(e.firstChild)},contents:function(e){return null!=e.contentDocument&&r(e.contentDocument)?e.contentDocument:(S(e,"template")&&(e=e.content||e),T.merge([],e.childNodes))}},(function(e,t){T.fn[e]=function(n,r){var i=T.map(this,t,n);return"Until"!==e.slice(-5)&&(r=n),r&&"string"==typeof r&&(i=T.filter(r,i)),this.length>1&&(B[e]||T.uniqueSort(i),$.test(e)&&i.reverse()),this.pushStack(i)}}));var z=/[^\x20\t\r\n\f]+/g;function X(e){return e}function U(e){throw e}function V(e,t,n,r){var i;try{e&&h(i=e.promise)?i.call(e).done(t).fail(n):e&&h(i=e.then)?i.call(e,t,n):t.apply(void 0,[e].slice(r))}catch(e){n.apply(void 0,[e])}}T.Callbacks=function(e){e="string"==typeof e?function(e){var t={};return T.each(e.match(z)||[],(function(e,n){t[n]=!0})),t}(e):T.extend({},e);var t,n,r,i,o=[],a=[],s=-1,u=function(){for(i=i||e.once,r=t=!0;a.length;s=-1)for(n=a.shift();++s<o.length;)!1===o[s].apply(n[0],n[1])&&e.stopOnFalse&&(s=o.length,n=!1);e.memory||(n=!1),t=!1,i&&(o=n?[]:"")},l={add:function(){return o&&(n&&!t&&(s=o.length-1,a.push(n)),function t(n){T.each(n,(function(n,r){h(r)?e.unique&&l.has(r)||o.push(r):r&&r.length&&"string"!==x(r)&&t(r)}))}(arguments),n&&!t&&u()),this},remove:function(){return T.each(arguments,(function(e,t){for(var n;(n=T.inArray(t,o,n))>-1;)o.splice(n,1),n<=s&&s--})),this},has:function(e){return e?T.inArray(e,o)>-1:o.length>0},empty:function(){return o&&(o=[]),this},disable:function(){return i=a=[],o=n="",this},disabled:function(){return!o},lock:function(){return i=a=[],n||t||(o=n=""),this},locked:function(){return!!i},fireWith:function(e,n){return i||(n=[e,(n=n||[]).slice?n.slice():n],a.push(n),t||u()),this},fire:function(){return l.fireWith(this,arguments),this},fired:function(){return!!r}};return l},T.extend({Deferred:function(t){var n=[["notify","progress",T.Callbacks("memory"),T.Callbacks("memory"),2],["resolve","done",T.Callbacks("once memory"),T.Callbacks("once memory"),0,"resolved"],["reject","fail",T.Callbacks("once memory"),T.Callbacks("once memory"),1,"rejected"]],r="pending",i={state:function(){return r},always:function(){return o.done(arguments).fail(arguments),this},catch:function(e){return i.then(null,e)},pipe:function(){var e=arguments;return T.Deferred((function(t){T.each(n,(function(n,r){var i=h(e[r[4]])&&e[r[4]];o[r[1]]((function(){var e=i&&i.apply(this,arguments);e&&h(e.promise)?e.promise().progress(t.notify).done(t.resolve).fail(t.reject):t[r[0]+"With"](this,i?[e]:arguments)}))})),e=null})).promise()},then:function(t,r,i){var o=0;function a(t,n,r,i){return function(){var s=this,u=arguments,l=function(){var e,l;if(!(t<o)){if((e=r.apply(s,u))===n.promise())throw new TypeError("Thenable self-resolution");l=e&&("object"==typeof e||"function"==typeof e)&&e.then,h(l)?i?l.call(e,a(o,n,X,i),a(o,n,U,i)):(o++,l.call(e,a(o,n,X,i),a(o,n,U,i),a(o,n,X,n.notifyWith))):(r!==X&&(s=void 0,u=[e]),(i||n.resolveWith)(s,u))}},c=i?l:function(){try{l()}catch(e){T.Deferred.exceptionHook&&T.Deferred.exceptionHook(e,c.error),t+1>=o&&(r!==U&&(s=void 0,u=[e]),n.rejectWith(s,u))}};t?c():(T.Deferred.getErrorHook?c.error=T.Deferred.getErrorHook():T.Deferred.getStackHook&&(c.error=T.Deferred.getStackHook()),e.setTimeout(c))}}return T.Deferred((function(e){n[0][3].add(a(0,e,h(i)?i:X,e.notifyWith)),n[1][3].add(a(0,e,h(t)?t:X)),n[2][3].add(a(0,e,h(r)?r:U))})).promise()},promise:function(e){return null!=e?T.extend(e,i):i}},o={};return T.each(n,(function(e,t){var a=t[2],s=t[5];i[t[1]]=a.add,s&&a.add((function(){r=s}),n[3-e][2].disable,n[3-e][3].disable,n[0][2].lock,n[0][3].lock),a.add(t[3].fire),o[t[0]]=function(){return o[t[0]+"With"](this===o?void 0:this,arguments),this},o[t[0]+"With"]=a.fireWith})),i.promise(o),t&&t.call(o,o),o},when:function(e){var t=arguments.length,n=t,r=Array(n),o=i.call(arguments),a=T.Deferred(),s=function(e){return function(n){r[e]=this,o[e]=arguments.length>1?i.call(arguments):n,--t||a.resolveWith(r,o)}};if(t<=1&&(V(e,a.done(s(n)).resolve,a.reject,!t),"pending"===a.state()||h(o[n]&&o[n].then)))return a.then();for(;n--;)V(o[n],s(n),a.reject);return a.promise()}});var G=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;T.Deferred.exceptionHook=function(t,n){e.console&&e.console.warn&&t&&G.test(t.name)&&e.console.warn("jQuery.Deferred exception: "+t.message,t.stack,n)},T.readyException=function(t){e.setTimeout((function(){throw t}))};var Y=T.Deferred();function Q(){v.removeEventListener("DOMContentLoaded",Q),e.removeEventListener("load",Q),T.ready()}T.fn.ready=function(e){return Y.then(e).catch((function(e){T.readyException(e)})),this},T.extend({isReady:!1,readyWait:1,ready:function(e){(!0===e?--T.readyWait:T.isReady)||(T.isReady=!0,!0!==e&&--T.readyWait>0||Y.resolveWith(v,[T]))}}),T.ready.then=Y.then,"complete"===v.readyState||"loading"!==v.readyState&&!v.documentElement.doScroll?e.setTimeout(T.ready):(v.addEventListener("DOMContentLoaded",Q),e.addEventListener("load",Q));var J=function(e,t,n,r,i,o,a){var s=0,u=e.length,l=null==n;if("object"===x(n))for(s in i=!0,n)J(e,t,s,n[s],!0,o,a);else if(void 0!==r&&(i=!0,h(r)||(a=!0),l&&(a?(t.call(e,r),t=null):(l=t,t=function(e,t,n){return l.call(T(e),n)})),t))for(;s<u;s++)t(e[s],n,a?r:r.call(e[s],s,t(e[s],n)));return i?e:l?t.call(e):u?t(e[0],n):o},K=/^-ms-/,Z=/-([a-z])/g;function ee(e,t){return t.toUpperCase()}function te(e){return e.replace(K,"ms-").replace(Z,ee)}var ne=function(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType};function re(){this.expando=T.expando+re.uid++}re.uid=1,re.prototype={cache:function(e){var t=e[this.expando];return t||(t={},ne(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var r,i=this.cache(e);if("string"==typeof t)i[te(t)]=n;else for(r in t)i[te(r)]=t[r];return i},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][te(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,r=e[this.expando];if(void 0!==r){if(void 0!==t){n=(t=Array.isArray(t)?t.map(te):(t=te(t))in r?[t]:t.match(z)||[]).length;for(;n--;)delete r[t[n]]}(void 0===t||T.isEmptyObject(r))&&(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var t=e[this.expando];return void 0!==t&&!T.isEmptyObject(t)}};var ie=new re,oe=new re,ae=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,se=/[A-Z]/g;function ue(e,t,n){var r;if(void 0===n&&1===e.nodeType)if(r="data-"+t.replace(se,"-$&").toLowerCase(),"string"==typeof(n=e.getAttribute(r))){try{n=function(e){return"true"===e||"false"!==e&&("null"===e?null:e===+e+""?+e:ae.test(e)?JSON.parse(e):e)}(n)}catch(e){}oe.set(e,t,n)}else n=void 0;return n}T.extend({hasData:function(e){return oe.hasData(e)||ie.hasData(e)},data:function(e,t,n){return oe.access(e,t,n)},removeData:function(e,t){oe.remove(e,t)},_data:function(e,t,n){return ie.access(e,t,n)},_removeData:function(e,t){ie.remove(e,t)}}),T.fn.extend({data:function(e,t){var n,r,i,o=this[0],a=o&&o.attributes;if(void 0===e){if(this.length&&(i=oe.get(o),1===o.nodeType&&!ie.get(o,"hasDataAttrs"))){for(n=a.length;n--;)a[n]&&0===(r=a[n].name).indexOf("data-")&&(r=te(r.slice(5)),ue(o,r,i[r]));ie.set(o,"hasDataAttrs",!0)}return i}return"object"==typeof e?this.each((function(){oe.set(this,e)})):J(this,(function(t){var n;if(o&&void 0===t)return void 0!==(n=oe.get(o,e))||void 0!==(n=ue(o,e))?n:void 0;this.each((function(){oe.set(this,e,t)}))}),null,t,arguments.length>1,null,!0)},removeData:function(e){return this.each((function(){oe.remove(this,e)}))}}),T.extend({queue:function(e,t,n){var r;if(e)return t=(t||"fx")+"queue",r=ie.get(e,t),n&&(!r||Array.isArray(n)?r=ie.access(e,t,T.makeArray(n)):r.push(n)),r||[]},dequeue:function(e,t){t=t||"fx";var n=T.queue(e,t),r=n.length,i=n.shift(),o=T._queueHooks(e,t);"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===t&&n.unshift("inprogress"),delete o.stop,i.call(e,(function(){T.dequeue(e,t)}),o)),!r&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return ie.get(e,n)||ie.access(e,n,{empty:T.Callbacks("once memory").add((function(){ie.remove(e,[t+"queue",n])}))})}}),T.fn.extend({queue:function(e,t){var n=2;return"string"!=typeof e&&(t=e,e="fx",n--),arguments.length<n?T.queue(this[0],e):void 0===t?this:this.each((function(){var n=T.queue(this,e,t);T._queueHooks(this,e),"fx"===e&&"inprogress"!==n[0]&&T.dequeue(this,e)}))},dequeue:function(e){return this.each((function(){T.dequeue(this,e)}))},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,r=1,i=T.Deferred(),o=this,a=this.length,s=function(){--r||i.resolveWith(o,[o])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";a--;)(n=ie.get(o[a],e+"queueHooks"))&&n.empty&&(r++,n.empty.add(s));return s(),i.promise(t)}});var le=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,ce=new RegExp("^(?:([+-])=|)("+le+")([a-z%]*)$","i"),fe=["Top","Right","Bottom","Left"],pe=v.documentElement,de=function(e){return T.contains(e.ownerDocument,e)},he={composed:!0};pe.getRootNode&&(de=function(e){return T.contains(e.ownerDocument,e)||e.getRootNode(he)===e.ownerDocument});var ge=function(e,t){return"none"===(e=t||e).style.display||""===e.style.display&&de(e)&&"none"===T.css(e,"display")};function ve(e,t,n,r){var i,o,a=20,s=r?function(){return r.cur()}:function(){return T.css(e,t,"")},u=s(),l=n&&n[3]||(T.cssNumber[t]?"":"px"),c=e.nodeType&&(T.cssNumber[t]||"px"!==l&&+u)&&ce.exec(T.css(e,t));if(c&&c[3]!==l){for(u/=2,l=l||c[3],c=+u||1;a--;)T.style(e,t,c+l),(1-o)*(1-(o=s()/u||.5))<=0&&(a=0),c/=o;c*=2,T.style(e,t,c+l),n=n||[]}return n&&(c=+c||+u||0,i=n[1]?c+(n[1]+1)*n[2]:+n[2],r&&(r.unit=l,r.start=c,r.end=i)),i}var ye={};function me(e){var t,n=e.ownerDocument,r=e.nodeName,i=ye[r];return i||(t=n.body.appendChild(n.createElement(r)),i=T.css(t,"display"),t.parentNode.removeChild(t),"none"===i&&(i="block"),ye[r]=i,i)}function xe(e,t){for(var n,r,i=[],o=0,a=e.length;o<a;o++)(r=e[o]).style&&(n=r.style.display,t?("none"===n&&(i[o]=ie.get(r,"display")||null,i[o]||(r.style.display="")),""===r.style.display&&ge(r)&&(i[o]=me(r))):"none"!==n&&(i[o]="none",ie.set(r,"display",n)));for(o=0;o<a;o++)null!=i[o]&&(e[o].style.display=i[o]);return e}T.fn.extend({show:function(){return xe(this,!0)},hide:function(){return xe(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each((function(){ge(this)?T(this).show():T(this).hide()}))}});var be,we,Te=/^(?:checkbox|radio)$/i,Ce=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,Se=/^$|^module$|\/(?:java|ecma)script/i;be=v.createDocumentFragment().appendChild(v.createElement("div")),(we=v.createElement("input")).setAttribute("type","radio"),we.setAttribute("checked","checked"),we.setAttribute("name","t"),be.appendChild(we),d.checkClone=be.cloneNode(!0).cloneNode(!0).lastChild.checked,be.innerHTML="<textarea>x</textarea>",d.noCloneChecked=!!be.cloneNode(!0).lastChild.defaultValue,be.innerHTML="<option></option>",d.option=!!be.lastChild;var Ee={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function ke(e,t){var n;return n=void 0!==e.getElementsByTagName?e.getElementsByTagName(t||"*"):void 0!==e.querySelectorAll?e.querySelectorAll(t||"*"):[],void 0===t||t&&S(e,t)?T.merge([e],n):n}function je(e,t){for(var n=0,r=e.length;n<r;n++)ie.set(e[n],"globalEval",!t||ie.get(t[n],"globalEval"))}Ee.tbody=Ee.tfoot=Ee.colgroup=Ee.caption=Ee.thead,Ee.th=Ee.td,d.option||(Ee.optgroup=Ee.option=[1,"<select multiple='multiple'>","</select>"]);var Ae=/<|&#?\w+;/;function De(e,t,n,r,i){for(var o,a,s,u,l,c,f=t.createDocumentFragment(),p=[],d=0,h=e.length;d<h;d++)if((o=e[d])||0===o)if("object"===x(o))T.merge(p,o.nodeType?[o]:o);else if(Ae.test(o)){for(a=a||f.appendChild(t.createElement("div")),s=(Ce.exec(o)||["",""])[1].toLowerCase(),u=Ee[s]||Ee._default,a.innerHTML=u[1]+T.htmlPrefilter(o)+u[2],c=u[0];c--;)a=a.lastChild;T.merge(p,a.childNodes),(a=f.firstChild).textContent=""}else p.push(t.createTextNode(o));for(f.textContent="",d=0;o=p[d++];)if(r&&T.inArray(o,r)>-1)i&&i.push(o);else if(l=de(o),a=ke(f.appendChild(o),"script"),l&&je(a),n)for(c=0;o=a[c++];)Se.test(o.type||"")&&n.push(o);return f}var Ne=/^([^.]*)(?:\.(.+)|)/;function qe(){return!0}function Le(){return!1}function He(e,t,n,r,i,o){var a,s;if("object"==typeof t){for(s in"string"!=typeof n&&(r=r||n,n=void 0),t)He(e,s,n,r,t[s],o);return e}if(null==r&&null==i?(i=n,r=n=void 0):null==i&&("string"==typeof n?(i=r,r=void 0):(i=r,r=n,n=void 0)),!1===i)i=Le;else if(!i)return e;return 1===o&&(a=i,i=function(e){return T().off(e),a.apply(this,arguments)},i.guid=a.guid||(a.guid=T.guid++)),e.each((function(){T.event.add(this,t,i,r,n)}))}function Oe(e,t,n){n?(ie.set(e,t,!1),T.event.add(e,t,{namespace:!1,handler:function(e){var n,r=ie.get(this,t);if(1&e.isTrigger&&this[t]){if(r)(T.event.special[t]||{}).delegateType&&e.stopPropagation();else if(r=i.call(arguments),ie.set(this,t,r),this[t](),n=ie.get(this,t),ie.set(this,t,!1),r!==n)return e.stopImmediatePropagation(),e.preventDefault(),n}else r&&(ie.set(this,t,T.event.trigger(r[0],r.slice(1),this)),e.stopPropagation(),e.isImmediatePropagationStopped=qe)}})):void 0===ie.get(e,t)&&T.event.add(e,t,qe)}T.event={global:{},add:function(e,t,n,r,i){var o,a,s,u,l,c,f,p,d,h,g,v=ie.get(e);if(ne(e))for(n.handler&&(n=(o=n).handler,i=o.selector),i&&T.find.matchesSelector(pe,i),n.guid||(n.guid=T.guid++),(u=v.events)||(u=v.events=Object.create(null)),(a=v.handle)||(a=v.handle=function(t){return void 0!==T&&T.event.triggered!==t.type?T.event.dispatch.apply(e,arguments):void 0}),l=(t=(t||"").match(z)||[""]).length;l--;)d=g=(s=Ne.exec(t[l])||[])[1],h=(s[2]||"").split(".").sort(),d&&(f=T.event.special[d]||{},d=(i?f.delegateType:f.bindType)||d,f=T.event.special[d]||{},c=T.extend({type:d,origType:g,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&T.expr.match.needsContext.test(i),namespace:h.join(".")},o),(p=u[d])||((p=u[d]=[]).delegateCount=0,f.setup&&!1!==f.setup.call(e,r,h,a)||e.addEventListener&&e.addEventListener(d,a)),f.add&&(f.add.call(e,c),c.handler.guid||(c.handler.guid=n.guid)),i?p.splice(p.delegateCount++,0,c):p.push(c),T.event.global[d]=!0)},remove:function(e,t,n,r,i){var o,a,s,u,l,c,f,p,d,h,g,v=ie.hasData(e)&&ie.get(e);if(v&&(u=v.events)){for(l=(t=(t||"").match(z)||[""]).length;l--;)if(d=g=(s=Ne.exec(t[l])||[])[1],h=(s[2]||"").split(".").sort(),d){for(f=T.event.special[d]||{},p=u[d=(r?f.delegateType:f.bindType)||d]||[],s=s[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),a=o=p.length;o--;)c=p[o],!i&&g!==c.origType||n&&n.guid!==c.guid||s&&!s.test(c.namespace)||r&&r!==c.selector&&("**"!==r||!c.selector)||(p.splice(o,1),c.selector&&p.delegateCount--,f.remove&&f.remove.call(e,c));a&&!p.length&&(f.teardown&&!1!==f.teardown.call(e,h,v.handle)||T.removeEvent(e,d,v.handle),delete u[d])}else for(d in u)T.event.remove(e,d+t[l],n,r,!0);T.isEmptyObject(u)&&ie.remove(e,"handle events")}},dispatch:function(e){var t,n,r,i,o,a,s=new Array(arguments.length),u=T.event.fix(e),l=(ie.get(this,"events")||Object.create(null))[u.type]||[],c=T.event.special[u.type]||{};for(s[0]=u,t=1;t<arguments.length;t++)s[t]=arguments[t];if(u.delegateTarget=this,!c.preDispatch||!1!==c.preDispatch.call(this,u)){for(a=T.event.handlers.call(this,u,l),t=0;(i=a[t++])&&!u.isPropagationStopped();)for(u.currentTarget=i.elem,n=0;(o=i.handlers[n++])&&!u.isImmediatePropagationStopped();)u.rnamespace&&!1!==o.namespace&&!u.rnamespace.test(o.namespace)||(u.handleObj=o,u.data=o.data,void 0!==(r=((T.event.special[o.origType]||{}).handle||o.handler).apply(i.elem,s))&&!1===(u.result=r)&&(u.preventDefault(),u.stopPropagation()));return c.postDispatch&&c.postDispatch.call(this,u),u.result}},handlers:function(e,t){var n,r,i,o,a,s=[],u=t.delegateCount,l=e.target;if(u&&l.nodeType&&!("click"===e.type&&e.button>=1))for(;l!==this;l=l.parentNode||this)if(1===l.nodeType&&("click"!==e.type||!0!==l.disabled)){for(o=[],a={},n=0;n<u;n++)void 0===a[i=(r=t[n]).selector+" "]&&(a[i]=r.needsContext?T(i,this).index(l)>-1:T.find(i,this,null,[l]).length),a[i]&&o.push(r);o.length&&s.push({elem:l,handlers:o})}return l=this,u<t.length&&s.push({elem:l,handlers:t.slice(u)}),s},addProp:function(e,t){Object.defineProperty(T.Event.prototype,e,{enumerable:!0,configurable:!0,get:h(t)?function(){if(this.originalEvent)return t(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[e]},set:function(t){Object.defineProperty(this,e,{enumerable:!0,configurable:!0,writable:!0,value:t})}})},fix:function(e){return e[T.expando]?e:new T.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){var t=this||e;return Te.test(t.type)&&t.click&&S(t,"input")&&Oe(t,"click",!0),!1},trigger:function(e){var t=this||e;return Te.test(t.type)&&t.click&&S(t,"input")&&Oe(t,"click"),!0},_default:function(e){var t=e.target;return Te.test(t.type)&&t.click&&S(t,"input")&&ie.get(t,"click")||S(t,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},T.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},T.Event=function(e,t){if(!(this instanceof T.Event))return new T.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?qe:Le,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&T.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),this[T.expando]=!0},T.Event.prototype={constructor:T.Event,isDefaultPrevented:Le,isPropagationStopped:Le,isImmediatePropagationStopped:Le,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=qe,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=qe,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=qe,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},T.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},T.event.addProp),T.each({focus:"focusin",blur:"focusout"},(function(e,t){function n(e){if(v.documentMode){var n=ie.get(this,"handle"),r=T.event.fix(e);r.type="focusin"===e.type?"focus":"blur",r.isSimulated=!0,n(e),r.target===r.currentTarget&&n(r)}else T.event.simulate(t,e.target,T.event.fix(e))}T.event.special[e]={setup:function(){var r;if(Oe(this,e,!0),!v.documentMode)return!1;(r=ie.get(this,t))||this.addEventListener(t,n),ie.set(this,t,(r||0)+1)},trigger:function(){return Oe(this,e),!0},teardown:function(){var e;if(!v.documentMode)return!1;(e=ie.get(this,t)-1)?ie.set(this,t,e):(this.removeEventListener(t,n),ie.remove(this,t))},_default:function(t){return ie.get(t.target,e)},delegateType:t},T.event.special[t]={setup:function(){var r=this.ownerDocument||this.document||this,i=v.documentMode?this:r,o=ie.get(i,t);o||(v.documentMode?this.addEventListener(t,n):r.addEventListener(e,n,!0)),ie.set(i,t,(o||0)+1)},teardown:function(){var r=this.ownerDocument||this.document||this,i=v.documentMode?this:r,o=ie.get(i,t)-1;o?ie.set(i,t,o):(v.documentMode?this.removeEventListener(t,n):r.removeEventListener(e,n,!0),ie.remove(i,t))}}})),T.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},(function(e,t){T.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,r=e.relatedTarget,i=e.handleObj;return r&&(r===this||T.contains(this,r))||(e.type=i.origType,n=i.handler.apply(this,arguments),e.type=t),n}}})),T.fn.extend({on:function(e,t,n,r){return He(this,e,t,n,r)},one:function(e,t,n,r){return He(this,e,t,n,r,1)},off:function(e,t,n){var r,i;if(e&&e.preventDefault&&e.handleObj)return r=e.handleObj,T(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if("object"==typeof e){for(i in e)this.off(i,t,e[i]);return this}return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=Le),this.each((function(){T.event.remove(this,e,n,t)}))}});var Pe=/<script|<style|<link/i,Me=/checked\s*(?:[^=]|=\s*.checked.)/i,Re=/^\s*<!\[CDATA\[|\]\]>\s*$/g;function Ie(e,t){return S(e,"table")&&S(11!==t.nodeType?t:t.firstChild,"tr")&&T(e).children("tbody")[0]||e}function We(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function Fe(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function $e(e,t){var n,r,i,o,a,s;if(1===t.nodeType){if(ie.hasData(e)&&(s=ie.get(e).events))for(i in ie.remove(t,"handle events"),s)for(n=0,r=s[i].length;n<r;n++)T.event.add(t,i,s[i][n]);oe.hasData(e)&&(o=oe.access(e),a=T.extend({},o),oe.set(t,a))}}function Be(e,t){var n=t.nodeName.toLowerCase();"input"===n&&Te.test(e.type)?t.checked=e.checked:"input"!==n&&"textarea"!==n||(t.defaultValue=e.defaultValue)}function _e(e,t,n,r){t=o(t);var i,a,s,u,l,c,f=0,p=e.length,g=p-1,v=t[0],y=h(v);if(y||p>1&&"string"==typeof v&&!d.checkClone&&Me.test(v))return e.each((function(i){var o=e.eq(i);y&&(t[0]=v.call(this,i,o.html())),_e(o,t,n,r)}));if(p&&(a=(i=De(t,e[0].ownerDocument,!1,e,r)).firstChild,1===i.childNodes.length&&(i=a),a||r)){for(u=(s=T.map(ke(i,"script"),We)).length;f<p;f++)l=i,f!==g&&(l=T.clone(l,!0,!0),u&&T.merge(s,ke(l,"script"))),n.call(e[f],l,f);if(u)for(c=s[s.length-1].ownerDocument,T.map(s,Fe),f=0;f<u;f++)l=s[f],Se.test(l.type||"")&&!ie.access(l,"globalEval")&&T.contains(c,l)&&(l.src&&"module"!==(l.type||"").toLowerCase()?T._evalUrl&&!l.noModule&&T._evalUrl(l.src,{nonce:l.nonce||l.getAttribute("nonce")},c):m(l.textContent.replace(Re,""),l,c))}return e}function ze(e,t,n){for(var r,i=t?T.filter(t,e):e,o=0;null!=(r=i[o]);o++)n||1!==r.nodeType||T.cleanData(ke(r)),r.parentNode&&(n&&de(r)&&je(ke(r,"script")),r.parentNode.removeChild(r));return e}T.extend({htmlPrefilter:function(e){return e},clone:function(e,t,n){var r,i,o,a,s=e.cloneNode(!0),u=de(e);if(!(d.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||T.isXMLDoc(e)))for(a=ke(s),r=0,i=(o=ke(e)).length;r<i;r++)Be(o[r],a[r]);if(t)if(n)for(o=o||ke(e),a=a||ke(s),r=0,i=o.length;r<i;r++)$e(o[r],a[r]);else $e(e,s);return(a=ke(s,"script")).length>0&&je(a,!u&&ke(e,"script")),s},cleanData:function(e){for(var t,n,r,i=T.event.special,o=0;void 0!==(n=e[o]);o++)if(ne(n)){if(t=n[ie.expando]){if(t.events)for(r in t.events)i[r]?T.event.remove(n,r):T.removeEvent(n,r,t.handle);n[ie.expando]=void 0}n[oe.expando]&&(n[oe.expando]=void 0)}}}),T.fn.extend({detach:function(e){return ze(this,e,!0)},remove:function(e){return ze(this,e)},text:function(e){return J(this,(function(e){return void 0===e?T.text(this):this.empty().each((function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)}))}),null,e,arguments.length)},append:function(){return _e(this,arguments,(function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||Ie(this,e).appendChild(e)}))},prepend:function(){return _e(this,arguments,(function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=Ie(this,e);t.insertBefore(e,t.firstChild)}}))},before:function(){return _e(this,arguments,(function(e){this.parentNode&&this.parentNode.insertBefore(e,this)}))},after:function(){return _e(this,arguments,(function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)}))},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(T.cleanData(ke(e,!1)),e.textContent="");return this},clone:function(e,t){return e=e??!1,t=t??e,this.map((function(){return T.clone(this,e,t)}))},html:function(e){return J(this,(function(e){var t=this[0]||{},n=0,r=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!Pe.test(e)&&!Ee[(Ce.exec(e)||["",""])[1].toLowerCase()]){e=T.htmlPrefilter(e);try{for(;n<r;n++)1===(t=this[n]||{}).nodeType&&(T.cleanData(ke(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)}),null,e,arguments.length)},replaceWith:function(){var e=[];return _e(this,arguments,(function(t){var n=this.parentNode;T.inArray(this,e)<0&&(T.cleanData(ke(this)),n&&n.replaceChild(t,this))}),e)}}),T.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},(function(e,t){T.fn[e]=function(e){for(var n,r=[],i=T(e),o=i.length-1,s=0;s<=o;s++)n=s===o?this:this.clone(!0),T(i[s])[t](n),a.apply(r,n.get());return this.pushStack(r)}}));var Xe=new RegExp("^("+le+")(?!px)[a-z%]+$","i"),Ue=/^--/,Ve=function(t){var n=t.ownerDocument.defaultView;return n&&n.opener||(n=e),n.getComputedStyle(t)},Ge=function(e,t,n){var r,i,o={};for(i in t)o[i]=e.style[i],e.style[i]=t[i];for(i in r=n.call(e),t)e.style[i]=o[i];return r},Ye=new RegExp(fe.join("|"),"i");function Qe(e,t,n){var r,i,o,a,s=Ue.test(t),u=e.style;return(n=n||Ve(e))&&(a=n.getPropertyValue(t)||n[t],s&&a&&(a=a.replace(D,"$1")||void 0),""!==a||de(e)||(a=T.style(e,t)),!d.pixelBoxStyles()&&Xe.test(a)&&Ye.test(t)&&(r=u.width,i=u.minWidth,o=u.maxWidth,u.minWidth=u.maxWidth=u.width=a,a=n.width,u.width=r,u.minWidth=i,u.maxWidth=o)),void 0!==a?a+"":a}function Je(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}!function(){function t(){if(c){l.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",c.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",pe.appendChild(l).appendChild(c);var t=e.getComputedStyle(c);r="1%"!==t.top,u=12===n(t.marginLeft),c.style.right="60%",a=36===n(t.right),i=36===n(t.width),c.style.position="absolute",o=12===n(c.offsetWidth/3),pe.removeChild(l),c=null}}function n(e){return Math.round(parseFloat(e))}var r,i,o,a,s,u,l=v.createElement("div"),c=v.createElement("div");c.style&&(c.style.backgroundClip="content-box",c.cloneNode(!0).style.backgroundClip="",d.clearCloneStyle="content-box"===c.style.backgroundClip,T.extend(d,{boxSizingReliable:function(){return t(),i},pixelBoxStyles:function(){return t(),a},pixelPosition:function(){return t(),r},reliableMarginLeft:function(){return t(),u},scrollboxSize:function(){return t(),o},reliableTrDimensions:function(){var t,n,r,i;return null==s&&(t=v.createElement("table"),n=v.createElement("tr"),r=v.createElement("div"),t.style.cssText="position:absolute;left:-11111px;border-collapse:separate",n.style.cssText="box-sizing:content-box;border:1px solid",n.style.height="1px",r.style.height="9px",r.style.display="block",pe.appendChild(t).appendChild(n).appendChild(r),i=e.getComputedStyle(n),s=parseInt(i.height,10)+parseInt(i.borderTopWidth,10)+parseInt(i.borderBottomWidth,10)===n.offsetHeight,pe.removeChild(t)),s}}))}();var Ke=["Webkit","Moz","ms"],Ze=v.createElement("div").style,et={};function tt(e){var t=T.cssProps[e]||et[e];return t||(e in Ze?e:et[e]=function(e){for(var t=e[0].toUpperCase()+e.slice(1),n=Ke.length;n--;)if((e=Ke[n]+t)in Ze)return e}(e)||e)}var nt=/^(none|table(?!-c[ea]).+)/,rt={position:"absolute",visibility:"hidden",display:"block"},it={letterSpacing:"0",fontWeight:"400"};function ot(e,t,n){var r=ce.exec(t);return r?Math.max(0,r[2]-(n||0))+(r[3]||"px"):t}function at(e,t,n,r,i,o){var a="width"===t?1:0,s=0,u=0,l=0;if(n===(r?"border":"content"))return 0;for(;a<4;a+=2)"margin"===n&&(l+=T.css(e,n+fe[a],!0,i)),r?("content"===n&&(u-=T.css(e,"padding"+fe[a],!0,i)),"margin"!==n&&(u-=T.css(e,"border"+fe[a]+"Width",!0,i))):(u+=T.css(e,"padding"+fe[a],!0,i),"padding"!==n?u+=T.css(e,"border"+fe[a]+"Width",!0,i):s+=T.css(e,"border"+fe[a]+"Width",!0,i));return!r&&o>=0&&(u+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-o-u-s-.5))||0),u+l}function st(e,t,n){var r=Ve(e),i=(!d.boxSizingReliable()||n)&&"border-box"===T.css(e,"boxSizing",!1,r),o=i,a=Qe(e,t,r),s="offset"+t[0].toUpperCase()+t.slice(1);if(Xe.test(a)){if(!n)return a;a="auto"}return(!d.boxSizingReliable()&&i||!d.reliableTrDimensions()&&S(e,"tr")||"auto"===a||!parseFloat(a)&&"inline"===T.css(e,"display",!1,r))&&e.getClientRects().length&&(i="border-box"===T.css(e,"boxSizing",!1,r),(o=s in e)&&(a=e[s])),(a=parseFloat(a)||0)+at(e,t,n||(i?"border":"content"),o,r,a)+"px"}function ut(e,t,n,r,i){return new ut.prototype.init(e,t,n,r,i)}T.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=Qe(e,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(e,t,n,r){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var i,o,a,s=te(t),u=Ue.test(t),l=e.style;if(u||(t=tt(s)),a=T.cssHooks[t]||T.cssHooks[s],void 0===n)return a&&"get"in a&&void 0!==(i=a.get(e,!1,r))?i:l[t];"string"===(o=typeof n)&&(i=ce.exec(n))&&i[1]&&(n=ve(e,t,i),o="number"),null!=n&&n==n&&("number"!==o||u||(n+=i&&i[3]||(T.cssNumber[s]?"":"px")),d.clearCloneStyle||""!==n||0!==t.indexOf("background")||(l[t]="inherit"),a&&"set"in a&&void 0===(n=a.set(e,n,r))||(u?l.setProperty(t,n):l[t]=n))}},css:function(e,t,n,r){var i,o,a,s=te(t);return Ue.test(t)||(t=tt(s)),(a=T.cssHooks[t]||T.cssHooks[s])&&"get"in a&&(i=a.get(e,!0,n)),void 0===i&&(i=Qe(e,t,r)),"normal"===i&&t in it&&(i=it[t]),""===n||n?(o=parseFloat(i),!0===n||isFinite(o)?o||0:i):i}}),T.each(["height","width"],(function(e,t){T.cssHooks[t]={get:function(e,n,r){if(n)return!nt.test(T.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?st(e,t,r):Ge(e,rt,(function(){return st(e,t,r)}))},set:function(e,n,r){var i,o=Ve(e),a=!d.scrollboxSize()&&"absolute"===o.position,s=(a||r)&&"border-box"===T.css(e,"boxSizing",!1,o),u=r?at(e,t,r,s,o):0;return s&&a&&(u-=Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-parseFloat(o[t])-at(e,t,"border",!1,o)-.5)),u&&(i=ce.exec(n))&&"px"!==(i[3]||"px")&&(e.style[t]=n,n=T.css(e,t)),ot(0,n,u)}}})),T.cssHooks.marginLeft=Je(d.reliableMarginLeft,(function(e,t){if(t)return(parseFloat(Qe(e,"marginLeft"))||e.getBoundingClientRect().left-Ge(e,{marginLeft:0},(function(){return e.getBoundingClientRect().left})))+"px"})),T.each({margin:"",padding:"",border:"Width"},(function(e,t){T.cssHooks[e+t]={expand:function(n){for(var r=0,i={},o="string"==typeof n?n.split(" "):[n];r<4;r++)i[e+fe[r]+t]=o[r]||o[r-2]||o[0];return i}},"margin"!==e&&(T.cssHooks[e+t].set=ot)})),T.fn.extend({css:function(e,t){return J(this,(function(e,t,n){var r,i,o={},a=0;if(Array.isArray(t)){for(r=Ve(e),i=t.length;a<i;a++)o[t[a]]=T.css(e,t[a],!1,r);return o}return void 0!==n?T.style(e,t,n):T.css(e,t)}),e,t,arguments.length>1)}}),T.Tween=ut,ut.prototype={constructor:ut,init:function(e,t,n,r,i,o){this.elem=e,this.prop=n,this.easing=i||T.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=r,this.unit=o||(T.cssNumber[n]?"":"px")},cur:function(){var e=ut.propHooks[this.prop];return e&&e.get?e.get(this):ut.propHooks._default.get(this)},run:function(e){var t,n=ut.propHooks[this.prop];return this.options.duration?this.pos=t=T.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):ut.propHooks._default.set(this),this}},ut.prototype.init.prototype=ut.prototype,ut.propHooks={_default:{get:function(e){var t;return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(t=T.css(e.elem,e.prop,""))&&"auto"!==t?t:0},set:function(e){T.fx.step[e.prop]?T.fx.step[e.prop](e):1!==e.elem.nodeType||!T.cssHooks[e.prop]&&null==e.elem.style[tt(e.prop)]?e.elem[e.prop]=e.now:T.style(e.elem,e.prop,e.now+e.unit)}}},ut.propHooks.scrollTop=ut.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},T.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},T.fx=ut.prototype.init,T.fx.step={};var lt,ct,ft=/^(?:toggle|show|hide)$/,pt=/queueHooks$/;function dt(){ct&&(!1===v.hidden&&e.requestAnimationFrame?e.requestAnimationFrame(dt):e.setTimeout(dt,T.fx.interval),T.fx.tick())}function ht(){return e.setTimeout((function(){lt=void 0})),lt=Date.now()}function gt(e,t){var n,r=0,i={height:e};for(t=t?1:0;r<4;r+=2-t)i["margin"+(n=fe[r])]=i["padding"+n]=e;return t&&(i.opacity=i.width=e),i}function vt(e,t,n){for(var r,i=(yt.tweeners[t]||[]).concat(yt.tweeners["*"]),o=0,a=i.length;o<a;o++)if(r=i[o].call(n,t,e))return r}function yt(e,t,n){var r,i,o=0,a=yt.prefilters.length,s=T.Deferred().always((function(){delete u.elem})),u=function(){if(i)return!1;for(var t=lt||ht(),n=Math.max(0,l.startTime+l.duration-t),r=1-(n/l.duration||0),o=0,a=l.tweens.length;o<a;o++)l.tweens[o].run(r);return s.notifyWith(e,[l,r,n]),r<1&&a?n:(a||s.notifyWith(e,[l,1,0]),s.resolveWith(e,[l]),!1)},l=s.promise({elem:e,props:T.extend({},t),opts:T.extend(!0,{specialEasing:{},easing:T.easing._default},n),originalProperties:t,originalOptions:n,startTime:lt||ht(),duration:n.duration,tweens:[],createTween:function(t,n){var r=T.Tween(e,l.opts,t,n,l.opts.specialEasing[t]||l.opts.easing);return l.tweens.push(r),r},stop:function(t){var n=0,r=t?l.tweens.length:0;if(i)return this;for(i=!0;n<r;n++)l.tweens[n].run(1);return t?(s.notifyWith(e,[l,1,0]),s.resolveWith(e,[l,t])):s.rejectWith(e,[l,t]),this}}),c=l.props;for(!function(e,t){var n,r,i,o,a;for(n in e)if(i=t[r=te(n)],o=e[n],Array.isArray(o)&&(i=o[1],o=e[n]=o[0]),n!==r&&(e[r]=o,delete e[n]),(a=T.cssHooks[r])&&"expand"in a)for(n in o=a.expand(o),delete e[r],o)n in e||(e[n]=o[n],t[n]=i);else t[r]=i}(c,l.opts.specialEasing);o<a;o++)if(r=yt.prefilters[o].call(l,e,c,l.opts))return h(r.stop)&&(T._queueHooks(l.elem,l.opts.queue).stop=r.stop.bind(r)),r;return T.map(c,vt,l),h(l.opts.start)&&l.opts.start.call(e,l),l.progress(l.opts.progress).done(l.opts.done,l.opts.complete).fail(l.opts.fail).always(l.opts.always),T.fx.timer(T.extend(u,{elem:e,anim:l,queue:l.opts.queue})),l}T.Animation=T.extend(yt,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return ve(n.elem,e,ce.exec(t),n),n}]},tweener:function(e,t){h(e)?(t=e,e=["*"]):e=e.match(z);for(var n,r=0,i=e.length;r<i;r++)n=e[r],yt.tweeners[n]=yt.tweeners[n]||[],yt.tweeners[n].unshift(t)},prefilters:[function(e,t,n){var r,i,o,a,s,u,l,c,f="width"in t||"height"in t,p=this,d={},h=e.style,g=e.nodeType&&ge(e),v=ie.get(e,"fxshow");for(r in n.queue||(null==(a=T._queueHooks(e,"fx")).unqueued&&(a.unqueued=0,s=a.empty.fire,a.empty.fire=function(){a.unqueued||s()}),a.unqueued++,p.always((function(){p.always((function(){a.unqueued--,T.queue(e,"fx").length||a.empty.fire()}))}))),t)if(i=t[r],ft.test(i)){if(delete t[r],o=o||"toggle"===i,i===(g?"hide":"show")){if("show"!==i||!v||void 0===v[r])continue;g=!0}d[r]=v&&v[r]||T.style(e,r)}if((u=!T.isEmptyObject(t))||!T.isEmptyObject(d))for(r in f&&1===e.nodeType&&(n.overflow=[h.overflow,h.overflowX,h.overflowY],null==(l=v&&v.display)&&(l=ie.get(e,"display")),"none"===(c=T.css(e,"display"))&&(l?c=l:(xe([e],!0),l=e.style.display||l,c=T.css(e,"display"),xe([e]))),("inline"===c||"inline-block"===c&&null!=l)&&"none"===T.css(e,"float")&&(u||(p.done((function(){h.display=l})),null==l&&(c=h.display,l="none"===c?"":c)),h.display="inline-block")),n.overflow&&(h.overflow="hidden",p.always((function(){h.overflow=n.overflow[0],h.overflowX=n.overflow[1],h.overflowY=n.overflow[2]}))),u=!1,d)u||(v?"hidden"in v&&(g=v.hidden):v=ie.access(e,"fxshow",{display:l}),o&&(v.hidden=!g),g&&xe([e],!0),p.done((function(){for(r in g||xe([e]),ie.remove(e,"fxshow"),d)T.style(e,r,d[r])}))),u=vt(g?v[r]:0,r,p),r in v||(v[r]=u.start,g&&(u.end=u.start,u.start=0))}],prefilter:function(e,t){t?yt.prefilters.unshift(e):yt.prefilters.push(e)}}),T.speed=function(e,t,n){var r=e&&"object"==typeof e?T.extend({},e):{complete:n||!n&&t||h(e)&&e,duration:e,easing:n&&t||t&&!h(t)&&t};return T.fx.off?r.duration=0:"number"!=typeof r.duration&&(r.duration in T.fx.speeds?r.duration=T.fx.speeds[r.duration]:r.duration=T.fx.speeds._default),null!=r.queue&&!0!==r.queue||(r.queue="fx"),r.old=r.complete,r.complete=function(){h(r.old)&&r.old.call(this),r.queue&&T.dequeue(this,r.queue)},r},T.fn.extend({fadeTo:function(e,t,n,r){return this.filter(ge).css("opacity",0).show().end().animate({opacity:t},e,n,r)},animate:function(e,t,n,r){var i=T.isEmptyObject(e),o=T.speed(t,n,r),a=function(){var t=yt(this,T.extend({},e),o);(i||ie.get(this,"finish"))&&t.stop(!0)};return a.finish=a,i||!1===o.queue?this.each(a):this.queue(o.queue,a)},stop:function(e,t,n){var r=function(e){var t=e.stop;delete e.stop,t(n)};return"string"!=typeof e&&(n=t,t=e,e=void 0),t&&this.queue(e||"fx",[]),this.each((function(){var t=!0,i=null!=e&&e+"queueHooks",o=T.timers,a=ie.get(this);if(i)a[i]&&a[i].stop&&r(a[i]);else for(i in a)a[i]&&a[i].stop&&pt.test(i)&&r(a[i]);for(i=o.length;i--;)o[i].elem!==this||null!=e&&o[i].queue!==e||(o[i].anim.stop(n),t=!1,o.splice(i,1));!t&&n||T.dequeue(this,e)}))},finish:function(e){return!1!==e&&(e=e||"fx"),this.each((function(){var t,n=ie.get(this),r=n[e+"queue"],i=n[e+"queueHooks"],o=T.timers,a=r?r.length:0;for(n.finish=!0,T.queue(this,e,[]),i&&i.stop&&i.stop.call(this,!0),t=o.length;t--;)o[t].elem===this&&o[t].queue===e&&(o[t].anim.stop(!0),o.splice(t,1));for(t=0;t<a;t++)r[t]&&r[t].finish&&r[t].finish.call(this);delete n.finish}))}}),T.each(["toggle","show","hide"],(function(e,t){var n=T.fn[t];T.fn[t]=function(e,r,i){return null==e||"boolean"==typeof e?n.apply(this,arguments):this.animate(gt(t,!0),e,r,i)}})),T.each({slideDown:gt("show"),slideUp:gt("hide"),slideToggle:gt("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},(function(e,t){T.fn[e]=function(e,n,r){return this.animate(t,e,n,r)}})),T.timers=[],T.fx.tick=function(){var e,t=0,n=T.timers;for(lt=Date.now();t<n.length;t++)(e=n[t])()||n[t]!==e||n.splice(t--,1);n.length||T.fx.stop(),lt=void 0},T.fx.timer=function(e){T.timers.push(e),T.fx.start()},T.fx.interval=13,T.fx.start=function(){ct||(ct=!0,dt())},T.fx.stop=function(){ct=null},T.fx.speeds={slow:600,fast:200,_default:400},T.fn.delay=function(t,n){return t=T.fx&&T.fx.speeds[t]||t,n=n||"fx",this.queue(n,(function(n,r){var i=e.setTimeout(n,t);r.stop=function(){e.clearTimeout(i)}}))},function(){var e=v.createElement("input"),t=v.createElement("select").appendChild(v.createElement("option"));e.type="checkbox",d.checkOn=""!==e.value,d.optSelected=t.selected,(e=v.createElement("input")).value="t",e.type="radio",d.radioValue="t"===e.value}();var mt,xt=T.expr.attrHandle;T.fn.extend({attr:function(e,t){return J(this,T.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each((function(){T.removeAttr(this,e)}))}}),T.extend({attr:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===e.getAttribute?T.prop(e,t,n):(1===o&&T.isXMLDoc(e)||(i=T.attrHooks[t.toLowerCase()]||(T.expr.match.bool.test(t)?mt:void 0)),void 0!==n?null===n?void T.removeAttr(e,t):i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:(e.setAttribute(t,n+""),n):i&&"get"in i&&null!==(r=i.get(e,t))?r:(r=T.find.attr(e,t))??void 0)},attrHooks:{type:{set:function(e,t){if(!d.radioValue&&"radio"===t&&S(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,r=0,i=t&&t.match(z);if(i&&1===e.nodeType)for(;n=i[r++];)e.removeAttribute(n)}}),mt={set:function(e,t,n){return!1===t?T.removeAttr(e,n):e.setAttribute(n,n),n}},T.each(T.expr.match.bool.source.match(/\w+/g),(function(e,t){var n=xt[t]||T.find.attr;xt[t]=function(e,t,r){var i,o,a=t.toLowerCase();return r||(o=xt[a],xt[a]=i,i=null!=n(e,t,r)?a:null,xt[a]=o),i}}));var bt=/^(?:input|select|textarea|button)$/i,wt=/^(?:a|area)$/i;function Tt(e){return(e.match(z)||[]).join(" ")}function Ct(e){return e.getAttribute&&e.getAttribute("class")||""}function St(e){return Array.isArray(e)?e:"string"==typeof e&&e.match(z)||[]}T.fn.extend({prop:function(e,t){return J(this,T.prop,e,t,arguments.length>1)},removeProp:function(e){return this.each((function(){delete this[T.propFix[e]||e]}))}}),T.extend({prop:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&T.isXMLDoc(e)||(t=T.propFix[t]||t,i=T.propHooks[t]),void 0!==n?i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:e[t]=n:i&&"get"in i&&null!==(r=i.get(e,t))?r:e[t]},propHooks:{tabIndex:{get:function(e){var t=T.find.attr(e,"tabindex");return t?parseInt(t,10):bt.test(e.nodeName)||wt.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),d.optSelected||(T.propHooks.selected={get:function(e){var t=e.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(e){var t=e.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),T.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],(function(){T.propFix[this.toLowerCase()]=this})),T.fn.extend({addClass:function(e){var t,n,r,i,o,a;return h(e)?this.each((function(t){T(this).addClass(e.call(this,t,Ct(this)))})):(t=St(e)).length?this.each((function(){if(r=Ct(this),n=1===this.nodeType&&" "+Tt(r)+" "){for(o=0;o<t.length;o++)i=t[o],n.indexOf(" "+i+" ")<0&&(n+=i+" ");a=Tt(n),r!==a&&this.setAttribute("class",a)}})):this},removeClass:function(e){var t,n,r,i,o,a;return h(e)?this.each((function(t){T(this).removeClass(e.call(this,t,Ct(this)))})):arguments.length?(t=St(e)).length?this.each((function(){if(r=Ct(this),n=1===this.nodeType&&" "+Tt(r)+" "){for(o=0;o<t.length;o++)for(i=t[o];n.indexOf(" "+i+" ")>-1;)n=n.replace(" "+i+" "," ");a=Tt(n),r!==a&&this.setAttribute("class",a)}})):this:this.attr("class","")},toggleClass:function(e,t){var n,r,i,o,a=typeof e,s="string"===a||Array.isArray(e);return h(e)?this.each((function(n){T(this).toggleClass(e.call(this,n,Ct(this),t),t)})):"boolean"==typeof t&&s?t?this.addClass(e):this.removeClass(e):(n=St(e),this.each((function(){if(s)for(o=T(this),i=0;i<n.length;i++)r=n[i],o.hasClass(r)?o.removeClass(r):o.addClass(r);else void 0!==e&&"boolean"!==a||((r=Ct(this))&&ie.set(this,"__className__",r),this.setAttribute&&this.setAttribute("class",r||!1===e?"":ie.get(this,"__className__")||""))})))},hasClass:function(e){var t,n,r=0;for(t=" "+e+" ";n=this[r++];)if(1===n.nodeType&&(" "+Tt(Ct(n))+" ").indexOf(t)>-1)return!0;return!1}});var Et=/\r/g;T.fn.extend({val:function(e){var t,n,r,i=this[0];return arguments.length?(r=h(e),this.each((function(n){var i;1===this.nodeType&&(null==(i=r?e.call(this,n,T(this).val()):e)?i="":"number"==typeof i?i+="":Array.isArray(i)&&(i=T.map(i,(function(e){return null==e?"":e+""}))),(t=T.valHooks[this.type]||T.valHooks[this.nodeName.toLowerCase()])&&"set"in t&&void 0!==t.set(this,i,"value")||(this.value=i))}))):i?(t=T.valHooks[i.type]||T.valHooks[i.nodeName.toLowerCase()])&&"get"in t&&void 0!==(n=t.get(i,"value"))?n:"string"==typeof(n=i.value)?n.replace(Et,""):n??"":void 0}}),T.extend({valHooks:{option:{get:function(e){var t=T.find.attr(e,"value");return null!=t?t:Tt(T.text(e))}},select:{get:function(e){var t,n,r,i=e.options,o=e.selectedIndex,a="select-one"===e.type,s=a?null:[],u=a?o+1:i.length;for(r=o<0?u:a?o:0;r<u;r++)if(((n=i[r]).selected||r===o)&&!n.disabled&&(!n.parentNode.disabled||!S(n.parentNode,"optgroup"))){if(t=T(n).val(),a)return t;s.push(t)}return s},set:function(e,t){for(var n,r,i=e.options,o=T.makeArray(t),a=i.length;a--;)((r=i[a]).selected=T.inArray(T.valHooks.option.get(r),o)>-1)&&(n=!0);return n||(e.selectedIndex=-1),o}}}}),T.each(["radio","checkbox"],(function(){T.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=T.inArray(T(e).val(),t)>-1}},d.checkOn||(T.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})}));var kt=e.location,jt={guid:Date.now()},At=/\?/;T.parseXML=function(t){var n,r;if(!t||"string"!=typeof t)return null;try{n=(new e.DOMParser).parseFromString(t,"text/xml")}catch(e){}return r=n&&n.getElementsByTagName("parsererror")[0],n&&!r||T.error("Invalid XML: "+(r?T.map(r.childNodes,(function(e){return e.textContent})).join("\n"):t)),n};var Dt=/^(?:focusinfocus|focusoutblur)$/,Nt=function(e){e.stopPropagation()};T.extend(T.event,{trigger:function(t,n,r,i){var o,a,s,u,l,f,p,d,y=[r||v],m=c.call(t,"type")?t.type:t,x=c.call(t,"namespace")?t.namespace.split("."):[];if(a=d=s=r=r||v,3!==r.nodeType&&8!==r.nodeType&&!Dt.test(m+T.event.triggered)&&(m.indexOf(".")>-1&&(x=m.split("."),m=x.shift(),x.sort()),l=m.indexOf(":")<0&&"on"+m,(t=t[T.expando]?t:new T.Event(m,"object"==typeof t&&t)).isTrigger=i?2:3,t.namespace=x.join("."),t.rnamespace=t.namespace?new RegExp("(^|\\.)"+x.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,t.result=void 0,t.target||(t.target=r),n=null==n?[t]:T.makeArray(n,[t]),p=T.event.special[m]||{},i||!p.trigger||!1!==p.trigger.apply(r,n))){if(!i&&!p.noBubble&&!g(r)){for(u=p.delegateType||m,Dt.test(u+m)||(a=a.parentNode);a;a=a.parentNode)y.push(a),s=a;s===(r.ownerDocument||v)&&y.push(s.defaultView||s.parentWindow||e)}for(o=0;(a=y[o++])&&!t.isPropagationStopped();)d=a,t.type=o>1?u:p.bindType||m,(f=(ie.get(a,"events")||Object.create(null))[t.type]&&ie.get(a,"handle"))&&f.apply(a,n),(f=l&&a[l])&&f.apply&&ne(a)&&(t.result=f.apply(a,n),!1===t.result&&t.preventDefault());return t.type=m,i||t.isDefaultPrevented()||p._default&&!1!==p._default.apply(y.pop(),n)||!ne(r)||l&&h(r[m])&&!g(r)&&((s=r[l])&&(r[l]=null),T.event.triggered=m,t.isPropagationStopped()&&d.addEventListener(m,Nt),r[m](),t.isPropagationStopped()&&d.removeEventListener(m,Nt),T.event.triggered=void 0,s&&(r[l]=s)),t.result}},simulate:function(e,t,n){var r=T.extend(new T.Event,n,{type:e,isSimulated:!0});T.event.trigger(r,null,t)}}),T.fn.extend({trigger:function(e,t){return this.each((function(){T.event.trigger(e,t,this)}))},triggerHandler:function(e,t){var n=this[0];if(n)return T.event.trigger(e,t,n,!0)}});var qt=/\[\]$/,Lt=/\r?\n/g,Ht=/^(?:submit|button|image|reset|file)$/i,Ot=/^(?:input|select|textarea|keygen)/i;function Pt(e,t,n,r){var i;if(Array.isArray(t))T.each(t,(function(t,i){n||qt.test(e)?r(e,i):Pt(e+"["+("object"==typeof i&&null!=i?t:"")+"]",i,n,r)}));else if(n||"object"!==x(t))r(e,t);else for(i in t)Pt(e+"["+i+"]",t[i],n,r)}T.param=function(e,t){var n,r=[],i=function(e,t){var n=h(t)?t():t;r[r.length]=encodeURIComponent(e)+"="+encodeURIComponent(n??"")};if(null==e)return"";if(Array.isArray(e)||e.jquery&&!T.isPlainObject(e))T.each(e,(function(){i(this.name,this.value)}));else for(n in e)Pt(n,e[n],t,i);return r.join("&")},T.fn.extend({serialize:function(){return T.param(this.serializeArray())},serializeArray:function(){return this.map((function(){var e=T.prop(this,"elements");return e?T.makeArray(e):this})).filter((function(){var e=this.type;return this.name&&!T(this).is(":disabled")&&Ot.test(this.nodeName)&&!Ht.test(e)&&(this.checked||!Te.test(e))})).map((function(e,t){var n=T(this).val();return null==n?null:Array.isArray(n)?T.map(n,(function(e){return{name:t.name,value:e.replace(Lt,"\r\n")}})):{name:t.name,value:n.replace(Lt,"\r\n")}})).get()}});var Mt=/%20/g,Rt=/#.*$/,It=/([?&])_=[^&]*/,Wt=/^(.*?):[ \t]*([^\r\n]*)$/gm,Ft=/^(?:GET|HEAD)$/,$t=/^\/\//,Bt={},_t={},zt="*/".concat("*"),Xt=v.createElement("a");function Ut(e){return function(t,n){"string"!=typeof t&&(n=t,t="*");var r,i=0,o=t.toLowerCase().match(z)||[];if(h(n))for(;r=o[i++];)"+"===r[0]?(r=r.slice(1)||"*",(e[r]=e[r]||[]).unshift(n)):(e[r]=e[r]||[]).push(n)}}function Vt(e,t,n,r){var i={},o=e===_t;function a(s){var u;return i[s]=!0,T.each(e[s]||[],(function(e,s){var l=s(t,n,r);return"string"!=typeof l||o||i[l]?o?!(u=l):void 0:(t.dataTypes.unshift(l),a(l),!1)})),u}return a(t.dataTypes[0])||!i["*"]&&a("*")}function Gt(e,t){var n,r,i=T.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((i[n]?e:r||(r={}))[n]=t[n]);return r&&T.extend(!0,e,r),e}Xt.href=kt.href,T.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:kt.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(kt.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":zt,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":T.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?Gt(Gt(e,T.ajaxSettings),t):Gt(T.ajaxSettings,e)},ajaxPrefilter:Ut(Bt),ajaxTransport:Ut(_t),ajax:function(t,n){"object"==typeof t&&(n=t,t=void 0),n=n||{};var r,i,o,a,s,u,l,c,f,p,d=T.ajaxSetup({},n),h=d.context||d,g=d.context&&(h.nodeType||h.jquery)?T(h):T.event,y=T.Deferred(),m=T.Callbacks("once memory"),x=d.statusCode||{},b={},w={},C="canceled",S={readyState:0,getResponseHeader:function(e){var t;if(l){if(!a)for(a={};t=Wt.exec(o);)a[t[1].toLowerCase()+" "]=(a[t[1].toLowerCase()+" "]||[]).concat(t[2]);t=a[e.toLowerCase()+" "]}return null==t?null:t.join(", ")},getAllResponseHeaders:function(){return l?o:null},setRequestHeader:function(e,t){return null==l&&(e=w[e.toLowerCase()]=w[e.toLowerCase()]||e,b[e]=t),this},overrideMimeType:function(e){return null==l&&(d.mimeType=e),this},statusCode:function(e){var t;if(e)if(l)S.always(e[S.status]);else for(t in e)x[t]=[x[t],e[t]];return this},abort:function(e){var t=e||C;return r&&r.abort(t),E(0,t),this}};if(y.promise(S),d.url=((t||d.url||kt.href)+"").replace($t,kt.protocol+"//"),d.type=n.method||n.type||d.method||d.type,d.dataTypes=(d.dataType||"*").toLowerCase().match(z)||[""],null==d.crossDomain){u=v.createElement("a");try{u.href=d.url,u.href=u.href,d.crossDomain=Xt.protocol+"//"+Xt.host!=u.protocol+"//"+u.host}catch(e){d.crossDomain=!0}}if(d.data&&d.processData&&"string"!=typeof d.data&&(d.data=T.param(d.data,d.traditional)),Vt(Bt,d,n,S),l)return S;for(f in(c=T.event&&d.global)&&0==T.active++&&T.event.trigger("ajaxStart"),d.type=d.type.toUpperCase(),d.hasContent=!Ft.test(d.type),i=d.url.replace(Rt,""),d.hasContent?d.data&&d.processData&&0===(d.contentType||"").indexOf("application/x-www-form-urlencoded")&&(d.data=d.data.replace(Mt,"+")):(p=d.url.slice(i.length),d.data&&(d.processData||"string"==typeof d.data)&&(i+=(At.test(i)?"&":"?")+d.data,delete d.data),!1===d.cache&&(i=i.replace(It,"$1"),p=(At.test(i)?"&":"?")+"_="+jt.guid+++p),d.url=i+p),d.ifModified&&(T.lastModified[i]&&S.setRequestHeader("If-Modified-Since",T.lastModified[i]),T.etag[i]&&S.setRequestHeader("If-None-Match",T.etag[i])),(d.data&&d.hasContent&&!1!==d.contentType||n.contentType)&&S.setRequestHeader("Content-Type",d.contentType),S.setRequestHeader("Accept",d.dataTypes[0]&&d.accepts[d.dataTypes[0]]?d.accepts[d.dataTypes[0]]+("*"!==d.dataTypes[0]?", "+zt+"; q=0.01":""):d.accepts["*"]),d.headers)S.setRequestHeader(f,d.headers[f]);if(d.beforeSend&&(!1===d.beforeSend.call(h,S,d)||l))return S.abort();if(C="abort",m.add(d.complete),S.done(d.success),S.fail(d.error),r=Vt(_t,d,n,S)){if(S.readyState=1,c&&g.trigger("ajaxSend",[S,d]),l)return S;d.async&&d.timeout>0&&(s=e.setTimeout((function(){S.abort("timeout")}),d.timeout));try{l=!1,r.send(b,E)}catch(e){if(l)throw e;E(-1,e)}}else E(-1,"No Transport");function E(t,n,a,u){var f,p,v,b,w,C=n;l||(l=!0,s&&e.clearTimeout(s),r=void 0,o=u||"",S.readyState=t>0?4:0,f=t>=200&&t<300||304===t,a&&(b=function(e,t,n){for(var r,i,o,a,s=e.contents,u=e.dataTypes;"*"===u[0];)u.shift(),void 0===r&&(r=e.mimeType||t.getResponseHeader("Content-Type"));if(r)for(i in s)if(s[i]&&s[i].test(r)){u.unshift(i);break}if(u[0]in n)o=u[0];else{for(i in n){if(!u[0]||e.converters[i+" "+u[0]]){o=i;break}a||(a=i)}o=o||a}if(o)return o!==u[0]&&u.unshift(o),n[o]}(d,S,a)),!f&&T.inArray("script",d.dataTypes)>-1&&T.inArray("json",d.dataTypes)<0&&(d.converters["text script"]=function(){}),b=function(e,t,n,r){var i,o,a,s,u,l={},c=e.dataTypes.slice();if(c[1])for(a in e.converters)l[a.toLowerCase()]=e.converters[a];for(o=c.shift();o;)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!u&&r&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),u=o,o=c.shift())if("*"===o)o=u;else if("*"!==u&&u!==o){if(!(a=l[u+" "+o]||l["* "+o]))for(i in l)if((s=i.split(" "))[1]===o&&(a=l[u+" "+s[0]]||l["* "+s[0]])){!0===a?a=l[i]:!0!==l[i]&&(o=s[0],c.unshift(s[1]));break}if(!0!==a)if(a&&e.throws)t=a(t);else try{t=a(t)}catch(e){return{state:"parsererror",error:a?e:"No conversion from "+u+" to "+o}}}return{state:"success",data:t}}(d,b,S,f),f?(d.ifModified&&((w=S.getResponseHeader("Last-Modified"))&&(T.lastModified[i]=w),(w=S.getResponseHeader("etag"))&&(T.etag[i]=w)),204===t||"HEAD"===d.type?C="nocontent":304===t?C="notmodified":(C=b.state,p=b.data,f=!(v=b.error))):(v=C,!t&&C||(C="error",t<0&&(t=0))),S.status=t,S.statusText=(n||C)+"",f?y.resolveWith(h,[p,C,S]):y.rejectWith(h,[S,C,v]),S.statusCode(x),x=void 0,c&&g.trigger(f?"ajaxSuccess":"ajaxError",[S,d,f?p:v]),m.fireWith(h,[S,C]),c&&(g.trigger("ajaxComplete",[S,d]),--T.active||T.event.trigger("ajaxStop")))}return S},getJSON:function(e,t,n){return T.get(e,t,n,"json")},getScript:function(e,t){return T.get(e,void 0,t,"script")}}),T.each(["get","post"],(function(e,t){T[t]=function(e,n,r,i){return h(n)&&(i=i||r,r=n,n=void 0),T.ajax(T.extend({url:e,type:t,dataType:i,data:n,success:r},T.isPlainObject(e)&&e))}})),T.ajaxPrefilter((function(e){var t;for(t in e.headers)"content-type"===t.toLowerCase()&&(e.contentType=e.headers[t]||"")})),T._evalUrl=function(e,t,n){return T.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(e){T.globalEval(e,t,n)}})},T.fn.extend({wrapAll:function(e){var t;return this[0]&&(h(e)&&(e=e.call(this[0])),t=T(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map((function(){for(var e=this;e.firstElementChild;)e=e.firstElementChild;return e})).append(this)),this},wrapInner:function(e){return h(e)?this.each((function(t){T(this).wrapInner(e.call(this,t))})):this.each((function(){var t=T(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)}))},wrap:function(e){var t=h(e);return this.each((function(n){T(this).wrapAll(t?e.call(this,n):e)}))},unwrap:function(e){return this.parent(e).not("body").each((function(){T(this).replaceWith(this.childNodes)})),this}}),T.expr.pseudos.hidden=function(e){return!T.expr.pseudos.visible(e)},T.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},T.ajaxSettings.xhr=function(){try{return new e.XMLHttpRequest}catch(e){}};var Yt={0:200,1223:204},Qt=T.ajaxSettings.xhr();d.cors=!!Qt&&"withCredentials"in Qt,d.ajax=Qt=!!Qt,T.ajaxTransport((function(t){var n,r;if(d.cors||Qt&&!t.crossDomain)return{send:function(i,o){var a,s=t.xhr();if(s.open(t.type,t.url,t.async,t.username,t.password),t.xhrFields)for(a in t.xhrFields)s[a]=t.xhrFields[a];for(a in t.mimeType&&s.overrideMimeType&&s.overrideMimeType(t.mimeType),t.crossDomain||i["X-Requested-With"]||(i["X-Requested-With"]="XMLHttpRequest"),i)s.setRequestHeader(a,i[a]);n=function(e){return function(){n&&(n=r=s.onload=s.onerror=s.onabort=s.ontimeout=s.onreadystatechange=null,"abort"===e?s.abort():"error"===e?"number"!=typeof s.status?o(0,"error"):o(s.status,s.statusText):o(Yt[s.status]||s.status,s.statusText,"text"!==(s.responseType||"text")||"string"!=typeof s.responseText?{binary:s.response}:{text:s.responseText},s.getAllResponseHeaders()))}},s.onload=n(),r=s.onerror=s.ontimeout=n("error"),void 0!==s.onabort?s.onabort=r:s.onreadystatechange=function(){4===s.readyState&&e.setTimeout((function(){n&&r()}))},n=n("abort");try{s.send(t.hasContent&&t.data||null)}catch(e){if(n)throw e}},abort:function(){n&&n()}}})),T.ajaxPrefilter((function(e){e.crossDomain&&(e.contents.script=!1)})),T.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return T.globalEval(e),e}}}),T.ajaxPrefilter("script",(function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")})),T.ajaxTransport("script",(function(e){var t,n;if(e.crossDomain||e.scriptAttrs)return{send:function(r,i){t=T("<script>").attr(e.scriptAttrs||{}).prop({charset:e.scriptCharset,src:e.url}).on("load error",n=function(e){t.remove(),n=null,e&&i("error"===e.type?404:200,e.type)}),v.head.appendChild(t[0])},abort:function(){n&&n()}}}));var Jt,Kt=[],Zt=/(=)\?(?=&|$)|\?\?/;T.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=Kt.pop()||T.expando+"_"+jt.guid++;return this[e]=!0,e}}),T.ajaxPrefilter("json jsonp",(function(t,n,r){var i,o,a,s=!1!==t.jsonp&&(Zt.test(t.url)?"url":"string"==typeof t.data&&0===(t.contentType||"").indexOf("application/x-www-form-urlencoded")&&Zt.test(t.data)&&"data");if(s||"jsonp"===t.dataTypes[0])return i=t.jsonpCallback=h(t.jsonpCallback)?t.jsonpCallback():t.jsonpCallback,s?t[s]=t[s].replace(Zt,"$1"+i):!1!==t.jsonp&&(t.url+=(At.test(t.url)?"&":"?")+t.jsonp+"="+i),t.converters["script json"]=function(){return a||T.error(i+" was not called"),a[0]},t.dataTypes[0]="json",o=e[i],e[i]=function(){a=arguments},r.always((function(){void 0===o?T(e).removeProp(i):e[i]=o,t[i]&&(t.jsonpCallback=n.jsonpCallback,Kt.push(i)),a&&h(o)&&o(a[0]),a=o=void 0})),"script"})),d.createHTMLDocument=((Jt=v.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===Jt.childNodes.length),T.parseHTML=function(e,t,n){return"string"!=typeof e?[]:("boolean"==typeof t&&(n=t,t=!1),t||(d.createHTMLDocument?((r=(t=v.implementation.createHTMLDocument("")).createElement("base")).href=v.location.href,t.head.appendChild(r)):t=v),o=!n&&[],(i=R.exec(e))?[t.createElement(i[1])]:(i=De([e],t,o),o&&o.length&&T(o).remove(),T.merge([],i.childNodes)));var r,i,o},T.fn.load=function(e,t,n){var r,i,o,a=this,s=e.indexOf(" ");return s>-1&&(r=Tt(e.slice(s)),e=e.slice(0,s)),h(t)?(n=t,t=void 0):t&&"object"==typeof t&&(i="POST"),a.length>0&&T.ajax({url:e,type:i||"GET",dataType:"html",data:t}).done((function(e){o=arguments,a.html(r?T("<div>").append(T.parseHTML(e)).find(r):e)})).always(n&&function(e,t){a.each((function(){n.apply(this,o||[e.responseText,t,e])}))}),this},T.expr.pseudos.animated=function(e){return T.grep(T.timers,(function(t){return e===t.elem})).length},T.offset={setOffset:function(e,t,n){var r,i,o,a,s,u,l=T.css(e,"position"),c=T(e),f={};"static"===l&&(e.style.position="relative"),s=c.offset(),o=T.css(e,"top"),u=T.css(e,"left"),("absolute"===l||"fixed"===l)&&(o+u).indexOf("auto")>-1?(a=(r=c.position()).top,i=r.left):(a=parseFloat(o)||0,i=parseFloat(u)||0),h(t)&&(t=t.call(e,n,T.extend({},s))),null!=t.top&&(f.top=t.top-s.top+a),null!=t.left&&(f.left=t.left-s.left+i),"using"in t?t.using.call(e,f):c.css(f)}},T.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each((function(t){T.offset.setOffset(this,e,t)}));var t,n,r=this[0];return r?r.getClientRects().length?(t=r.getBoundingClientRect(),n=r.ownerDocument.defaultView,{top:t.top+n.pageYOffset,left:t.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var e,t,n,r=this[0],i={top:0,left:0};if("fixed"===T.css(r,"position"))t=r.getBoundingClientRect();else{for(t=this.offset(),n=r.ownerDocument,e=r.offsetParent||n.documentElement;e&&(e===n.body||e===n.documentElement)&&"static"===T.css(e,"position");)e=e.parentNode;e&&e!==r&&1===e.nodeType&&((i=T(e).offset()).top+=T.css(e,"borderTopWidth",!0),i.left+=T.css(e,"borderLeftWidth",!0))}return{top:t.top-i.top-T.css(r,"marginTop",!0),left:t.left-i.left-T.css(r,"marginLeft",!0)}}},offsetParent:function(){return this.map((function(){for(var e=this.offsetParent;e&&"static"===T.css(e,"position");)e=e.offsetParent;return e||pe}))}}),T.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},(function(e,t){var n="pageYOffset"===t;T.fn[e]=function(r){return J(this,(function(e,r,i){var o;if(g(e)?o=e:9===e.nodeType&&(o=e.defaultView),void 0===i)return o?o[t]:e[r];o?o.scrollTo(n?o.pageXOffset:i,n?i:o.pageYOffset):e[r]=i}),e,r,arguments.length)}})),T.each(["top","left"],(function(e,t){T.cssHooks[t]=Je(d.pixelPosition,(function(e,n){if(n)return n=Qe(e,t),Xe.test(n)?T(e).position()[t]+"px":n}))})),T.each({Height:"height",Width:"width"},(function(e,t){T.each({padding:"inner"+e,content:t,"":"outer"+e},(function(n,r){T.fn[r]=function(i,o){var a=arguments.length&&(n||"boolean"!=typeof i),s=n||(!0===i||!0===o?"margin":"border");return J(this,(function(t,n,i){var o;return g(t)?0===r.indexOf("outer")?t["inner"+e]:t.document.documentElement["client"+e]:9===t.nodeType?(o=t.documentElement,Math.max(t.body["scroll"+e],o["scroll"+e],t.body["offset"+e],o["offset"+e],o["client"+e])):void 0===i?T.css(t,n,s):T.style(t,n,i,s)}),t,a?i:void 0,a)}}))})),T.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],(function(e,t){T.fn[t]=function(e){return this.on(t,e)}})),T.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)},hover:function(e,t){return this.on("mouseenter",e).on("mouseleave",t||e)}}),T.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),(function(e,t){T.fn[t]=function(e,n){return arguments.length>0?this.on(t,null,e,n):this.trigger(t)}}));var en=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;T.proxy=function(e,t){var n,r,o;if("string"==typeof t&&(n=e[t],t=e,e=n),h(e))return r=i.call(arguments,2),o=function(){return e.apply(t||this,r.concat(i.call(arguments)))},o.guid=e.guid=e.guid||T.guid++,o},T.holdReady=function(e){e?T.readyWait++:T.ready(!0)},T.isArray=Array.isArray,T.parseJSON=JSON.parse,T.nodeName=S,T.isFunction=h,T.isWindow=g,T.camelCase=te,T.type=x,T.now=Date.now,T.isNumeric=function(e){var t=T.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},T.trim=function(e){return null==e?"":(e+"").replace(en,"$1")},"function"==typeof define&&define.amd&&define("jquery",[],(function(){return T}));var tn=e.jQuery,nn=e.$;return T.noConflict=function(t){return e.$===T&&(e.$=nn),t&&e.jQuery===T&&(e.jQuery=tn),T},void 0===t&&(e.jQuery=e.$=T),T}))},
706: function _(t,n,i,o,e){var r=t(704);function u(){var t=!1,n=!1;this.stopPropagation=function(){t=!0},this.isPropagationStopped=function(){return t},this.stopImmediatePropagation=function(){n=!0},this.isImmediatePropagationStopped=function(){return n}}function s(){this.__nonDataRow=!0}function l(){this.__group=!0,this.level=0,this.count=0,this.value=null,this.title=null,this.collapsed=!1,this.selectChecked=!1,this.totals=null,this.rows=[],this.groups=null,this.groupingKey=null}function c(){this.__groupTotals=!0,this.group=null,this.initialized=!1}function a(){var t=null;this.isActive=function(n){return n?t===n:null!==t},this.activate=function(n){if(n!==t){if(null!==t)throw new Error("SlickGrid.EditorLock.activate: an editController is still active, can't activate another editController");if(!n.commitCurrentEdit)throw new Error("SlickGrid.EditorLock.activate: editController must implement .commitCurrentEdit()");if(!n.cancelCurrentEdit)throw new Error("SlickGrid.EditorLock.activate: editController must implement .cancelCurrentEdit()");t=n}},this.deactivate=function(n){if(t){if(t!==n)throw new Error("SlickGrid.EditorLock.deactivate: specified editController is not the currently active one");t=null}},this.commitCurrentEdit=function(){return!t||t.commitCurrentEdit()},this.cancelCurrentEdit=function(){return!t||t.cancelCurrentEdit()}}l.prototype=new s,l.prototype.equals=function(t){return this.value===t.value&&this.count===t.count&&this.collapsed===t.collapsed&&this.title===t.title},c.prototype=new s;var h="Map"in window?window.Map:function(){var t={};this.get=function(n){return t[n]},this.set=function(n,i){t[n]=i},this.has=function(n){return n in t},this.delete=function(n){delete t[n]}};n.exports={Event:function(){var t=[];this.subscribe=function(n){t.push(n)},this.unsubscribe=function(n){for(var i=t.length-1;i>=0;i--)t[i]===n&&t.splice(i,1)},this.notify=function(n,i,o){var e;i=i||new u,o=o||this;for(var r=0;r<t.length&&!i.isPropagationStopped()&&!i.isImmediatePropagationStopped();r++)e=t[r].call(o,i,n);return e}},EventData:u,EventHandler:function(){var t=[];this.subscribe=function(n,i){return t.push({event:n,handler:i}),n.subscribe(i),this},this.unsubscribe=function(n,i){for(var o=t.length;o--;)if(t[o].event===n&&t[o].handler===i)return t.splice(o,1),void n.unsubscribe(i);return this},this.unsubscribeAll=function(){for(var n=t.length;n--;)t[n].event.unsubscribe(t[n].handler);return t=[],this}},Range:function(t,n,i,o){void 0===i&&void 0===o&&(i=t,o=n),this.fromRow=Math.min(t,i),this.fromCell=Math.min(n,o),this.toRow=Math.max(t,i),this.toCell=Math.max(n,o),this.isSingleRow=function(){return this.fromRow==this.toRow},this.isSingleCell=function(){return this.fromRow==this.toRow&&this.fromCell==this.toCell},this.contains=function(t,n){return t>=this.fromRow&&t<=this.toRow&&n>=this.fromCell&&n<=this.toCell},this.toString=function(){return this.isSingleCell()?"("+this.fromRow+":"+this.fromCell+")":"("+this.fromRow+":"+this.fromCell+" - "+this.toRow+":"+this.toCell+")"}},Map:h,NonDataRow:s,Group:l,GroupTotals:c,EditorLock:a,GlobalEditorLock:new a,TreeColumns:function(t){var n={};function i(t){t.forEach((function(t){n[t.id]=t,t.columns&&i(t.columns)}))}function o(t,n){return t.filter((function(t){var i=n.call(t);return i&&t.columns&&(t.columns=o(t.columns,n)),i&&(!t.columns||t.columns.length)}))}function e(t,n){t.sort((function(t,i){return u(n.getColumnIndex(t.id))-u(n.getColumnIndex(i.id))})).forEach((function(t){t.columns&&e(t.columns,n)}))}function u(t){return void 0===t?-1:t}function s(t){if(!t.length)return t.columns?1+s(t.columns):1;for(var n in t)return s(t[n])}function l(t,n,i){var o=[];if(n==(i=i||0))return t.length&&t.forEach((function(t){t.columns&&(t.extractColumns=function(){return c(t)})})),t;for(var e in t)t[e].columns&&(o=o.concat(l(t[e].columns,n,i+1)));return o}function c(t){var n=[];if(t.hasOwnProperty("length"))for(var i=0;i<t.length;i++)n=n.concat(c(t[i]));else{if(!t.hasOwnProperty("columns"))return t;n=n.concat(c(t.columns))}return n}function a(){return r.extend(!0,[],t)}i(t),this.hasDepth=function(){for(var n in t)if(t[n].hasOwnProperty("columns"))return!0;return!1},this.getTreeColumns=function(){return t},this.extractColumns=function(){return this.hasDepth()?c(t):t},this.getDepth=function(){return s(t)},this.getColumnsInDepth=function(n){return l(t,n)},this.getColumnsInGroup=function(t){return c(t)},this.visibleColumns=function(){return o(a(),(function(){return this.visible}))},this.filter=function(t){return o(a(),t)},this.reOrder=function(n){return e(t,n)},this.getById=function(t){return n[t]},this.getInIds=function(t){return t.map((function(t){return n[t]}))}},keyCode:{SPACE:8,BACKSPACE:8,DELETE:46,DOWN:40,END:35,ENTER:13,ESCAPE:27,ESC:27,HOME:36,INSERT:45,LEFT:37,PAGE_DOWN:34,PAGE_UP:33,RIGHT:39,TAB:9,UP:38,A:65,C:67,V:86},preClickClassName:"slick-edit-preclick",GridAutosizeColsMode:{None:"NOA",LegacyOff:"LOF",LegacyForceFit:"LFF",IgnoreViewport:"IGV",FitColsToViewport:"FCV",FitViewportToCols:"FVC"},ColAutosizeMode:{Locked:"LCK",Guide:"GUI",Content:"CON",ContentIntelligent:"CTI"},RowSelectionMode:{FirstRow:"FS1",FirstNRows:"FSN",AllRows:"ALL",LastRow:"LS1"},ValueFilterMode:{None:"NONE",DeDuplicate:"DEDP",GetGreatestAndSub:"GR8T",GetLongestTextAndSub:"LNSB",GetLongestText:"LNSC"},WidthEvalMode:{CanvasTextSize:"CANV",HTML:"HTML"}}},
707: function _(e,t,o,l,n){var i=e(704),c=e(706);t.exports={CheckboxSelectColumn:function(e){var t,o=null,l=k(),n=new c.EventHandler,r={},d=!1,a=i.extend(!0,{},{columnId:"_checkbox_selector",cssClass:null,hideSelectAllCheckbox:!1,toolTip:"Select/Deselect All",width:30,hideInColumnTitleRow:!1,hideInFilterHeaderRow:!0},e);function s(){t.updateColumnHeader(a.columnId,"","")}function u(){i("#filter-checkbox-selectall-container").hide()}function h(e,n){var c,s,u,h=t.getSelectedRows(),f={},p=0;if("function"==typeof o)for(u=0;u<t.getDataLength();u++){C(s,t.getDataItem(u),t)||p++}var b=[];for(s=0;s<h.length;s++){c=h[s],C(s,t.getDataItem(c),t)?(f[c]=!0,f[c]!==r[c]&&(t.invalidateRow(c),delete r[c])):b.push(c)}for(s in r)t.invalidateRow(s);(r=f,t.render(),d=h.length&&h.length+p>=t.getDataLength(),a.hideInColumnTitleRow||a.hideSelectAllCheckbox||v(d),a.hideInFilterHeaderRow)||i("#header-filter-selector"+l).prop("checked",d);if(b.length>0){for(s=0;s<b.length;s++){var g=h.indexOf(b[s]);h.splice(g,1)}t.setSelectedRows(h)}}function f(e,o){32==e.which&&t.getColumns()[o.cell].id===a.columnId&&(t.getEditorLock().isActive()&&!t.getEditorLock().commitCurrentEdit()||b(o.row),e.preventDefault(),e.stopImmediatePropagation())}function p(e,o){if(t.getColumns()[o.cell].id===a.columnId&&i(e.target).is(":checkbox")){if(t.getEditorLock().isActive()&&!t.getEditorLock().commitCurrentEdit())return e.preventDefault(),void e.stopImmediatePropagation();b(o.row),e.stopPropagation(),e.stopImmediatePropagation()}}function b(e){var o=t.getDataItem(e);C(e,o,t)&&(r[e]?t.setSelectedRows(i.grep(t.getSelectedRows(),(function(t){return t!=e}))):t.setSelectedRows(t.getSelectedRows().concat(e)),t.setActiveCell(e,function(){if(null===m){m=0;for(var e=t.getColumns(),o=0;o<e.length;o++)e[o].id==a.columnId&&(m=o)}return m}()))}function g(e,o){if(o.column.id==a.columnId&&i(e.target).is(":checkbox")){if(t.getEditorLock().isActive()&&!t.getEditorLock().commitCurrentEdit())return e.preventDefault(),void e.stopImmediatePropagation();if(i(e.target).is(":checked")){for(var l=[],n=0;n<t.getDataLength();n++){var c=t.getDataItem(n);c.__group||c.__groupTotals||!C(n,c,t)||l.push(n)}t.setSelectedRows(l)}else t.setSelectedRows([]);e.stopPropagation(),e.stopImmediatePropagation()}}"function"==typeof a.selectableOverride&&R(a.selectableOverride);var m=null;function k(){return Math.round(1e7*Math.random())}function w(e,t,o,l,n,i){var c=k()+e;return n&&C(e,n,i)?r[e]?"<input id='selector"+c+"' type='checkbox' checked='checked'><label for='selector"+c+"'></label>":"<input id='selector"+c+"' type='checkbox'><label for='selector"+c+"'></label>":null}function C(e,t,l){return"function"!=typeof o||o(e,t,l)}function v(e){e?t.updateColumnHeader(a.columnId,"<input id='header-selector"+l+"' type='checkbox' checked='checked'><label for='header-selector"+l+"'></label>",a.toolTip):t.updateColumnHeader(a.columnId,"<input id='header-selector"+l+"' type='checkbox'><label for='header-selector"+l+"'></label>",a.toolTip)}function R(e){o=e}i.extend(this,{init:function(e){t=e,n.subscribe(t.onSelectedRowsChanged,h).subscribe(t.onClick,p).subscribe(t.onKeyDown,f),a.hideInFilterHeaderRow||function(e){n.subscribe(e.onHeaderRowCellRendered,(function(e,t){"sel"===t.column.field&&(i(t.node).empty(),i("<span id='filter-checkbox-selectall-container'><input id='header-filter-selector"+l+"' type='checkbox'><label for='header-filter-selector"+l+"'></label></span>").appendTo(t.node).on("click",(function(e){g(e,t)})))}))}(e),a.hideInColumnTitleRow||n.subscribe(t.onHeaderClick,g)},destroy:function(){n.unsubscribeAll()},pluginName:"CheckboxSelectColumn",deSelectRows:function(e){var o,l=e.length,n=[];for(o=0;o<l;o++)r[e[o]]&&(n[n.length]=e[o]);t.setSelectedRows(i.grep(t.getSelectedRows(),(function(e){return n.indexOf(e)<0})))},selectRows:function(e){var o,l=e.length,n=[];for(o=0;o<l;o++)r[e[o]]||(n[n.length]=e[o]);t.setSelectedRows(t.getSelectedRows().concat(n))},getColumnDefinition:function(){return{id:a.columnId,name:a.hideSelectAllCheckbox||a.hideInColumnTitleRow?"":"<input id='header-selector"+l+"' type='checkbox'><label for='header-selector"+l+"'></label>",toolTip:a.hideSelectAllCheckbox||a.hideInColumnTitleRow?"":a.toolTip,field:"sel",width:a.width,resizable:!1,sortable:!1,cssClass:a.cssClass,hideSelectAllCheckbox:a.hideSelectAllCheckbox,formatter:w}},getOptions:function(){return a},selectableOverride:R,setOptions:function(e){if((a=i.extend(!0,{},a,e)).hideSelectAllCheckbox)s(),u();else if(a.hideInColumnTitleRow?s():(v(d),n.subscribe(t.onHeaderClick,g)),a.hideInFilterHeaderRow)u();else{var o=i("#filter-checkbox-selectall-container");o.show(),o.find('input[type="checkbox"]').prop("checked",d)}}})}}},
708: function _(e,t,o,l,n){var a=e(704),r=e(706),i=r.keyCode;t.exports={CellExternalCopyManager:function(e){var t,o,l=this,n=e||{},s=n.copiedCellStyleLayerKey||"copy-manager",u=n.copiedCellStyle||"copied",c=0,d=n.bodyElement||document.body,f=n.onCopyInit||null,h=n.onCopySuccess||null;function C(e){if(n.headerColumnValueExtractor){var t=n.headerColumnValueExtractor(e);if(t)return t}return e.name}function m(e,o,l){if(n.dataItemColumnValueExtractor){var r=n.dataItemColumnValueExtractor(e,o);if(r)return r}var i="";if(o.editor){var s={container:a("<p>"),column:o,position:{top:0,left:0},grid:t,event:l},u=new o.editor(s);u.loadValue(e),i=u.serializeValue(),u.destroy()}else i=e[o.field];return i}function g(e,o,l){if(o.denyPaste)return null;if(n.dataItemColumnValueSetter)return n.dataItemColumnValueSetter(e,o,l);if(o.editor){var r={container:a("body"),column:o,position:{top:0,left:0},grid:t},i=new o.editor(r);i.loadValue(e),i.applyValue(e,l),i.destroy()}else e[o.field]=l}function p(e){var t=document.createElement("textarea");return t.style.position="absolute",t.style.left="-1000px",t.style.top=document.body.scrollTop+"px",t.value=e,d.appendChild(t),t.select(),t}function y(e,a){var r;if(!t.getEditorLock().isActive()||t.getOptions().autoEdit){if(e.which==i.ESC&&o&&(e.preventDefault(),w(),l.onCopyCancelled.notify({ranges:o}),o=null),(e.which===i.C||e.which===i.INSERT)&&(e.ctrlKey||e.metaKey)&&!e.shiftKey&&(f&&f.call(),0!==(r=t.getSelectionModel().getSelectedRanges()).length)){o=r,v(r),l.onCopyCells.notify({ranges:r});for(var s=t.getColumns(),u="",c=0;c<r.length;c++){for(var y=r[c],D=[],S=y.fromRow;S<y.toRow+1;S++){var R=[],x=t.getDataItem(S);if(0===D.length&&n.includeHeaderWhenCopying){for(var E=[],V=y.fromCell;V<y.toCell+1;V++)s[V].name.length>0&&E.push(C(s[V]));D.push(E.join("\t"))}for(V=y.fromCell;V<y.toCell+1;V++)R.push(m(x,s[V],e));D.push(R.join("\t"))}u+=D.join("\r\n")+"\r\n"}if(window.clipboardData)return window.clipboardData.setData("Text",u),!0;var b=document.activeElement;if((M=p(u)).focus(),setTimeout((function(){d.removeChild(M),b?b.focus():console.log("Not element to restore focus to after copy?")}),100),h){var I=0;I=1===r.length?r[0].toRow+1-r[0].fromRow:r.length,h.call(this,I)}return!1}if(!n.readOnlyMode&&(e.which===i.V&&(e.ctrlKey||e.metaKey)&&!e.shiftKey||e.which===i.INSERT&&e.shiftKey&&!e.ctrlKey)){var M=p("");return setTimeout((function(){!function(e,t){var o=e.getColumns(),a=t.value.split(/[\n\f\r]/);""===a[a.length-1]&&a.pop();var r=[],i=0;d.removeChild(t);for(var s=0;s<a.length;s++)""!==a[s]?r[i++]=a[s].split("\t"):r[i++]=[""];var u=e.getActiveCell(),c=e.getSelectionModel().getSelectedRanges(),f=c&&c.length?c[0]:null,h=null,C=null;if(f)h=f.fromRow,C=f.fromCell;else{if(!u)return;h=u.row,C=u.cell}var m=!1,p=r.length,y=r.length?r[0].length:0;1==r.length&&1==r[0].length&&f&&(m=!0,p=f.toRow-f.fromRow+1,y=f.toCell-f.fromCell+1);var w=e.getData().length-h,D=0;if(w<p&&n.newRowCreator){var S=e.getData();for(D=1;D<=p-w;D++)S.push({});e.setData(S),e.render()}var R=h+p>e.getDataLength();if(n.newRowCreator&&R){var x=h+p-e.getDataLength();n.newRowCreator(x)}var E={isClipboardCommand:!0,clippedRange:r,oldValues:[],cellExternalCopyManager:l,_options:n,setDataItemValueForColumn:g,markCopySelection:v,oneCellToMultiple:m,activeRow:h,activeCell:C,destH:p,destW:y,maxDestY:e.getDataLength(),maxDestX:e.getColumns().length,h:0,w:0,execute:function(){this.h=0;for(var t=0;t<this.destH;t++){this.oldValues[t]=[],this.w=0,this.h++;for(var l=0;l<this.destW;l++){this.w++;var n=h+t,a=C+l;if(n<this.maxDestY&&a<this.maxDestX){e.getCellNode(n,a);var i=e.getDataItem(n);this.oldValues[t][l]=i[o[a].field],m?this.setDataItemValueForColumn(i,o[a],r[0][0]):this.setDataItemValueForColumn(i,o[a],r[t]?r[t][l]:""),e.updateCell(n,a),e.onCellChange.notify({row:n,cell:a,item:i,grid:e})}}}var s={fromCell:C,fromRow:h,toCell:C+this.w-1,toRow:h+this.h-1};this.markCopySelection([s]),e.getSelectionModel().setSelectedRanges([s]),this.cellExternalCopyManager.onPasteCells.notify({ranges:[s]})},undo:function(){for(var t=0;t<this.destH;t++)for(var l=0;l<this.destW;l++){var n=h+t,a=C+l;if(n<this.maxDestY&&a<this.maxDestX){e.getCellNode(n,a);var r=e.getDataItem(n);m?this.setDataItemValueForColumn(r,o[a],this.oldValues[0][0]):this.setDataItemValueForColumn(r,o[a],this.oldValues[t][l]),e.updateCell(n,a),e.onCellChange.notify({row:n,cell:a,item:r,grid:e})}}var i={fromCell:C,fromRow:h,toCell:C+this.w-1,toRow:h+this.h-1};if(this.markCopySelection([i]),e.getSelectionModel().setSelectedRanges([i]),this.cellExternalCopyManager.onPasteCells.notify({ranges:[i]}),D>1){for(var s=e.getData();D>1;D--)s.splice(s.length-1,1);e.setData(s),e.render()}}};n.clipboardCommandHandler?n.clipboardCommandHandler(E):E.execute()}(t,M)}),100),!1}}}function v(e){w();for(var o=t.getColumns(),n={},a=0;a<e.length;a++)for(var r=e[a].fromRow;r<=e[a].toRow;r++){n[r]={};for(var i=e[a].fromCell;i<=e[a].toCell&&i<o.length;i++)n[r][o[i].id]=u}t.setCellCssStyles(s,n),clearTimeout(c),c=setTimeout((function(){l.clearCopySelection()}),2e3)}function w(){t.removeCellCssStyles(s)}a.extend(this,{init:function(e){(t=e).onKeyDown.subscribe(y);var o=e.getSelectionModel();if(!o)throw new Error("Selection model is mandatory for this plugin. Please set a selection model on the grid before adding this plugin: grid.setSelectionModel(new Slick.CellSelectionModel())");o.onSelectedRangesChanged.subscribe((function(e,o){t.focus()}))},destroy:function(){t.onKeyDown.unsubscribe(y)},pluginName:"CellExternalCopyManager",clearCopySelection:w,handleKeyDown:y,onCopyCells:new r.Event,onCopyCancelled:new r.Event,onPasteCells:new r.Event,setIncludeHeaderWhenCopying:function(e){n.includeHeaderWhenCopying=e}})}}},
709: function _(r,t,o,_,e){var p=r(1);p.__exportStar(r(706),t.exports),p.__exportStar(r(710),t.exports),p.__exportStar(r(713),t.exports),p.__exportStar(r(714),t.exports),p.__exportStar(r(715),t.exports),p.__exportStar(r(716),t.exports),p.__exportStar(r(717),t.exports)},
710: function _(require,module,exports,__esModule,__esExport){
/**
     * @license
     * (c) 2009-2016 Michael Leibman
     * michael{dot}leibman{at}gmail{dot}com
     * http://github.com/mleibman/slickgrid
     *
     * Distributed under MIT license.
     * All rights reserved.
     *
     * SlickGrid v2.4
     *
     * NOTES:
     *     Cell/row DOM manipulations are done directly bypassing jQuery's DOM manipulation methods.
     *     This increases the speed dramatically, but can only be done safely because there are no event handlers
     *     or data associated with any cell/row DOM nodes.  Cell editors must make sure they implement .destroy()
     *     and do proper cleanup.
     */
const jQuery=require(704),$=jQuery;var Slick=require(706),scrollbarDimensions,maxSupportedCssHeight;function SlickGrid(container,data,columns,options){$.fn.drag||require(711),$.fn.drop||require(712);var defaults={alwaysShowVerticalScroll:!1,alwaysAllowHorizontalScroll:!1,explicitInitialization:!1,rowHeight:25,defaultColumnWidth:80,enableAddRow:!1,leaveSpaceForNewRows:!1,editable:!1,autoEdit:!0,suppressActiveCellChangeOnEdit:!1,enableCellNavigation:!0,enableColumnReorder:!0,asyncEditorLoading:!1,asyncEditorLoadDelay:100,forceFitColumns:!1,enableAsyncPostRender:!1,asyncPostRenderDelay:50,enableAsyncPostRenderCleanup:!1,asyncPostRenderCleanupDelay:40,autoHeight:!1,editorLock:Slick.GlobalEditorLock,showColumnHeader:!0,showHeaderRow:!1,headerRowHeight:25,createFooterRow:!1,showFooterRow:!1,footerRowHeight:25,createPreHeaderPanel:!1,showPreHeaderPanel:!1,preHeaderPanelHeight:25,showTopPanel:!1,topPanelHeight:25,formatterFactory:null,editorFactory:null,cellFlashingCssClass:"flashing",selectedCellCssClass:"selected",multiSelect:!0,enableTextSelectionOnCells:!1,dataItemColumnValueExtractor:null,frozenBottom:!1,frozenColumn:-1,frozenRow:-1,frozenRightViewportMinWidth:100,fullWidthRows:!1,multiColumnSort:!1,numberedMultiColumnSort:!1,tristateMultiColumnSort:!1,sortColNumberInSeparateSpan:!1,defaultFormatter,forceSyncScrolling:!1,addNewRowCssClass:"new-row",preserveCopiedSelectionOnPaste:!1,showCellSelection:!0,viewportClass:null,minRowBuffer:3,emulatePagingWhenScrolling:!0,editorCellNavOnLRKeys:!1,enableMouseWheelScrollHandler:!0,doPaging:!0,autosizeColsMode:Slick.GridAutosizeColsMode.LegacyOff,autosizeColPaddingPx:4,autosizeTextAvgToMWidthRatio:.75,viewportSwitchToScrollModeWidthPercent:void 0,viewportMinWidthPx:void 0,viewportMaxWidthPx:void 0,suppressCssChangesOnHiddenInit:!1},columnDefaults={name:"",resizable:!0,sortable:!1,minWidth:30,maxWidth:void 0,rerenderOnResize:!1,headerCssClass:null,defaultSortAsc:!0,focusable:!0,selectable:!0},columnAutosizeDefaults={ignoreHeaderText:!1,colValueArray:void 0,allowAddlPercent:void 0,formatterOverride:void 0,autosizeMode:Slick.ColAutosizeMode.ContentIntelligent,rowSelectionModeOnInit:void 0,rowSelectionMode:Slick.RowSelectionMode.FirstNRows,rowSelectionCount:100,valueFilterMode:Slick.ValueFilterMode.None,widthEvalMode:Slick.WidthEvalMode.CanvasTextSize,sizeToRemaining:void 0,widthPx:void 0,colDataTypeOf:void 0},th,h,ph,n,cj,page=0,offset=0,vScrollDir=1,initialized=!1,$container,uid="slickgrid_"+Math.round(1e6*Math.random()),self=this,$focusSink,$focusSink2,$groupHeaders=$(),$headerScroller,$headers,$headerRow,$headerRowScroller,$headerRowSpacerL,$headerRowSpacerR,$footerRow,$footerRowScroller,$footerRowSpacerL,$footerRowSpacerR,$preHeaderPanel,$preHeaderPanelScroller,$preHeaderPanelSpacer,$preHeaderPanelR,$preHeaderPanelScrollerR,$preHeaderPanelSpacerR,$topPanelScroller,$topPanel,$viewport,$canvas,$style,$boundAncestors,treeColumns,stylesheet,columnCssRulesL,columnCssRulesR,viewportH,viewportW,canvasWidth,canvasWidthL,canvasWidthR,headersWidth,headersWidthL,headersWidthR,viewportHasHScroll,viewportHasVScroll,headerColumnWidthDiff=0,headerColumnHeightDiff=0,cellWidthDiff=0,cellHeightDiff=0,jQueryNewWidthBehaviour=!1,absoluteColumnMinWidth,hasFrozenRows=!1,frozenRowsHeight=0,actualFrozenRow=-1,paneTopH=0,paneBottomH=0,viewportTopH=0,viewportBottomH=0,topPanelH=0,headerRowH=0,footerRowH=0,tabbingDirection=1,$activeCanvasNode,$activeViewportNode,activePosX,activeRow,activeCell,activeCellNode=null,currentEditor=null,serializedEditorValue,editController,rowsCache={},renderedRows=0,numVisibleRows=0,prevScrollTop=0,scrollTop=0,lastRenderedScrollTop=0,lastRenderedScrollLeft=0,prevScrollLeft=0,scrollLeft=0,selectionModel,selectedRows=[],plugins=[],cellCssClasses={},columnsById={},sortColumns=[],columnPosLeft=[],columnPosRight=[],pagingActive=!1,pagingIsLastPage=!1,scrollThrottle=ActionThrottle(render,50),h_editorLoader=null,h_render=null,h_postrender=null,h_postrenderCleanup=null,postProcessedRows={},postProcessToRow=null,postProcessFromRow=null,postProcessedCleanupQueue=[],postProcessgroupId=0,counter_rows_rendered=0,counter_rows_removed=0,$paneHeaderL,$paneHeaderR,$paneTopL,$paneTopR,$paneBottomL,$paneBottomR,$headerScrollerL,$headerScrollerR,$headerL,$headerR,$groupHeadersL,$groupHeadersR,$headerRowScrollerL,$headerRowScrollerR,$footerRowScrollerL,$footerRowScrollerR,$headerRowL,$headerRowR,$footerRowL,$footerRowR,$topPanelScrollerL,$topPanelScrollerR,$topPanelL,$topPanelR,$viewportTopL,$viewportTopR,$viewportBottomL,$viewportBottomR,$canvasTopL,$canvasTopR,$canvasBottomL,$canvasBottomR,$viewportScrollContainerX,$viewportScrollContainerY,$headerScrollContainer,$headerRowScrollContainer,$footerRowScrollContainer,cssShow={position:"absolute",visibility:"hidden",display:"block"},$hiddenParents,oldProps=[],columnResizeDragging=!1;function init(){if(($container=container instanceof $?container:$(container)).length<1)throw new Error("SlickGrid requires a valid container, "+container+" does not exist in the DOM.");if(maxSupportedCssHeight=maxSupportedCssHeight||getMaxSupportedCssHeight(),options=$.extend({},defaults,options),validateAndEnforceOptions(),columnDefaults.width=options.defaultColumnWidth,options.suppressCssChangesOnHiddenInit||cacheCssForHiddenInit(),treeColumns=new Slick.TreeColumns(columns),columns=treeColumns.extractColumns(),updateColumnProps(),options.enableColumnReorder&&!$.fn.sortable)throw new Error("SlickGrid's 'enableColumnReorder = true' option requires jquery-ui.sortable module to be loaded");if(editController={commitCurrentEdit,cancelCurrentEdit},$container.empty().css("overflow","hidden").css("outline",0).addClass(uid).addClass("ui-widget"),/relative|absolute|fixed/.test($container.css("position"))||$container.css("position","relative"),$focusSink=$("<div tabIndex='0' hideFocus style='position:fixed;width:0;height:0;top:0;left:0;outline:0;'></div>").appendTo($container),$paneHeaderL=$("<div class='slick-pane slick-pane-header slick-pane-left' tabIndex='0' />").appendTo($container),$paneHeaderR=$("<div class='slick-pane slick-pane-header slick-pane-right' tabIndex='0' />").appendTo($container),$paneTopL=$("<div class='slick-pane slick-pane-top slick-pane-left' tabIndex='0' />").appendTo($container),$paneTopR=$("<div class='slick-pane slick-pane-top slick-pane-right' tabIndex='0' />").appendTo($container),$paneBottomL=$("<div class='slick-pane slick-pane-bottom slick-pane-left' tabIndex='0' />").appendTo($container),$paneBottomR=$("<div class='slick-pane slick-pane-bottom slick-pane-right' tabIndex='0' />").appendTo($container),options.createPreHeaderPanel&&($preHeaderPanelScroller=$("<div class='slick-preheader-panel ui-state-default' style='overflow:hidden;position:relative;' />").appendTo($paneHeaderL),$preHeaderPanel=$("<div />").appendTo($preHeaderPanelScroller),$preHeaderPanelSpacer=$("<div style='display:block;height:1px;position:absolute;top:0;left:0;'></div>").appendTo($preHeaderPanelScroller),$preHeaderPanelScrollerR=$("<div class='slick-preheader-panel ui-state-default' style='overflow:hidden;position:relative;' />").appendTo($paneHeaderR),$preHeaderPanelR=$("<div />").appendTo($preHeaderPanelScrollerR),$preHeaderPanelSpacerR=$("<div style='display:block;height:1px;position:absolute;top:0;left:0;'></div>").appendTo($preHeaderPanelScrollerR),options.showPreHeaderPanel||($preHeaderPanelScroller.hide(),$preHeaderPanelScrollerR.hide())),$headerScrollerL=$("<div class='slick-header ui-state-default slick-header-left' />").appendTo($paneHeaderL),$headerScrollerR=$("<div class='slick-header ui-state-default slick-header-right' />").appendTo($paneHeaderR),$headerScroller=$().add($headerScrollerL).add($headerScrollerR),treeColumns.hasDepth()){$groupHeadersL=[],$groupHeadersR=[];for(var e=0;e<treeColumns.getDepth()-1;e++)$groupHeadersL[e]=$("<div class='slick-group-header-columns slick-group-header-columns-left' style='left:-1000px' />").appendTo($headerScrollerL),$groupHeadersR[e]=$("<div class='slick-group-header-columns slick-group-header-columns-right' style='left:-1000px' />").appendTo($headerScrollerR);$groupHeaders=$().add($groupHeadersL).add($groupHeadersR)}$headerL=$("<div class='slick-header-columns slick-header-columns-left' style='left:-1000px' />").appendTo($headerScrollerL),$headerR=$("<div class='slick-header-columns slick-header-columns-right' style='left:-1000px' />").appendTo($headerScrollerR),$headers=$().add($headerL).add($headerR),$headerRowScrollerL=$("<div class='slick-headerrow ui-state-default' />").appendTo($paneTopL),$headerRowScrollerR=$("<div class='slick-headerrow ui-state-default' />").appendTo($paneTopR),$headerRowScroller=$().add($headerRowScrollerL).add($headerRowScrollerR),$headerRowSpacerL=$("<div style='display:block;height:1px;position:absolute;top:0;left:0;'></div>").appendTo($headerRowScrollerL),$headerRowSpacerR=$("<div style='display:block;height:1px;position:absolute;top:0;left:0;'></div>").appendTo($headerRowScrollerR),$headerRowL=$("<div class='slick-headerrow-columns slick-headerrow-columns-left' />").appendTo($headerRowScrollerL),$headerRowR=$("<div class='slick-headerrow-columns slick-headerrow-columns-right' />").appendTo($headerRowScrollerR),$headerRow=$().add($headerRowL).add($headerRowR),$topPanelScrollerL=$("<div class='slick-top-panel-scroller ui-state-default' />").appendTo($paneTopL),$topPanelScrollerR=$("<div class='slick-top-panel-scroller ui-state-default' />").appendTo($paneTopR),$topPanelScroller=$().add($topPanelScrollerL).add($topPanelScrollerR),$topPanelL=$("<div class='slick-top-panel' style='width:10000px' />").appendTo($topPanelScrollerL),$topPanelR=$("<div class='slick-top-panel' style='width:10000px' />").appendTo($topPanelScrollerR),$topPanel=$().add($topPanelL).add($topPanelR),options.showColumnHeader||$headerScroller.hide(),options.showTopPanel||$topPanelScroller.hide(),options.showHeaderRow||$headerRowScroller.hide(),$viewportTopL=$("<div class='slick-viewport slick-viewport-top slick-viewport-left' tabIndex='0' hideFocus />").appendTo($paneTopL),$viewportTopR=$("<div class='slick-viewport slick-viewport-top slick-viewport-right' tabIndex='0' hideFocus />").appendTo($paneTopR),$viewportBottomL=$("<div class='slick-viewport slick-viewport-bottom slick-viewport-left' tabIndex='0' hideFocus />").appendTo($paneBottomL),$viewportBottomR=$("<div class='slick-viewport slick-viewport-bottom slick-viewport-right' tabIndex='0' hideFocus />").appendTo($paneBottomR),$viewport=$().add($viewportTopL).add($viewportTopR).add($viewportBottomL).add($viewportBottomR),$activeViewportNode=$viewportTopL,$canvasTopL=$("<div class='grid-canvas grid-canvas-top grid-canvas-left' tabIndex='0' hideFocus />").appendTo($viewportTopL),$canvasTopR=$("<div class='grid-canvas grid-canvas-top grid-canvas-right' tabIndex='0' hideFocus />").appendTo($viewportTopR),$canvasBottomL=$("<div class='grid-canvas grid-canvas-bottom grid-canvas-left' tabIndex='0' hideFocus />").appendTo($viewportBottomL),$canvasBottomR=$("<div class='grid-canvas grid-canvas-bottom grid-canvas-right' tabIndex='0' hideFocus />").appendTo($viewportBottomR),options.viewportClass&&$viewport.toggleClass(options.viewportClass,!0),$canvas=$().add($canvasTopL).add($canvasTopR).add($canvasBottomL).add($canvasBottomR),scrollbarDimensions=scrollbarDimensions||measureScrollbar(),$activeCanvasNode=$canvasTopL,$preHeaderPanelSpacer&&$preHeaderPanelSpacer.css("width",getCanvasWidth()+scrollbarDimensions.width+"px"),$headers.width(getHeadersWidth()),$headerRowSpacerL.css("width",getCanvasWidth()+scrollbarDimensions.width+"px"),$headerRowSpacerR.css("width",getCanvasWidth()+scrollbarDimensions.width+"px"),options.createFooterRow&&($footerRowScrollerR=$("<div class='slick-footerrow ui-state-default' />").appendTo($paneTopR),$footerRowScrollerL=$("<div class='slick-footerrow ui-state-default' />").appendTo($paneTopL),$footerRowScroller=$().add($footerRowScrollerL).add($footerRowScrollerR),$footerRowSpacerL=$("<div style='display:block;height:1px;position:absolute;top:0;left:0;'></div>").css("width",getCanvasWidth()+scrollbarDimensions.width+"px").appendTo($footerRowScrollerL),$footerRowSpacerR=$("<div style='display:block;height:1px;position:absolute;top:0;left:0;'></div>").css("width",getCanvasWidth()+scrollbarDimensions.width+"px").appendTo($footerRowScrollerR),$footerRowL=$("<div class='slick-footerrow-columns slick-footerrow-columns-left' />").appendTo($footerRowScrollerL),$footerRowR=$("<div class='slick-footerrow-columns slick-footerrow-columns-right' />").appendTo($footerRowScrollerR),$footerRow=$().add($footerRowL).add($footerRowR),options.showFooterRow||$footerRowScroller.hide()),$focusSink2=$focusSink.clone().appendTo($container),options.explicitInitialization||finishInitialization()}function finishInitialization(){initialized||(initialized=!0,getViewportWidth(),getViewportHeight(),measureCellPaddingAndBorder(),disableSelection($headers),options.enableTextSelectionOnCells||$viewport.on("selectstart.ui",(function(e){return $(e.target).is("input,textarea")})),setFrozenOptions(),setPaneVisibility(),setScroller(),setOverflow(),updateColumnCaches(),createColumnHeaders(),createColumnGroupHeaders(),createColumnFooter(),setupColumnSort(),createCssRules(),resizeCanvas(),bindAncestorScrollEvents(),$container.on("resize.slickgrid",resizeCanvas),$viewport.on("scroll",handleScroll),jQuery.fn.mousewheel&&options.enableMouseWheelScrollHandler&&$viewport.on("mousewheel",handleMouseWheel),$headerScroller.on("contextmenu",handleHeaderContextMenu).on("click",handleHeaderClick).on("mouseenter",".slick-header-column",handleHeaderMouseEnter).on("mouseleave",".slick-header-column",handleHeaderMouseLeave),$headerRowScroller.on("scroll",handleHeaderRowScroll),options.createFooterRow&&($footerRow.on("contextmenu",handleFooterContextMenu).on("click",handleFooterClick),$footerRowScroller.on("scroll",handleFooterRowScroll)),options.createPreHeaderPanel&&$preHeaderPanelScroller.on("scroll",handlePreHeaderPanelScroll),$focusSink.add($focusSink2).on("keydown",handleKeyDown),$canvas.on("keydown",handleKeyDown).on("click",handleClick).on("dblclick",handleDblClick).on("contextmenu",handleContextMenu).on("draginit",handleDragInit).on("dragstart",{distance:3},handleDragStart).on("drag",handleDrag).on("dragend",handleDragEnd).on("mouseenter",".slick-cell",handleMouseEnter).on("mouseleave",".slick-cell",handleMouseLeave),options.suppressCssChangesOnHiddenInit||restoreCssFromHiddenInit())}function cacheCssForHiddenInit(){($hiddenParents=$container.parents().addBack().not(":visible")).each((function(){var e={};for(var o in cssShow)e[o]=this.style[o],this.style[o]=cssShow[o];oldProps.push(e)}))}function restoreCssFromHiddenInit(){$hiddenParents.each((function(e){var o=oldProps[e];for(var t in cssShow)this.style[t]=o[t]}))}function hasFrozenColumns(){return options.frozenColumn>-1}function registerPlugin(e){plugins.unshift(e),e.init(self)}function unregisterPlugin(e){for(var o=plugins.length;o>=0;o--)if(plugins[o]===e){plugins[o].destroy&&plugins[o].destroy(),plugins.splice(o,1);break}}function getPluginByName(e){for(var o=plugins.length-1;o>=0;o--)if(plugins[o].pluginName===e)return plugins[o]}function setSelectionModel(e){selectionModel&&(selectionModel.onSelectedRangesChanged.unsubscribe(handleSelectedRangesChanged),selectionModel.destroy&&selectionModel.destroy()),(selectionModel=e)&&(selectionModel.init(self),selectionModel.onSelectedRangesChanged.subscribe(handleSelectedRangesChanged))}function getSelectionModel(){return selectionModel}function getCanvasNode(e,o){return _getContainerElement(getCanvases(),e,o)}function getActiveCanvasNode(e){return setActiveCanvasNode(e),$activeCanvasNode[0]}function getCanvases(){return $canvas}function setActiveCanvasNode(e){e&&($activeCanvasNode=$(e.target).closest(".grid-canvas"))}function getViewportNode(e,o){return _getContainerElement(getViewports(),e,o)}function getViewports(){return $viewport}function getActiveViewportNode(e){return setActiveViewportNode(e),$activeViewportNode[0]}function setActiveViewportNode(e){e&&($activeViewportNode=$(e.target).closest(".slick-viewport"))}function _getContainerElement(e,o,t){if(e){o||(o=0),t||(t=0);var n="number"==typeof o?o:getColumnIndex(o);return e[(hasFrozenRows&&t>=actualFrozenRow+(options.frozenBottom?0:1)?2:0)+(hasFrozenColumns()&&n>options.frozenColumn?1:0)]}}function measureScrollbar(){var e=$('<div class="'+$viewport.className+'" style="position:absolute; top:-10000px; left:-10000px; overflow:auto; width:100px; height:100px;"></div>').appendTo("body"),o=$('<div style="width:200px; height:200px; overflow:auto;"></div>').appendTo(e),t={width:e[0].offsetWidth-e[0].clientWidth,height:e[0].offsetHeight-e[0].clientHeight};return o.remove(),e.remove(),t}function getHeadersWidth(){headersWidth=headersWidthL=headersWidthR=0;for(var e=!options.autoHeight,o=0,t=columns.length;o<t;o++){var n=columns[o].width;options.frozenColumn>-1&&o>options.frozenColumn?headersWidthR+=n:headersWidthL+=n}return e&&(options.frozenColumn>-1&&o>options.frozenColumn?headersWidthR+=scrollbarDimensions.width:headersWidthL+=scrollbarDimensions.width),hasFrozenColumns()?(headersWidthL+=1e3,headersWidthR=Math.max(headersWidthR,viewportW)+headersWidthL,headersWidthR+=scrollbarDimensions.width):(headersWidthL+=scrollbarDimensions.width,headersWidthL=Math.max(headersWidthL,viewportW)+1e3),headersWidth=headersWidthL+headersWidthR,Math.max(headersWidth,viewportW)+1e3}function getHeadersWidthL(){return headersWidthL=0,columns.forEach((function(e,o){options.frozenColumn>-1&&o>options.frozenColumn||(headersWidthL+=e.width)})),hasFrozenColumns()?headersWidthL+=1e3:(headersWidthL+=scrollbarDimensions.width,headersWidthL=Math.max(headersWidthL,viewportW)+1e3),headersWidthL}function getHeadersWidthR(){return headersWidthR=0,columns.forEach((function(e,o){options.frozenColumn>-1&&o>options.frozenColumn&&(headersWidthR+=e.width)})),hasFrozenColumns()&&(headersWidthR=Math.max(headersWidthR,viewportW)+getHeadersWidthL(),headersWidthR+=scrollbarDimensions.width),headersWidthR}function getCanvasWidth(){var e=viewportHasVScroll?viewportW-scrollbarDimensions.width:viewportW,o=columns.length;for(canvasWidthL=canvasWidthR=0;o--;)hasFrozenColumns()&&o>options.frozenColumn?canvasWidthR+=columns[o].width:canvasWidthL+=columns[o].width;var t=canvasWidthL+canvasWidthR;return options.fullWidthRows?Math.max(t,e):t}function updateCanvasWidth(e){var o,t=canvasWidth,n=canvasWidthL,l=canvasWidthR;((o=(canvasWidth=getCanvasWidth())!==t||canvasWidthL!==n||canvasWidthR!==l)||hasFrozenColumns()||hasFrozenRows)&&($canvasTopL.width(canvasWidthL),getHeadersWidth(),$headerL.width(headersWidthL),$headerR.width(headersWidthR),hasFrozenColumns()?($canvasTopR.width(canvasWidthR),$paneHeaderL.width(canvasWidthL),$paneHeaderR.css("left",canvasWidthL),$paneHeaderR.css("width",viewportW-canvasWidthL),$paneTopL.width(canvasWidthL),$paneTopR.css("left",canvasWidthL),$paneTopR.css("width",viewportW-canvasWidthL),$headerRowScrollerL.width(canvasWidthL),$headerRowScrollerR.width(viewportW-canvasWidthL),$headerRowL.width(canvasWidthL),$headerRowR.width(canvasWidthR),options.createFooterRow&&($footerRowScrollerL.width(canvasWidthL),$footerRowScrollerR.width(viewportW-canvasWidthL),$footerRowL.width(canvasWidthL),$footerRowR.width(canvasWidthR)),options.createPreHeaderPanel&&$preHeaderPanel.width(canvasWidth),$viewportTopL.width(canvasWidthL),$viewportTopR.width(viewportW-canvasWidthL),hasFrozenRows&&($paneBottomL.width(canvasWidthL),$paneBottomR.css("left",canvasWidthL),$viewportBottomL.width(canvasWidthL),$viewportBottomR.width(viewportW-canvasWidthL),$canvasBottomL.width(canvasWidthL),$canvasBottomR.width(canvasWidthR))):($paneHeaderL.width("100%"),$paneTopL.width("100%"),$headerRowScrollerL.width("100%"),$headerRowL.width(canvasWidth),options.createFooterRow&&($footerRowScrollerL.width("100%"),$footerRowL.width(canvasWidth)),options.createPreHeaderPanel&&($preHeaderPanel.width("100%"),$preHeaderPanel.width(canvasWidth)),$viewportTopL.width("100%"),hasFrozenRows&&($viewportBottomL.width("100%"),$canvasBottomL.width(canvasWidthL))),viewportHasHScroll=canvasWidth>=viewportW-scrollbarDimensions.width),$headerRowSpacerL.width(canvasWidth+(viewportHasVScroll?scrollbarDimensions.width:0)),$headerRowSpacerR.width(canvasWidth+(viewportHasVScroll?scrollbarDimensions.width:0)),options.createFooterRow&&($footerRowSpacerL.width(canvasWidth+(viewportHasVScroll?scrollbarDimensions.width:0)),$footerRowSpacerR.width(canvasWidth+(viewportHasVScroll?scrollbarDimensions.width:0))),(o||e)&&applyColumnWidths()}function disableSelection(e){e&&e.jquery&&e.attr("unselectable","on").css("MozUserSelect","none").on("selectstart.ui",(function(){return!1}))}function getMaxSupportedCssHeight(){for(var e=1e6,o=navigator.userAgent.toLowerCase().match(/firefox/)?6e6:1e9,t=$("<div style='display:none' />").appendTo(document.body);;){var n=2*e;if(t.css("height",n),n>o||t.height()!==n)break;e=n}return t.remove(),e}function getUID(){return uid}function getHeaderColumnWidthDiff(){return headerColumnWidthDiff}function getScrollbarDimensions(){return scrollbarDimensions}function bindAncestorScrollEvents(){for(var e=hasFrozenRows&&!options.frozenBottom?$canvasBottomL[0]:$canvasTopL[0];(e=e.parentNode)!=document.body&&null!=e;)if(e==$viewportTopL[0]||e.scrollWidth!=e.clientWidth||e.scrollHeight!=e.clientHeight){var o=$(e);$boundAncestors=$boundAncestors?$boundAncestors.add(o):o,o.on("scroll."+uid,handleActiveCellPositionChange)}}function unbindAncestorScrollEvents(){$boundAncestors&&($boundAncestors.off("scroll."+uid),$boundAncestors=null)}function updateColumnHeader(e,o,t){if(initialized){var n=getColumnIndex(e);if(null!=n){var l=columns[n],r=$headers.children().eq(n);r&&(void 0!==o&&(columns[n].name=o),void 0!==t&&(columns[n].toolTip=t),trigger(self.onBeforeHeaderCellDestroy,{node:r[0],column:l,grid:self}),r.attr("title",t||"").children().eq(0).html(o),trigger(self.onHeaderCellRendered,{node:r[0],column:l,grid:self}))}}}function getHeader(e){if(!e)return hasFrozenColumns()?$headers:$headerL;var o=getColumnIndex(e.id);return hasFrozenColumns()?o<=options.frozenColumn?$headerL:$headerR:$headerL}function getHeaderColumn(e){var o="number"==typeof e?e:getColumnIndex(e),t=hasFrozenColumns()?o<=options.frozenColumn?$headerL:$headerR:$headerL,n=hasFrozenColumns()?o<=options.frozenColumn?o:o-options.frozenColumn-1:o,l=t.children().eq(n);return l&&l[0]}function getHeaderRow(){return hasFrozenColumns()?$headerRow:$headerRow[0]}function getFooterRow(){return hasFrozenColumns()?$footerRow:$footerRow[0]}function getPreHeaderPanel(){return $preHeaderPanel[0]}function getPreHeaderPanelRight(){return $preHeaderPanelR[0]}function getHeaderRowColumn(e){var o,t="number"==typeof e?e:getColumnIndex(e);hasFrozenColumns()?t<=options.frozenColumn?o=$headerRowL:(o=$headerRowR,t-=options.frozenColumn+1):o=$headerRowL;var n=o.children().eq(t);return n&&n[0]}function getFooterRowColumn(e){var o,t="number"==typeof e?e:getColumnIndex(e);hasFrozenColumns()?t<=options.frozenColumn?o=$footerRowL:(o=$footerRowR,t-=options.frozenColumn+1):o=$footerRowL;var n=o&&o.children().eq(t);return n&&n[0]}function createColumnFooter(){if(options.createFooterRow){$footerRow.find(".slick-footerrow-column").each((function(){var e=$(this).data("column");e&&trigger(self.onBeforeFooterRowCellDestroy,{node:this,column:e,grid:self})})),$footerRowL.empty(),$footerRowR.empty();for(var e=0;e<columns.length;e++){var o=columns[e],t=$("<div class='ui-state-default slick-footerrow-column l"+e+" r"+e+"'></div>").data("column",o).addClass(hasFrozenColumns()&&e<=options.frozenColumn?"frozen":"").appendTo(hasFrozenColumns()&&e>options.frozenColumn?$footerRowR:$footerRowL);trigger(self.onFooterRowCellRendered,{node:t[0],column:o,grid:self})}}}function createColumnGroupHeaders(){var e=0,o=!1;if(treeColumns.hasDepth()){for(var t=0;t<$groupHeadersL.length;t++){$groupHeadersL[t].empty(),$groupHeadersR[t].empty();var n=treeColumns.getColumnsInDepth(t);for(var l in n){var r=n[l];e+=r.extractColumns().length,hasFrozenColumns()&&0===t&&e-1===options.frozenColumn&&(o=!0),$("<div class='ui-state-default slick-group-header-column' />").html("<span class='slick-column-name'>"+r.name+"</span>").attr("id",""+uid+r.id).attr("title",r.toolTip||"").data("column",r).addClass(r.headerCssClass||"").addClass(hasFrozenColumns()&&e-1>options.frozenColumn?"frozen":"").appendTo(hasFrozenColumns()&&e-1>options.frozenColumn?$groupHeadersR[t]:$groupHeadersL[t])}if(hasFrozenColumns()&&0===t&&!o){$groupHeadersL[t].empty(),$groupHeadersR[t].empty(),alert("All columns of group should to be grouped!");break}}applyColumnGroupHeaderWidths()}}function createColumnHeaders(){function e(){$(this).addClass("ui-state-hover")}function o(){$(this).removeClass("ui-state-hover")}$headers.find(".slick-header-column").each((function(){var e=$(this).data("column");e&&trigger(self.onBeforeHeaderCellDestroy,{node:this,column:e,grid:self})})),$headerL.empty(),$headerR.empty(),getHeadersWidth(),$headerL.width(headersWidthL),$headerR.width(headersWidthR),$headerRow.find(".slick-headerrow-column").each((function(){var e=$(this).data("column");e&&trigger(self.onBeforeHeaderRowCellDestroy,{node:this,column:e,grid:self})})),$headerRowL.empty(),$headerRowR.empty(),options.createFooterRow&&($footerRowL.find(".slick-footerrow-column").each((function(){var e=$(this).data("column");e&&trigger(self.onBeforeFooterRowCellDestroy,{node:this,column:e,grid:self})})),$footerRowL.empty(),hasFrozenColumns()&&($footerRowR.find(".slick-footerrow-column").each((function(){var e=$(this).data("column");e&&trigger(self.onBeforeFooterRowCellDestroy,{node:this,column:e,grid:self})})),$footerRowR.empty()));for(var t=0;t<columns.length;t++){var n=columns[t],l=hasFrozenColumns()?t<=options.frozenColumn?$headerL:$headerR:$headerL,r=hasFrozenColumns()?t<=options.frozenColumn?$headerRowL:$headerRowR:$headerRowL,i=$("<div class='ui-state-default slick-header-column' />").html("<span class='slick-column-name'>"+n.name+"</span>").width(n.width-headerColumnWidthDiff).attr("id",""+uid+n.id).attr("title",n.toolTip||"").data("column",n).addClass(n.headerCssClass||"").addClass(hasFrozenColumns()&&t<=options.frozenColumn?"frozen":"").appendTo(l);if((options.enableColumnReorder||n.sortable)&&i.on("mouseenter",e).on("mouseleave",o),n.hasOwnProperty("headerCellAttrs")&&n.headerCellAttrs instanceof Object)for(var a in n.headerCellAttrs)n.headerCellAttrs.hasOwnProperty(a)&&i.attr(a,n.headerCellAttrs[a]);if(n.sortable&&(i.addClass("slick-header-sortable"),i.append("<span class='slick-sort-indicator"+(options.numberedMultiColumnSort&&!options.sortColNumberInSeparateSpan?" slick-sort-indicator-numbered":"")+"' />"),options.numberedMultiColumnSort&&options.sortColNumberInSeparateSpan&&i.append("<span class='slick-sort-indicator-numbered' />")),trigger(self.onHeaderCellRendered,{node:i[0],column:n,grid:self}),options.showHeaderRow){var s=$("<div class='ui-state-default slick-headerrow-column l"+t+" r"+t+"'></div>").data("column",n).addClass(hasFrozenColumns()&&t<=options.frozenColumn?"frozen":"").appendTo(r);trigger(self.onHeaderRowCellRendered,{node:s[0],column:n,grid:self})}if(options.createFooterRow&&options.showFooterRow){var d=$("<div class='ui-state-default slick-footerrow-column l"+t+" r"+t+"'></div>").data("column",n).appendTo($footerRow);trigger(self.onFooterRowCellRendered,{node:d[0],column:n,grid:self})}}setSortColumns(sortColumns),setupColumnResize(),options.enableColumnReorder&&("function"==typeof options.enableColumnReorder?options.enableColumnReorder(self,$headers,headerColumnWidthDiff,setColumns,setupColumnResize,columns,getColumnIndex,uid,trigger):setupColumnReorder())}function setupColumnSort(){$headers.click((function(e){if(!columnResizeDragging&&(e.metaKey=e.metaKey||e.ctrlKey,!$(e.target).hasClass("slick-resizable-handle"))){var o=$(e.target).closest(".slick-header-column");if(o.length){var t=o.data("column");if(t.sortable){if(!getEditorLock().commitCurrentEdit())return;for(var n=$.extend(!0,[],sortColumns),l=null,r=0;r<sortColumns.length;r++)if(sortColumns[r].columnId==t.id){(l=sortColumns[r]).sortAsc=!l.sortAsc;break}var i,a=!!l;options.tristateMultiColumnSort?(l||(l={columnId:t.id,sortAsc:t.defaultSortAsc}),a&&l.sortAsc&&(sortColumns.splice(r,1),l=null),options.multiColumnSort||(sortColumns=[]),!l||a&&options.multiColumnSort||sortColumns.push(l)):e.metaKey&&options.multiColumnSort?l&&sortColumns.splice(r,1):((e.shiftKey||e.metaKey)&&options.multiColumnSort||(sortColumns=[]),l?0===sortColumns.length&&sortColumns.push(l):(l={columnId:t.id,sortAsc:t.defaultSortAsc},sortColumns.push(l))),i=options.multiColumnSort?{multiColumnSort:!0,previousSortColumns:n,sortCols:$.map(sortColumns,(function(e){return{columnId:columns[getColumnIndex(e.columnId)].id,sortCol:columns[getColumnIndex(e.columnId)],sortAsc:e.sortAsc}}))}:{multiColumnSort:!1,previousSortColumns:n,columnId:sortColumns.length>0?t.id:null,sortCol:sortColumns.length>0?t:null,sortAsc:!(sortColumns.length>0)||sortColumns[0].sortAsc},!1!==trigger(self.onBeforeSort,i,e)&&(setSortColumns(sortColumns),trigger(self.onSort,i,e))}}}}))}function currentPositionInHeader(e){var o=0;return $headers.find(".slick-header-column").each((function(t){if(this.id==e)return o=t,!1})),o}function limitPositionInGroup(e){var o,t=0,n=0;return treeColumns.getColumnsInDepth($groupHeadersL.length-1).some((function(l){return t=n,n+=l.columns.length,l.columns.some((function(t){return t.id===e&&(o=l),o})),o})),n--,{start:t,end:n,group:o}}function remove(e,o){var t=e.lastIndexOf(o);t>-1&&(e.splice(t,1),remove(e,o))}function columnPositionValidInGroup(e){var o=currentPositionInHeader(e[0].id),t=limitPositionInGroup(e.data("column").id),n=t.start<=o&&o<=t.end;return{limit:t,valid:n,message:n?"":'Column "'.concat(e.text(),'" can be reordered only within the "',t.group.name,'" group!')}}function setupColumnReorder(){$headers.filter(":ui-sortable").sortable("destroy");var e,o=null;function t(){$viewportScrollContainerX[0].scrollLeft=$viewportScrollContainerX[0].scrollLeft+10}function n(){$viewportScrollContainerX[0].scrollLeft=$viewportScrollContainerX[0].scrollLeft-10}$headers.sortable({containment:"parent",distance:3,axis:"x",cursor:"default",tolerance:"intersection",helper:"clone",placeholder:"slick-sortable-placeholder ui-state-default slick-header-column",start:function(o,t){t.placeholder.width(t.helper.outerWidth()-headerColumnWidthDiff),e=!hasFrozenColumns()||t.placeholder.offset().left+t.placeholder.width()>$viewportScrollContainerX.offset().left,$(t.helper).addClass("slick-header-column-active")},beforeStop:function(e,o){$(o.helper).removeClass("slick-header-column-active")},sort:function(l,r){e&&l.originalEvent.pageX>$container[0].clientWidth?o||(o=setInterval(t,100)):e&&l.originalEvent.pageX<$viewportScrollContainerX.offset().left?o||(o=setInterval(n,100)):(clearInterval(o),o=null)},stop:function(e,t){var n=!1;clearInterval(o),o=null;var l=null;if(treeColumns.hasDepth()){var r=columnPositionValidInGroup(t.item);l=r.limit,(n=!r.valid)&&alert(r.message)}if(!n&&getEditorLock().commitCurrentEdit()){var i=$headerL.sortable("toArray");i=i.concat($headerR.sortable("toArray"));for(var a=[],s=0;s<i.length;s++)a.push(columns[getColumnIndex(i[s].replace(uid,""))]);setColumns(a),trigger(self.onColumnsReordered,{impactedColumns:getImpactedColumns(l)}),e.stopPropagation(),setupColumnResize()}else $(this).sortable("cancel")}})}function getImpactedColumns(e){var o=[];if(e)for(var t=e.start;t<=e.end;t++)o.push(columns[t]);else o=columns;return o}function setupColumnResize(){var e,o,t,n,l,r,i,a,s,d=0;(l=$headers.children()).find(".slick-resizable-handle").remove(),l.each((function(e,o){e>=columns.length||columns[e].resizable&&(void 0===a&&(a=e),s=e)})),void 0!==a&&l.each((function(c,u){c>=columns.length||c<a||options.forceFitColumns&&c>=s||($(u),$("<div class='slick-resizable-handle' />").appendTo(u).on("dragstart",(function(o,a){if(!getEditorLock().commitCurrentEdit())return!1;n=o.pageX,d=0,$(this).parent().addClass("slick-header-column-active");var s=null,u=null;if(l.each((function(e,o){e>=columns.length||(columns[e].previousWidth=$(o).outerWidth())})),options.forceFitColumns)for(s=0,u=0,e=c+1;e<columns.length;e++)(t=columns[e]).resizable&&(null!==u&&(t.maxWidth?u+=t.maxWidth-t.previousWidth:u=null),s+=t.previousWidth-Math.max(t.minWidth||0,absoluteColumnMinWidth));var h=0,p=0;for(e=0;e<=c;e++)(t=columns[e]).resizable&&(null!==p&&(t.maxWidth?p+=t.maxWidth-t.previousWidth:p=null),h+=t.previousWidth-Math.max(t.minWidth||0,absoluteColumnMinWidth));null===s&&(s=1e5),null===h&&(h=1e5),null===u&&(u=1e5),null===p&&(p=1e5),i=n+Math.min(s,p),r=n-Math.min(h,u)})).on("drag",(function(l,a){columnResizeDragging=!0;var s,u,h=Math.min(i,Math.max(r,l.pageX))-n,p=0,w=viewportHasVScroll?viewportW-scrollbarDimensions.width:viewportW;if(h<0){for(u=h,e=c;e>=0;e--)(t=columns[e]).resizable&&(s=Math.max(t.minWidth||0,absoluteColumnMinWidth),u&&t.previousWidth+u<s?(u+=t.previousWidth-s,t.width=s):(t.width=t.previousWidth+u,u=0));for(o=0;o<=c;o++)t=columns[o],hasFrozenColumns()&&o>options.frozenColumn?t.width:p+=t.width;if(options.forceFitColumns)for(u=-h,e=c+1;e<columns.length;e++)(t=columns[e]).resizable&&(u&&t.maxWidth&&t.maxWidth-t.previousWidth<u?(u-=t.maxWidth-t.previousWidth,t.width=t.maxWidth):(t.width=t.previousWidth+u,u=0),hasFrozenColumns()&&e>options.frozenColumn?t.width:p+=t.width);else for(e=c+1;e<columns.length;e++)t=columns[e],hasFrozenColumns()&&e>options.frozenColumn?t.width:p+=t.width;if(options.forceFitColumns)for(u=-h,e=c+1;e<columns.length;e++)(t=columns[e]).resizable&&(u&&t.maxWidth&&t.maxWidth-t.previousWidth<u?(u-=t.maxWidth-t.previousWidth,t.width=t.maxWidth):(t.width=t.previousWidth+u,u=0))}else{for(u=h,p=0,e=c;e>=0;e--)if((t=columns[e]).resizable)if(u&&t.maxWidth&&t.maxWidth-t.previousWidth<u)u-=t.maxWidth-t.previousWidth,t.width=t.maxWidth;else{var f=t.previousWidth+u,m=canvasWidthL+u;hasFrozenColumns()&&e<=options.frozenColumn?(f>d&&m<w-options.frozenRightViewportMinWidth&&(d=f),t.width=m+options.frozenRightViewportMinWidth>w?d:f):t.width=f,u=0}for(o=0;o<=c;o++)t=columns[o],hasFrozenColumns()&&o>options.frozenColumn?t.width:p+=t.width;if(options.forceFitColumns)for(u=-h,e=c+1;e<columns.length;e++)(t=columns[e]).resizable&&(s=Math.max(t.minWidth||0,absoluteColumnMinWidth),u&&t.previousWidth+u<s?(u+=t.previousWidth-s,t.width=s):(t.width=t.previousWidth+u,u=0),hasFrozenColumns()&&e>options.frozenColumn?t.width:p+=t.width);else for(e=c+1;e<columns.length;e++)t=columns[e],hasFrozenColumns()&&e>options.frozenColumn?t.width:p+=t.width}hasFrozenColumns()&&p!=canvasWidthL&&($headerL.width(p+1e3),$paneHeaderR.css("left",p)),applyColumnHeaderWidths(),applyColumnGroupHeaderWidths(),options.syncColumnCellResize&&applyColumnWidths(),trigger(self.onColumnsDrag,{triggeredByColumn:$(this).parent().attr("id").replace(uid,""),resizeHandle:$(this)})})).on("dragend",(function(o,n){$(this).parent().removeClass("slick-header-column-active");var r,i=$(this).parent().attr("id").replace(uid,"");for(!0===trigger(self.onBeforeColumnsResize,{triggeredByColumn:i})&&(applyColumnHeaderWidths(),applyColumnGroupHeaderWidths()),e=0;e<columns.length;e++)t=columns[e],r=$(l[e]).outerWidth(),t.previousWidth!==r&&t.rerenderOnResize&&invalidateAllRows();updateCanvasWidth(!0),render(),trigger(self.onColumnsResized,{triggeredByColumn:i}),setTimeout((function(){columnResizeDragging=!1}),300)})).on("dblclick",(function(){var e=$(this).parent().attr("id").replace(uid,"");trigger(self.onColumnsResizeDblClick,{triggeredByColumn:e})})))}))}function getVBoxDelta(e){var o=0;return e&&"function"==typeof e.css&&$.each(["borderTopWidth","borderBottomWidth","paddingTop","paddingBottom"],(function(t,n){o+=parseFloat(e.css(n))||0})),o}function setFrozenOptions(){if(options.frozenColumn=options.frozenColumn>=0&&options.frozenColumn<columns.length?parseInt(options.frozenColumn):-1,options.frozenRow>-1){hasFrozenRows=!0,frozenRowsHeight=options.frozenRow*options.rowHeight;var e=getDataLength();actualFrozenRow=options.frozenBottom?e-options.frozenRow:options.frozenRow}else hasFrozenRows=!1}function setPaneVisibility(){hasFrozenColumns()?($paneHeaderR.show(),$paneTopR.show(),hasFrozenRows?($paneBottomL.show(),$paneBottomR.show()):($paneBottomR.hide(),$paneBottomL.hide())):($paneHeaderR.hide(),$paneTopR.hide(),$paneBottomR.hide(),hasFrozenRows?$paneBottomL.show():($paneBottomR.hide(),$paneBottomL.hide()))}function setOverflow(){$viewportTopL.css({"overflow-x":hasFrozenColumns()?hasFrozenRows&&!options.alwaysAllowHorizontalScroll?"hidden":"scroll":hasFrozenRows&&!options.alwaysAllowHorizontalScroll?"hidden":"auto","overflow-y":!hasFrozenColumns()&&options.alwaysShowVerticalScroll?"scroll":hasFrozenColumns()?"hidden":hasFrozenRows?"scroll":"auto"}),$viewportTopR.css({"overflow-x":hasFrozenColumns()?hasFrozenRows&&!options.alwaysAllowHorizontalScroll?"hidden":"scroll":hasFrozenRows&&!options.alwaysAllowHorizontalScroll?"hidden":"auto","overflow-y":options.alwaysShowVerticalScroll?"scroll":(hasFrozenColumns(),hasFrozenRows?"scroll":"auto")}),$viewportBottomL.css({"overflow-x":hasFrozenColumns()?hasFrozenRows&&!options.alwaysAllowHorizontalScroll?"scroll":"auto":(hasFrozenRows&&options.alwaysAllowHorizontalScroll,"auto"),"overflow-y":!hasFrozenColumns()&&options.alwaysShowVerticalScroll?"scroll":hasFrozenColumns()?"hidden":hasFrozenRows?"scroll":"auto"}),$viewportBottomR.css({"overflow-x":hasFrozenColumns()?hasFrozenRows&&!options.alwaysAllowHorizontalScroll?"scroll":"auto":(hasFrozenRows&&options.alwaysAllowHorizontalScroll,"auto"),"overflow-y":options.alwaysShowVerticalScroll?"scroll":(hasFrozenColumns(),"auto")}),options.viewportClass&&($viewportTopL.toggleClass(options.viewportClass,!0),$viewportTopR.toggleClass(options.viewportClass,!0),$viewportBottomL.toggleClass(options.viewportClass,!0),$viewportBottomR.toggleClass(options.viewportClass,!0))}function setScroller(){hasFrozenColumns()?($headerScrollContainer=$headerScrollerR,$headerRowScrollContainer=$headerRowScrollerR,$footerRowScrollContainer=$footerRowScrollerR,hasFrozenRows?options.frozenBottom?($viewportScrollContainerX=$viewportBottomR,$viewportScrollContainerY=$viewportTopR):$viewportScrollContainerX=$viewportScrollContainerY=$viewportBottomR:$viewportScrollContainerX=$viewportScrollContainerY=$viewportTopR):($headerScrollContainer=$headerScrollerL,$headerRowScrollContainer=$headerRowScrollerL,$footerRowScrollContainer=$footerRowScrollerL,hasFrozenRows?options.frozenBottom?($viewportScrollContainerX=$viewportBottomL,$viewportScrollContainerY=$viewportTopL):$viewportScrollContainerX=$viewportScrollContainerY=$viewportBottomL:$viewportScrollContainerX=$viewportScrollContainerY=$viewportTopL)}function measureCellPaddingAndBorder(){var e,o=["borderLeftWidth","borderRightWidth","paddingLeft","paddingRight"],t=["borderTopWidth","borderBottomWidth","paddingTop","paddingBottom"],n=$.fn.jquery.split(".");jQueryNewWidthBehaviour=1==n[0]&&n[1]>=8||n[0]>=2,e=$("<div class='ui-state-default slick-header-column' style='visibility:hidden'>-</div>").appendTo($headers),headerColumnWidthDiff=headerColumnHeightDiff=0,"border-box"!=e.css("box-sizing")&&"border-box"!=e.css("-moz-box-sizing")&&"border-box"!=e.css("-webkit-box-sizing")&&($.each(o,(function(o,t){headerColumnWidthDiff+=parseFloat(e.css(t))||0})),$.each(t,(function(o,t){headerColumnHeightDiff+=parseFloat(e.css(t))||0}))),e.remove();var l=$("<div class='slick-row' />").appendTo($canvas);e=$("<div class='slick-cell' id='' style='visibility:hidden'>-</div>").appendTo(l),cellWidthDiff=cellHeightDiff=0,"border-box"!=e.css("box-sizing")&&"border-box"!=e.css("-moz-box-sizing")&&"border-box"!=e.css("-webkit-box-sizing")&&($.each(o,(function(o,t){cellWidthDiff+=parseFloat(e.css(t))||0})),$.each(t,(function(o,t){cellHeightDiff+=parseFloat(e.css(t))||0}))),l.remove(),absoluteColumnMinWidth=Math.max(headerColumnWidthDiff,cellWidthDiff)}function createCssRules(){$style=$("<style type='text/css' rel='stylesheet' />"),$container[0].parentNode instanceof ShadowRoot?$container[0].parentNode.insertBefore($style[0],$container[0]):$style.appendTo($("head"));for(var e=options.rowHeight-cellHeightDiff,o=["."+uid+" .slick-group-header-column { left: 1000px; }","."+uid+" .slick-header-column { left: 1000px; }","."+uid+" .slick-top-panel { height:"+options.topPanelHeight+"px; }","."+uid+" .slick-preheader-panel { height:"+options.preHeaderPanelHeight+"px; }","."+uid+" .slick-headerrow-columns { height:"+options.headerRowHeight+"px; }","."+uid+" .slick-footerrow-columns { height:"+options.footerRowHeight+"px; }","."+uid+" .slick-cell { height:"+e+"px; }","."+uid+" .slick-row { height:"+options.rowHeight+"px; }"],t=0;t<columns.length;t++)o.push("."+uid+" .l"+t+" { }"),o.push("."+uid+" .r"+t+" { }");$style[0].styleSheet?$style[0].styleSheet.cssText=o.join(" "):$style[0].appendChild(document.createTextNode(o.join(" ")))}function getColumnCssRules(e){var o;if(!stylesheet){var t;for(t=$container[0].parentNode instanceof ShadowRoot?$container[0].parentNode.styleSheets:document.styleSheets,o=0;o<t.length;o++)if((t[o].ownerNode||t[o].owningElement)==$style[0]){stylesheet=t[o];break}if(!stylesheet)throw new Error("SlickGrid Cannot find stylesheet.");columnCssRulesL=[],columnCssRulesR=[];var n,l,r=stylesheet.cssRules||stylesheet.rules;for(o=0;o<r.length;o++){var i=r[o].selectorText;(n=/\.l\d+/.exec(i))?(l=parseInt(n[0].substr(2,n[0].length-2),10),columnCssRulesL[l]=r[o]):(n=/\.r\d+/.exec(i))&&(l=parseInt(n[0].substr(2,n[0].length-2),10),columnCssRulesR[l]=r[o])}}return{left:columnCssRulesL[e],right:columnCssRulesR[e]}}function removeCssRules(){$style.remove(),stylesheet=null}function destroy(e){getEditorLock().cancelCurrentEdit(),trigger(self.onBeforeDestroy,{});for(var o=plugins.length;o--;)unregisterPlugin(plugins[o]);options.enableColumnReorder&&$headers.filter(":ui-sortable").sortable("destroy"),unbindAncestorScrollEvents(),$container.off(".slickgrid"),removeCssRules(),$canvas.off(),$viewport.off(),$headerScroller.off(),$headerRowScroller.off(),$footerRow&&$footerRow.off(),$footerRowScroller&&$footerRowScroller.off(),$preHeaderPanelScroller&&$preHeaderPanelScroller.off(),$focusSink.off(),$(".slick-resizable-handle").off(),$(".slick-header-column").off(),$container.empty().removeClass(uid),e&&destroyAllElements()}function destroyAllElements(){$activeCanvasNode=null,$activeViewportNode=null,$boundAncestors=null,$canvas=null,$canvasTopL=null,$canvasTopR=null,$canvasBottomL=null,$canvasBottomR=null,$container=null,$focusSink=null,$focusSink2=null,$groupHeaders=null,$groupHeadersL=null,$groupHeadersR=null,$headerL=null,$headerR=null,$headers=null,$headerRow=null,$headerRowL=null,$headerRowR=null,$headerRowSpacerL=null,$headerRowSpacerR=null,$headerRowScrollContainer=null,$headerRowScroller=null,$headerRowScrollerL=null,$headerRowScrollerR=null,$headerScrollContainer=null,$headerScroller=null,$headerScrollerL=null,$headerScrollerR=null,$hiddenParents=null,$footerRow=null,$footerRowL=null,$footerRowR=null,$footerRowSpacerL=null,$footerRowSpacerR=null,$footerRowScroller=null,$footerRowScrollerL=null,$footerRowScrollerR=null,$footerRowScrollContainer=null,$preHeaderPanel=null,$preHeaderPanelR=null,$preHeaderPanelScroller=null,$preHeaderPanelScrollerR=null,$preHeaderPanelSpacer=null,$preHeaderPanelSpacerR=null,$topPanel=null,$topPanelScroller=null,$style=null,$topPanelScrollerL=null,$topPanelScrollerR=null,$topPanelL=null,$topPanelR=null,$paneHeaderL=null,$paneHeaderR=null,$paneTopL=null,$paneTopR=null,$paneBottomL=null,$paneBottomR=null,$viewport=null,$viewportTopL=null,$viewportTopR=null,$viewportBottomL=null,$viewportBottomR=null,$viewportScrollContainerX=null,$viewportScrollContainerY=null}var canvas=null,canvas_context=null;function autosizeColumn(e,o){var t=e;if("number"==typeof e)t=columns[e];else if("string"==typeof e)for(var n=0;n<columns.length;n++)columns[n].Id===e&&(t=columns[n]);getColAutosizeWidth(t,$(getCanvasNode(0,0)),o)}function autosizeColumns(e,o){if((e=e||options.autosizeColsMode)!==Slick.GridAutosizeColsMode.LegacyForceFit&&e!==Slick.GridAutosizeColsMode.LegacyOff){if(e!==Slick.GridAutosizeColsMode.None){(canvas=document.createElement("canvas"))&&canvas.getContext&&(canvas_context=canvas.getContext("2d"));var t,n,l,r,i=$(getCanvasNode(0,0)),a=viewportHasVScroll?viewportW-scrollbarDimensions.width:viewportW,s=0,d=0,c=0,u=0,h=0;for(t=0;t<columns.length;t++)getColAutosizeWidth(n=columns[t],i,o),h+=n.autoSize.autosizeMode===Slick.ColAutosizeMode.Locked?n.width:0,u+=n.autoSize.autosizeMode===Slick.ColAutosizeMode.Locked?n.width:n.minWidth,s+=n.autoSize.widthPx,d+=n.autoSize.sizeToRemaining?0:n.autoSize.widthPx,c+=n.autoSize.sizeToRemaining&&n.minWidth||0;var p=s-d;if(e===Slick.GridAutosizeColsMode.FitViewportToCols){var w=s+scrollbarDimensions.width;e=Slick.GridAutosizeColsMode.IgnoreViewport,options.viewportMaxWidthPx&&w>options.viewportMaxWidthPx?(w=options.viewportMaxWidthPx,e=Slick.GridAutosizeColsMode.FitColsToViewport):options.viewportMinWidthPx&&w<options.viewportMinWidthPx&&(w=options.viewportMinWidthPx,e=Slick.GridAutosizeColsMode.FitColsToViewport),$container.width(w)}if(e===Slick.GridAutosizeColsMode.FitColsToViewport)if(p>0&&d<a-c)for(t=0;t<columns.length;t++){var f=a-d;l=(n=columns[t]).autoSize.sizeToRemaining?f*n.autoSize.widthPx/p:n.autoSize.widthPx,n.rerenderOnResize&&n.width!=l&&(r=!0),n.width=l}else if(options.viewportSwitchToScrollModeWidthPercent&&d+c>a*options.viewportSwitchToScrollModeWidthPercent/100||u>a)e=Slick.GridAutosizeColsMode.IgnoreViewport;else{var m=d-h,v=a-h-c;for(t=0;t<columns.length;t++)l=(n=columns[t]).width,n.autoSize.autosizeMode!==Slick.ColAutosizeMode.Locked&&(n.autoSize.sizeToRemaining?l=n.minWidth:((l=v/m*n.autoSize.widthPx)<n.minWidth&&(l=n.minWidth),m-=n.autoSize.widthPx,v-=l)),n.rerenderOnResize&&n.width!=l&&(r=!0),n.width=l}if(e===Slick.GridAutosizeColsMode.IgnoreViewport)for(t=0;t<columns.length;t++)l=columns[t].autoSize.widthPx,columns[t].rerenderOnResize&&columns[t].width!=l&&(r=!0),columns[t].width=l;reRenderColumns(r)}}else legacyAutosizeColumns()}function LogColWidths(){for(var e="Col Widths:",o=0;o<columns.length;o++)e+=" "+columns[o].width;console.log(e)}function getColAutosizeWidth(e,o,t){var n=e.autoSize;if(n.widthPx=e.width,n.autosizeMode!==Slick.ColAutosizeMode.Locked&&n.autosizeMode!==Slick.ColAutosizeMode.Guide){var l=getDataLength();if(n.autosizeMode===Slick.ColAutosizeMode.ContentIntelligent){var r,i=n.colDataTypeOf;if(l>0){var a=getDataItem(0);a&&"object"===(i=typeof(r=a[e.field]))&&(r instanceof Date&&(i="date"),"undefined"!=typeof moment&&r instanceof moment&&(i="moment"))}"boolean"===i&&(n.colValueArray=[!0,!1]),"number"===i&&(n.valueFilterMode=Slick.ValueFilterMode.GetGreatestAndSub,n.rowSelectionMode=Slick.RowSelectionMode.AllRows),"string"===i&&(n.valueFilterMode=Slick.ValueFilterMode.GetLongestText,n.rowSelectionMode=Slick.RowSelectionMode.AllRows,n.allowAddlPercent=5),"date"===i&&(n.colValueArray=[new Date(2009,8,30,12,20,20)]),"moment"===i&&"undefined"!=typeof moment&&(n.colValueArray=[moment([2009,8,30,12,20,20])])}var s=getColContentSize(e,o,t);s=s*(n.allowAddlPercent?1+n.allowAddlPercent/100:1)+options.autosizeColPaddingPx,e.minWidth&&s<e.minWidth&&(s=e.minWidth),e.maxWidth&&s>e.maxWidth&&(s=e.maxWidth),n.widthPx=s}}function getColContentSize(e,o,t){var n,l=e.autoSize,r=1,i=0,a=0;if(l.ignoreHeaderText||(a=getColHeaderWidth(e)),l.colValueArray)return i=getColWidth(e,o,l.colValueArray),Math.max(a,i);var s=getData();s.getItems&&(s=s.getItems());var d=(t?l.rowSelectionModeOnInit:void 0)||l.rowSelectionMode;if(d===Slick.RowSelectionMode.FirstRow&&(s=s.slice(0,1)),d===Slick.RowSelectionMode.LastRow&&(s=s.slice(s.length-1,s.length)),d===Slick.RowSelectionMode.FirstNRows&&(s=s.slice(0,l.rowSelectionCount)),l.valueFilterMode===Slick.ValueFilterMode.DeDuplicate){var c={};for(u=0,n=s.length;u<n;u++)c[s[u][e.field]]=!0;if(Object.keys)s=Object.keys(c);else for(var u in s=[],c)s.push(u)}if(l.valueFilterMode===Slick.ValueFilterMode.GetGreatestAndSub){var h,p=0;for(u=0,n=s.length;u<n;u++)f=s[u][e.field],Math.abs(f)>p&&(h=f,p=Math.abs(f));h=""+h,s=[h=+(h=Array(h.length+1).join("9"))]}if(l.valueFilterMode===Slick.ValueFilterMode.GetLongestTextAndSub){var w=0;for(u=0,n=s.length;u<n;u++)((f=s[u][e.field])||"").length>w&&(w=f.length);f=Array(w+1).join("m"),r=options.autosizeTextAvgToMWidthRatio,s=[f]}if(l.valueFilterMode===Slick.ValueFilterMode.GetLongestText){w=0;var f,m=0;if(row.length){for(u=0,n=s.length;u<n;u++)((f=s[u][e.field])||"").length>w&&(w=f.length,m=u);f=s[m][e.field]}s=[f]}return i=getColWidth(e,o,s)*r,Math.max(a,i)}function getColWidth(e,o,t){var n=getColumnIndex(e.id),l=$('<div class="slick-row ui-widget-content"></div>'),r=$('<div class="slick-cell"></div>');r.css({position:"absolute",visibility:"hidden","text-overflow":"initial","white-space":"nowrap"}),l.append(r),o.append(l);var i,a,s,d,c=0;return canvas_context&&e.autoSize.widthEvalMode===Slick.WidthEvalMode.CanvasTextSize?(canvas_context.font=r.css("font-size")+" "+r.css("font-family"),$(t).each((function(o,t){d=Array.isArray(t)?t[e.field]:t,(i=(a=""+d)?canvas_context.measureText(a).width:0)>c&&(c=i,s=a)})),r.html(s),i=r.outerWidth(),l.remove(),r=null,i):($(t).each((function(o,t){d=Array.isArray(t)?t[e.field]:t,applyFormatResultToCellNode(e.formatterOverride?e.formatterOverride(o,n,d,e,t,self):e.formatter?e.formatter(o,n,d,e,t,self):""+d,r[0]),(i=r.outerWidth())>c&&(c=i)})),l.remove(),r=null,c)}function getColHeaderWidth(e){var o=0,t=getUID()+e.id,n=document.getElementById(t),l=t+"_";if(n){var r=n.cloneNode(!0);r.id=l,r.style.cssText="position: absolute; visibility: hidden;right: auto;text-overflow: initial;white-space: nowrap;",n.parentNode.insertBefore(r,n),o=r.offsetWidth,r.parentNode.removeChild(r)}else{var i=getHeader(e);o=(n=$("<div class='ui-state-default slick-header-column' />").html("<span class='slick-column-name'>"+e.name+"</span>").attr("id",l).css({position:"absolute",visibility:"hidden",right:"auto","text-overflow:":"initial","white-space":"nowrap"}).addClass(e.headerCssClass||"").appendTo(i))[0].offsetWidth,i[0].removeChild(n[0])}return o}function legacyAutosizeColumns(){var e,o,t,n=[],l=0,r=0,i=viewportHasVScroll?viewportW-scrollbarDimensions.width:viewportW;for(e=0;e<columns.length;e++)o=columns[e],n.push(o.width),r+=o.width,o.resizable&&(l+=o.width-Math.max(o.minWidth,absoluteColumnMinWidth));for(t=r;r>i&&l;){var a=(r-i)/l;for(e=0;e<columns.length&&r>i;e++){o=columns[e];var s=n[e];if(!(!o.resizable||s<=o.minWidth||s<=absoluteColumnMinWidth)){var d=Math.max(o.minWidth,absoluteColumnMinWidth),c=Math.floor(a*(s-d))||1;r-=c=Math.min(c,s-d),l-=c,n[e]-=c}}if(t<=r)break;t=r}for(t=r;r<i;){var u=i/r;for(e=0;e<columns.length&&r<i;e++){o=columns[e];var h,p=n[e];r+=h=!o.resizable||o.maxWidth<=p?0:Math.min(Math.floor(u*p)-p,o.maxWidth-p||1e6)||1,n[e]+=r<=i?h:0}if(t>=r)break;t=r}var w=!1;for(e=0;e<columns.length;e++)columns[e].rerenderOnResize&&columns[e].width!=n[e]&&(w=!0),columns[e].width=n[e];reRenderColumns(w)}function reRenderColumns(e){applyColumnHeaderWidths(),applyColumnGroupHeaderWidths(),updateCanvasWidth(!0),trigger(self.onAutosizeColumns,{columns}),e&&(invalidateAllRows(),render())}function trigger(e,o,t){return t=t||new Slick.EventData,(o=o||{}).grid=self,e.notify(o,t,self)}function getEditorLock(){return options.editorLock}function getEditController(){return editController}function getColumnIndex(e){return columnsById[e]}function applyColumnGroupHeaderWidths(){if(treeColumns.hasDepth())for(var e=$groupHeadersL.length-1;e>=0;e--){treeColumns.getColumnsInDepth(e);$().add($groupHeadersL[e]).add($groupHeadersR[e]).each((function(e){var o=$(this),t=0;o.width(0===e?getHeadersWidthL():getHeadersWidthR()),o.children().each((function(){var e=$(this),n=$(this).data("column");n.width=0,n.columns.forEach((function(){var e=o.next().children(":eq("+t+++")");n.width+=e.outerWidth()})),e.width(n.width-headerColumnWidthDiff)}))}))}}function applyColumnHeaderWidths(){if(initialized){for(var e,o=0,t=$headers.children(),n=columns.length;o<n;o++)e=$(t[o]),jQueryNewWidthBehaviour?e.outerWidth()!==columns[o].width&&e.outerWidth(columns[o].width):e.width()!==columns[o].width-headerColumnWidthDiff&&e.width(columns[o].width-headerColumnWidthDiff);updateColumnCaches()}}function applyColumnWidths(){for(var e,o,t=0,n=0;n<columns.length;n++)e=columns[n].width,(o=getColumnCssRules(n)).left.style.left=t+"px",o.right.style.right=(-1!=options.frozenColumn&&n>options.frozenColumn?canvasWidthR:canvasWidthL)-t-e+"px",options.frozenColumn==n?t=0:t+=columns[n].width}function setSortColumn(e,o){setSortColumns([{columnId:e,sortAsc:o}])}function setSortColumns(e){sortColumns=e;var o=options.numberedMultiColumnSort&&sortColumns.length>1,t=$headers.children();t.removeClass("slick-header-column-sorted").find(".slick-sort-indicator").removeClass("slick-sort-indicator-asc slick-sort-indicator-desc"),t.find(".slick-sort-indicator-numbered").text(""),$.each(sortColumns,(function(e,n){null==n.sortAsc&&(n.sortAsc=!0);var l=getColumnIndex(n.columnId);null!=l&&(t.eq(l).addClass("slick-header-column-sorted").find(".slick-sort-indicator").addClass(n.sortAsc?"slick-sort-indicator-asc":"slick-sort-indicator-desc"),o&&t.eq(l).find(".slick-sort-indicator-numbered").text(e+1))}))}function getSortColumns(){return sortColumns}function handleSelectedRangesChanged(e,o){var t=selectedRows.slice(0);selectedRows=[];for(var n={},l=0;l<o.length;l++)for(var r=o[l].fromRow;r<=o[l].toRow;r++){n[r]||(selectedRows.push(r),n[r]={});for(var i=o[l].fromCell;i<=o[l].toCell;i++)canCellBeSelected(r,i)&&(n[r][columns[i].id]=options.selectedCellCssClass)}setCellCssStyles(options.selectedCellCssClass,n),simpleArrayEquals(t,selectedRows)||trigger(self.onSelectedRowsChanged,{rows:getSelectedRows(),previousSelectedRows:t},e)}function simpleArrayEquals(e,o){if(!Array.isArray(e)||!Array.isArray(o))return!0;const t=new Set(e),n=new Set(o);if(t.size!=n.size)return!1;for(const e of t)if(!n.has(e))return!1;return!0}function getColumns(){return columns}function updateColumnCaches(){columnPosLeft=[],columnPosRight=[];for(var e=0,o=0,t=columns.length;o<t;o++)columnPosLeft[o]=e,columnPosRight[o]=e+columns[o].width,options.frozenColumn==o?e=0:e+=columns[o].width}function updateColumnProps(){columnsById={};for(var e=0;e<columns.length;e++){columns[e].width&&(columns[e].widthRequest=columns[e].width);var o=columns[e]=$.extend({},columnDefaults,columns[e]);o.autoSize=$.extend({},columnAutosizeDefaults,o.autoSize),columnsById[o.id]=e,o.minWidth&&o.width<o.minWidth&&(o.width=o.minWidth),o.maxWidth&&o.width>o.maxWidth&&(o.width=o.maxWidth),o.resizable}}function setColumns(e){trigger(self.onBeforeSetColumns,{previousColumns:columns,newColumns:e,grid:self});var o=new Slick.TreeColumns(e);columns=o.hasDepth()?(treeColumns=o).extractColumns():e,updateColumnProps(),updateColumnCaches(),initialized&&(setPaneVisibility(),setOverflow(),invalidateAllRows(),createColumnHeaders(),createColumnGroupHeaders(),createColumnFooter(),removeCssRules(),createCssRules(),resizeCanvas(),updateCanvasWidth(),applyColumnHeaderWidths(),applyColumnWidths(),handleScroll())}function getOptions(){return options}function setOptions(e,o,t,n){if(getEditorLock().commitCurrentEdit()){makeActiveCellNormal(),void 0!==e.showColumnHeader&&setColumnHeaderVisibility(e.showColumnHeader),options.enableAddRow!==e.enableAddRow&&invalidateRow(getDataLength());var l=$.extend(!0,{},options);if(options=$.extend(options,e),trigger(self.onSetOptions,{optionsBefore:l,optionsAfter:options}),validateAndEnforceOptions(),$viewport.css("overflow-y",options.autoHeight?"hidden":"auto"),o||render(),setFrozenOptions(),setScroller(),n||setOverflow(),t||setColumns(treeColumns.extractColumns()),options.enableMouseWheelScrollHandler&&$viewport&&jQuery.fn.mousewheel){var r=$._data($viewport[0],"events");r&&r.mousewheel||$viewport.on("mousewheel",handleMouseWheel)}else!1===options.enableMouseWheelScrollHandler&&$viewport.off("mousewheel")}}function validateAndEnforceOptions(){options.autoHeight&&(options.leaveSpaceForNewRows=!1),options.forceFitColumns&&(options.autosizeColsMode=Slick.GridAutosizeColsMode.LegacyForceFit,console.log("forceFitColumns option is deprecated - use autosizeColsMode"))}function setData(e,o){data=e,invalidateAllRows(),updateRowCount(),o&&scrollTo(0)}function getData(){return data}function getDataLength(){return data.getLength?data.getLength():data&&data.length||0}function getDataLengthIncludingAddNew(){return getDataLength()+(options.enableAddRow&&(!pagingActive||pagingIsLastPage)?1:0)}function getDataItem(e){return data.getItem?data.getItem(e):data[e]}function getTopPanel(){return $topPanel[0]}function setTopPanelVisibility(e,o){var t=!1!==o;options.showTopPanel!=e&&(options.showTopPanel=e,e?t?$topPanelScroller.slideDown("fast",resizeCanvas):($topPanelScroller.show(),resizeCanvas()):t?$topPanelScroller.slideUp("fast",resizeCanvas):($topPanelScroller.hide(),resizeCanvas()))}function setHeaderRowVisibility(e,o){var t=!1!==o;options.showHeaderRow!=e&&(options.showHeaderRow=e,e?t?$headerRowScroller.slideDown("fast",resizeCanvas):($headerRowScroller.show(),resizeCanvas()):t?$headerRowScroller.slideUp("fast",resizeCanvas):($headerRowScroller.hide(),resizeCanvas()))}function setColumnHeaderVisibility(e,o){options.showColumnHeader!=e&&(options.showColumnHeader=e,e?o?$headerScroller.slideDown("fast",resizeCanvas):($headerScroller.show(),resizeCanvas()):o?$headerScroller.slideUp("fast",resizeCanvas):($headerScroller.hide(),resizeCanvas()))}function setFooterRowVisibility(e,o){var t=!1!==o;options.showFooterRow!=e&&(options.showFooterRow=e,e?t?$footerRowScroller.slideDown("fast",resizeCanvas):($footerRowScroller.show(),resizeCanvas()):t?$footerRowScroller.slideUp("fast",resizeCanvas):($footerRowScroller.hide(),resizeCanvas()))}function setPreHeaderPanelVisibility(e,o){var t=!1!==o;options.showPreHeaderPanel!=e&&(options.showPreHeaderPanel=e,e?t?($preHeaderPanelScroller.slideDown("fast",resizeCanvas),$preHeaderPanelScrollerR.slideDown("fast",resizeCanvas)):($preHeaderPanelScroller.show(),$preHeaderPanelScrollerR.show(),resizeCanvas()):t?($preHeaderPanelScroller.slideUp("fast",resizeCanvas),$preHeaderPanelScrollerR.slideUp("fast",resizeCanvas)):($preHeaderPanelScroller.hide(),$preHeaderPanelScrollerR.hide(),resizeCanvas()))}function getContainerNode(){return $container.get(0)}function getRowTop(e){return options.rowHeight*e-offset}function getRowFromPosition(e){return Math.floor((e+offset)/options.rowHeight)}function scrollTo(e){e=Math.max(e,0),e=Math.min(e,th-$viewportScrollContainerY.height()+(viewportHasHScroll||hasFrozenColumns()?scrollbarDimensions.height:0));var o=offset;page=Math.min(n-1,Math.floor(e/ph));var t=e-(offset=Math.round(page*cj));offset!=o&&(cleanupRows(getVisibleRange(t)),updateRowPositions());prevScrollTop!=t&&(vScrollDir=prevScrollTop+o<t+offset?1:-1,lastRenderedScrollTop=scrollTop=prevScrollTop=t,hasFrozenColumns()&&($viewportTopL[0].scrollTop=t),hasFrozenRows&&($viewportBottomL[0].scrollTop=$viewportBottomR[0].scrollTop=t),$viewportScrollContainerY[0].scrollTop=t,trigger(self.onViewportChanged,{}))}function defaultFormatter(e,o,t,n,l,r){return null==t?"":(t+"").replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function getFormatter(e,o){var t=data.getItemMetadata&&data.getItemMetadata(e),n=t&&t.columns&&(t.columns[o.id]||t.columns[getColumnIndex(o.id)]);return n&&n.formatter||t&&t.formatter||o.formatter||options.formatterFactory&&options.formatterFactory.getFormatter(o)||options.defaultFormatter}function getEditor(e,o){var t=columns[o],n=data.getItemMetadata&&data.getItemMetadata(e),l=n&&n.columns;return l&&l[t.id]&&void 0!==l[t.id].editor?l[t.id].editor:l&&l[o]&&void 0!==l[o].editor?l[o].editor:t.editor||options.editorFactory&&options.editorFactory.getEditor(t)}function getDataItemValueForColumn(e,o){return options.dataItemColumnValueExtractor?options.dataItemColumnValueExtractor(e,o):e[o.field]}function appendRowHtml(e,o,t,n,l){var r=getDataItem(t),i=t<l&&!r,a="slick-row"+(hasFrozenRows&&t<=options.frozenRow?" frozen":"")+(i?" loading":"")+(t===activeRow&&options.showCellSelection?" active":"")+(t%2==1?" odd":" even");r||(a+=" "+options.addNewRowCssClass);var s=data.getItemMetadata&&data.getItemMetadata(t);s&&s.cssClasses&&(a+=" "+s.cssClasses);var d,c,u=getFrozenRowOffset(t),h="<div class='ui-widget-content "+a+"' style='top:"+(getRowTop(t)-u)+"px'>";e.push(h),hasFrozenColumns()&&o.push(h);for(var p=0,w=columns.length;p<w;p++){if(c=columns[p],d=1,s&&s.columns){var f=s.columns[c.id]||s.columns[p];"*"===(d=f&&f.colspan||1)&&(d=w-p)}if(columnPosRight[Math.min(w-1,p+d-1)]>n.leftPx){if(!c.alwaysRenderColumn&&columnPosLeft[p]>n.rightPx)break;hasFrozenColumns()&&p>options.frozenColumn?appendCellHtml(o,t,p,d,r):appendCellHtml(e,t,p,d,r)}else(c.alwaysRenderColumn||hasFrozenColumns()&&p<=options.frozenColumn)&&appendCellHtml(e,t,p,d,r);d>1&&(p+=d-1)}e.push("</div>"),hasFrozenColumns()&&o.push("</div>")}function appendCellHtml(e,o,t,n,l){var r=columns[t],i="slick-cell l"+t+" r"+Math.min(columns.length-1,t+n-1)+(r.cssClass?" "+r.cssClass:"");for(var a in hasFrozenColumns()&&t<=options.frozenColumn&&(i+=" frozen"),o===activeRow&&t===activeCell&&options.showCellSelection&&(i+=" active"),cellCssClasses)cellCssClasses[a][o]&&cellCssClasses[a][o][r.id]&&(i+=" "+cellCssClasses[a][o][r.id]);var s=null,d="";l&&(s=getDataItemValueForColumn(l,r),null==(d=getFormatter(o,r)(o,t,s,r,l,self))&&(d=""));var c=trigger(self.onBeforeAppendCell,{row:o,cell:t,value:s,dataContext:l})||"";c+=d&&d.addClasses?(c?" ":"")+d.addClasses:"";var u=d&&d.toolTip?"title='"+d.toolTip+"'":"",h="";if(r.hasOwnProperty("cellAttrs")&&r.cellAttrs instanceof Object)for(var a in r.cellAttrs)r.cellAttrs.hasOwnProperty(a)&&(h+=" "+a+'="'+r.cellAttrs[a]+'" ');e.push("<div class='"+i+(c?" "+c:"")+"' "+u+h+">"),l&&e.push("[object Object]"!==Object.prototype.toString.call(d)?d:d.text),e.push("</div>"),rowsCache[o].cellRenderQueue.push(t),rowsCache[o].cellColSpans[t]=n}function cleanupRows(e){for(var o in rowsCache){var t=!0;hasFrozenRows&&(options.frozenBottom&&o>=actualFrozenRow||!options.frozenBottom&&o<=actualFrozenRow)&&(t=!1),(o=parseInt(o,10))!==activeRow&&(o<e.top||o>e.bottom)&&t&&removeRowFromCache(o)}options.enableAsyncPostRenderCleanup&&startPostProcessingCleanup()}function invalidate(){updateRowCount(),invalidateAllRows(),render()}function invalidateAllRows(){for(var e in currentEditor&&makeActiveCellNormal(),rowsCache)removeRowFromCache(e);options.enableAsyncPostRenderCleanup&&startPostProcessingCleanup()}function queuePostProcessedRowForCleanup(e,o,t){for(var n in postProcessgroupId++,o)o.hasOwnProperty(n)&&postProcessedCleanupQueue.push({actionType:"C",groupId:postProcessgroupId,node:e.cellNodesByColumnIdx[0|n],columnIdx:0|n,rowIdx:t});postProcessedCleanupQueue.push({actionType:"R",groupId:postProcessgroupId,node:e.rowNode}),e.rowNode.detach()}function queuePostProcessedCellForCleanup(e,o,t){postProcessedCleanupQueue.push({actionType:"C",groupId:postProcessgroupId,node:e,columnIdx:o,rowIdx:t}),$(e).detach()}function removeRowFromCache(e){var o=rowsCache[e];o&&(options.enableAsyncPostRenderCleanup&&postProcessedRows[e]?queuePostProcessedRowForCleanup(o,postProcessedRows[e],e):o.rowNode.each((function(){this.parentElement.removeChild(this)})),delete rowsCache[e],delete postProcessedRows[e],renderedRows--,counter_rows_removed++)}function invalidateRows(e){var o,t;if(e&&e.length){for(vScrollDir=0,t=e.length,o=0;o<t;o++)currentEditor&&activeRow===e[o]&&makeActiveCellNormal(),rowsCache[e[o]]&&removeRowFromCache(e[o]);options.enableAsyncPostRenderCleanup&&startPostProcessingCleanup()}}function invalidateRow(e){(e||0===e)&&invalidateRows([e])}function applyFormatResultToCellNode(e,o,t){null==e&&(e=""),"[object Object]"===Object.prototype.toString.call(e)?(o.innerHTML=e.text,e.removeClasses&&!t&&$(o).removeClass(e.removeClasses),e.addClasses&&$(o).addClass(e.addClasses),e.toolTip&&$(o).attr("title",e.toolTip)):o.innerHTML=e}function updateCell(e,o){var t=getCellNode(e,o);if(t){var n=columns[o],l=getDataItem(e);if(currentEditor&&activeRow===e&&activeCell===o)currentEditor.loadValue(l);else applyFormatResultToCellNode(l?getFormatter(e,n)(e,o,getDataItemValueForColumn(l,n),n,l,self):"",t),invalidatePostProcessingResults(e)}}function updateRow(e){var o=rowsCache[e];if(o){ensureCellNodesInRowsCache(e);var t=getDataItem(e);for(var n in o.cellNodesByColumnIdx)if(o.cellNodesByColumnIdx.hasOwnProperty(n)){var l=columns[n|=0],r=o.cellNodesByColumnIdx[n][0];e===activeRow&&n===activeCell&&currentEditor?currentEditor.loadValue(t):t?applyFormatResultToCellNode(getFormatter(e,l)(e,n,getDataItemValueForColumn(t,l),l,t,self),r):r.innerHTML=""}invalidatePostProcessingResults(e)}}function getViewportHeight(){if(options.autoHeight&&-1==options.frozenColumn||(topPanelH=options.showTopPanel?options.topPanelHeight+getVBoxDelta($topPanelScroller):0,headerRowH=options.showHeaderRow?options.headerRowHeight+getVBoxDelta($headerRowScroller):0,footerRowH=options.showFooterRow?options.footerRowHeight+getVBoxDelta($footerRowScroller):0),options.autoHeight){var e=$paneHeaderL.outerHeight();e+=options.showHeaderRow?options.headerRowHeight+getVBoxDelta($headerRowScroller):0,e+=options.showFooterRow?options.footerRowHeight+getVBoxDelta($footerRowScroller):0,e+=getCanvasWidth()>viewportW?scrollbarDimensions.height:0,viewportH=options.rowHeight*getDataLengthIncludingAddNew()+(-1==options.frozenColumn?e:0)}else{var o=options.showColumnHeader?parseFloat($.css($headerScroller[0],"height"))+getVBoxDelta($headerScroller):0,t=options.createPreHeaderPanel&&options.showPreHeaderPanel?options.preHeaderPanelHeight+getVBoxDelta($preHeaderPanelScroller):0;viewportH=parseFloat($.css($container[0],"height",!0))-parseFloat($.css($container[0],"paddingTop",!0))-parseFloat($.css($container[0],"paddingBottom",!0))-o-topPanelH-headerRowH-footerRowH-t}return numVisibleRows=Math.ceil(viewportH/options.rowHeight),viewportH}function getViewportWidth(){viewportW=parseFloat($container.width())}function resizeCanvas(){if(initialized){paneTopH=0,paneBottomH=0,viewportTopH=0,viewportBottomH=0,getViewportWidth(),getViewportHeight(),hasFrozenRows?options.frozenBottom?(paneTopH=viewportH-frozenRowsHeight-scrollbarDimensions.height,paneBottomH=frozenRowsHeight+scrollbarDimensions.height):(paneTopH=frozenRowsHeight,paneBottomH=viewportH-frozenRowsHeight):paneTopH=viewportH,paneTopH+=topPanelH+headerRowH+footerRowH,hasFrozenColumns()&&options.autoHeight&&(paneTopH+=scrollbarDimensions.height),viewportTopH=paneTopH-topPanelH-headerRowH-footerRowH,options.autoHeight&&(hasFrozenColumns()&&$container.height(paneTopH+parseFloat($.css($headerScrollerL[0],"height"))),$paneTopL.css("position","relative")),$paneTopL.css({top:$paneHeaderL.height()||(options.showHeaderRow?options.headerRowHeight:0)+(options.showPreHeaderPanel?options.preHeaderPanelHeight:0),height:paneTopH});var e=$paneTopL.position().top+paneTopH;options.autoHeight||$viewportTopL.height(viewportTopH),hasFrozenColumns()?($paneTopR.css({top:$paneHeaderL.height(),height:paneTopH}),$viewportTopR.height(viewportTopH),hasFrozenRows&&($paneBottomL.css({top:e,height:paneBottomH}),$paneBottomR.css({top:e,height:paneBottomH}),$viewportBottomR.height(paneBottomH))):hasFrozenRows&&($paneBottomL.css({width:"100%",height:paneBottomH}),$paneBottomL.css("top",e)),hasFrozenRows?($viewportBottomL.height(paneBottomH),options.frozenBottom?($canvasBottomL.height(frozenRowsHeight),hasFrozenColumns()&&$canvasBottomR.height(frozenRowsHeight)):($canvasTopL.height(frozenRowsHeight),hasFrozenColumns()&&$canvasTopR.height(frozenRowsHeight))):$viewportTopR.height(viewportTopH),scrollbarDimensions&&scrollbarDimensions.width||(scrollbarDimensions=measureScrollbar()),options.autosizeColsMode===Slick.GridAutosizeColsMode.LegacyForceFit&&autosizeColumns(),updateRowCount(),handleScroll(),lastRenderedScrollLeft=-1,render()}}function updatePagingStatusFromView(e){pagingActive=0!==e.pageSize,pagingIsLastPage=e.pageNum==e.totalPages-1}function updateRowCount(){if(initialized){var e=getDataLength(),o=getDataLengthIncludingAddNew(),t=0,l=hasFrozenRows&&!options.frozenBottom?$canvasBottomL.height():$canvasTopL.height();if(hasFrozenRows)t=getDataLength()-options.frozenRow;else t=o+(options.leaveSpaceForNewRows?numVisibleRows-1:0);var r=$viewportScrollContainerY.height(),i=viewportHasVScroll;viewportHasVScroll=options.alwaysShowVerticalScroll||!options.autoHeight&&t*options.rowHeight>r,makeActiveCellNormal();var a=e-1;for(var s in rowsCache)s>a&&removeRowFromCache(s);options.enableAsyncPostRenderCleanup&&startPostProcessingCleanup(),activeCellNode&&activeRow>a&&resetActiveCell();l=h;options.autoHeight?h=options.rowHeight*t:(th=Math.max(options.rowHeight*t,r-scrollbarDimensions.height))<maxSupportedCssHeight?(h=ph=th,n=1,cj=0):(ph=(h=maxSupportedCssHeight)/100,n=Math.floor(th/ph),cj=(th-h)/(n-1)),h!==l&&(hasFrozenRows&&!options.frozenBottom?($canvasBottomL.css("height",h),hasFrozenColumns()&&$canvasBottomR.css("height",h)):($canvasTopL.css("height",h),$canvasTopR.css("height",h)),scrollTop=$viewportScrollContainerY[0].scrollTop);var d=scrollTop+offset<=th-r;0==th||0==scrollTop?page=offset=0:scrollTo(d?scrollTop+offset:th-r),h!=l&&options.autoHeight&&resizeCanvas(),options.autosizeColsMode===Slick.GridAutosizeColsMode.LegacyForceFit&&i!=viewportHasVScroll&&autosizeColumns(),updateCanvasWidth(!1)}}function getVisibleRange(e,o){return null==e&&(e=scrollTop),null==o&&(o=scrollLeft),{top:getRowFromPosition(e),bottom:getRowFromPosition(e+viewportH)+1,leftPx:o,rightPx:o+viewportW}}function getRenderedRange(e,o){var t=getVisibleRange(e,o),n=Math.round(viewportH/options.rowHeight),l=options.minRowBuffer;return-1==vScrollDir?(t.top-=n,t.bottom+=l):1==vScrollDir?(t.top-=l,t.bottom+=n):(t.top-=l,t.bottom+=l),t.top=Math.max(0,t.top),t.bottom=Math.min(getDataLengthIncludingAddNew()-1,t.bottom),t.leftPx-=viewportW,t.rightPx+=viewportW,t.leftPx=Math.max(0,t.leftPx),t.rightPx=Math.min(canvasWidth,t.rightPx),t}function ensureCellNodesInRowsCache(e){var o=rowsCache[e];if(o&&o.cellRenderQueue.length)for(var t=o.rowNode.children().last();o.cellRenderQueue.length;){var n=o.cellRenderQueue.pop();o.cellNodesByColumnIdx[n]=t,0===(t=t.prev()).length&&(t=$(o.rowNode[0]).children().last())}}function cleanUpCells(e,o){if(!hasFrozenRows||!(options.frozenBottom&&o>actualFrozenRow||o<=actualFrozenRow)){var t,n,l=rowsCache[o],r=[];for(var i in l.cellNodesByColumnIdx)if(l.cellNodesByColumnIdx.hasOwnProperty(i)&&!((i|=0)<=options.frozenColumn||Array.isArray(columns)&&columns[i]&&columns[i].alwaysRenderColumn)){var a=l.cellColSpans[i];(columnPosLeft[i]>e.rightPx||columnPosRight[Math.min(columns.length-1,i+a-1)]<e.leftPx)&&(o==activeRow&&i==activeCell||r.push(i))}for(;null!=(t=r.pop());)n=l.cellNodesByColumnIdx[t][0],options.enableAsyncPostRenderCleanup&&postProcessedRows[o]&&postProcessedRows[o][t]?queuePostProcessedCellForCleanup(n,t,o):n.parentElement.removeChild(n),delete l.cellColSpans[t],delete l.cellNodesByColumnIdx[t],postProcessedRows[o]&&delete postProcessedRows[o][t]}}function cleanUpAndRenderCells(e){for(var o,t,n,l=[],r=[],i=e.top,a=e.bottom;i<=a;i++)if(o=rowsCache[i]){ensureCellNodesInRowsCache(i),cleanUpCells(e,i),t=0;var s=data.getItemMetadata&&data.getItemMetadata(i);s=s&&s.columns;for(var d=getDataItem(i),c=0,u=columns.length;c<u&&!(columnPosLeft[c]>e.rightPx);c++)if(null==(n=o.cellColSpans[c])){if(n=1,s){var h=s[columns[c].id]||s[c];"*"===(n=h&&h.colspan||1)&&(n=u-c)}columnPosRight[Math.min(u-1,c+n-1)]>e.leftPx&&(appendCellHtml(l,i,c,n,d),t++),c+=n>1?n-1:0}else c+=n>1?n-1:0;t&&r.push(i)}if(l.length){var p,w,f=document.createElement("div");for(f.innerHTML=l.join("");null!=(p=r.pop());){var m;for(o=rowsCache[p];null!=(m=o.cellRenderQueue.pop());)w=f.lastChild,hasFrozenColumns()&&m>options.frozenColumn?o.rowNode[1].appendChild(w):o.rowNode[0].appendChild(w),o.cellNodesByColumnIdx[m]=$(w)}}}function renderRows(e){for(var o=[],t=[],n=[],l=!1,r=getDataLength(),i=e.top,a=e.bottom;i<=a;i++)rowsCache[i]||hasFrozenRows&&options.frozenBottom&&i==getDataLength()||(renderedRows++,n.push(i),rowsCache[i]={rowNode:null,cellColSpans:[],cellNodesByColumnIdx:[],cellRenderQueue:[]},appendRowHtml(o,t,i,e,r),activeCellNode&&activeRow===i&&(l=!0),counter_rows_rendered++);if(n.length){var s=document.createElement("div"),d=document.createElement("div");s.innerHTML=o.join(""),d.innerHTML=t.join("");for(i=0,a=n.length;i<a;i++)hasFrozenRows&&n[i]>=actualFrozenRow?hasFrozenColumns()?(rowsCache[n[i]].rowNode=$().add($(s.firstChild)).add($(d.firstChild)),$canvasBottomL[0].append(s.firstChild),$canvasBottomR[0].append(d.firstChild)):(rowsCache[n[i]].rowNode=$().add($(s.firstChild)),$canvasBottomL[0].append($(s.firstChild))):hasFrozenColumns()?(rowsCache[n[i]].rowNode=$().add($(s.firstChild)).add($(d.firstChild)),$canvasTopL[0].append(s.firstChild),$canvasTopR[0].append(d.firstChild)):(rowsCache[n[i]].rowNode=$().add($(s.firstChild)),$canvasTopL[0].append(s.firstChild));l&&(activeCellNode=getCellNode(activeRow,activeCell))}}function startPostProcessing(){options.enableAsyncPostRender&&(clearTimeout(h_postrender),h_postrender=setTimeout(asyncPostProcessRows,options.asyncPostRenderDelay))}function startPostProcessingCleanup(){options.enableAsyncPostRenderCleanup&&(clearTimeout(h_postrenderCleanup),h_postrenderCleanup=setTimeout(asyncPostProcessCleanupRows,options.asyncPostRenderCleanupDelay))}function invalidatePostProcessingResults(e){for(var o in postProcessedRows[e])postProcessedRows[e].hasOwnProperty(o)&&(postProcessedRows[e][o]="C");postProcessFromRow=Math.min(postProcessFromRow,e),postProcessToRow=Math.max(postProcessToRow,e),startPostProcessing()}function updateRowPositions(){for(var e in rowsCache){var o=e?parseInt(e):0;rowsCache[o].rowNode[0].style.top=getRowTop(o)+"px"}}function render(){if(initialized){scrollThrottle.dequeue();var e=getVisibleRange(),o=getRenderedRange();if(cleanupRows(o),lastRenderedScrollLeft!=scrollLeft){if(hasFrozenRows){var t=$.extend(!0,{},o);options.frozenBottom?(t.top=actualFrozenRow,t.bottom=getDataLength()):(t.top=0,t.bottom=options.frozenRow),cleanUpAndRenderCells(t)}cleanUpAndRenderCells(o)}renderRows(o),hasFrozenRows&&(options.frozenBottom?renderRows({top:actualFrozenRow,bottom:getDataLength()-1,leftPx:o.leftPx,rightPx:o.rightPx}):renderRows({top:0,bottom:options.frozenRow-1,leftPx:o.leftPx,rightPx:o.rightPx})),postProcessFromRow=e.top,postProcessToRow=Math.min(getDataLengthIncludingAddNew()-1,e.bottom),startPostProcessing(),lastRenderedScrollTop=scrollTop,lastRenderedScrollLeft=scrollLeft,h_render=null,trigger(self.onRendered,{startRow:e.top,endRow:e.bottom,grid:self})}}function handleHeaderScroll(){handleElementScroll($headerScrollContainer[0])}function handleHeaderRowScroll(){var e=$headerRowScrollContainer[0].scrollLeft;e!=$viewportScrollContainerX[0].scrollLeft&&($viewportScrollContainerX[0].scrollLeft=e)}function handleFooterRowScroll(){var e=$footerRowScrollContainer[0].scrollLeft;e!=$viewportScrollContainerX[0].scrollLeft&&($viewportScrollContainerX[0].scrollLeft=e)}function handlePreHeaderPanelScroll(){handleElementScroll($preHeaderPanelScroller[0])}function handleElementScroll(e){var o=e.scrollLeft;o!=$viewportScrollContainerX[0].scrollLeft&&($viewportScrollContainerX[0].scrollLeft=o)}function handleScroll(){return scrollTop=$viewportScrollContainerY[0].scrollTop,scrollLeft=$viewportScrollContainerX[0].scrollLeft,_handleScroll(!1)}function _handleScroll(e){var o=$viewportScrollContainerY[0].scrollHeight-$viewportScrollContainerY[0].clientHeight,t=$viewportScrollContainerY[0].scrollWidth-$viewportScrollContainerY[0].clientWidth;o=Math.max(0,o),t=Math.max(0,t),scrollTop>o&&(scrollTop=o),scrollLeft>t&&(scrollLeft=t);var l=Math.abs(scrollTop-prevScrollTop),r=Math.abs(scrollLeft-prevScrollLeft);if(r&&(prevScrollLeft=scrollLeft,$viewportScrollContainerX[0].scrollLeft=scrollLeft,$headerScrollContainer[0].scrollLeft=scrollLeft,$topPanelScroller[0].scrollLeft=scrollLeft,$headerRowScrollContainer[0].scrollLeft=scrollLeft,options.createFooterRow&&($footerRowScrollContainer[0].scrollLeft=scrollLeft),options.createPreHeaderPanel&&(hasFrozenColumns()?$preHeaderPanelScrollerR[0].scrollLeft=scrollLeft:$preHeaderPanelScroller[0].scrollLeft=scrollLeft),hasFrozenColumns()?hasFrozenRows&&($viewportTopR[0].scrollLeft=scrollLeft):hasFrozenRows&&($viewportTopL[0].scrollLeft=scrollLeft)),l)if(vScrollDir=prevScrollTop<scrollTop?1:-1,prevScrollTop=scrollTop,e&&($viewportScrollContainerY[0].scrollTop=scrollTop),hasFrozenColumns()&&(hasFrozenRows&&!options.frozenBottom?$viewportBottomL[0].scrollTop=scrollTop:$viewportTopL[0].scrollTop=scrollTop),l<viewportH)scrollTo(scrollTop+offset);else{var i=offset;page=h==viewportH?0:Math.min(n-1,Math.floor(scrollTop*((th-viewportH)/(h-viewportH))*(1/ph))),i!=(offset=Math.round(page*cj))&&invalidateAllRows()}if(r||l){var a=Math.abs(lastRenderedScrollLeft-scrollLeft),s=Math.abs(lastRenderedScrollTop-scrollTop);(a>20||s>20)&&(options.forceSyncScrolling||s<viewportH&&a<viewportW?render():scrollThrottle.enqueue(),trigger(self.onViewportChanged,{}))}return trigger(self.onScroll,{scrollLeft,scrollTop}),!(!r&&!l)}function ActionThrottle(e,o){var t=!1,n=!1;function l(){n=!1}function r(){t=!0,setTimeout(i,o),e()}function i(){n?(l(),r()):t=!1}return{enqueue:function(){t?n=!0:r()},dequeue:l}}function asyncPostProcessRows(){for(var e=getDataLength();postProcessFromRow<=postProcessToRow;){var o=vScrollDir>=0?postProcessFromRow++:postProcessToRow--,t=rowsCache[o];if(t&&!(o>=e)){for(var n in postProcessedRows[o]||(postProcessedRows[o]={}),ensureCellNodesInRowsCache(o),t.cellNodesByColumnIdx)if(t.cellNodesByColumnIdx.hasOwnProperty(n)){var l=columns[n|=0],r=postProcessedRows[o][n];if(l.asyncPostRender&&"R"!==r){var i=t.cellNodesByColumnIdx[n];i&&l.asyncPostRender(i,o,getDataItem(o),l,"C"===r),postProcessedRows[o][n]="R"}}return void(h_postrender=setTimeout(asyncPostProcessRows,options.asyncPostRenderDelay))}}}function asyncPostProcessCleanupRows(){if(postProcessedCleanupQueue.length>0){for(var e=postProcessedCleanupQueue[0].groupId;postProcessedCleanupQueue.length>0&&postProcessedCleanupQueue[0].groupId==e;){var o=postProcessedCleanupQueue.shift();if("R"==o.actionType&&$(o.node).remove(),"C"==o.actionType){var t=columns[o.columnIdx];t.asyncPostRenderCleanup&&o.node&&t.asyncPostRenderCleanup(o.node,o.rowIdx,t)}}h_postrenderCleanup=setTimeout(asyncPostProcessCleanupRows,options.asyncPostRenderCleanupDelay)}}function updateCellCssStylesOnRenderedRows(e,o){var t,n,l,r;for(var i in rowsCache){if(r=o&&o[i],l=e&&e[i],r)for(n in r)l&&r[n]==l[n]||(t=getCellNode(i,getColumnIndex(n)))&&$(t).removeClass(r[n]);if(l)for(n in l)r&&r[n]==l[n]||(t=getCellNode(i,getColumnIndex(n)))&&$(t).addClass(l[n])}}function addCellCssStyles(e,o){if(cellCssClasses[e])throw new Error("SlickGrid addCellCssStyles: cell CSS hash with key '"+e+"' already exists.");cellCssClasses[e]=o,updateCellCssStylesOnRenderedRows(o,null),trigger(self.onCellCssStylesChanged,{key:e,hash:o,grid:self})}function removeCellCssStyles(e){cellCssClasses[e]&&(updateCellCssStylesOnRenderedRows(null,cellCssClasses[e]),delete cellCssClasses[e],trigger(self.onCellCssStylesChanged,{key:e,hash:null,grid:self}))}function setCellCssStyles(e,o){var t=cellCssClasses[e];cellCssClasses[e]=o,updateCellCssStylesOnRenderedRows(o,t),trigger(self.onCellCssStylesChanged,{key:e,hash:o,grid:self})}function getCellCssStyles(e){return cellCssClasses[e]}function flashCell(e,o,t){(t=t||100,rowsCache[e])&&function e(o,n){n&&setTimeout((function(){o.queue((function(){o.toggleClass(options.cellFlashingCssClass).dequeue(),e(o,n-1)}))}),t)}($(getCellNode(e,o)),4)}function handleMouseWheel(e,o,t,n){scrollTop=Math.max(0,$viewportScrollContainerY[0].scrollTop-n*options.rowHeight),scrollLeft=$viewportScrollContainerX[0].scrollLeft+10*t,_handleScroll(!0)&&e.preventDefault()}function handleDragInit(e,o){var t=getCellFromEvent(e);if(!t||!cellExists(t.row,t.cell))return!1;var n=trigger(self.onDragInit,o,e);return!!e.isImmediatePropagationStopped()&&n}function handleDragStart(e,o){var t=getCellFromEvent(e);if(!t||!cellExists(t.row,t.cell))return!1;var n=trigger(self.onDragStart,o,e);return!!e.isImmediatePropagationStopped()&&n}function handleDrag(e,o){return trigger(self.onDrag,o,e)}function handleDragEnd(e,o){trigger(self.onDragEnd,o,e)}function handleKeyDown(e){trigger(self.onKeyDown,{row:activeRow,cell:activeCell},e);var o=e.isImmediatePropagationStopped(),t=Slick.keyCode;if(!o&&!e.shiftKey&&!e.altKey){if(options.editable&&currentEditor&&currentEditor.keyCaptureList&&currentEditor.keyCaptureList.indexOf(e.which)>-1)return;e.which==t.HOME?o=e.ctrlKey?navigateTop():navigateRowStart():e.which==t.END&&(o=e.ctrlKey?navigateBottom():navigateRowEnd())}if(!o)if(e.shiftKey||e.altKey||e.ctrlKey)e.which!=t.TAB||!e.shiftKey||e.ctrlKey||e.altKey||(o=navigatePrev());else{if(options.editable&&currentEditor&&currentEditor.keyCaptureList&&currentEditor.keyCaptureList.indexOf(e.which)>-1)return;if(e.which==t.ESCAPE){if(!getEditorLock().isActive())return;cancelEditAndSetFocus()}else e.which==t.PAGE_DOWN?(navigatePageDown(),o=!0):e.which==t.PAGE_UP?(navigatePageUp(),o=!0):e.which==t.LEFT?o=navigateLeft():e.which==t.RIGHT?o=navigateRight():e.which==t.UP?o=navigateUp():e.which==t.DOWN?o=navigateDown():e.which==t.TAB?o=navigateNext():e.which==t.ENTER&&(options.editable&&(currentEditor?activeRow===getDataLength()?navigateDown():commitEditAndSetFocus():getEditorLock().commitCurrentEdit()&&makeActiveCellEditable(void 0,void 0,e)),o=!0)}if(o){e.stopPropagation(),e.preventDefault();try{e.originalEvent.keyCode=0}catch(e){}}}function handleClick(e){if(!currentEditor&&(e.target!=document.activeElement||$(e.target).hasClass("slick-cell"))){var o=getTextSelection();setFocus(),setTextSelection(o)}var t=getCellFromEvent(e);if(t&&(null===currentEditor||activeRow!=t.row||activeCell!=t.cell)&&(trigger(self.onClick,{row:t.row,cell:t.cell},e),!e.isImmediatePropagationStopped()&&canCellBeActive(t.row,t.cell)&&(!getEditorLock().isActive()||getEditorLock().commitCurrentEdit()))){scrollRowIntoView(t.row,!1);var n=e.target&&e.target.className===Slick.preClickClassName,l=columns[t.cell],r=!!(options.editable&&l&&l.editor&&options.suppressActiveCellChangeOnEdit);setActiveCellInternal(getCellNode(t.row,t.cell),null,n,r,e)}}function handleContextMenu(e){var o=$(e.target).closest(".slick-cell",$canvas);0!==o.length&&(activeCellNode===o[0]&&null!==currentEditor||trigger(self.onContextMenu,{},e))}function handleDblClick(e){var o=getCellFromEvent(e);!o||null!==currentEditor&&activeRow==o.row&&activeCell==o.cell||(trigger(self.onDblClick,{row:o.row,cell:o.cell},e),e.isImmediatePropagationStopped()||options.editable&&gotoCell(o.row,o.cell,!0,e))}function handleHeaderMouseEnter(e){trigger(self.onHeaderMouseEnter,{column:$(this).data("column"),grid:self},e)}function handleHeaderMouseLeave(e){trigger(self.onHeaderMouseLeave,{column:$(this).data("column"),grid:self},e)}function handleHeaderContextMenu(e){var o=$(e.target).closest(".slick-header-column",".slick-header-columns"),t=o&&o.data("column");trigger(self.onHeaderContextMenu,{column:t},e)}function handleHeaderClick(e){if(!columnResizeDragging){var o=$(e.target).closest(".slick-header-column",".slick-header-columns"),t=o&&o.data("column");t&&trigger(self.onHeaderClick,{column:t},e)}}function handleFooterContextMenu(e){var o=$(e.target).closest(".slick-footerrow-column",".slick-footerrow-columns"),t=o&&o.data("column");trigger(self.onFooterContextMenu,{column:t},e)}function handleFooterClick(e){var o=$(e.target).closest(".slick-footerrow-column",".slick-footerrow-columns"),t=o&&o.data("column");trigger(self.onFooterClick,{column:t},e)}function handleMouseEnter(e){trigger(self.onMouseEnter,{},e)}function handleMouseLeave(e){trigger(self.onMouseLeave,{},e)}function cellExists(e,o){return!(e<0||e>=getDataLength()||o<0||o>=columns.length)}function getCellFromPoint(e,o){for(var t=getRowFromPosition(o),n=0,l=0,r=0;r<columns.length&&l<e;r++)l+=columns[r].width,n++;return n<0&&(n=0),{row:t,cell:n-1}}function getCellFromNode(e){var o=/l\d+/.exec(e.className);if(!o)throw new Error("SlickGrid getCellFromNode: cannot get cell - "+e.className);return parseInt(o[0].substr(1,o[0].length-1),10)}function getRowFromNode(e){for(var o in rowsCache)for(var t in rowsCache[o].rowNode)if(rowsCache[o].rowNode[t]===e)return o?parseInt(o):0;return null}function getFrozenRowOffset(e){return hasFrozenRows?options.frozenBottom?e>=actualFrozenRow?h<viewportTopH?actualFrozenRow*options.rowHeight:h:0:e>=actualFrozenRow?frozenRowsHeight:0:0}function getCellFromEvent(e){var o,t,n=$(e.target).closest(".slick-cell",$canvas);if(!n.length)return null;if(o=getRowFromNode(n[0].parentNode),hasFrozenRows){var l=n.parents(".grid-canvas").offset(),r=0;n.parents(".grid-canvas-bottom").length&&(r=options.frozenBottom?$canvasTopL.height():frozenRowsHeight),o=getCellFromPoint(e.clientX-l.left,e.clientY-l.top+r+$(document).scrollTop()).row}return t=getCellFromNode(n[0]),null==o||null==t?null:{row:o,cell:t}}function getCellNodeBox(e,o){if(!cellExists(e,o))return null;for(var t=getFrozenRowOffset(e),n=getRowTop(e)-t,l=n+options.rowHeight-1,r=0,i=0;i<o;i++)r+=columns[i].width,options.frozenColumn==i&&(r=0);return{top:n,left:r,bottom:l,right:r+columns[o].width}}function resetActiveCell(){setActiveCellInternal(null,!1)}function setFocus(){-1==tabbingDirection?$focusSink[0].focus():$focusSink2[0].focus()}function scrollCellIntoView(e,o,t){if(scrollRowIntoView(e,t),!(o<=options.frozenColumn)){var n=getColspan(e,o);internalScrollColumnIntoView(columnPosLeft[o],columnPosRight[o+(n>1?n-1:0)])}}function internalScrollColumnIntoView(e,o){var t=scrollLeft+$viewportScrollContainerX.width();e<scrollLeft?($viewportScrollContainerX.scrollLeft(e),handleScroll(),render()):o>t&&($viewportScrollContainerX.scrollLeft(Math.min(e,o-$viewportScrollContainerX[0].clientWidth)),handleScroll(),render())}function scrollColumnIntoView(e){internalScrollColumnIntoView(columnPosLeft[e],columnPosRight[e])}function setActiveCellInternal(e,o,t,n,l){null!==activeCellNode&&(makeActiveCellNormal(),$(activeCellNode).removeClass("active"),rowsCache[activeRow]&&rowsCache[activeRow].rowNode.removeClass("active"));if(null!=(activeCellNode=e)){var r=$(activeCellNode),i=r.offset(),a=Math.floor(r.parents(".grid-canvas").offset().top),s=r.parents(".grid-canvas-bottom").length;hasFrozenRows&&s&&(a-=options.frozenBottom?$canvasTopL.height():frozenRowsHeight);var d=getCellFromPoint(i.left,Math.ceil(i.top)-a);activeRow=d.row,activeCell=activePosX=activeCell=activePosX=getCellFromNode(activeCellNode),null==o&&(o=activeRow==getDataLength()||options.autoEdit),options.showCellSelection&&(r.addClass("active"),rowsCache[activeRow]&&rowsCache[activeRow].rowNode.addClass("active")),options.editable&&o&&isCellPotentiallyEditable(activeRow,activeCell)&&(clearTimeout(h_editorLoader),options.asyncEditorLoading?h_editorLoader=setTimeout((function(){makeActiveCellEditable(void 0,t,l)}),options.asyncEditorLoadDelay):makeActiveCellEditable(void 0,t,l))}else activeRow=activeCell=null;n||trigger(self.onActiveCellChanged,getActiveCell())}function clearTextSelection(){if(document.selection&&document.selection.empty)try{document.selection.empty()}catch(e){}else if(window.getSelection){var e=window.getSelection();e&&e.removeAllRanges&&e.removeAllRanges()}}function isCellPotentiallyEditable(e,o){var t=getDataLength();return!(e<t&&!getDataItem(e))&&(!(columns[o].cannotTriggerInsert&&e>=t)&&!!getEditor(e,o))}function makeActiveCellNormal(){if(currentEditor){if(trigger(self.onBeforeCellEditorDestroy,{editor:currentEditor}),currentEditor.destroy(),currentEditor=null,activeCellNode){var e=getDataItem(activeRow);if($(activeCellNode).removeClass("editable invalid"),e){var o=columns[activeCell];applyFormatResultToCellNode(getFormatter(activeRow,o)(activeRow,activeCell,getDataItemValueForColumn(e,o),o,e,self),activeCellNode),invalidatePostProcessingResults(activeRow)}}navigator.userAgent.toLowerCase().match(/msie/)&&clearTextSelection(),getEditorLock().deactivate(editController)}}function makeActiveCellEditable(e,o,t){if(activeCellNode){if(!options.editable)throw new Error("SlickGrid makeActiveCellEditable : should never get called when options.editable is false");if(clearTimeout(h_editorLoader),isCellPotentiallyEditable(activeRow,activeCell)){var n=columns[activeCell],l=getDataItem(activeRow);if(!1!==trigger(self.onBeforeEditCell,{row:activeRow,cell:activeCell,item:l,column:n,target:"grid"})){getEditorLock().activate(editController),$(activeCellNode).addClass("editable");var r=e||getEditor(activeRow,activeCell);e||r.suppressClearOnEdit||(activeCellNode.innerHTML="");var i=data.getItemMetadata&&data.getItemMetadata(activeRow),a=(i=i&&i.columns)&&(i[n.id]||i[activeCell]);currentEditor=new r({grid:self,gridPosition:absBox($container[0]),position:absBox(activeCellNode),container:activeCellNode,column:n,columnMetaData:a,item:l||{},event:t,commitChanges:commitEditAndSetFocus,cancelChanges:cancelEditAndSetFocus}),l&&(currentEditor.loadValue(l),o&&currentEditor.preClick&&currentEditor.preClick()),serializedEditorValue=currentEditor.serializeValue(),currentEditor.position&&handleActiveCellPositionChange()}else setFocus()}}}function commitEditAndSetFocus(){getEditorLock().commitCurrentEdit()&&(setFocus(),options.autoEdit&&navigateDown())}function cancelEditAndSetFocus(){getEditorLock().cancelCurrentEdit()&&setFocus()}function absBox(e){var o={top:e.offsetTop,left:e.offsetLeft,bottom:0,right:0,width:$(e).outerWidth(),height:$(e).outerHeight(),visible:!0};o.bottom=o.top+o.height,o.right=o.left+o.width;for(var t=e.offsetParent;(e=e.parentNode)!=document.body&&null!=e;)o.visible&&e.scrollHeight!=e.offsetHeight&&"visible"!=$(e).css("overflowY")&&(o.visible=o.bottom>e.scrollTop&&o.top<e.scrollTop+e.clientHeight),o.visible&&e.scrollWidth!=e.offsetWidth&&"visible"!=$(e).css("overflowX")&&(o.visible=o.right>e.scrollLeft&&o.left<e.scrollLeft+e.clientWidth),o.left-=e.scrollLeft,o.top-=e.scrollTop,e===t&&(o.left+=e.offsetLeft,o.top+=e.offsetTop,t=e.offsetParent),o.bottom=o.top+o.height,o.right=o.left+o.width;return o}function getActiveCellPosition(){return absBox(activeCellNode)}function getGridPosition(){return absBox($container[0])}function handleActiveCellPositionChange(){if(activeCellNode&&(trigger(self.onActiveCellPositionChanged,{}),currentEditor)){var e=getActiveCellPosition();currentEditor.show&&currentEditor.hide&&(e.visible?currentEditor.show():currentEditor.hide()),currentEditor.position&&currentEditor.position(e)}}function getCellEditor(){return currentEditor}function getActiveCell(){return activeCellNode?{row:activeRow,cell:activeCell}:null}function getActiveCellNode(){return activeCellNode}function getTextSelection(){var e=null;if(window.getSelection){var o=window.getSelection();o.rangeCount>0&&(e=o.getRangeAt(0))}return e}function setTextSelection(e){if(window.getSelection&&e){var o=window.getSelection();o.removeAllRanges(),o.addRange(e)}}function scrollRowIntoView(e,o){if(!hasFrozenRows||!options.frozenBottom&&e>actualFrozenRow-1||options.frozenBottom&&e<actualFrozenRow-1){var t=$viewportScrollContainerY.height(),n=hasFrozenRows&&!options.frozenBottom?e-options.frozenRow:e,l=n*options.rowHeight,r=(n+1)*options.rowHeight-t+(viewportHasHScroll?scrollbarDimensions.height:0);(n+1)*options.rowHeight>scrollTop+t+offset?(scrollTo(o?l:r),render()):n*options.rowHeight<scrollTop+offset&&(scrollTo(o?r:l),render())}}function scrollRowToTop(e){scrollTo(e*options.rowHeight),render()}function scrollPage(e){var o=e*numVisibleRows;if(scrollTo((getRowFromPosition(scrollTop+options.rowHeight-1)+o)*options.rowHeight),render(),options.enableCellNavigation&&null!=activeRow){var t=activeRow+o,n=getDataLengthIncludingAddNew();t>=n&&(t=n-1),t<0&&(t=0);for(var l=0,r=null,i=activePosX;l<=activePosX;)canCellBeActive(t,l)&&(r=l),l+=getColspan(t,l);null!==r?(setActiveCellInternal(getCellNode(t,r)),activePosX=i):resetActiveCell()}}function navigatePageDown(){scrollPage(1)}function navigatePageUp(){scrollPage(-1)}function navigateTop(){navigateToRow(0)}function navigateBottom(){navigateToRow(getDataLength()-1)}function navigateToRow(e){var o=getDataLength();if(!o)return!0;if(e<0?e=0:e>=o&&(e=o-1),scrollCellIntoView(e,0,!0),options.enableCellNavigation&&null!=activeRow){for(var t=0,n=null,l=activePosX;t<=activePosX;)canCellBeActive(e,t)&&(n=t),t+=getColspan(e,t);null!==n?(setActiveCellInternal(getCellNode(e,n)),activePosX=l):resetActiveCell()}return!0}function getColspan(e,o){var t=data.getItemMetadata&&data.getItemMetadata(e);if(!t||!t.columns)return 1;var n=t.columns[columns[o].id]||t.columns[o],l=n&&n.colspan;return l="*"===l?columns.length-o:l||1}function findFirstFocusableCell(e){for(var o=0;o<columns.length;){if(canCellBeActive(e,o))return o;o+=getColspan(e,o)}return null}function findLastFocusableCell(e){for(var o=0,t=null;o<columns.length;)canCellBeActive(e,o)&&(t=o),o+=getColspan(e,o);return t}function gotoRight(e,o,t){if(o>=columns.length)return null;do{o+=getColspan(e,o)}while(o<columns.length&&!canCellBeActive(e,o));return o<columns.length?{row:e,cell:o,posX:o}:null}function gotoLeft(e,o,t){if(o<=0)return null;var n=findFirstFocusableCell(e);if(null===n||n>=o)return null;for(var l,r={row:e,cell:n,posX:n};;){if(!(l=gotoRight(r.row,r.cell,r.posX)))return null;if(l.cell>=o)return r;r=l}}function gotoDown(e,o,t){for(var n,l=getDataLengthIncludingAddNew();;){if(++e>=l)return null;for(n=o=0;o<=t;)n=o,o+=getColspan(e,o);if(canCellBeActive(e,n))return{row:e,cell:n,posX:t}}}function gotoUp(e,o,t){for(var n;;){if(--e<0)return null;for(n=o=0;o<=t;)n=o,o+=getColspan(e,o);if(canCellBeActive(e,n))return{row:e,cell:n,posX:t}}}function gotoNext(e,o,t){if(null==e&&null==o&&canCellBeActive(e=o=t=0,o))return{row:e,cell:o,posX:o};var n=gotoRight(e,o,t);if(n)return n;var l=null,r=getDataLengthIncludingAddNew();for(e===r-1&&e--;++e<r;)if(null!==(l=findFirstFocusableCell(e)))return{row:e,cell:l,posX:l};return null}function gotoPrev(e,o,t){if(null==e&&null==o&&canCellBeActive(e=getDataLengthIncludingAddNew()-1,o=t=columns.length-1))return{row:e,cell:o,posX:o};for(var n,l;!n&&!(n=gotoLeft(e,o,t));){if(--e<0)return null;o=0,null!==(l=findLastFocusableCell(e))&&(n={row:e,cell:l,posX:l})}return n}function gotoRowStart(e,o,t){var n=findFirstFocusableCell(e);return null===n?null:{row:e,cell:n,posX:n}}function gotoRowEnd(e,o,t){var n=findLastFocusableCell(e);return null===n?null:{row:e,cell:n,posX:n}}function navigateRight(){return navigate("right")}function navigateLeft(){return navigate("left")}function navigateDown(){return navigate("down")}function navigateUp(){return navigate("up")}function navigateNext(){return navigate("next")}function navigatePrev(){return navigate("prev")}function navigateRowStart(){return navigate("home")}function navigateRowEnd(){return navigate("end")}function navigate(e){if(!options.enableCellNavigation)return!1;if(!activeCellNode&&"prev"!=e&&"next"!=e)return!1;if(!getEditorLock().commitCurrentEdit())return!0;setFocus();tabbingDirection={up:-1,down:1,left:-1,right:1,prev:-1,next:1,home:-1,end:1}[e];var o=(0,{up:gotoUp,down:gotoDown,left:gotoLeft,right:gotoRight,prev:gotoPrev,next:gotoNext,home:gotoRowStart,end:gotoRowEnd}[e])(activeRow,activeCell,activePosX);if(o){if(hasFrozenRows&&options.frozenBottom&o.row==getDataLength())return;var t=o.row==getDataLength();return(!options.frozenBottom&&o.row>=actualFrozenRow||options.frozenBottom&&o.row<actualFrozenRow)&&scrollCellIntoView(o.row,o.cell,!t&&options.emulatePagingWhenScrolling),setActiveCellInternal(getCellNode(o.row,o.cell)),activePosX=o.posX,!0}return setActiveCellInternal(getCellNode(activeRow,activeCell)),!1}function getCellNode(e,o){if(rowsCache[e]){ensureCellNodesInRowsCache(e);try{return rowsCache[e].cellNodesByColumnIdx.length>o?rowsCache[e].cellNodesByColumnIdx[o][0]:null}catch(t){return rowsCache[e].cellNodesByColumnIdx[o]}}return null}function setActiveCell(e,o,t,n,l){initialized&&(e>getDataLength()||e<0||o>=columns.length||o<0||options.enableCellNavigation&&(scrollCellIntoView(e,o,!1),setActiveCellInternal(getCellNode(e,o),t,n,l)))}function setActiveRow(e,o,t){initialized&&(e>getDataLength()||e<0||o>=columns.length||o<0||(activeRow=e,t||scrollCellIntoView(e,o||0,!1)))}function canCellBeActive(e,o){if(!options.enableCellNavigation||e>=getDataLengthIncludingAddNew()||e<0||o>=columns.length||o<0)return!1;var t=data.getItemMetadata&&data.getItemMetadata(e);if(t&&void 0!==t.focusable)return!!t.focusable;var n=t&&t.columns;return n&&n[columns[o].id]&&void 0!==n[columns[o].id].focusable?!!n[columns[o].id].focusable:n&&n[o]&&void 0!==n[o].focusable?!!n[o].focusable:!!columns[o].focusable}function canCellBeSelected(e,o){if(e>=getDataLength()||e<0||o>=columns.length||o<0)return!1;var t=data.getItemMetadata&&data.getItemMetadata(e);if(t&&void 0!==t.selectable)return!!t.selectable;var n=t&&t.columns&&(t.columns[columns[o].id]||t.columns[o]);return n&&void 0!==n.selectable?!!n.selectable:!!columns[o].selectable}function gotoCell(e,o,t,n){if(initialized&&canCellBeActive(e,o)&&getEditorLock().commitCurrentEdit()){scrollCellIntoView(e,o,!1);var l=getCellNode(e,o),r=columns[o],i=!!(options.editable&&r&&r.editor&&options.suppressActiveCellChangeOnEdit);setActiveCellInternal(l,t||e===getDataLength()||options.autoEdit,null,i,n),currentEditor||setFocus()}}function commitCurrentEdit(){var e=getDataItem(activeRow),o=columns[activeCell];if(currentEditor){if(currentEditor.isValueChanged()){var t=currentEditor.validate();if(t.valid){if(activeRow<getDataLength()){var n={row:activeRow,cell:activeCell,editor:currentEditor,serializedValue:currentEditor.serializeValue(),prevSerializedValue:serializedEditorValue,execute:function(){this.editor.applyValue(e,this.serializedValue),updateRow(this.row),trigger(self.onCellChange,{command:"execute",row:this.row,cell:this.cell,item:e,column:o})},undo:function(){this.editor.applyValue(e,this.prevSerializedValue),updateRow(this.row),trigger(self.onCellChange,{command:"undo",row:this.row,cell:this.cell,item:e,column:o})}};options.editCommandHandler?(makeActiveCellNormal(),options.editCommandHandler(e,o,n)):(n.execute(),makeActiveCellNormal())}else{var l={};currentEditor.applyValue(l,currentEditor.serializeValue()),makeActiveCellNormal(),trigger(self.onAddNewRow,{item:l,column:o})}return!getEditorLock().isActive()}return $(activeCellNode).removeClass("invalid"),$(activeCellNode).width(),$(activeCellNode).addClass("invalid"),trigger(self.onValidationError,{editor:currentEditor,cellNode:activeCellNode,validationResults:t,row:activeRow,cell:activeCell,column:o}),currentEditor.focus(),!1}makeActiveCellNormal()}return!0}function cancelCurrentEdit(){return makeActiveCellNormal(),!0}function rowsToRanges(e){for(var o=[],t=columns.length-1,n=0;n<e.length;n++)o.push(new Slick.Range(e[n],0,e[n],t));return o}function getSelectedRows(){if(!selectionModel)throw new Error("SlickGrid Selection model is not set");return selectedRows.slice(0)}function setSelectedRows(e){if(!selectionModel)throw new Error("SlickGrid Selection model is not set");self&&self.getEditorLock&&!self.getEditorLock().isActive()&&selectionModel.setSelectedRanges(rowsToRanges(e))}this.debug=function(){var e="";e+="\ncounter_rows_rendered:  "+counter_rows_rendered,e+="\ncounter_rows_removed:  "+counter_rows_removed,e+="\nrenderedRows:  "+renderedRows,e+="\nnumVisibleRows:  "+numVisibleRows,e+="\nmaxSupportedCssHeight:  "+maxSupportedCssHeight,e+="\nn(umber of pages):  "+n,e+="\n(current) page:  "+page,e+="\npage height (ph):  "+ph,e+="\nvScrollDir:  "+vScrollDir,alert(e)},this.eval=function(expr){return eval(expr)},$.extend(this,{slickGridVersion:"2.4.41",onScroll:new Slick.Event,onBeforeSort:new Slick.Event,onSort:new Slick.Event,onHeaderMouseEnter:new Slick.Event,onHeaderMouseLeave:new Slick.Event,onHeaderContextMenu:new Slick.Event,onHeaderClick:new Slick.Event,onHeaderCellRendered:new Slick.Event,onBeforeHeaderCellDestroy:new Slick.Event,onHeaderRowCellRendered:new Slick.Event,onFooterRowCellRendered:new Slick.Event,onFooterContextMenu:new Slick.Event,onFooterClick:new Slick.Event,onBeforeHeaderRowCellDestroy:new Slick.Event,onBeforeFooterRowCellDestroy:new Slick.Event,onMouseEnter:new Slick.Event,onMouseLeave:new Slick.Event,onClick:new Slick.Event,onDblClick:new Slick.Event,onContextMenu:new Slick.Event,onKeyDown:new Slick.Event,onAddNewRow:new Slick.Event,onBeforeAppendCell:new Slick.Event,onValidationError:new Slick.Event,onViewportChanged:new Slick.Event,onColumnsReordered:new Slick.Event,onColumnsDrag:new Slick.Event,onColumnsResized:new Slick.Event,onColumnsResizeDblClick:new Slick.Event,onBeforeColumnsResize:new Slick.Event,onCellChange:new Slick.Event,onCompositeEditorChange:new Slick.Event,onBeforeEditCell:new Slick.Event,onBeforeCellEditorDestroy:new Slick.Event,onBeforeDestroy:new Slick.Event,onActiveCellChanged:new Slick.Event,onActiveCellPositionChanged:new Slick.Event,onDragInit:new Slick.Event,onDragStart:new Slick.Event,onDrag:new Slick.Event,onDragEnd:new Slick.Event,onSelectedRowsChanged:new Slick.Event,onCellCssStylesChanged:new Slick.Event,onAutosizeColumns:new Slick.Event,onBeforeSetColumns:new Slick.Event,onRendered:new Slick.Event,onSetOptions:new Slick.Event,registerPlugin,unregisterPlugin,getPluginByName,getColumns,setColumns,getColumnIndex,updateColumnHeader,setSortColumn,setSortColumns,getSortColumns,autosizeColumns,autosizeColumn,getOptions,setOptions,getData,getDataLength,getDataItem,setData,getSelectionModel,setSelectionModel,getSelectedRows,setSelectedRows,getContainerNode,updatePagingStatusFromView,applyFormatResultToCellNode,render,reRenderColumns,invalidate,invalidateRow,invalidateRows,invalidateAllRows,updateCell,updateRow,getViewport:getVisibleRange,getRenderedRange,resizeCanvas,updateRowCount,scrollRowIntoView,scrollRowToTop,scrollCellIntoView,scrollColumnIntoView,getCanvasNode,getUID,getHeaderColumnWidthDiff,getScrollbarDimensions,getHeadersWidth,getCanvasWidth,getCanvases,getActiveCanvasNode,setActiveCanvasNode,getViewportNode,getViewports,getActiveViewportNode,setActiveViewportNode,focus:setFocus,scrollTo,cacheCssForHiddenInit,restoreCssFromHiddenInit,getCellFromPoint,getCellFromEvent,getActiveCell,setActiveCell,setActiveRow,getActiveCellNode,getActiveCellPosition,resetActiveCell,editActiveCell:makeActiveCellEditable,getCellEditor,getCellNode,getCellNodeBox,canCellBeSelected,canCellBeActive,navigatePrev,navigateNext,navigateUp,navigateDown,navigateLeft,navigateRight,navigatePageUp,navigatePageDown,navigateTop,navigateBottom,navigateRowStart,navigateRowEnd,gotoCell,getTopPanel,setTopPanelVisibility,getPreHeaderPanel,getPreHeaderPanelLeft:getPreHeaderPanel,getPreHeaderPanelRight,setPreHeaderPanelVisibility,getHeader,getHeaderColumn,setHeaderRowVisibility,getHeaderRow,getHeaderRowColumn,setFooterRowVisibility,getFooterRow,getFooterRowColumn,getGridPosition,flashCell,addCellCssStyles,setCellCssStyles,removeCellCssStyles,getCellCssStyles,getFrozenRowOffset,setColumnHeaderVisibility,init:finishInitialization,destroy,getEditorLock,getEditController}),init()}module.exports={Grid:SlickGrid}},
711: function _(t,e,a,n,r){
/*!
     * jquery.event.drag - v 2.3.0
     * Copyright (c) 2010 Three Dub Media - http://threedubmedia.com
     * Open Source MIT License - http://threedubmedia.com/code/license
     */
var o=t(704);o.fn.drag=function(t,e,a){var n="string"==typeof t?t:"",r=o.isFunction(t)?t:o.isFunction(e)?e:null;return 0!==n.indexOf("drag")&&(n="drag"+n),a=(t==r?e:a)||{},r?this.on(n,a,r):this.trigger(n)};var i=o.event,d=i.special,s=d.drag={defaults:{which:1,distance:0,not:":input",handle:null,relative:!1,drop:!0,click:!1},datakey:"dragdata",noBubble:!0,add:function(t){var e=o.data(this,s.datakey),a=t.data||{};e.related+=1,o.each(s.defaults,(function(t,n){void 0!==a[t]&&(e[t]=a[t])}))},remove:function(){o.data(this,s.datakey).related-=1},setup:function(){if(!o.data(this,s.datakey)){var t=o.extend({related:0},s.defaults);o.data(this,s.datakey,t),i.add(this,"touchstart mousedown",s.init,t),this.attachEvent&&this.attachEvent("ondragstart",s.dontstart)}},teardown:function(){(o.data(this,s.datakey)||{}).related||(o.removeData(this,s.datakey),i.remove(this,"touchstart mousedown",s.init),s.textselect(!0),this.detachEvent&&this.detachEvent("ondragstart",s.dontstart))},init:function(t){if(!s.touched){var e,a=t.data;if(!(0!=t.which&&a.which>0&&t.which!=a.which)){var n=o(t.target).attr("class")||"";if(!o(t.target).is(a.not)&&n&&-1!==n.toString().indexOf("slick")&&(!a.handle||o(t.target).closest(a.handle,t.currentTarget).length)&&(s.touched="touchstart"==t.type?this:null,a.propagates=1,a.mousedown=this,a.interactions=[s.interaction(this,a)],a.target=t.target,a.pageX=t.pageX,a.pageY=t.pageY,a.dragging=null,e=s.hijack(t,"draginit",a),a.propagates))return(e=s.flatten(e))&&e.length&&(a.interactions=[],o.each(e,(function(){a.interactions.push(s.interaction(this,a))}))),a.propagates=a.interactions.length,!1!==a.drop&&d.drop&&d.drop.handler(t,a),s.textselect(!1),s.touched?i.add(s.touched,"touchmove touchend",s.handler,a):i.add(document,"mousemove mouseup",s.handler,a),!(!s.touched||a.live)&&void 0}}},interaction:function(t,e){var a=t&&t.ownerDocument&&o(t)[e.relative?"position":"offset"]()||{top:0,left:0};return{drag:t,callback:new s.callback,droppable:[],offset:a}},handler:function(t){var e=t.data;switch(t.type){case!e.dragging&&"touchmove":t.preventDefault();case!e.dragging&&"mousemove":if(Math.pow(t.pageX-e.pageX,2)+Math.pow(t.pageY-e.pageY,2)<Math.pow(e.distance,2))break;t.target=e.target,s.hijack(t,"dragstart",e),e.propagates&&(e.dragging=!0);case"touchmove":t.preventDefault();case"mousemove":if(e.dragging){if(s.hijack(t,"drag",e),e.propagates){!1!==e.drop&&d.drop&&d.drop.handler(t,e);break}t.type="mouseup"}default:s.touched?i.remove(s.touched,"touchmove touchend",s.handler):i.remove(document,"mousemove mouseup",s.handler),e.dragging&&(!1!==e.drop&&d.drop&&d.drop.handler(t,e),s.hijack(t,"dragend",e)),s.textselect(!0),!1===e.click&&e.dragging&&o.data(e.mousedown,"suppress.click",(new Date).getTime()+5),e.dragging=s.touched=!1}},hijack:function(t,e,a,n,r){if(a){var d,c,l,p={event:t.originalEvent,type:t.type},u=e.indexOf("drop")?"drag":"drop",g=n||0,h=isNaN(n)?a.interactions.length:n;t.type=e;var f=function(){};t.originalEvent=new o.Event(p.event,{preventDefault:f,stopPropagation:f,stopImmediatePropagation:f}),a.results=[];do{if(c=a.interactions[g]){if("dragend"!==e&&c.cancelled)continue;l=s.properties(t,a,c),c.results=[],o(r||c[u]||a.droppable).each((function(n,r){if(l.target=r,t.isPropagationStopped=function(){return!1},!1===(d=r?i.dispatch.call(r,t,l):null)?("drag"==u&&(c.cancelled=!0,a.propagates-=1),"drop"==e&&(c[u][n]=null)):"dropinit"==e&&c.droppable.push(s.element(d)||r),"dragstart"==e&&(c.proxy=o(s.element(d)||c.drag)[0]),c.results.push(d),delete t.result,"dropinit"!==e)return d})),a.results[g]=s.flatten(c.results),"dropinit"==e&&(c.droppable=s.flatten(c.droppable)),"dragstart"!=e||c.cancelled||l.update()}}while(++g<h);return t.type=p.type,t.originalEvent=p.event,s.flatten(a.results)}},properties:function(t,e,a){var n=a.callback;return n.drag=a.drag,n.proxy=a.proxy||a.drag,n.startX=e.pageX,n.startY=e.pageY,n.deltaX=t.pageX-e.pageX,n.deltaY=t.pageY-e.pageY,n.originalX=a.offset.left,n.originalY=a.offset.top,n.offsetX=n.originalX+n.deltaX,n.offsetY=n.originalY+n.deltaY,n.drop=s.flatten((a.drop||[]).slice()),n.available=s.flatten((a.droppable||[]).slice()),n},element:function(t){if(t&&(t.jquery||1==t.nodeType))return t},flatten:function(t){return o.map(t,(function(t){return t&&t.jquery?o.makeArray(t):t&&t.length?s.flatten(t):t}))},textselect:function(t){o(document)[t?"off":"on"]("selectstart",s.dontstart).css("MozUserSelect",t?"":"none"),document.unselectable=t?"off":"on"},dontstart:function(){return!1},callback:function(){}};s.callback.prototype={update:function(){d.drop&&this.available.length&&o.each(this.available,(function(t){d.drop.locate(this,t)}))}};var c=i.dispatch;i.dispatch=function(t){if(!(o.data(this,"suppress."+t.type)-(new Date).getTime()>0))return c.apply(this,arguments);o.removeData(this,"suppress."+t.type)},d.draginit=d.dragstart=d.dragend=s},
712: function _(t,e,a,n,i){
/*!
     * jquery.event.drop - v 2.3.0
     * Copyright (c) 2010 Three Dub Media - http://threedubmedia.com
     * Open Source MIT License - http://threedubmedia.com/code/license
     */
var o=t(704);o.fn.drop=function(t,e,a){var n="string"==typeof t?t:"",i=o.isFunction(t)?t:o.isFunction(e)?e:null;return 0!==n.indexOf("drop")&&(n="drop"+n),a=(t==i?e:a)||{},i?this.on(n,a,i):this.trigger(n)},o.drop=function(t){t=t||{},d.multi=!0===t.multi?1/0:!1===t.multi?1:isNaN(t.multi)?d.multi:t.multi,d.delay=t.delay||d.delay,d.tolerance=o.isFunction(t.tolerance)?t.tolerance:null===t.tolerance?null:d.tolerance,d.mode=t.mode||d.mode||"intersect"};var r=o.event.special,d=o.event.special.drop={multi:1,delay:20,mode:"overlap",targets:[],datakey:"dropdata",noBubble:!0,add:function(t){o.data(this,d.datakey).related+=1},remove:function(){o.data(this,d.datakey).related-=1},setup:function(){if(!o.data(this,d.datakey)){o.data(this,d.datakey,{related:0,active:[],anyactive:0,winner:0,location:{}}),d.targets.push(this)}},teardown:function(){if(!(o.data(this,d.datakey)||{}).related){o.removeData(this,d.datakey);var t=this;d.targets=o.grep(d.targets,(function(e){return e!==t}))}},handler:function(t,e){var a;if(e)switch(t.type){case"mousedown":case"touchstart":a=o(d.targets),"string"==typeof e.drop&&(a=a.filter(e.drop)),a.each((function(){var t=o.data(this,d.datakey);t.active=[],t.anyactive=0,t.winner=0})),e.droppable=a,r.drag.hijack(t,"dropinit",e);break;case"mousemove":case"touchmove":d.event=t,d.timer||d.tolerate(e);break;case"mouseup":case"touchend":d.timer=clearTimeout(d.timer),e.propagates&&(r.drag.hijack(t,"drop",e),r.drag.hijack(t,"dropend",e))}},locate:function(t,e){var a=o.data(t,d.datakey),n=o(t),i=n.length&&!n.is(document)?n.offset():{},r=n.outerHeight(),l=n.outerWidth(),c={elem:t,width:l,height:r,top:i.top,left:i.left,right:i.left+l,bottom:i.top+r};return a&&(a.location=c,a.index=e,a.elem=t),c},contains:function(t,e){return(e[0]||e.left)>=t.left&&(e[0]||e.right)<=t.right&&(e[1]||e.top)>=t.top&&(e[1]||e.bottom)<=t.bottom},modes:{intersect:function(t,e,a){return this.contains(a,[t.pageX,t.pageY])?1e9:this.modes.overlap.apply(this,arguments)},overlap:function(t,e,a){return Math.max(0,Math.min(a.bottom,e.bottom)-Math.max(a.top,e.top))*Math.max(0,Math.min(a.right,e.right)-Math.max(a.left,e.left))},fit:function(t,e,a){return this.contains(a,e)?1:0},middle:function(t,e,a){return this.contains(a,[e.left+.5*e.width,e.top+.5*e.height])?1:0}},sort:function(t,e){return e.winner-t.winner||t.index-e.index},tolerate:function(t){var e,a,n,i,l,c,s,u,p=0,h=t.interactions.length,m=[d.event.pageX,d.event.pageY],f=d.tolerance||d.modes[d.mode];do{if(u=t.interactions[p]){if(!u)return;u.drop=[],l=[],c=u.droppable.length,f&&(n=d.locate(u.proxy)),e=0;do{if(s=u.droppable[e]){if(!(a=(i=o.data(s,d.datakey)).location))continue;i.winner=f?f.call(d,d.event,n,a):d.contains(a,m)?1:0,l.push(i)}}while(++e<c);l.sort(d.sort),e=0;do{(i=l[e])&&(i.winner&&u.drop.length<d.multi?(i.active[p]||i.anyactive||(!1!==r.drag.hijack(d.event,"dropstart",t,p,i.elem)[0]?(i.active[p]=1,i.anyactive+=1):i.winner=0),i.winner&&u.drop.push(i.elem)):i.active[p]&&1==i.anyactive&&(r.drag.hijack(d.event,"dropend",t,p,i.elem),i.active[p]=0,i.anyactive-=1))}while(++e<c)}}while(++p<h);d.last&&m[0]==d.last.pageX&&m[1]==d.last.pageY?delete d.timer:d.timer=setTimeout((function(){d.tolerate(t)}),d.delay),d.last=d.event}};r.dropinit=r.dropstart=r.dropend=d},
713: function _(e,t,n,r,i){var o=e(704),l=e(706);var a={Avg:function(e){this.field_=e,this.init=function(){this.count_=0,this.nonNullCount_=0,this.sum_=0},this.accumulate=function(e){var t=e[this.field_];this.count_++,null==t||""===t||isNaN(t)||(this.nonNullCount_++,this.sum_+=parseFloat(t))},this.storeResult=function(e){e.avg||(e.avg={}),0!==this.nonNullCount_&&(e.avg[this.field_]=this.sum_/this.nonNullCount_)}},Min:function(e){this.field_=e,this.init=function(){this.min_=null},this.accumulate=function(e){var t=e[this.field_];null==t||""===t||isNaN(t)||(null==this.min_||t<this.min_)&&(this.min_=t)},this.storeResult=function(e){e.min||(e.min={}),e.min[this.field_]=this.min_}},Max:function(e){this.field_=e,this.init=function(){this.max_=null},this.accumulate=function(e){var t=e[this.field_];null==t||""===t||isNaN(t)||(null==this.max_||t>this.max_)&&(this.max_=t)},this.storeResult=function(e){e.max||(e.max={}),e.max[this.field_]=this.max_}},Sum:function(e){this.field_=e,this.init=function(){this.sum_=null},this.accumulate=function(e){var t=e[this.field_];null==t||""===t||isNaN(t)||(this.sum_+=parseFloat(t))},this.storeResult=function(e){e.sum||(e.sum={}),e.sum[this.field_]=this.sum_}},Count:function(e){this.field_=e,this.init=function(){},this.storeResult=function(e){e.count||(e.count={}),e.count[this.field_]=e.group.rows.length}}};t.exports={DataView:function(e){var t,n,r,i,a,u=this,s="id",g=[],c=[],f=new l.Map,d=null,h=null,p=null,m=!1,v=!1,w=new l.Map,_=!0,y={},C={},$=[],I=[],R=null,S={getter:null,formatter:null,comparer:function(e,t){return e.value===t.value?0:e.value>t.value?1:-1},predefinedValues:[],aggregators:[],aggregateEmpty:!1,aggregateCollapsed:!1,aggregateChildGroups:!1,collapsed:!1,displayTotalsRow:!0,lazyTotalsCalculation:!1},x=[],E=[],M=[],G=":|:",b=null,A=0,D=0,F=0,V=new l.Event,N=new l.Event,T=new l.Event,O=new l.Event,k=new l.Event,P=new l.Event,B=new l.Event,K=new l.Event;function j(e){if(!v)for(var t,n=e=e||0,r=g.length;n<r;n++){if(void 0===(t=g[n][s]))throw new Error("[SlickGrid DataView] Each data element must implement a unique 'id' property");f.set(t,n)}}function z(){if(!v)for(var e,t=0,n=g.length;t<n;t++)if(void 0===(e=g[t][s])||f.get(e)!==t)throw new Error("[SlickGrid DataView] Each data element must implement a unique 'id' property")}function q(){var e=A?Math.max(1,Math.ceil(F/A)):1;return{pageSize:A,pageNum:D,totalRows:F,totalPages:e,dataView:u}}function U(e,r){_=r,n=e,t=null,!1===r&&g.reverse(),g.sort(e),!1===r&&g.reverse(),f=new l.Map,j(),pe()}function L(e,r){_=r,t=e,n=null;var i=Object.prototype.toString;Object.prototype.toString="function"==typeof e?e:function(){return this[e]},!1===r&&g.reverse(),g.sort(),Object.prototype.toString=i,!1===r&&g.reverse(),f=new l.Map,j(),pe()}function H(t){e.groupItemMetadataProvider||(e.groupItemMetadataProvider=new l.Data.GroupItemMetadataProvider),E=[],M=[],x=(t=t||[])instanceof Array?t:[t];for(var n=0;n<x.length;n++){var r=x[n]=o.extend(!0,{},S,x[n]);r.getterIsAFn="function"==typeof r.getter,r.compiledAccumulators=[];for(var i=r.aggregators.length;i--;)r.compiledAccumulators[i]=se(r.aggregators[i]);M[n]={}}pe()}function W(){if(!d){d={};for(var e=0,t=c.length;e<t;e++)d[c[e][s]]=e}}function J(e){return g[f.get(e)]}function Q(e,t){if(!f.has(e))throw new Error("[SlickGrid DataView] Invalid id");if(e!==t[s]){var n=t[s];if(null==n)throw new Error("[SlickGrid DataView] Cannot update item to associate with a null id");if(f.has(n))throw new Error("[SlickGrid DataView] Cannot update item to associate with a non-unique id");f.set(n,f.get(e)),f.delete(e),p&&p[e]&&delete p[e],e=n}g[f.get(e)]=t,p||(p={}),p[e]=!0}function X(e,t){Q(e,t),pe()}function Y(e,t){g.splice(e,0,t),j(e),pe()}function Z(e){if(v)w.set(e,!0);else{var t=f.get(e);if(void 0===t)throw new Error("[SlickGrid DataView] Invalid id");f.delete(e),g.splice(t,1),j(t),pe()}}function ee(e){if(!n)throw new Error("[SlickGrid DataView] sortedAddItem() requires a sort comparer, use sort()");Y(function(e){var t=0,r=g.length;for(;t<r;){var i=t+r>>>1;-1===n(g[i],e)?t=i+1:r=i}return t}(e),e)}function te(e,t){if(null==e)for(var n=0;n<x.length;n++)M[n]={},x[n].collapsed=t,!0===t?K.notify({level:n,groupingKey:null}):B.notify({level:n,groupingKey:null});else M[e]={},x[e].collapsed=t,!0===t?K.notify({level:e,groupingKey:null}):B.notify({level:e,groupingKey:null});pe()}function ne(e,t,n){M[e][t]=x[e].collapsed^n,pe()}function re(e,t){for(var n,r,i,o=[],a={},u=t?t.level+1:0,s=x[u],g=0,c=s.predefinedValues.length;g<c;g++)(n=a[r=s.predefinedValues[g]])||((n=new l.Group).value=r,n.level=u,n.groupingKey=(t?t.groupingKey+G:"")+r,o[o.length]=n,a[r]=n);for(g=0,c=e.length;g<c;g++)i=e[g],(n=a[r=s.getterIsAFn?s.getter(i):i[s.getter]])||((n=new l.Group).value=r,n.level=u,n.groupingKey=(t?t.groupingKey+G:"")+r,o[o.length]=n,a[r]=n),n.rows[n.count++]=i;if(u<x.length-1)for(g=0;g<o.length;g++)(n=o[g]).groups=re(n.rows,n);return o.length&&le(o,u),o.sort(x[u].comparer),o}function ie(e){var t,n=e.group,r=x[n.level],i=n.level==x.length,o=r.aggregators.length;if(!i&&r.aggregateChildGroups)for(var l=n.groups.length;l--;)n.groups[l].totals.initialized||ie(n.groups[l].totals);for(;o--;)(t=r.aggregators[o]).init(),!i&&r.aggregateChildGroups?r.compiledAccumulators[o].call(t,n.groups):r.compiledAccumulators[o].call(t,n.rows),t.storeResult(e);e.initialized=!0}function oe(e){var t=x[e.level],n=new l.GroupTotals;n.group=e,e.totals=n,t.lazyTotalsCalculation||ie(n)}function le(e,t){for(var n,r=x[t=t||0],i=r.collapsed,o=M[t],l=e.length;l--;)(n=e[l]).collapsed&&!r.aggregateCollapsed||(n.groups&&le(n.groups,t+1),r.aggregators.length&&(r.aggregateEmpty||n.rows.length||n.groups&&n.groups.length)&&oe(n),n.collapsed=i^o[n.groupingKey],n.title=r.formatter?r.formatter(n):n.value)}function ae(e,t){for(var n,r,i=x[t=t||0],o=[],l=0,a=0,u=e.length;a<u;a++){if(r=e[a],o[l++]=r,!r.collapsed)for(var s=0,g=(n=r.groups?ae(r.groups,t+1):r.rows).length;s<g;s++)o[l++]=n[s];r.totals&&i.displayTotalsRow&&(!r.collapsed||i.aggregateCollapsed)&&(o[l++]=r.totals)}return o}function ue(e){var t=e.toString().indexOf("function")>=0?/^function[^(]*\(([^)]*)\)\s*{([\s\S]*)}$/:/^[^(]*\(([^)]*)\)\s*{([\s\S]*)}$/,n=e.toString().match(t);return{params:n[1].split(","),body:n[2]}}function se(e){if(e.accumulate){var t=ue(e.accumulate),n=new Function("_items","for (var "+t.params[0]+", _i=0, _il=_items.length; _i<_il; _i++) {"+t.params[0]+" = _items[_i]; "+t.body+"}"),r="compiledAccumulatorLoop";return n.displayName=r,n.name=ge(n,r),n}return function(){}}function ge(e,t){try{Object.defineProperty(e,"name",{writable:!0,value:t})}catch(n){e.name=t}}function ce(e,t){for(var n=[],r=0,i=0,o=e.length;i<o;i++)h(e[i],t)&&(n[r++]=e[i]);return n}function fe(e,t,n){for(var r,i=[],o=0,l=0,a=e.length;l<a;l++)r=e[l],n[l]?i[o++]=r:h(r,t)&&(i[o++]=r,n[l]=!0);return i}function de(t){if(h){var n=e.inlineFilters?i:ce,o=e.inlineFilters?a:fe;y.isFilterNarrowing?$=n($,r):y.isFilterExpanding?$=o(t,r,I):y.isFilterUnchanged||($=n(t,r))}else $=A?t:t.concat();var l;return A?($.length<=D*A&&(D=0===$.length?0:Math.floor(($.length-1)/A)),l=$.slice(A*D,A*D+A)):l=$,{totalRows:$.length,rows:l}}function he(e){d=null,y.isFilterNarrowing==C.isFilterNarrowing&&y.isFilterExpanding==C.isFilterExpanding||(I=[]);var t=de(e);F=t.totalRows;var n=t.rows;E=[],x.length&&(E=re(n)).length&&(n=ae(E));var r=function(e,t){var n,r,i,o=[],l=0,a=Math.max(t.length,e.length);y&&y.ignoreDiffsBefore&&(l=Math.max(0,Math.min(t.length,y.ignoreDiffsBefore))),y&&y.ignoreDiffsAfter&&(a=Math.min(t.length,Math.max(0,y.ignoreDiffsAfter)));for(var u=l,g=e.length;u<a;u++)u>=g?o[o.length]=u:(n=t[u],r=e[u],(!n||x.length&&(i=n.__nonDataRow||r.__nonDataRow)&&n.__group!==r.__group||n.__group&&!n.equals(r)||i&&(n.__groupTotals||r.__groupTotals)||n[s]!=r[s]||p&&p[n[s]])&&(o[o.length]=u));return o}(c,n);return c=n,r}function pe(){if(!m){var e=o.extend(!0,{},q()),t=c.length,n=F,r=he(g);A&&F<D*A&&(D=Math.max(0,Math.ceil(F/A)-1),r=he(g)),p=null,C=y,y={},n!==F&&!1!==k.notify(e,null,u)&&P.notify(q(),null,u),t!==c.length&&N.notify({previous:t,current:c.length,itemCount:g.length,dataView:u,callingOnRowsChanged:r.length>0},null,u),r.length>0&&T.notify({rows:r,itemCount:g.length,dataView:u,calledOnRowCountChanged:t!==c.length},null,u),(t!==c.length||r.length>0)&&O.notify({rowsDiff:r,previousRowCount:t,currentRowCount:c.length,itemCount:g.length,rowCountChanged:t!==c.length,rowsChanged:r.length>0,dataView:u},null,u)}}function me(){return b}e=o.extend(!0,{},{groupItemMetadataProvider:null,inlineFilters:!1},e),o.extend(this,{beginUpdate:function(e){m=!0,v=!0===e},endUpdate:function(){var e=v;v=!1,m=!1,e&&(!function(){for(var e,t,n=0,r=0,i=g.length;r<i;r++){if(void 0===(e=(t=g[r])[s]))throw new Error("[SlickGrid DataView] Each data element must implement a unique 'id' property");w.has(e)?f.delete(e):(g[n]=t,f.set(e,n),++n)}g.length=n,w=new l.Map}(),z()),pe()},destroy:function(){g=[],f=null,d=null,h=null,p=null,n=null,I=[],$=[],i=null,a=null,R&&R.onSelectedRowsChanged&&R.onCellCssStylesChanged&&(R.onSelectedRowsChanged.unsubscribe(),R.onCellCssStylesChanged.unsubscribe()),u.onRowsOrCountChanged&&u.onRowsOrCountChanged.unsubscribe()},setPagingOptions:function(e){!1!==k.notify(q(),null,u)&&(null!=e.pageSize&&(A=e.pageSize,D=A?Math.min(D,Math.max(0,Math.ceil(F/A)-1)):0),null!=e.pageNum&&(D=Math.min(e.pageNum,Math.max(0,Math.ceil(F/A)-1))),P.notify(q(),null,u),pe())},getPagingInfo:q,getIdPropertyName:function(){return s},getItems:function(){return g},setItems:function(e,t){void 0!==t&&(s=t),g=$=e,V.notify({idProperty:t,itemCount:g.length},null,u),f=new l.Map,j(),z(),pe()},setFilter:function(t){h=t,e.inlineFilters&&(i=function(){var e=ue(h),t="{ continue _coreloop; }$1",n="{ _retval[_idx++] = $item$; continue _coreloop; }$1",r=e.body.replace(/return false\s*([;}]|\}|$)/gi,t).replace(/return!1([;}]|\}|$)/gi,t).replace(/return true\s*([;}]|\}|$)/gi,n).replace(/return!0([;}]|\}|$)/gi,n).replace(/return ([^;}]+?)\s*([;}]|$)/gi,"{ if ($1) { _retval[_idx++] = $item$; }; continue _coreloop; }$2"),i=["var _retval = [], _idx = 0; ","var $item$, $args$ = _args; ","_coreloop: ","for (var _i = 0, _il = _items.length; _i < _il; _i++) { ","$item$ = _items[_i]; ","$filter$; ","} ","return _retval; "].join("");i=(i=(i=i.replace(/\$filter\$/gi,r)).replace(/\$item\$/gi,e.params[0])).replace(/\$args\$/gi,e.params[1]);var o=new Function("_items,_args",i),l="compiledFilter";return o.displayName=l,o.name=ge(o,l),o}(),a=function(){var e=ue(h),t="{ continue _coreloop; }$1",n="{ _cache[_i] = true;_retval[_idx++] = $item$; continue _coreloop; }$1",r=e.body.replace(/return false\s*([;}]|\}|$)/gi,t).replace(/return!1([;}]|\}|$)/gi,t).replace(/return true\s*([;}]|\}|$)/gi,n).replace(/return!0([;}]|\}|$)/gi,n).replace(/return ([^;}]+?)\s*([;}]|$)/gi,"{ if ((_cache[_i] = $1)) { _retval[_idx++] = $item$; }; continue _coreloop; }$2"),i=["var _retval = [], _idx = 0; ","var $item$, $args$ = _args; ","_coreloop: ","for (var _i = 0, _il = _items.length; _i < _il; _i++) { ","$item$ = _items[_i]; ","if (_cache[_i]) { ","_retval[_idx++] = $item$; ","continue _coreloop; ","} ","$filter$; ","} ","return _retval; "].join("");i=(i=(i=i.replace(/\$filter\$/gi,r)).replace(/\$item\$/gi,e.params[0])).replace(/\$args\$/gi,e.params[1]);var o=new Function("_items,_args,_cache",i),l="compiledFilterWithCaching";return o.displayName=l,o.name=ge(o,l),o}()),pe()},getFilter:function(){return h},getFilteredItems:function(){return $},getFilteredItemCount:function(){return $.length},sort:U,fastSort:L,reSort:function(){n?U(n,_):t&&L(t,_)},setGrouping:H,getGrouping:function(){return x},groupBy:function(e,t,n){H(null!=e?{getter:e,formatter:t,comparer:n}:[])},setAggregators:function(e,t){if(!x.length)throw new Error("[SlickGrid DataView] At least one grouping must be specified before calling setAggregators().");x[0].aggregators=e,x[0].aggregateCollapsed=t,H(x)},collapseAllGroups:function(e){te(e,!0)},expandAllGroups:function(e){te(e,!1)},collapseGroup:function(e){var t,n,r=Array.prototype.slice.call(arguments),i=r[0];1===r.length&&-1!==i.indexOf(G)?(t=i,n=i.split(G).length-1):(t=r.join(G),n=r.length-1),ne(n,t,!0),K.notify({level:n,groupingKey:t})},expandGroup:function(e){var t,n,r=Array.prototype.slice.call(arguments),i=r[0];1===r.length&&-1!==i.indexOf(G)?(n=i.split(G).length-1,t=i):(n=r.length-1,t=r.join(G)),ne(n,t,!1),B.notify({level:n,groupingKey:t})},getGroups:function(){return E},getAllSelectedIds:me,getAllSelectedItems:function(){var e=[];return me().forEach((function(t){e.push(u.getItemById(t))})),e},getIdxById:function(e){return f.get(e)},getRowByItem:function(e){return W(),d[e[s]]},getRowById:function(e){return W(),d[e]},getItemById:J,getItemByIdx:function(e){return g[e]},mapItemsToRows:function(e){var t=[];W();for(var n=0,r=e.length;n<r;n++){var i=d[e[n][s]];null!=i&&(t[t.length]=i)}return t},mapRowsToIds:function(e){for(var t=[],n=0,r=e.length;n<r;n++)e[n]<c.length&&(t[t.length]=c[e[n]][s]);return t},mapIdsToRows:function(e){var t=[];W();for(var n=0,r=e.length;n<r;n++){var i=d[e[n]];null!=i&&(t[t.length]=i)}return t},setRefreshHints:function(e){y=e},setFilterArgs:function(e){r=e},refresh:pe,updateItem:X,updateItems:function(e,t){if(e.length!==t.length)throw new Error("[SlickGrid DataView] Mismatch on the length of ids and items provided to update");for(var n=0,r=t.length;n<r;n++)Q(e[n],t[n]);pe()},insertItem:Y,insertItems:function(e,t){Array.prototype.splice.apply(g,[e,0].concat(t)),j(e),pe()},addItem:function(e){g.push(e),j(g.length-1),pe()},addItems:function(e){j((g=g.concat(e)).length-e.length),pe()},deleteItem:Z,deleteItems:function(e){if(0!==e.length)if(v)for(var t=0,n=e.length;t<n;t++){var r=e[t];if(void 0===(o=f.get(r)))throw new Error("[SlickGrid DataView] Invalid id");w.set(r,!0)}else{var i=[];for(t=0,n=e.length;t<n;t++){var o;r=e[t];if(void 0===(o=f.get(r)))throw new Error("[SlickGrid DataView] Invalid id");f.delete(r),i.push(o)}i.sort();for(t=i.length-1;t>=0;--t)g.splice(i[t],1);j(i[0]),pe()}},sortedAddItem:ee,sortedUpdateItem:function(e,t){if(!f.has(e)||e!==t[s])throw new Error("[SlickGrid DataView] Invalid or non-matching id "+f.get(e));if(!n)throw new Error("[SlickGrid DataView] sortedUpdateItem() requires a sort comparer, use sort()");var r=J(e);0!==n(r,t)?(Z(e),ee(t)):X(e,t)},syncGridSelection:function(e,t,n){var r,i=this;R=e,b=i.mapRowsToIds(e.getSelectedRows());var a=new l.Event;function u(t){b.join(",")!=t.join(",")&&(b=t,a.notify({grid:e,ids:b,dataView:i},new l.EventData,i))}return e.onSelectedRowsChanged.subscribe((function(t,l){if(!r){var a=i.mapRowsToIds(e.getSelectedRows());if(n&&e.getOptions().multiSelect)u(o.grep(b,(function(e){return void 0===i.getRowById(e)})).concat(a));else u(a)}})),this.onRowsOrCountChanged.subscribe((function(){if(b.length>0){r=!0;var n=i.mapIdsToRows(b);t||u(i.mapRowsToIds(n)),e.setSelectedRows(n),r=!1}})),a},syncGridCellCssStyles:function(e,t){var n,r;function i(e){for(var t in n={},e){var r=c[t][s];n[r]=e[t]}}function o(){if(n){r=!0,W();var i={};for(var o in n){var l=d[o];null!=l&&(i[l]=n[o])}e.setCellCssStyles(t,i),r=!1}}i(e.getCellCssStyles(t)),e.onCellCssStylesChanged.subscribe((function(n,l){r||t==l.key&&(l.hash?i(l.hash):(e.onCellCssStylesChanged.unsubscribe(),u.onRowsOrCountChanged.unsubscribe(o)))})),this.onRowsOrCountChanged.subscribe(o)},getItemCount:function(){return g.length},getLength:function(){return c.length},getItem:function(e){var t=c[e];if(t&&t.__group&&t.totals&&!t.totals.initialized){var n=x[t.level];n.displayTotalsRow||(ie(t.totals),t.title=n.formatter?n.formatter(t):t.value)}else t&&t.__groupTotals&&!t.initialized&&ie(t);return t},getItemMetadata:function(t){var n=c[t];return void 0===n?null:n.__group?e.groupItemMetadataProvider.getGroupRowMetadata(n):n.__groupTotals?e.groupItemMetadataProvider.getTotalsRowMetadata(n):null},onSetItemsCalled:V,onRowCountChanged:N,onRowsChanged:T,onRowsOrCountChanged:O,onBeforePagingInfoChanged:k,onPagingInfoChanged:P,onGroupExpanded:B,onGroupCollapsed:K})},Aggregators:a,Data:{Aggregators:a}}},
714: function _(e,i,t,o,n){var a=e(704),l=e(706);function s(e){var i,t,o=this;function n(){var i=e.column.editorFixedDecimalPlaces;return void 0===i&&(i=s.DefaultDecimalPlaces),i||0===i?i:null}this.args=e,this.init=function(){var t=e.grid.getOptions().editorCellNavOnLRKeys;i=a("<INPUT type=text class='editor-text' />").appendTo(e.container).on("keydown.nav",t?r:u).focus().select(),e.compositeEditorOptions&&i.on("change",(function(){var i=e.grid.getActiveCell();o.validate().valid&&o.applyValue(o.args.item,o.serializeValue()),o.applyValue(o.args.compositeEditorOptions.formValues,o.serializeValue()),e.grid.onCompositeEditorChange.notify({row:i.row,cell:i.cell,item:o.args.item,column:o.args.column,formValues:o.args.compositeEditorOptions.formValues})}))},this.destroy=function(){i.remove()},this.focus=function(){i.focus()},this.loadValue=function(o){t=o[e.column.field];var a=n();null!==a&&(t||0===t)&&t.toFixed&&(t=t.toFixed(a)),i.val(t),i[0].defaultValue=t,i.select()},this.serializeValue=function(){var e=parseFloat(i.val());s.AllowEmptyValue?e||0===e||(e=""):e=e||0;var t=n();return null!==t&&(e||0===e)&&e.toFixed&&(e=parseFloat(e.toFixed(t))),e},this.applyValue=function(i,t){i[e.column.field]=t},this.isValueChanged=function(){return!(""===i.val()&&null==t)&&i.val()!=t},this.validate=function(){if(isNaN(i.val()))return{valid:!1,msg:"Please enter a valid number"};if(e.column.validator){var t=e.column.validator(i.val(),e);if(!t.valid)return t}return{valid:!0,msg:null}},this.init()}function r(e){var i=this.selectionStart,t=this.value.length;(e.keyCode===l.keyCode.LEFT&&i>0||e.keyCode===l.keyCode.RIGHT&&i<t-1)&&e.stopImmediatePropagation()}function u(e){e.keyCode!==l.keyCode.LEFT&&e.keyCode!==l.keyCode.RIGHT||e.stopImmediatePropagation()}s.DefaultDecimalPlaces=null,s.AllowEmptyValue=!1,i.exports={Editors:{Text:function(e){var i,t,o=this;this.args=e,this.init=function(){var t=e.grid.getOptions().editorCellNavOnLRKeys;i=a("<INPUT type=text class='editor-text' />").appendTo(e.container).on("keydown.nav",t?r:u).focus().select(),e.compositeEditorOptions&&i.on("change",(function(){var i=e.grid.getActiveCell();o.validate().valid&&o.applyValue(o.args.item,o.serializeValue()),o.applyValue(o.args.compositeEditorOptions.formValues,o.serializeValue()),e.grid.onCompositeEditorChange.notify({row:i.row,cell:i.cell,item:o.args.item,column:o.args.column,formValues:o.args.compositeEditorOptions.formValues})}))},this.destroy=function(){i.remove()},this.focus=function(){i.focus()},this.getValue=function(){return i.val()},this.setValue=function(e){i.val(e)},this.loadValue=function(o){t=o[e.column.field]||"",i.val(t),i[0].defaultValue=t,i.select()},this.serializeValue=function(){return i.val()},this.applyValue=function(i,t){i[e.column.field]=t},this.isValueChanged=function(){return!(""===i.val()&&null==t)&&i.val()!=t},this.validate=function(){if(e.column.validator){var t=e.column.validator(i.val(),e);if(!t.valid)return t}return{valid:!0,msg:null}},this.init()},Integer:function(e){var i,t,o=this;this.args=e,this.init=function(){var t=e.grid.getOptions().editorCellNavOnLRKeys;i=a("<INPUT type=text class='editor-text' />").appendTo(e.container).on("keydown.nav",t?r:u).focus().select(),e.compositeEditorOptions&&i.on("change",(function(){var i=e.grid.getActiveCell();o.validate().valid&&o.applyValue(o.args.item,o.serializeValue()),o.applyValue(o.args.compositeEditorOptions.formValues,o.serializeValue()),e.grid.onCompositeEditorChange.notify({row:i.row,cell:i.cell,item:o.args.item,column:o.args.column,formValues:o.args.compositeEditorOptions.formValues})}))},this.destroy=function(){i.remove()},this.focus=function(){i.focus()},this.loadValue=function(o){t=o[e.column.field],i.val(t),i[0].defaultValue=t,i.select()},this.serializeValue=function(){return parseInt(i.val(),10)||0},this.applyValue=function(i,t){i[e.column.field]=t},this.isValueChanged=function(){return!(""===i.val()&&null==t)&&i.val()!=t},this.validate=function(){if(isNaN(i.val()))return{valid:!1,msg:"Please enter a valid integer"};if(e.column.validator){var t=e.column.validator(i.val(),e);if(!t.valid)return t}return{valid:!0,msg:null}},this.init()},Float:s,Date:function(e){var i,t,o=this,n=!1;this.args=e,this.init=function(){(i=a("<INPUT type=text class='editor-text' />")).appendTo(e.container),i.focus().select(),i.datepicker({showOn:"button",buttonImageOnly:!0,beforeShow:function(){n=!0},onClose:function(){if(n=!1,e.compositeEditorOptions){var i=e.grid.getActiveCell();o.validate().valid&&o.applyValue(o.args.item,o.serializeValue()),o.applyValue(o.args.compositeEditorOptions.formValues,o.serializeValue()),e.grid.onCompositeEditorChange.notify({row:i.row,cell:i.cell,item:o.args.item,column:o.args.column,formValues:o.args.compositeEditorOptions.formValues})}}}),i.width(i.width()-(e.compositeEditorOptions?28:18))},this.destroy=function(){a.datepicker.dpDiv.stop(!0,!0),i.datepicker("hide"),i.datepicker("destroy"),i.remove()},this.show=function(){n&&a.datepicker.dpDiv.stop(!0,!0).show()},this.hide=function(){n&&a.datepicker.dpDiv.stop(!0,!0).hide()},this.position=function(e){n&&a.datepicker.dpDiv.css("top",e.top+30).css("left",e.left)},this.focus=function(){i.focus()},this.loadValue=function(o){t=o[e.column.field],i.val(t),i[0].defaultValue=t,i.select()},this.serializeValue=function(){return i.val()},this.applyValue=function(i,t){i[e.column.field]=t},this.isValueChanged=function(){return!(""===i.val()&&null==t)&&i.val()!=t},this.validate=function(){if(e.column.validator){var t=e.column.validator(i.val(),e);if(!t.valid)return t}return{valid:!0,msg:null}},this.init()},YesNoSelect:function(e){var i,t,o=this;this.args=e,this.init=function(){(i=a("<SELECT tabIndex='0' class='editor-yesno'><OPTION value='yes'>Yes</OPTION><OPTION value='no'>No</OPTION></SELECT>")).appendTo(e.container),i.focus(),e.compositeEditorOptions&&i.on("change",(function(){var i=e.grid.getActiveCell();o.validate().valid&&o.applyValue(o.args.item,o.serializeValue()),o.applyValue(o.args.compositeEditorOptions.formValues,o.serializeValue()),e.grid.onCompositeEditorChange.notify({row:i.row,cell:i.cell,item:o.args.item,column:o.args.column,formValues:o.args.compositeEditorOptions.formValues})}))},this.destroy=function(){i.remove()},this.focus=function(){i.focus()},this.loadValue=function(o){i.val((t=o[e.column.field])?"yes":"no"),i.select()},this.serializeValue=function(){return"yes"==i.val()},this.applyValue=function(i,t){i[e.column.field]=t},this.isValueChanged=function(){return i.val()!=t},this.validate=function(){return{valid:!0,msg:null}},this.init()},Checkbox:function(e){var i,t,o=this;this.args=e,this.init=function(){(i=a("<INPUT type=checkbox value='true' class='editor-checkbox' hideFocus>")).appendTo(e.container),i.focus(),e.compositeEditorOptions&&i.on("change",(function(){var i=e.grid.getActiveCell();o.validate().valid&&o.applyValue(o.args.item,o.serializeValue()),o.applyValue(o.args.compositeEditorOptions.formValues,o.serializeValue()),e.grid.onCompositeEditorChange.notify({row:i.row,cell:i.cell,item:o.args.item,column:o.args.column,formValues:o.args.compositeEditorOptions.formValues})}))},this.destroy=function(){i.remove()},this.focus=function(){i.focus()},this.loadValue=function(o){(t=!!o[e.column.field])?i.prop("checked",!0):i.prop("checked",!1)},this.preClick=function(){i.prop("checked",!i.prop("checked"))},this.serializeValue=function(){return i.prop("checked")},this.applyValue=function(i,t){i[e.column.field]=t},this.isValueChanged=function(){return this.serializeValue()!==t},this.validate=function(){return{valid:!0,msg:null}},this.init()},PercentComplete:function(e){var i,t,o,n=this;this.args=e,this.init=function(){(i=a("<INPUT type=text class='editor-percentcomplete' />")).width(a(e.container).innerWidth()-25),i.appendTo(e.container),(t=a("<div class='editor-percentcomplete-picker' />").appendTo(e.container)).append("<div class='editor-percentcomplete-helper'><div class='editor-percentcomplete-wrapper'><div class='editor-percentcomplete-slider' /><div class='editor-percentcomplete-buttons' /></div></div>"),t.find(".editor-percentcomplete-buttons").append("<button val=0>Not started</button><br/><button val=50>In Progress</button><br/><button val=100>Complete</button>"),i.focus().select(),t.find(".editor-percentcomplete-slider").slider({orientation:"vertical",range:"min",value:o,slide:function(e,t){i.val(t.value)},stop:function(i,t){if(e.compositeEditorOptions){var o=e.grid.getActiveCell();n.validate().valid&&n.applyValue(n.args.item,n.serializeValue()),n.applyValue(n.args.compositeEditorOptions.formValues,n.serializeValue()),e.grid.onCompositeEditorChange.notify({row:o.row,cell:o.cell,item:n.args.item,column:n.args.column,formValues:n.args.compositeEditorOptions.formValues})}}}),t.find(".editor-percentcomplete-buttons button").on("click",(function(e){i.val(a(this).attr("val")),t.find(".editor-percentcomplete-slider").slider("value",a(this).attr("val"))}))},this.destroy=function(){i.remove(),t.remove()},this.focus=function(){i.focus()},this.loadValue=function(t){i.val(o=t[e.column.field]),i.select()},this.serializeValue=function(){return parseInt(i.val(),10)||0},this.applyValue=function(i,t){i[e.column.field]=t},this.isValueChanged=function(){return!(""===i.val()&&null==o)&&(parseInt(i.val(),10)||0)!=o},this.validate=function(){return isNaN(parseInt(i.val(),10))?{valid:!1,msg:"Please enter a valid positive number"}:{valid:!0,msg:null}},this.init()},LongText:function(e){var i,t,o,n=this;this.args=e,this.init=function(){var o=e.compositeEditorOptions,l=(e.grid.getOptions().editorCellNavOnLRKeys,o?e.container:a("body"));t=a("<DIV class='slick-large-editor-text' style='z-index:10000;background:white;padding:5px;border:3px solid gray; border-radius:10px;'/>").appendTo(l),o?t.css({position:"relative",padding:0,border:0}):t.css({position:"absolute"}),i=a("<TEXTAREA hidefocus rows=5 style='background:white;width:250px;height:80px;border:0;outline:0'>").appendTo(t),o?i.on("change",(function(){var i=e.grid.getActiveCell();n.validate().valid&&n.applyValue(n.args.item,n.serializeValue()),n.applyValue(n.args.compositeEditorOptions.formValues,n.serializeValue()),e.grid.onCompositeEditorChange.notify({row:i.row,cell:i.cell,item:n.args.item,column:n.args.column,formValues:n.args.compositeEditorOptions.formValues})})):(a("<DIV style='text-align:right'><BUTTON>Save</BUTTON><BUTTON>Cancel</BUTTON></DIV>").appendTo(t),t.find("button:first").on("click",this.save),t.find("button:last").on("click",this.cancel),i.on("keydown",this.handleKeyDown),n.position(e.position)),i.focus().select()},this.handleKeyDown=function(i){if(i.which==l.keyCode.ENTER&&i.ctrlKey)n.save();else if(i.which==l.keyCode.ESCAPE)i.preventDefault(),n.cancel();else if(i.which==l.keyCode.TAB&&i.shiftKey)i.preventDefault(),e.grid.navigatePrev();else if(i.which==l.keyCode.TAB)i.preventDefault(),e.grid.navigateNext();else if((i.which==l.keyCode.LEFT||i.which==l.keyCode.RIGHT)&&e.grid.getOptions().editorCellNavOnLRKeys){var t=this.selectionStart,o=this.value.length;i.keyCode===l.keyCode.LEFT&&0===t&&e.grid.navigatePrev(),i.keyCode===l.keyCode.RIGHT&&t>=o-1&&e.grid.navigateNext()}},this.save=function(){e.commitChanges()},this.cancel=function(){i.val(o),e.cancelChanges()},this.hide=function(){t.hide()},this.show=function(){t.show()},this.position=function(e){t.css("top",e.top-5).css("left",e.left-5)},this.destroy=function(){t.remove()},this.focus=function(){i.focus()},this.loadValue=function(t){i.val(o=t[e.column.field]),i.select()},this.serializeValue=function(){return i.val()},this.applyValue=function(i,t){i[e.column.field]=t},this.isValueChanged=function(){return!(""===i.val()&&null==o)&&i.val()!=o},this.validate=function(){if(e.column.validator){var t=e.column.validator(i.val(),e);if(!t.valid)return t}return{valid:!0,msg:null}},this.init()}}}},
715: function _(e,n,r,t,c){e(706);n.exports={Formatters:{PercentComplete:function(e,n,r,t,c){return null==r||""===r?"-":r<50?"<span style='color:red;font-weight:bold;'>"+r+"%</span>":"<span style='color:green'>"+r+"%</span>"},PercentCompleteBar:function(e,n,r,t,c){return null==r||""===r?"":"<span class='percent-complete-bar' style='background:"+(r<30?"red":r<70?"silver":"green")+";width:"+r+"%'></span>"},YesNo:function(e,n,r,t,c){return r?"Yes":"No"},Checkmark:function(e,n,r,t,c){return r?"<img src='../images/tick.png'>":""},Checkbox:function(e,n,r,t,c){return'<img class="slick-edit-preclick" src="../images/'+(r?"CheckboxY":"CheckboxN")+'.png">'}}}},
716: function _(t,o,r,e,n){var a=t(704),l=t(706);o.exports={RemoteModel:function(){var t=50,o={length:0},r="",e=null,n=1,i=null,s=null,u=new l.Event,f=new l.Event;function c(){for(var t in o)delete o[t];o.length=0}function h(l,c){if(s){s.abort();for(var h=s.fromPage;h<=s.toPage;h++)o[h*t]=void 0}l<0&&(l=0),o.length>0&&(c=Math.min(c,o.length-1));for(var v=Math.floor(l/t),m=Math.floor(c/t);void 0!==o[v*t]&&v<m;)v++;for(;void 0!==o[m*t]&&v<m;)m--;if(v>m||v==m&&void 0!==o[v*t])f.notify({from:l,to:c});else{var g="http://octopart.com/api/v3/parts/search?apikey=68b25f31&include[]=short_description&show[]=uid&show[]=manufacturer&show[]=mpn&show[]=brand&show[]=octopart_url&show[]=short_description&q="+r+"&start="+v*t+"&limit="+((m-v)*t+t);null!=e&&(g+="&sortby="+e+(n>0?"+asc":"+desc")),null!=i&&clearTimeout(i),i=setTimeout((function(){for(var r=v;r<=m;r++)o[r*t]=null;u.notify({from:l,to:c}),s=a.jsonp({url:g,callbackParameter:"callback",cache:!0,success:d,error:function(){!function(t,o){alert("error loading pages "+t+" to "+o)}(v,m)}}),s.fromPage=v,s.toPage=m}),50)}}function d(t){var r=t.request.start,e=r+t.results.length;o.length=Math.min(parseInt(t.hits),1e3);for(var n=0;n<t.results.length;n++){var a=t.results[n].item;o[r+n]=a,o[r+n].index=r+n}s=null,f.notify({from:r,to:e})}return{data:o,clear:c,isDataLoaded:function(t,r){for(var e=t;e<=r;e++)if(null==o[e]||null==o[e])return!1;return!0},ensureData:h,reloadData:function(t,r){for(var e=t;e<=r;e++)delete o[e];h(t,r)},setSort:function(t,o){e=t,n=o,c()},setSearch:function(t){r=t,c()},onDataLoading:u,onDataLoaded:f}}}},
717: function _(e,s,t,o,l){var a=e(704),r=e(706);s.exports={GroupItemMetadataProvider:function(e){var s,t={checkboxSelect:!1,checkboxSelectCssClass:"slick-group-select-checkbox",checkboxSelectPlugin:null,groupCssClass:"slick-group",groupTitleCssClass:"slick-group-title",totalsCssClass:"slick-group-totals",groupFocusable:!0,totalsFocusable:!1,toggleCssClass:"slick-group-toggle",toggleExpandedCssClass:"expanded",toggleCollapsedCssClass:"collapsed",enableExpandCollapse:!0,groupFormatter:function(e,s,t,l,a,r){if(!o.enableExpandCollapse)return a.title;var c=15*a.level+"px";return(o.checkboxSelect?'<span class="'+o.checkboxSelectCssClass+" "+(a.selectChecked?"checked":"unchecked")+'"></span>':"")+"<span class='"+o.toggleCssClass+" "+(a.collapsed?o.toggleCollapsedCssClass:o.toggleExpandedCssClass)+"' style='margin-left:"+c+"'></span><span class='"+o.groupTitleCssClass+"' level='"+a.level+"'>"+a.title+"</span>"},totalsFormatter:function(e,s,t,o,l,a){return o.groupTotalsFormatter&&o.groupTotalsFormatter(l,o,a)||""},includeHeaderTotals:!1},o=a.extend(!0,{},t,e);function l(e,t){var l=a(e.target),c=this.getDataItem(t.row);if(c&&c instanceof r.Group&&l.hasClass(o.toggleCssClass)){var n=s.getRenderedRange();this.getData().setRefreshHints({ignoreDiffsBefore:n.top,ignoreDiffsAfter:n.bottom+1}),c.collapsed?this.getData().expandGroup(c.groupingKey):this.getData().collapseGroup(c.groupingKey),e.stopImmediatePropagation(),e.preventDefault()}if(c&&c instanceof r.Group&&l.hasClass(o.checkboxSelectCssClass)){c.selectChecked=!c.selectChecked,l.removeClass(c.selectChecked?"unchecked":"checked"),l.addClass(c.selectChecked?"checked":"unchecked");var i=s.getData().mapItemsToRows(c.rows);(c.selectChecked?o.checkboxSelectPlugin.selectRows:o.checkboxSelectPlugin.deSelectRows)(i)}}function c(e,t){if(o.enableExpandCollapse&&e.which==r.keyCode.SPACE){var l=this.getActiveCell();if(l){var a=this.getDataItem(l.row);if(a&&a instanceof r.Group){var c=s.getRenderedRange();this.getData().setRefreshHints({ignoreDiffsBefore:c.top,ignoreDiffsAfter:c.bottom+1}),a.collapsed?this.getData().expandGroup(a.groupingKey):this.getData().collapseGroup(a.groupingKey),e.stopImmediatePropagation(),e.preventDefault()}}}}return{init:function(e){(s=e).onClick.subscribe(l),s.onKeyDown.subscribe(c)},destroy:function(){s&&(s.onClick.unsubscribe(l),s.onKeyDown.unsubscribe(c))},getGroupRowMetadata:function(e){var s=e&&e.level;return{selectable:!1,focusable:o.groupFocusable,cssClasses:o.groupCssClass+" slick-group-level-"+s,formatter:o.includeHeaderTotals&&o.totalsFormatter,columns:{0:{colspan:o.includeHeaderTotals?"1":"*",formatter:o.groupFormatter,editor:null}}}},getTotalsRowMetadata:function(e){var s=e&&e.group&&e.group.level;return{selectable:!1,focusable:o.totalsFocusable,cssClasses:o.totalsCssClass+" slick-group-level-"+s,formatter:o.totalsFormatter,editor:null}},getOptions:function(){return o},setOptions:function(e){a.extend(!0,o,e)}}}}},
718: function _(t,e,i,r,s){var a;r();const n=t(411),o=t(182);class d extends n.LayoutDOMView{get child_models(){return[]}get provider(){return o.default_provider}async lazy_initialize(){await super.lazy_initialize(),"not_started"==this.provider.status&&await this.provider.fetch()}_after_layout(){super._after_layout(),"loading"==this.provider.status&&(this._has_finished=!1)}process_tex(t){if(null==this.provider.MathJax)return t;const e=this.provider.MathJax.find_tex(t),i=[];let r=0;for(const s of e)i.push(t.slice(r,s.start.n)),i.push(this.provider.MathJax.tex2svg(s.math,{display:s.display}).outerHTML),r=s.end.n;return r<t.length&&i.push(t.slice(r)),i.join("")}contains_tex_string(t){return null!=this.provider.MathJax&&this.provider.MathJax.find_tex(t).length>0}}i.WidgetView=d,d.__name__="WidgetView";class _ extends n.LayoutDOM{constructor(t){super(t)}}i.Widget=_,a=_,_.__name__="Widget",a.override({margin:5})},
719: function _(e,n,t,a,o){var c;a();const i=e(718),u=e(133),r=e(237);class s extends i.Widget{constructor(e){super(e)}}t.TableWidget=s,c=s,s.__name__="TableWidget",c.define((({Ref:e})=>({source:[e(u.ColumnDataSource),()=>new u.ColumnDataSource],view:[e(r.CDSView),()=>new r.CDSView]})))},
720: function _(t,e,r,i,o){var l;i();const s=t(699),d=t(696),a=t(40),n=t(21),u=t(334),f=t(51);class h extends f.Model{constructor(t){super(t)}toColumn(){return{id:(0,a.unique_id)(),field:this.field,name:this.title??this.field,width:this.width,formatter:this.formatter.doFormat.bind(this.formatter),model:this.editor,editor:this.editor.default_view,sortable:this.sortable,defaultSortAsc:"ascending"==this.default_sort,sorter:this.sorter}}}r.TableColumn=h,l=h,h.__name__="TableColumn",l.define((({Bool:t,Float:e,Str:r,Nullable:i,Ref:o})=>({field:[r],title:[i(r),null],width:[e,300],formatter:[o(s.CellFormatter),()=>new s.StringFormatter],editor:[o(d.CellEditor),()=>new d.StringEditor],sortable:[t,!0],default_sort:[n.Sort,"ascending"],visible:[t,!0],sorter:[i(o(u.Comparison)),null]})))},
721: function _(A,e,i,o,l){o(),i.default='.slick-header.ui-state-default,.slick-headerrow.ui-state-default,.slick-footerrow.ui-state-default,.slick-top-panel-scroller.ui-state-default,.slick-group-header.ui-state-default{width:100%;overflow:auto;position:relative;border-left:0px !important;}.slick-header.ui-state-default{overflow:inherit;}.slick-header::-webkit-scrollbar,.slick-headerrow::-webkit-scrollbar,.slick-footerrow::-webkit-scrollbar{display:none;}.slick-header-columns,.slick-headerrow-columns,.slick-footerrow-columns,.slick-group-header-columns{position:relative;white-space:nowrap;cursor:default;overflow:hidden;}.slick-header-column.ui-state-default,.slick-group-header-column.ui-state-default{position:relative;display:inline-block;box-sizing:content-box !important;overflow:hidden;-o-text-overflow:ellipsis;text-overflow:ellipsis;height:16px;line-height:16px;margin:0;padding:4px;border-right:1px solid silver;border-left:0px !important;border-top:0px !important;border-bottom:0px !important;float:left;}.slick-footerrow-column.ui-state-default{-o-text-overflow:ellipsis;text-overflow:ellipsis;margin:0;padding:4px;border-right:1px solid silver;border-left:0px;border-top:0px;border-bottom:0px;float:left;line-height:20px;vertical-align:middle;}.slick-headerrow-column.ui-state-default,.slick-footerrow-column.ui-state-default{padding:4px;}.slick-header-column-sorted{font-style:italic;}.slick-sort-indicator{display:inline-block;width:8px;height:5px;margin-left:4px;margin-top:6px;float:left;}.slick-sort-indicator-numbered{display:inline-block;width:8px;height:5px;margin-left:4px;margin-top:0;line-height:20px;float:left;font-family:Arial;font-style:normal;font-weight:bold;color:#6190CD;}.slick-sort-indicator-desc{background:url(images/sort-desc.gif);}.slick-sort-indicator-asc{background:url(images/sort-asc.gif);}.slick-resizable-handle{position:absolute;font-size:0.1px;display:block;cursor:col-resize;width:9px;right:-5px;top:0;height:100%;z-index:1;}.slick-sortable-placeholder{background:silver;}.grid-canvas{position:relative;outline:0;}.slick-row.ui-widget-content,.slick-row.ui-state-active{position:absolute;border:0px;width:100%;}.slick-cell,.slick-headerrow-column,.slick-footerrow-column{position:absolute;border:1px solid transparent;border-right:1px dotted silver;border-bottom-color:silver;overflow:hidden;-o-text-overflow:ellipsis;text-overflow:ellipsis;vertical-align:middle;z-index:1;padding:1px 2px 2px 1px;margin:0;white-space:nowrap;cursor:default;}.slick-cell,.slick-headerrow-column{border-bottom-color:silver;}.slick-footerrow-column{border-top-color:silver;}.slick-group-toggle{display:inline-block;}.slick-cell.highlighted{background:lightskyblue;background:rgba(0, 0, 255, 0.2);-webkit-transition:all 0.5s;-moz-transition:all 0.5s;-o-transition:all 0.5s;transition:all 0.5s;}.slick-cell.flashing{border:1px solid red !important;}.slick-cell.editable{z-index:11;overflow:visible;background:white;border-color:black;border-style:solid;}.slick-cell:focus{outline:none;}.slick-reorder-proxy{display:inline-block;background:blue;opacity:0.15;cursor:move;}.slick-reorder-guide{display:inline-block;height:2px;background:blue;opacity:0.7;}.slick-selection{z-index:10;position:absolute;border:2px dashed black;}.slick-pane{position:absolute;outline:0;overflow:hidden;width:100%;}.slick-pane-header{display:block;}.slick-header{overflow:hidden;position:relative;}.slick-headerrow{overflow:hidden;position:relative;}.slick-top-panel-scroller{overflow:hidden;position:relative;}.slick-top-panel{width:10000px;}.slick-viewport{position:relative;outline:0;width:100%;}.slick-header-columns{background:url(\'images/header-columns-bg.gif\') repeat-x center bottom;border-bottom:1px solid silver;}.slick-header-column{background:url(\'images/header-columns-bg.gif\') repeat-x center bottom;border-right:1px solid silver;}.slick-header-column:hover,.slick-header-column-active{background:white url(\'images/header-columns-over-bg.gif\') repeat-x center bottom;}.slick-headerrow{background:#fafafa;}.slick-headerrow-column{background:#fafafa;border-bottom:0;height:100%;}.slick-row.ui-state-active{background:#F5F7D7;}.slick-row{position:absolute;background:white;border:0px;line-height:20px;}.slick-row.selected{z-index:10;background:#DFE8F6;}.slick-cell{padding-left:4px;padding-right:4px;}.slick-group{border-bottom:2px solid silver;}.slick-group-toggle{width:9px;height:9px;margin-right:5px;}.slick-group-toggle.expanded{background:url(images/collapse.gif) no-repeat center center;}.slick-group-toggle.collapsed{background:url(images/expand.gif) no-repeat center center;}.slick-group-totals{color:gray;background:white;}.slick-group-select-checkbox{width:13px;height:13px;margin:3px 10px 0 0;display:inline-block;}.slick-group-select-checkbox.checked{background:url(images/GrpCheckboxY.png) no-repeat center center;}.slick-group-select-checkbox.unchecked{background:url(images/GrpCheckboxN.png) no-repeat center center;}.slick-cell.selected{background-color:beige;}.slick-cell.active{border-color:gray;border-style:solid;}.slick-sortable-placeholder{background:silver !important;}.slick-row.odd{background:#fafafa;}.slick-row.ui-state-active{background:#F5F7D7;}.slick-row.loading{opacity:0.5;}.slick-cell.invalid{border-color:red;-moz-animation-duration:0.2s;-webkit-animation-duration:0.2s;-moz-animation-name:slickgrid-invalid-hilite;-webkit-animation-name:slickgrid-invalid-hilite;}@-moz-keyframes slickgrid-invalid-hilite{from{box-shadow:0 0 6px red;}to{box-shadow:none;}}@-webkit-keyframes slickgrid-invalid-hilite{from{box-shadow:0 0 6px red;}to{box-shadow:none;}}.slick-column-name,.slick-sort-indicator{display:inline-block;float:left;margin-bottom:100px;}.slick-header-button{display:inline-block;float:right;vertical-align:top;margin:1px;margin-bottom:100px;height:15px;width:15px;background-repeat:no-repeat;background-position:center center;cursor:pointer;}.slick-header-button-hidden{width:0;-webkit-transition:0.2s width;-ms-transition:0.2s width;transition:0.2s width;}.slick-header-column:hover > .slick-header-button{width:15px;}.slick-header-menubutton{position:absolute;right:0;top:0;bottom:0;width:14px;background-repeat:no-repeat;background-position:left center;background-image:url(../images/down.gif);cursor:pointer;display:none;border-left:thin ridge silver;}.slick-header-column:hover > .slick-header-menubutton,.slick-header-column-active .slick-header-menubutton{display:inline-block;}.slick-header-menu{position:absolute;display:inline-block;margin:0;padding:2px;cursor:default;}.slick-header-menuitem{list-style:none;margin:0;padding:0;cursor:pointer;display:block;}.slick-header-menuicon{display:inline-block;width:16px;height:16px;vertical-align:middle;margin-right:4px;background-repeat:no-repeat;background-position:center center;}.slick-header-menucontent{display:inline-block;vertical-align:middle;}.slick-header-menuitem-disabled{color:silver;}.slick-header-menuitem-hidden{display:none;}.slick-header-menuitem.slick-header-menuitem-divider{cursor:default;border:none;overflow:hidden;padding:0;height:1px;margin:8px 2px;background-color:#cecece;}.slick-header-menuitem-divider.slick-header-menuitem:hover{background-color:#cecece;}.slick-columnpicker{border:1px solid #718BB7;background:#f0f0f0;padding:6px;-moz-box-shadow:2px 2px 2px silver;-webkit-box-shadow:2px 2px 2px silver;box-shadow:2px 2px 2px silver;min-width:150px;cursor:default;position:absolute;z-index:20;overflow:auto;resize:both;}.slick-columnpicker > .close{float:right;}.slick-columnpicker .title{font-size:16px;width:60%;border-bottom:solid 1px #d6d6d6;margin-bottom:10px;}.slick-columnpicker li{list-style:none;margin:0;padding:0;background:none;}.slick-columnpicker input{margin:4px;}.slick-columnpicker li a{display:block;padding:4px;font-weight:bold;}.slick-columnpicker li a:hover{background:white;}.slick-columnpicker-list li.hidden{display:none;}.slick-pager{width:100%;height:26px;border:1px solid gray;border-top:0;background:url(\'../images/header-columns-bg.gif\') repeat-x center bottom;vertical-align:middle;}.slick-pager .slick-pager-status{display:inline-block;padding:6px;}.slick-pager .ui-icon-container{display:inline-block;margin:2px;border-color:gray;}.slick-pager .slick-pager-nav{display:inline-block;float:left;padding:2px;}.slick-pager .slick-pager-settings{display:block;float:right;padding:2px;}.slick-pager .slick-pager-settings *{vertical-align:middle;}.slick-pager .slick-pager-settings a{padding:2px;text-decoration:underline;cursor:pointer;}.slick-header-columns{border-bottom:1px solid silver;background-image:none;}.slick-header-column{border-right:1px solid transparent;background-image:none;}.slick-header-column:last-of-type{border-right-color:transparent;}.slick-header-column:hover,.slick-header-column-active{background-color:#F0F8FF;background-image:none;}.slick-header-columns{background-image:none;}.slick-header-column{background-image:none;}.slick-header-column:hover,.slick-header-column-active{background-image:none;}.slick-group-toggle.expanded{background-image:url("data:image/gif;base64,R0lGODlhCQAJAPcAAAFGeoCAgNXz/+v5/+v6/+z5/+36//L7//X8//j9/wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACwAAAAACQAJAAAIMwADCBxIUIDBgwIEChgwwECBAgQUFjBAkaJCABgxGlB4AGHCAAIQiBypEEECkScJqgwQEAA7");}.slick-group-toggle.collapsed{background-image:url("data:image/gif;base64,R0lGODlhCQAJAPcAAAFGeoCAgNXz/+v5/+v6/+z5/+36//L7//X8//j9/wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACwAAAAACQAJAAAIOAADCBxIUIDBgwIEChgwAECBAgQUFjAAQIABAwoBaNSIMYCAAwIqGlSIAEHFkiQTIBCgkqDLAAEBADs=");}.slick-group-select-checkbox.checked{background-image:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAIAAACQKrqGAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAOwQAADsEBuJFr7QAAABl0RVh0U29mdHdhcmUAcGFpbnQubmV0IDQuMC4xNkRpr/UAAAEcSURBVChTjdI9S8NQFAbg/raQXVwCRRFE7GK7OXTwD+ikk066VF3a0ja0hQTyQdJrwNq0zrYSQRLEXMSWSlCIb8glqRcFD+9yz3nugXwU4n9XQqMoGjj36uBJsTwuaNo3EwBG4Yy7pe7Gv8YcvhJCGFVsjxsjxujj6OTSGlHv+U2WZUZbPWKOv1ZjT5a7pbIoiptbO5b73mwrjHa1B27l8VlTEIS1damlTnEE+EEN9/P8WrfH81qdAIGeXvTTmzltdCy46sEhxpKUINReZR9NnqZbr9puugxV3NjWh/k74WmmEdWhmUNy2jNmWRc6fZTVADCqao52u+DGWTACYNT3fRxwtatPufTNR4yCIGAUn5hS+vJHhWGY/ANx/A3tvdv+1tZmuwAAAABJRU5ErkJggg==");}.slick-group-select-checkbox.unchecked{background-image:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAIAAACQKrqGAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAOwQAADsEBuJFr7QAAABl0RVh0U29mdHdhcmUAcGFpbnQubmV0IDQuMC4xNkRpr/UAAACXSURBVChT1dIxC4MwEAXg/v8/VOhQVDBNakV0KA6pxS4JhWRSIYPEJxwdDi1de7wleR+3JIf486w0hKCKRpSvvOhZcCmvNQBRuKqdah03U7UjNNH81rOaBYDo8SQaPX8JANFEaLaGBeAPaaY61rGksiN6TmR5H1j9CSoAosYYHLA7vTxYMvVEZa0liif23r93xjm3/oEYF8PiDn/I2FHCAAAAAElFTkSuQmCC");}.slick-sort-indicator-desc{background-image:url("data:image/gif;base64,R0lGODlhDQAFAIcAAGGQzUD/QOPu+wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACH5BAMAAAEALAAAAAANAAUAAAgeAAUAGEgQgIAACBEKLHgwYcKFBh1KFNhQosOKEgMCADs=");}.slick-sort-indicator-asc{background-image:url("data:image/gif;base64,R0lGODlhDQAFAIcAAGGQzUD/QOPu+wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACH5BAMAAAEALAAAAAANAAUAAAgbAAMIDABgoEGDABIeRJhQ4cKGEA8KmEiRosGAADs=");}.slick-header-menubutton{background-image:url("data:image/gif;base64,R0lGODlhDgAOAIABADtKYwAAACH5BAEAAAEALAAAAAAOAA4AAAISjI+py+0PHZgUsGobhTn6DxoFADs=");}.detailView-toggle.expand{background-image:url("data:image/gif;base64,R0lGODlhBQANAHAAACH5BAEAAAMALAAAAAAFAA0AgePu+wAAAGGQzQAAAAIQxD4gqJsCHxRttaMY2rz3AgA7");}.detailView-toggle.collapse{background-image:url("data:image/gif;base64,R0lGODlhDQAFAIcAAGGQzUD/QOPu+wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACH5BAMAAAEALAAAAAANAAUAAAgeAAUAGEgQgIAACBEKLHgwYcKFBh1KFNhQosOKEgMCADs=");}.slick-pager{background-image:none;}'},
722: function _(t,e,s,r,a){var i;r();const n=t(709),{Avg:u,Min:o,Max:c,Sum:g}=n.Data.Aggregators,l=t(51);class _ extends l.Model{constructor(t){super(t)}}s.RowAggregator=_,i=_,_.__name__="RowAggregator",i.define((({Str:t})=>({field_:[t,""]})));const m=new u;class h extends _{constructor(){super(...arguments),this.key="avg",this.init=m.init,this.accumulate=m.accumulate,this.storeResult=m.storeResult}}s.AvgAggregator=h,h.__name__="AvgAggregator";const A=new o;class R extends _{constructor(){super(...arguments),this.key="min",this.init=A.init,this.accumulate=A.accumulate,this.storeResult=A.storeResult}}s.MinAggregator=R,R.__name__="MinAggregator";const x=new c;class d extends _{constructor(){super(...arguments),this.key="max",this.init=x.init,this.accumulate=x.accumulate,this.storeResult=x.storeResult}}s.MaxAggregator=d,d.__name__="MaxAggregator";const M=new g;class w extends _{constructor(){super(...arguments),this.key="sum",this.init=M.init,this.accumulate=M.accumulate,this.storeResult=M.storeResult}}s.SumAggregator=w,w.__name__="SumAggregator"},
723: function _(t,e,s,o,r){var i,a;o();const l=t(63),n=t(9),u=t(8),g=t(12),c=t(709),p=t(697),d=t(702),h=t(133),f=t(722),m=t(51);function w(t,e,s,o,r){const{collapsed:i,level:a,title:n}=r,u=(0,l.span)({class:"slick-group-toggle "+(i?"collapsed":"expanded"),style:{"margin-left":15*a+"px"}}),g=(0,l.span)({class:"slick-group-title"},n);return`${u.outerHTML}${g.outerHTML}`}function v(t,e){const s=this.getDataItem(e.row);s instanceof c.Group&&t.target.classList.contains("slick-group-toggle")&&(s.collapsed?this.getData().expandGroup(s.groupingKey):this.getData().collapseGroup(s.groupingKey),t.stopImmediatePropagation(),t.preventDefault(),this.invalidate(),this.render())}class _ extends m.Model{constructor(t){super(t)}get comparer(){return(t,e)=>t.value===e.value?0:t.value>e.value?1:-1}}s.GroupingInfo=_,i=_,_.__name__="GroupingInfo",i.define((({Bool:t,Str:e,List:s,Ref:o})=>({getter:[e,""],aggregators:[s(o(f.RowAggregator)),[]],collapsed:[t,!1]})));class G extends d.TableDataProvider{constructor(t,e,s,o){super(t,e),this.columns=s,this.groupingInfos=[],this.groupingDelimiter=":|:",this.target=o}setGrouping(t){this.groupingInfos=t,this.toggledGroupsByLevel=t.map((()=>({}))),this.refresh()}extractGroups(t,e){const s=[],o=new Map,r=null!=e?e.level+1:0,{comparer:i,getter:a}=this.groupingInfos[r];for(const i of t){const t=(0,n.dict)(this.source.data).get(a);(0,g.assert)(null!=t);const l=t[i];let u=o.get(l);if(null==u){const t=null!=e?`${e.groupingKey}${this.groupingDelimiter}${l}`:`${l}`;u=Object.assign(new c.Group,{value:l,level:r,groupingKey:t}),s.push(u),o.set(l,u)}u.rows.push(i)}if(r<this.groupingInfos.length-1)for(const t of s)t.groups=this.extractGroups(t.rows,t);return s.sort(i),s}calculateTotals(t,e){const s={avg:{},max:{},min:{},sum:{}},o=(0,n.dict)(this.source.data),r=[...o.keys()],i=t.rows.map((t=>r.reduce(((e,s)=>({...e,[s]:o.get(s)[t]})),{})));for(const t of e){t.init();for(const e of i)t.accumulate(e);t.storeResult(s)}return s}addTotals(t,e=0){const{aggregators:s,collapsed:o}=this.groupingInfos[e],r=this.toggledGroupsByLevel[e];for(const i of t)(0,u.is_nullish)(i.groups)||this.addTotals(i.groups,e+1),0!=s.length&&0!=i.rows.length&&(i.totals=this.calculateTotals(i,s)),i.collapsed=o!==r[i.groupingKey],i.title=i.value?`${i.value}`:""}flattenedGroupedRows(t,e=0){const s=[];for(const o of t)if(s.push(o),!o.collapsed){const t=(0,u.is_nullish)(o.groups)?o.rows:this.flattenedGroupedRows(o.groups,e+1);s.push(...t)}return s}refresh(){const t=this.extractGroups(this.view.indices),e=(0,n.dict)(this.source.data).get(this.columns[0].field);(0,g.assert)(null!=e),0!=t.length&&(this.addTotals(t),this.rows=this.flattenedGroupedRows(t),this.target.data={row_indices:this.rows.map((t=>t instanceof c.Group?t.rows:t)),labels:this.rows.map((t=>t instanceof c.Group?t.title:e[t]))})}getLength(){return this.rows.length}getItem(t){const e=this.rows[t],s=(0,n.dict)(this.source.data);return e instanceof c.Group?e:[...s.keys()].reduce(((t,o)=>({...t,[o]:s.get(o)[e]})),{[p.DTINDEX_NAME]:e})}getItemMetadata(t){const e=this.rows[t],s=this.columns.slice(1),o=e instanceof c.Group?this.groupingInfos[e.level].aggregators:[];return e instanceof c.Group?{selectable:!1,focusable:!1,cssClasses:"slick-group",columns:[{formatter:w},...s.map((function(t){const{field:e,formatter:s}=t,r=o.find((({field_:t})=>t===e));if(null!=r){const{key:t}=r;return{formatter:(o,r,i,a,l)=>null!=s?s(o,r,l.totals[t][e],a,l):""}}return{}}))]}:{}}collapseGroup(t){const e=t.split(this.groupingDelimiter).length-1;this.toggledGroupsByLevel[e][t]=!this.groupingInfos[e].collapsed,this.refresh()}expandGroup(t){const e=t.split(this.groupingDelimiter).length-1;this.toggledGroupsByLevel[e][t]=this.groupingInfos[e].collapsed,this.refresh()}}s.DataCubeProvider=G,G.__name__="DataCubeProvider";class b extends d.DataTableView{_render_table(){const t={enableCellNavigation:!1!==this.model.selectable,enableColumnReorder:!1,autosizeColsMode:this.autosize,multiColumnSort:!1,editable:this.model.editable,autoEdit:this.model.auto_edit,rowHeight:this.model.row_height},e=this.model.columns.map((t=>t.toColumn()));var s,o;e[0].formatter=(s=e[0].formatter,o=this.model.grouping.length,(t,e,r,i,a)=>{const n=(0,l.span)({class:"slick-group-toggle",style:{"margin-left":15*(o??0)+"px"}}),u=null!=s?s(t,e,r,i,a):`${r}`;return`${n.outerHTML}${u.replace(/^<div/,"<span").replace(/div>$/,"span>")}`}),delete e[0].editor,this.data=new G(this.model.source,this.model.view,e,this.model.target),this.data.setGrouping(this.model.grouping),this.el.style.width=`${this.model.width}px`,this.grid=new c.Grid(this.wrapper_el,this.data,e,t),this.grid.onClick.subscribe(v)}}s.DataCubeView=b,b.__name__="DataCubeView";class D extends d.DataTable{constructor(t){super(t)}}s.DataCube=D,a=D,D.__name__="DataCube",a.prototype.default_view=b,a.define((({List:t,Ref:e})=>({grouping:[t(e(_)),[]],target:[e(h.ColumnDataSource)]})))},
}, 694, {"models/widgets/tables/main":694,"models/widgets/tables/index":695,"models/widgets/tables/cell_editors":696,"models/widgets/tables/definitions":697,"styles/widgets/tables.css":698,"models/widgets/tables/cell_formatters":699,"models/widgets/tables/data_table":702,"models/widgets/widget":718,"models/widgets/tables/table_widget":719,"models/widgets/tables/table_column":720,"styles/widgets/slickgrid.css":721,"models/widgets/tables/row_aggregators":722,"models/widgets/tables/data_cube":723}, {});});
