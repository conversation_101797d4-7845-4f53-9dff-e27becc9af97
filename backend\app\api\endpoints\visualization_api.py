from fastapi import APIRouter, Depends, HTTPException, Query, Request
from fastapi.params import Path
from sqlalchemy.orm import Session
from typing import List, Optional
import os
from fastapi.responses import HTMLResponse
from bokeh.embed import server_document
from bokeh.server.server import Server
from bokeh.application import Application
from bokeh.application.handlers.function import FunctionHandler
from fastapi.templating import Jinja2Templates

from app.database import get_db
from app.models import RawFile, ParquetFile
from app.core.dependencies import get_templates
from app.core import settings, logger
from app.core.bokeh_server import bokeh_manager
from app.visualization import TestBokehApp
# from app.pages.visualization_page import modify_doc

router = APIRouter()

# 初始化可视化页面模板
visualization_templates = Jinja2Templates(
    directory=str(settings.VISUALIZATION_PAGES_DIR))

@router.get("/files")
async def get_visualization_files(db: Session = Depends(get_db)):
    """获取可用于可视化的文件列表"""
    try:
        # 获取已处理完成的文件
        processed_files = db.query(ParquetFile).filter(
            ParquetFile.status == "completed"
        ).all()
        
        return {
            "status": "success",
            "count": len(processed_files),
            "files": [file.to_dict() for file in processed_files]
        }
    except Exception as e:
        logger.error(f"获取可视化文件列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取文件列表失败: {str(e)}")

@router.get("/preview/{file_id}")
async def preview_file_data(
    file_id: int = Path(..., title="文件ID"),
    limit: int = Query(100, title="预览行数"),
    db: Session = Depends(get_db)
):
    """预览文件数据"""
    try:
        # 查找对应的Parquet文件
        file = db.query(ParquetFile).filter(ParquetFile.id == file_id).first()
        if not file:
            raise HTTPException(status_code=404, detail=f"ID为{file_id}的文件不存在")
        
        # 验证文件状态
        if file.status != "completed":
            raise HTTPException(status_code=400, detail=f"文件尚未处理完成，当前状态: {file.status}")
        
        # 这里简单返回文件信息，实际实现中应该读取Parquet文件内容
        return {
            "status": "success",
            "file": file.to_dict(),
            "message": "此处应返回文件预览数据，需要实现具体的Parquet文件读取逻辑"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"预览文件数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"预览数据失败: {str(e)}")

@router.get("/schema/{file_id}")
async def get_file_schema(
    file_id: int = Path(..., title="文件ID"),
    db: Session = Depends(get_db)
):
    """获取文件结构信息"""
    try:
        # 查找对应的Parquet文件
        file = db.query(ParquetFile).filter(ParquetFile.id == file_id).first()
        if not file:
            raise HTTPException(status_code=404, detail=f"ID为{file_id}的文件不存在")
        
        # 返回文件结构信息
        return {
            "status": "success",
            "file_id": file_id,
            "filename": file.filename,
            "schema": file.schema
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文件结构失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取文件结构失败: {str(e)}")

@router.get("/page")
async def visualization_page(request: Request, templates=Depends(get_templates)):
    """可视化页面"""
    return templates.TemplateResponse("visualization.html", {"request": request})


@router.get("/test", response_class=HTMLResponse)
async def test_visualization(request: Request, templates=Depends(get_templates)):
    """测试可视化页面"""
    try:
        # 确保Bokeh服务器正在运行
        if not bokeh_manager.is_running:
            bokeh_manager.start()

        # 获取Bokeh服务器URL
        bokeh_url = f"http://{settings.HOST}:{settings.BOKEH_PORT}/bokeh"

        # 嵌入Bokeh应用
        script = server_document(bokeh_url + "/test_bokeh")

        # 渲染模板
        return templates.TemplateResponse(
            "visualization.html",
            {
                "request": request,
                "script": script,
                "title": "测试可视化"
            }
        )
    except Exception as e:
        logger.error(f"加载测试可视化页面时出错: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"加载测试可视化页面失败: {str(e)}"
        )
