"""
燃烧与喷淋计算系统 v3.6
主要修复：
1. NASA多项式系数修正
2. Reactants属性兼容性修复
3. 化学式解析器实现
4. 增强错误处理
"""

import cantera as ct
import numpy as np
import pandas as pd
import traceback
from dataclasses import dataclass
from typing import Dict, List, Optional
from collections import defaultdict

# 使用config.py中的logger
from app.core.config import logger

# ----------------------
# 燃料特性数据库（修正NASA系数）
# ----------------------
FUEL_DATABASE = {
    'CH4': {
        'formula': 'CH4',
        'LHV': 50100,
        'atoms': {'C': 1, 'H': 4},
        'thermo': [
            [300.0, 1000.0],  # 低温区温度范围
            [1000.0, 5000.0],  # 高温区温度范围
            # 高温区系数 (7个)
            [3.5, 0.0, -0.2e-5, 0.4e-9, 0.02e-12, -0.001e-15, 0.0],
            # 低温区系数 (7个)
            [3.5, 0.0, -0.2e-5, 0.4e-9, 0.02e-12, -0.001e-15, 0.0]
        ],
        'profile': {
            'phi_range': (0.5, 2.5),
            'T_max': 2220,
            'stoich_phi': 1.0
        }
    },
    'C12H23': {
        'formula': 'C12H23',
        'LHV': 42800,
        'atoms': {'C': 12, 'H': 23},
        'thermo': [
            [300.0, 1000.0],  # 低温区温度范围
            [1000.0, 5000.0],  # 高温区温度范围
            # 高温区系数 (7个)
            [2.8, 0.1e-3, -0.5e-6, 0.1e-9, 0.05e-12, -0.002e-15, 0.0],
            # 低温区系数 (7个)
            [2.8, 0.1e-3, -0.5e-6, 0.1e-9, 0.05e-12, -0.002e-15, 0.0]
        ],
        'profile': {
            'phi_range': (0.4, 3.0),
            'T_max': 2100,
            'stoich_phi': 1.02
        }
    }
}


@dataclass
class Fuel:
    """燃料数据类"""
    name: str
    formula: str
    LHV: Optional[float] = None
    custom: bool = False


class CombustionSystem:
    def __init__(self, mechanism: str = 'gri30.yaml'):
        """
        初始化燃烧系统
        mechanism: 化学机理文件路径
        """
        self.mechanism = mechanism  # 保存机理文件名
        self.gas = ct.Solution(mechanism)
        self._fuel_cache = {}
        self._init_fuel_database()

    def _init_fuel_database(self):
        """安全初始化燃料数据库"""
        for fuel_name, fuel_data in FUEL_DATABASE.items():
            try:
                self._add_fuel(fuel_data)
            except Exception as e:
                logger.error(f"初始化燃料 {fuel_name} 失败: {str(e)}")
                continue

    def _add_fuel(self, fuel_data: dict):
        """添加单个燃料"""
        try:
            logger.debug(f"尝试添加燃料 {fuel_data['formula']}")

            # 检查燃料是否已在数据库中
            if fuel_data['formula'] in self.gas.species_names:
                logger.info(f"燃料 {fuel_data['formula']} 已在Cantera数据库中")
                return

            # 验证NASA系数
            if len(fuel_data['thermo'][2]) != 7 or len(fuel_data['thermo'][3]) != 7:
                raise ValueError("NASA多项式必须包含14个系数（7个高温系数 + 7个低温系数）")

            # 创建NASA多项式
            nasa = ct.NasaPoly2(
                T_low=fuel_data['thermo'][0][0],  # 低温区下限
                T_high=fuel_data['thermo'][1][1],  # 高温区上限
                P_ref=ct.one_atm,  # 参考压力
                coeffs=[
                    fuel_data['thermo'][1][0],  # 中间温度点
                    *fuel_data['thermo'][2],    # 高温区系数
                    *fuel_data['thermo'][3]     # 低温区系数
                ]
            )

            # 创建物种
            species = ct.Species(
                name=fuel_data['formula'],
                atoms=fuel_data['atoms'],
                thermo=nasa
            )

            # 添加到气相
            self.gas.add_species(species)
            logger.info(f"成功添加燃料: {fuel_data['formula']}")

        except Exception as e:
            logger.error(f"添加燃料 {fuel_data['formula']} 失败: {str(e)}")
            raise

    def analyze_combustion(self, fuel: Fuel, T_target: float, P: float) -> Dict:
        """执行燃烧分析"""
        try:
            # 每次分析使用新实例
            self.gas = ct.Solution(self.mechanism)
            self._init_fuel_database()

            # 验证燃料是否存在
            if fuel.formula not in self.gas.species_names:
                raise ValueError(f"燃料{fuel.formula}未成功添加到机理中")

            # 核心计算逻辑
            return self._core_calculation(fuel, T_target, P)

        except Exception as e:
            print(f"分析失败: {str(e)}")
            traceback.print_exc()
            return {}

    def _core_calculation(self, fuel: Fuel, T_target: float, P: float) -> Dict:
        """核心计算流程"""
        # 初始化反应物
        self.gas.TPX = 300, P, {fuel.formula: 1, 'O2': 1, 'N2': 3.76}

        # 计算当量比
        profile = FUEL_DATABASE.get(fuel.formula, {}).get('profile', {})
        phi = self._find_phi(T_target, fuel.formula, profile)

        # 计算流量
        AF_stoich = 1 / \
            self.gas.stoich_air_fuel_ratio(fuel.formula, 'O2')  # 修正参数
        m_fuel = 1 / (1 + AF_stoich/phi)

        # 获取物性
        return {
            '当量比': phi,
            '燃料流量 (kg/s)': m_fuel,
            '空气流量 (kg/s)': 1 - m_fuel,
            '总流量 (kg/s)': 1.0,
            '温度 (K)': self.gas.T,
            '分子量 (kg/kmol)': self.gas.mean_molecular_weight,
            '比热比': self.gas.cp / self.gas.cv,
            '产物组成': {sp: f"{self.gas[sp].X[0]:.2%}"
                     for sp in self.gas.species_names
                     if self.gas[sp].X[0] > 0.01}
        }

    def _parse_formula(self, formula: str) -> Dict[str, int]:
        """化学式解析器"""
        atoms = defaultdict(int)
        element = ''
        count_str = ''

        for char in formula:
            if char.isupper():
                if element:
                    count = int(count_str) if count_str else 1
                    atoms[element] += count
                element = char
                count_str = ''
            elif char.islower():
                element += char
            elif char.isdigit():
                count_str += char

        if element:
            count = int(count_str) if count_str else 1
            atoms[element] += count

        return dict(atoms)

    def _validate_fuel(self, fuel: Fuel):
        """燃料验证与预处理"""
        # 检查LHV值
        if fuel.LHV is None and fuel.name != '空气':
            raise ValueError(f"燃料{fuel.name}需要提供LHV值")

        if fuel.formula not in self.gas.species_names:
            self._add_custom_fuel(fuel)

    def _add_custom_fuel(self, fuel: Fuel):
        """添加自定义燃料到机理"""
        if fuel.formula in self._fuel_cache:
            return

        try:
            # 标准NASA7系数格式
            coeffs_lo = [300.0, 1000.0, 10.0,
                         0.00, 0.00, 0.00, 0.00, 0.00, 0.00]
            coeffs_hi = [1000.0, 5000.0, 10.0,
                         0.00, 0.00, 0.00, 0.00, 0.00, 0.00]

            # 创建NASA多项式
            nasa = ct.NasaPoly2(
                Tmin=coeffs_lo[0],
                Tmax=coeffs_hi[1],
                coeffs=coeffs_lo[2:] + coeffs_hi[2:]
            )

            # 创建物种
            spec = ct.Species(
                name=fuel.formula,
                atoms=self._parse_formula(fuel.formula),
                thermo=nasa
            )

            # 添加到气相
            self.gas.add_species(spec)
            self._fuel_cache[fuel.formula] = fuel.LHV
            print(f"成功添加燃料: {fuel.name}")

        except Exception as e:
            print(f"燃料添加错误: {str(e)}")
            raise

    def _get_profile(self, fuel: Fuel) -> dict:
        """获取燃料特性参数"""
        # 默认参数
        default_profile = {
            'phi_range': (0.3, 5.0),  # 当量比范围
            'T_max': 2500,            # 最大温度
            'stoich_phi': 1.0         # 化学计量当量比
        }

        # 从数据库获取或使用默认值
        return FUEL_DATABASE.get(fuel.formula, {}).get('profile', default_profile)

    def _find_phi(self, T_target: float, fuel_formula: str, profile: dict) -> float:
        """改进的当量比搜索算法，优先考虑贫燃燃烧"""
        # 获取参数
        phi_min, phi_max = profile.get('phi_range', (0.3, 5.0))
        stoich_phi = profile.get('stoich_phi', 1.0)
        T_max = profile.get('T_max', 2500)

        if T_target > T_max * 1.1:
            raise ValueError(f"目标温度{T_target}K超过理论极限{T_max}K")

        print(f"\n开始搜索当量比，目标温度: {T_target}K")
        print(f"化学计量当量比: {stoich_phi:.3f}")
        print(f"搜索范围: [{phi_min:.3f}, {phi_max:.3f}]")

        # 优先从贫燃侧开始搜索
        phi = max(0.3, stoich_phi * 0.8)  # 从贫燃侧开始
        best_phi, min_error = phi, float('inf')
        best_phi_rich, min_error_rich = None, float('inf')  # 记录富燃侧的最佳结果

        # 准备迭代数据
        iterations = []
        prev_phi = None
        prev_T = None
        prev_error = None

        for i in range(100):
            try:
                # 每次迭代都创建新的气体对象
                self.gas = ct.Solution(self.mechanism)
                self.gas.TPX = 300, ct.one_atm, {
                    fuel_formula: 1, 'O2': 1, 'N2': 3.76}

                self.gas.set_equivalence_ratio(
                    phi, {fuel_formula: 1.0}, {'O2': 1.0, 'N2': 3.76})
                self.gas.equilibrate('HP')
                current_T = self.gas.T
                error = current_T - T_target

                # 检查是否与上一次迭代结果相同
                if (prev_phi is not None and
                    abs(prev_phi - phi) < 1e-6 and
                    abs(prev_T - current_T) < 1e-6 and
                        abs(prev_error - error) < 1e-6):
                    print("\n连续两次迭代结果相同，停止搜索")
                    break

                # 记录每次迭代的结果
                iterations.append({
                    '迭代': i+1,
                    '当量比': f"{phi:.4f}",
                    '燃烧类型': '贫燃' if phi <= stoich_phi else '富燃',
                    '温度(K)': f"{current_T:.1f}",
                    '误差(K)': f"{error:.1f}",
                    '状态': '继续搜索'
                })

                # 保存当前迭代结果用于下一次比较
                prev_phi = phi
                prev_T = current_T
                prev_error = error

                if abs(error) < max(1.0, 0.001*T_target):
                    if phi <= stoich_phi:
                        iterations[-1]['状态'] = '找到贫燃解'
                        self._print_iteration_table(iterations)
                        return phi
                    else:
                        iterations[-1]['状态'] = '找到富燃解，继续搜索'
                        if abs(error) < abs(min_error_rich):
                            best_phi_rich, min_error_rich = phi, error
                        phi = max(0.3, stoich_phi * 0.8)
                        continue

                # 更新最佳解
                if abs(error) < abs(min_error):
                    if phi <= stoich_phi:
                        best_phi, min_error = phi, error
                        iterations[-1]['状态'] = '更新最佳贫燃解'
                    else:
                        if abs(error) < abs(min_error_rich):
                            best_phi_rich, min_error_rich = phi, error
                            iterations[-1]['状态'] = '更新最佳富燃解'

                # 计算步长
                delta = 0.01
                T_perturbed = self._perturb_phi(phi*(1+delta), fuel_formula)
                dT_dphi = (T_perturbed - current_T) / (phi*delta)

                # 改进步长计算
                if abs(dT_dphi) < 1e-3:
                    # 如果温度对当量比不敏感，使用固定步长
                    step = 0.1 * np.sign(error) * phi
                else:
                    # 使用牛顿法计算步长，但限制最大步长
                    step = -error / dT_dphi
                    max_step = 0.2 * phi
                    step = np.clip(step, -max_step, max_step)

                # 确保步长不会太小
                min_step = 0.01 * phi
                if abs(step) < min_step:
                    step = min_step * np.sign(step)

                # 更新搜索范围
                if error > 0:  # 温度过高，需要减小当量比
                    phi_max = min(phi_max, phi)
                else:  # 温度过低，需要增大当量比
                    phi_min = max(phi_min, phi)

                # 更新当量比
                new_phi = phi + step
                new_phi = np.clip(new_phi, phi_min, phi_max)

                # 如果新的当量比与当前值相同，强制改变
                if abs(new_phi - phi) < 1e-6:
                    if error > 0:
                        new_phi = phi - min_step
                    else:
                        new_phi = phi + min_step
                    new_phi = np.clip(new_phi, phi_min, phi_max)

                phi = new_phi

            except ct.CanteraError as e:
                print(f"热力学计算错误: {str(e)}")
                break

        # 打印最终迭代表
        self._print_iteration_table(iterations)

        # 优先返回贫燃解，如果没有贫燃解才返回富燃解
        if best_phi <= stoich_phi:
            print(f"\n达到最大迭代，最佳贫燃当量比: {best_phi:.4f}，误差: {min_error:.1f}K")
            return best_phi
        elif best_phi_rich is not None:
            print(
                f"\n未找到贫燃解，使用富燃解，当量比: {best_phi_rich:.4f}，误差: {min_error_rich:.1f}K")
            return best_phi_rich
        else:
            raise ValueError("无法找到满足温度要求的当量比")

    def _print_iteration_table(self, iterations):
        """打印迭代过程表格"""
        if not iterations:
            return

        df = pd.DataFrame(iterations)
        pd.set_option('display.float_format', lambda x: '%.3f' % x)
        pd.set_option('display.max_columns', None)
        pd.set_option('display.width', None)

        print("\n迭代过程:")
        print(df.to_string(index=False))

    def _smart_initial_guess(self, T_target: float, T_max: float, stoich_phi: float) -> float:
        """基于温度比例的智能猜测"""
        T_ratio = T_target / T_max
        if T_ratio < 0.8:
            return stoich_phi * 1.5
        elif T_ratio > 1.1:
            return stoich_phi * 0.6
        return stoich_phi

    def _perturb_phi(self, phi: float, fuel_formula: str):
        """扰动当量比并计算温度"""
        # 创建新的气体对象，避免状态污染
        gas = ct.Solution(self.mechanism)
        gas.TPX = 300, ct.one_atm, {fuel_formula: 1, 'O2': 1, 'N2': 3.76}

        # 设置当量比并计算平衡
        gas.set_equivalence_ratio(phi, {fuel_formula: 1.0}, {
                                  'O2': 1.0, 'N2': 3.76})
        gas.equilibrate('HP')
        return gas.T

    def _calc_flows(self, phi: float, fuel: Fuel, profile: dict) -> Dict:
        """计算质量流量"""
        AF_stoich = 1/self.gas.stoich_air_fuel_ratio(fuel.formula)
        m_fuel = 1 / (1 + AF_stoich/phi)
        return {
            '燃料流量 (kg/s)': m_fuel,
            '空气流量 (kg/s)': 1 - m_fuel,
            '总流量 (kg/s)': 1.0
        }

    def _get_properties(self) -> Dict:
        """获取物性参数"""
        return {
            '温度 (K)': self.gas.T,
            '压力 (Pa)': self.gas.P,
            '分子量 (kg/kmol)': self.gas.mean_molecular_weight,
            '比热比': self.gas.cp / self.gas.cv,
            '定压比热 (kJ/kg-K)': self.gas.cp_mass / 1000,
            '产物组成': {sp: f"{self.gas[sp].X[0]:.2%}"
                     for sp in self.gas.species_names
                     if self.gas[sp].X[0] > 0.01}
        }

    def _add_fuel(self, species: str):
        """添加燃料物种"""
        try:
            logger.debug(f"尝试添加燃料 {species}")
            # 检查物种是否在Cantera数据库中
            if species not in self.gas.species_names:
                logger.warning(f"燃料 {species} 不在Cantera数据库中，尝试添加")
                self.gas.add_species(species)
            logger.info(f"成功添加燃料 {species}")
        except Exception as e:
            logger.error(f"添加燃料 {species} 失败: {str(e)}")
            raise

    def _setup_fuels(self):
        """设置燃料"""
        try:
            # 使用Cantera支持的燃料
            fuels = [
                "CH4",  # 甲烷
                "C2H6",  # 乙烷
                "C3H8",  # 丙烷
                "C4H10",  # 丁烷
                "C8H18",  # 辛烷
                "H2",    # 氢气
                "CO"     # 一氧化碳
            ]

            for fuel in fuels:
                self._add_fuel(fuel)

            logger.info(f"成功设置 {len(fuels)} 种燃料")
        except Exception as e:
            logger.error(f"设置燃料失败: {str(e)}")
            raise


class SprayCooling:
    def __init__(self, gas: ct.Solution):
        self.gas = gas
        self.water = ct.Water()

    def calculate(self, m_gas: float, T_comb: float, T_target: float = 473.15) -> Dict:
        """喷淋计算"""
        try:
            h_in = self._get_gas_enthalpy(T_comb)
            h_out = self._get_gas_enthalpy(T_target)
            delta_h_gas = h_in - h_out

            h_water_in = self._get_water_enthalpy(298.15)
            h_water_out = self._get_water_enthalpy(T_target)
            delta_h_water = h_water_out - h_water_in

            m_water = m_gas * delta_h_gas / delta_h_water
            return {
                '喷水量 (kg/s)': abs(m_water),
                '终温 (K)': T_target
            }
        except Exception as e:
            print(f"喷淋计算错误: {str(e)}")
            return {'喷水量 (kg/s)': 0.0, '终温 (K)': T_target}

    def _get_gas_enthalpy(self, T: float) -> float:
        g = self.gas = ct.Solution(self.mechanism)
        g.TP = T, self.gas.P
        return g.enthalpy_mass

    def _get_water_enthalpy(self, T: float) -> float:
        self.water.TP = T, ct.one_atm
        return self.water.enthalpy_mass


def fuel_comparison(fuels: List[Fuel], T_comb: float, P: float = 101325):
    """增强的对比分析"""
    results = []
    for fuel in fuels:
        print(f"\n=== 分析 {fuel.name} ===")
        try:
            system = CombustionSystem()
            result = system.analyze_combustion(fuel, T_comb, P)
            if result:
                result['燃料'] = fuel.name
                results.append(result)
        except Exception as e:
            print(f"分析失败: {str(e)}")

    if results:
        df = pd.DataFrame(results)
        # 选择要显示的列
        columns = ['燃料', '当量比', '燃料流量 (kg/s)', '空气流量 (kg/s)',
                   '分子量 (kg/kmol)', '比热比']
        df = df[columns]

        # 设置显示格式
        pd.set_option('display.float_format', lambda x: '%.3f' % x)
        pd.set_option('display.max_columns', None)
        pd.set_option('display.width', None)

        print("\n对比结果:")
        print(df.to_string(index=False))
    else:
        print("\n所有分析均失败")


if __name__ == "__main__":
    test_fuels = [
        Fuel('甲烷', 'CH4', LHV=50100),
        Fuel('航空煤油', 'C12H23', LHV=42800),
        Fuel('氢气', 'H2', LHV=120000)
    ]
    fuel_comparison(test_fuels, T_comb=873.15)
