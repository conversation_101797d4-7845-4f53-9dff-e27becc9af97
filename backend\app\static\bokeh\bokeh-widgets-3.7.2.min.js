'use strict';
/*!
 * Copyright (c) Anaconda, Inc., and Bokeh Contributors
 * All rights reserved.
 * 
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 * 
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * 
 * Redistributions in binary form must reproduce the above copyright notice,
 * this list of conditions and the following disclaimer in the documentation
 * and/or other materials provided with the distribution.
 * 
 * Neither the name of <PERSON><PERSON><PERSON> nor the names of any contributors
 * may be used to endorse or promote products derived from this software
 * without specific prior written permission.
 * 
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENT<PERSON>, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
 * THE POSSIBILITY OF SUCH DAMAGE.
 */
(function(root, factory) {
  factory(root["Bokeh"], "3.7.2");
})(this, function(Bokeh, version) {
  let define;
  return (function(modules, entry, aliases, externals) {
    const bokeh = typeof Bokeh !== "undefined" ? (version != null ? Bokeh[version] : Bokeh) : null;
    if (bokeh != null) {
      return bokeh.register_plugin(modules, entry, aliases);
    } else {
      throw new Error("Cannot find Bokeh" + (version != null ? " " + version : "") + ". You have to load it prior to loading plugins.");
    }
  })
({
605: function _(t,e,i,o,r){o();const s=t(1).__importStar(t(606));i.Widgets=s;(0,t(7).register_models)(s)},
606: function _(e,t,i,r,o){r();const u=e(1);o("AbstractButton",e(607).AbstractButton),o("AutocompleteInput",e(611).AutocompleteInput),o("Button",e(617).Button),o("CheckboxButtonGroup",e(618).CheckboxButtonGroup),o("CheckboxGroup",e(621).CheckboxGroup),o("Checkbox",e(624).Checkbox),o("ColorPicker",e(627).ColorPicker),o("DatePicker",e(628).DatePicker),o("DateRangePicker",e(640).DateRangePicker),o("DatetimePicker",e(641).DatetimePicker),o("DatetimeRangePicker",e(643).DatetimeRangePicker),o("Div",e(644).Div),o("Dropdown",e(647).Dropdown),o("FileInput",e(649).FileInput),o("HelpButton",e(650).HelpButton),o("InputWidget",e(614).InputWidget),o("Markup",e(645).Markup),o("MultiChoice",e(651).MultiChoice),o("MultiSelect",e(654).MultiSelect),o("MultipleDatePicker",e(655).MultipleDatePicker),o("MultipleDatetimePicker",e(656).MultipleDatetimePicker),o("NumericInput",e(657).NumericInput),o("PaletteSelect",e(658).PaletteSelect),o("Paragraph",e(664).Paragraph),o("PasswordInput",e(665).PasswordInput),o("PreText",e(667).PreText),o("Progress",e(668).Progress),o("RadioButtonGroup",e(671).RadioButtonGroup),o("RadioGroup",e(672).RadioGroup),o("Select",e(673).Select),o("Spinner",e(674).Spinner),o("Switch",e(675).Switch),o("TextAreaInput",e(677).TextAreaInput),o("TextInput",e(612).TextInput),o("TimePicker",e(678).TimePicker),o("Toggle",e(679).Toggle),o("Widget",e(718).Widget),u.__exportStar(e(680),i)},
607: function _(e,t,i,n,s){var l;n();const o=e(1),r=e(21),a=e(63),_=e(56),c=e(8),h=e(608),u=e(125),d=e(326),b=e(493),w=o.__importStar(e(610)),v=w;class p extends h.ControlView{*controls(){yield this.button_el}*children(){yield*super.children(),null!=this.label_view&&(yield this.label_view),null!=this.icon_view&&(yield this.icon_view)}async lazy_initialize(){await super.lazy_initialize(),await this._rebuild_label(),await this._rebuild_icon()}async _rebuild_label(){this.label_view?.remove();const e=(()=>{const{label:e}=this.model;return(0,c.isString)(e)?new d.Text({content:e}):e})();this.label_view=await this.owner.build_view(e,this)}async _rebuild_icon(){this.icon_view?.remove();const{icon:e}=this.model;null!=e&&(this.icon_view=await(0,_.build_view)(e,{parent:this}))}connect_signals(){super.connect_signals();const{label:e,icon:t,button_type:i,disabled:n}=this.model.properties;this.on_transitive_change(e,(async()=>{await this._rebuild_label(),this.rerender()})),this.on_transitive_change(t,(async()=>{await this._rebuild_icon(),this.rerender()})),this.on_change([i,n],(()=>{this.rerender()}))}remove(){this.label_view?.remove(),this.icon_view?.remove(),super.remove()}stylesheets(){return[...super.stylesheets(),w.default]}_render_button(...e){return(0,a.button)({type:"button",disabled:this.model.disabled,class:[v.btn,v[`btn_${this.model.button_type}`]]},...e)}render(){if(super.render(),this.label_view?.render(),this.button_el=this._render_button(this.label_view?.el),this.button_el.addEventListener("click",(()=>this.click())),null!=this.icon_view){const e=""!=this.model.label?(0,a.nbsp)():(0,a.text)("");(0,a.prepend)(this.button_el,this.icon_view.el,e),this.icon_view.render()}this.group_el=(0,a.div)({class:v.btn_group},this.button_el),this.shadow_el.append(this.group_el)}click(){}}i.AbstractButtonView=p,p.__name__="AbstractButtonView";class y extends h.Control{constructor(e){super(e)}}i.AbstractButton=y,l=y,y.__name__="AbstractButton",l.define((({Str:e,Ref:t,Or:i,Nullable:n})=>({label:[i(t(u.DOMNode),e),"Button"],icon:[n(t(b.Icon)),null],button_type:[r.ButtonType,"default"]})))},
608: function _(t,n,o,s,e){s();const c=t(718),i=t(63);class l extends c.WidgetView{connect_signals(){super.connect_signals(),this.connect(this.disabled,(t=>{for(const n of this.controls())(0,i.toggle_attribute)(n,"disabled",t)}))}}o.ControlView=l,l.__name__="ControlView";class _ extends c.Widget{constructor(t){super(t)}}o.Control=_,_.__name__="Control"},
718: function _(t,e,i,r,s){var a;r();const n=t(411),o=t(182);class d extends n.LayoutDOMView{get child_models(){return[]}get provider(){return o.default_provider}async lazy_initialize(){await super.lazy_initialize(),"not_started"==this.provider.status&&await this.provider.fetch()}_after_layout(){super._after_layout(),"loading"==this.provider.status&&(this._has_finished=!1)}process_tex(t){if(null==this.provider.MathJax)return t;const e=this.provider.MathJax.find_tex(t),i=[];let r=0;for(const s of e)i.push(t.slice(r,s.start.n)),i.push(this.provider.MathJax.tex2svg(s.math,{display:s.display}).outerHTML),r=s.end.n;return r<t.length&&i.push(t.slice(r)),i.join("")}contains_tex_string(t){return null!=this.provider.MathJax&&this.provider.MathJax.find_tex(t).length>0}}i.WidgetView=d,d.__name__="WidgetView";class _ extends n.LayoutDOM{constructor(t){super(t)}}i.Widget=_,a=_,_.__name__="Widget",a.override({margin:5})},
610: function _(b,o,r,e,t){e(),r.btn="bk-btn",r.active="bk-active",r.btn_default="bk-btn-default",r.btn_primary="bk-btn-primary",r.btn_success="bk-btn-success",r.btn_warning="bk-btn-warning",r.btn_danger="bk-btn-danger",r.btn_light="bk-btn-light",r.btn_group="bk-btn-group",r.vertical="bk-vertical",r.horizontal="bk-horizontal",r.dropdown_toggle="bk-dropdown-toggle",r.default=".bk-btn,::file-selector-button{height:100%;display:inline-block;text-align:center;vertical-align:middle;white-space:nowrap;cursor:pointer;padding:var(--padding-vertical) var(--padding-horizontal);font-size:var(--font-size);border:1px solid transparent;border-radius:var(--border-radius);outline:0;outline-offset:-5px;user-select:none;-webkit-user-select:none;}.bk-btn:hover,::file-selector-button:hover,.bk-btn:focus,::file-selector-button:focus{text-decoration:none;}.bk-btn:active,::file-selector-button:active,.bk-active.bk-btn,.bk-active::file-selector-button{background-image:none;box-shadow:inset 0 3px 5px rgba(0, 0, 0, 0.125);}.bk-btn[disabled]{cursor:not-allowed;pointer-events:none;opacity:0.65;box-shadow:none;}::file-selector-button{color:#333;background-color:#fff;border-color:#ccc;}::file-selector-button:hover{background-color:#f5f5f5;border-color:#b8b8b8;}.bk-active::file-selector-button{background-color:#ebebeb;border-color:#adadad;}::file-selector-button[disabled],::file-selector-button[disabled]:hover,::file-selector-button[disabled]:focus,::file-selector-button[disabled]:active,.bk-active::file-selector-button[disabled]{background-color:#e6e6e6;border-color:#ccc;}::file-selector-button:focus,::file-selector-button:active{outline:1px dotted #ccc;}.bk-btn-default{color:#333;background-color:#fff;border-color:#ccc;}.bk-btn-default:hover{background-color:#f5f5f5;border-color:#b8b8b8;}.bk-active.bk-btn-default{background-color:#ebebeb;border-color:#adadad;}.bk-btn-default[disabled],.bk-btn-default[disabled]:hover,.bk-btn-default[disabled]:focus,.bk-btn-default[disabled]:active,.bk-active.bk-btn-default[disabled]{background-color:#e6e6e6;border-color:#ccc;}.bk-btn-default:focus,.bk-btn-default:active{outline:1px dotted #ccc;}.bk-btn-primary{color:#fff;background-color:#428bca;border-color:#357ebd;}.bk-btn-primary:hover{background-color:#3681c1;border-color:#2c699e;}.bk-active.bk-btn-primary{background-color:#3276b1;border-color:#285e8e;}.bk-btn-primary[disabled],.bk-btn-primary[disabled]:hover,.bk-btn-primary[disabled]:focus,.bk-btn-primary[disabled]:active,.bk-active.bk-btn-primary[disabled]{background-color:#506f89;border-color:#357ebd;}.bk-btn-primary:focus,.bk-btn-primary:active{outline:1px dotted #ccc;}.bk-btn-success{color:#fff;background-color:#5cb85c;border-color:#4cae4c;}.bk-btn-success:hover{background-color:#4eb24e;border-color:#409240;}.bk-active.bk-btn-success{background-color:#47a447;border-color:#398439;}.bk-btn-success[disabled],.bk-btn-success[disabled]:hover,.bk-btn-success[disabled]:focus,.bk-btn-success[disabled]:active,.bk-active.bk-btn-success[disabled]{background-color:#667b66;border-color:#4cae4c;}.bk-btn-success:focus,.bk-btn-success:active{outline:1px dotted #ccc;}.bk-btn-warning{color:#fff;background-color:#f0ad4e;border-color:#eea236;}.bk-btn-warning:hover{background-color:#eea43b;border-color:#e89014;}.bk-active.bk-btn-warning{background-color:#ed9c28;border-color:#d58512;}.bk-btn-warning[disabled],.bk-btn-warning[disabled]:hover,.bk-btn-warning[disabled]:focus,.bk-btn-warning[disabled]:active,.bk-active.bk-btn-warning[disabled]{background-color:#c89143;border-color:#eea236;}.bk-btn-warning:focus,.bk-btn-warning:active{outline:1px dotted #ccc;}.bk-btn-danger{color:#fff;background-color:#d9534f;border-color:#d43f3a;}.bk-btn-danger:hover{background-color:#d5433e;border-color:#bd2d29;}.bk-active.bk-btn-danger{background-color:#d2322d;border-color:#ac2925;}.bk-btn-danger[disabled],.bk-btn-danger[disabled]:hover,.bk-btn-danger[disabled]:focus,.bk-btn-danger[disabled]:active,.bk-active.bk-btn-danger[disabled]{background-color:#a55350;border-color:#d43f3a;}.bk-btn-danger:focus,.bk-btn-danger:active{outline:1px dotted #ccc;}.bk-btn-light{color:#333;background-color:#fff;border-color:#ccc;border-color:transparent;}.bk-btn-light:hover{background-color:#f5f5f5;border-color:#b8b8b8;}.bk-active.bk-btn-light{background-color:#ebebeb;border-color:#adadad;}.bk-btn-light[disabled],.bk-btn-light[disabled]:hover,.bk-btn-light[disabled]:focus,.bk-btn-light[disabled]:active,.bk-active.bk-btn-light[disabled]{background-color:#e6e6e6;border-color:#ccc;}.bk-btn-light:focus,.bk-btn-light:active{outline:1px dotted #ccc;}.bk-btn-group{height:100%;display:flex;flex-wrap:nowrap;align-items:center;}.bk-btn-group:not(.bk-vertical),.bk-btn-group.bk-horizontal{flex-direction:row;}.bk-btn-group.bk-vertical{flex-direction:column;}.bk-btn-group > .bk-btn{flex-grow:1;}.bk-btn-group:not(.bk-vertical) > .bk-btn + .bk-btn{margin-left:-1px;}.bk-btn-group.bk-vertical > .bk-btn + .bk-btn{margin-top:-1px;}.bk-btn-group:not(.bk-vertical) > .bk-btn:first-child:not(:last-child){border-bottom-right-radius:0;border-top-right-radius:0;}.bk-btn-group.bk-vertical > .bk-btn:first-child:not(:last-child){border-bottom-left-radius:0;border-bottom-right-radius:0;}.bk-btn-group:not(.bk-vertical) > .bk-btn:not(:first-child):last-child{border-bottom-left-radius:0;border-top-left-radius:0;}.bk-btn-group.bk-vertical > .bk-btn:not(:first-child):last-child{border-top-left-radius:0;border-top-right-radius:0;}.bk-btn-group > .bk-btn:not(:first-child):not(:last-child){border-radius:0;}.bk-btn-group.bk-vertical > .bk-btn{width:100%;}.bk-btn-group .bk-dropdown-toggle{flex:0 0 0;padding:var(--padding-vertical) calc(var(--padding-horizontal)/2);}"},
611: function _(e,t,s,i,n){var h;i();const o=e(1),_=e(612),u=e(63),r=e(14),c=e(11),l=e(22),a=o.__importStar(e(616)),m=a,d=(0,l.Enum)("starts_with","includes");class p extends _.TextInputView{constructor(){super(...arguments),this._open=!1,this._last_value="",this._hover_index=0}stylesheets(){return[...super.stylesheets(),a.default]}render(){super.render(),this.input_el.addEventListener("focusin",(()=>this._toggle_menu())),this.menu=(0,u.div)({class:[m.menu,m.below]}),this.menu.addEventListener("click",(e=>this._menu_click(e))),this.menu.addEventListener("mouseover",(e=>this._menu_hover(e))),this.shadow_el.appendChild(this.menu),(0,u.undisplay)(this.menu)}change_input(){this._open&&this.menu.children.length>0?(this.model.value=this.menu.children[this._hover_index].textContent,this.input_el.focus(),this._hide_menu()):this.model.restrict||super.change_input()}_update_completions(e){(0,u.empty)(this.menu);const{max_completions:t}=this.model,s=null!=t?(0,r.take)(e,t):e;for(const e of s){const t=(0,u.div)(e);this.menu.append(t)}this.menu.firstElementChild?.classList.add(m.active)}compute_completions(e){const t=(()=>{const{case_sensitive:e}=this.model;return e?e=>e:e=>e.toLowerCase()})(),s=(()=>{switch(this.model.search_strategy){case"starts_with":return(e,t)=>e.startsWith(t);case"includes":return(e,t)=>e.includes(t)}})(),i=t(e),n=[];for(const e of this.model.completions){s(t(e),i)&&n.push(e)}return n}_toggle_menu(){const{value:e}=this.input_el;if(e.length<this.model.min_characters)return void this._hide_menu();const t=this.compute_completions(e);this._update_completions(t),0==t.length?this._hide_menu():this._show_menu()}_show_menu(){if(!this._open){this._open=!0,this._hover_index=0,this._last_value=this.model.value,(0,u.display)(this.menu);const e=t=>{t.composedPath().includes(this.el)||(document.removeEventListener("click",e),this._hide_menu())};document.addEventListener("click",e)}}_hide_menu(){this._open&&(this._open=!1,(0,u.undisplay)(this.menu))}_menu_click(e){e.target!=e.currentTarget&&e.target instanceof Element&&(this.model.value=e.target.textContent,this.input_el.focus(),this._hide_menu())}_menu_hover(e){if(e.target!=e.currentTarget&&e.target instanceof Element)for(let t=0;t<this.menu.children.length;t++)if(this.menu.children[t].textContent==e.target.textContent){this._bump_hover(t);break}}_bump_hover(e){const t=this.menu.children.length;this._open&&t>0&&(this.menu.children[this._hover_index].classList.remove(m.active),this._hover_index=(0,c.clamp)(e,0,t-1),this.menu.children[this._hover_index].classList.add(m.active))}_keyup(e){switch(super._keyup(e),e.key){case"Enter":this.change_input();break;case"Escape":this._hide_menu();break;case"ArrowUp":this._bump_hover(this._hover_index-1);break;case"ArrowDown":this._bump_hover(this._hover_index+1);break;default:this._toggle_menu()}}}s.AutocompleteInputView=p,p.__name__="AutocompleteInputView";class v extends _.TextInput{constructor(e){super(e)}}s.AutocompleteInput=v,h=v,v.__name__="AutocompleteInput",h.prototype.default_view=p,h.define((({Bool:e,Int:t,Str:s,List:i,NonNegative:n,Positive:h,Nullable:o})=>({completions:[i(s),[]],min_characters:[n(t),2],max_completions:[o(h(t)),null],case_sensitive:[e,!0],restrict:[e,!0],search_strategy:[d,"starts_with"]})))},
612: function _(e,t,n,i,s){var u;i();const l=e(1),r=e(613),p=e(63),_=e(53),a=l.__importStar(e(615));class c extends r.TextLikeInputView{connect_signals(){super.connect_signals();const{prefix:e,suffix:t}=this.model.properties;this.on_change([e,t],(()=>this.rerender()))}_render_input(){this.input_el=(0,p.input)({type:"text",class:a.input});const{prefix:e,suffix:t}=this.model,n=null!=e?(0,p.div)({class:"bk-input-prefix"},e):null,i=null!=t?(0,p.div)({class:"bk-input-suffix"},t):null;return(0,p.div)({class:"bk-input-container"},n,this.input_el,i)}render(){super.render(),this.input_el.addEventListener("keyup",(e=>this._keyup(e)))}_keyup(e){"Enter"!=e.key||e.shiftKey||e.ctrlKey||e.altKey||this.model.trigger_event(new _.ValueSubmit(this.input_el.value))}}n.TextInputView=c,c.__name__="TextInputView";class o extends r.TextLikeInput{constructor(e){super(e)}}n.TextInput=o,u=o,o.__name__="TextInput",u.prototype.default_view=c,u.define((({Str:e,Nullable:t})=>({prefix:[t(e),null],suffix:[t(e),null]})))},
613: function _(e,t,n,i,l){var s;i();const h=e(614);class a extends h.InputWidgetView{connect_signals(){super.connect_signals(),this.connect(this.model.properties.value.change,(()=>this.input_el.value=this.model.value)),this.connect(this.model.properties.value_input.change,(()=>this.input_el.value=this.model.value_input)),this.connect(this.model.properties.disabled.change,(()=>this.input_el.disabled=this.model.disabled)),this.connect(this.model.properties.placeholder.change,(()=>this.input_el.placeholder=this.model.placeholder)),this.connect(this.model.properties.max_length.change,(()=>{const{max_length:e}=this.model;null!=e?this.input_el.maxLength=e:this.input_el.removeAttribute("maxLength")}))}render(){super.render();const{input_el:e}=this;e.value=this.model.value,e.disabled=this.model.disabled,e.placeholder=this.model.placeholder,null!=this.model.max_length&&(e.maxLength=this.model.max_length),e.addEventListener("change",(()=>this.change_input())),e.addEventListener("input",(()=>this.change_input_value()))}change_input(){this.model.value=this.input_el.value,super.change_input()}change_input_value(){this.model.value_input=this.input_el.value,super.change_input()}}n.TextLikeInputView=a,a.__name__="TextLikeInputView";class u extends h.InputWidget{constructor(e){super(e)}}n.TextLikeInput=u,s=u,u.__name__="TextLikeInput",s.define((({Int:e,Str:t,Nullable:n})=>({value:[t,""],value_input:[t,""],placeholder:[t,""],max_length:[n(e),null]})))},
614: function _(e,t,i,s,n){var l,r;s();const o=e(1);var c,d=this&&this.__decorate||function(e,t,i,s){var n,l=arguments.length,r=l<3?t:null===s?s=Object.getOwnPropertyDescriptor(t,i):s;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(e,t,i,s);else for(var o=e.length-1;o>=0;o--)(n=e[o])&&(r=(l<3?n(r):l>3?n(t,i,r):n(t,i))||r);return l>3&&r&&Object.defineProperty(t,i,r),r};const a=e(608),u=e(425),_=e(413),p=e(8),h=e(56),f=e(63),v=e(58),m=e(53),w=o.__importStar(e(615)),b=w,g=o.__importDefault(e(123));let y=(l=class extends m.ModelEvent{constructor(e){super(),this.model=e,this.origin=e}static from_values(e){const{model:t}=e;return new c(t)}},c=l,l.__name__="ClearInput",l);i.ClearInput=y,i.ClearInput=y=c=d([(0,m.server_event)("clear_input")],y);class L extends a.ControlView{constructor(){super(...arguments),this.description=null,this.desc_el=null}*controls(){yield this.input_el}*children(){yield*super.children();const{title:e,description:t}=this;e instanceof v.View&&(yield e),t instanceof v.View&&(yield t)}async lazy_initialize(){await super.lazy_initialize(),await this._build_title(),await this._build_description()}remove(){const{title:e,description:t}=this;e instanceof v.View&&e.remove(),t instanceof v.View&&t.remove(),super.remove()}connect_signals(){super.connect_signals();const{title:e,description:t}=this.model.properties;this.on_change(e,(async()=>{await this._build_title(),this.rerender()})),this.on_change(t,(async()=>{await this._build_description(),this.rerender()}))}stylesheets(){return[...super.stylesheets(),w.default,g.default]}render(){super.render(),this.desc_el=this._build_description_el(),this.title_el=this._build_title_el();const e=this._render_input();this.input_el.id="input",this.group_el=(0,f.div)({class:b.input_group},this.title_el,e),this.shadow_el.append(this.group_el)}_build_description_el(){const{description:e}=this;if(null==e)return null;{const t=(0,f.div)({class:b.icon}),i=(0,f.div)({class:b.description},t);if((0,p.isString)(e))i.title=e;else{"auto"==e.model.target&&(e.target=i);let s=!1;const n=i=>{e.model.setv({visible:i,closable:s}),t.classList.toggle(b.opaque,i&&s)};this.on_change(e.model.properties.visible,(()=>{const{visible:t}=e.model;t||(s=!1),n(t)})),i.addEventListener("mouseenter",(()=>{n(!0)})),i.addEventListener("mouseleave",(()=>{s||n(!1)})),document.addEventListener("mousedown",(t=>{const l=t.composedPath();l.includes(e.el)||(l.includes(i)?(s=!s,n(s)):(s=!1,n(!1)))})),window.addEventListener("blur",(()=>{s=!1,n(!1)}))}return i}}async _build_title(){const{title:e}=this.model;e instanceof _.HTML?this.title=await(0,h.build_view)(e,{parent:this}):this.title=e}async _build_description(){const{description:e}=this.model;e instanceof u.Tooltip?this.description=await(0,h.build_view)(e,{parent:this}):this.description=e}_build_title_el(){const{title:e}=this,t=e instanceof _.HTMLView?(e.render(),e.el):e,i=""==e?"none":"";return(0,f.label)({for:"input",style:{display:i}},t,this.desc_el)}change_input(){}}i.InputWidgetView=L,L.__name__="InputWidgetView";class V extends a.Control{constructor(e){super(e)}}i.InputWidget=V,r=V,V.__name__="InputWidget",r.define((({Str:e,Nullable:t,Or:i,Ref:s})=>({title:[i(e,s(_.HTML)),""],description:[t(i(e,s(u.Tooltip))),null]})))},
615: function _(i,n,t,e,p){e(),t.input="bk-input",t.disabled="bk-disabled",t.input_container="bk-input-container",t.input_prefix="bk-input-prefix",t.input_suffix="bk-input-suffix",t.input_group="bk-input-group",t.inline="bk-inline",t.spin_wrapper="bk-spin-wrapper",t.spin_btn="bk-spin-btn",t.spin_btn_up="bk-spin-btn-up",t.spin_btn_down="bk-spin-btn-down",t.description="bk-description",t.icon="bk-icon",t.opaque="bk-opaque",t.default=':host{--input-min-height:calc(var(--line-height-computed) + 2*var(--padding-vertical) + 2px);}.bk-input{position:relative;display:inline-block;width:100%;flex-grow:1;min-height:var(--input-min-height);padding:0 var(--padding-horizontal);background-color:#fff;border:1px solid #ccc;border-radius:var(--border-radius);resize:none;}.bk-input:focus{border-color:#66afe9;outline:0;box-shadow:inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6);}.bk-input::placeholder,.bk-input:-ms-input-placeholder,.bk-input::-moz-placeholder,.bk-input::-webkit-input-placeholder{color:#999;opacity:1;}.bk-input[disabled],.bk-input.bk-disabled{cursor:not-allowed;background-color:#eee;opacity:1;}.bk-input-container{width:100%;height:100%;display:flex;flex-direction:row;flex-wrap:nowrap;}.bk-input-container .bk-input-prefix,.bk-input-container .bk-input-suffix{display:flex;align-items:center;flex:0 1 0;border:1px solid #ccc;border-radius:var(--border-radius);padding:0 var(--padding-horizontal);background-color:#e6e6e6;}.bk-input-container .bk-input-prefix{border-right:none;border-top-right-radius:0;border-bottom-right-radius:0;}.bk-input-container .bk-input-suffix{border-left:none;border-top-left-radius:0;border-bottom-left-radius:0;}.bk-input-container .bk-input{flex:1 0 0;}.bk-input-container .bk-input:not(:first-child){border-top-left-radius:0;border-bottom-left-radius:0;}.bk-input-container .bk-input:not(:last-child){border-top-right-radius:0;border-bottom-right-radius:0;}input[type=file].bk-input{padding-left:0;}input[type=file]::file-selector-button{box-sizing:inherit;font-family:inherit;font-size:inherit;line-height:inherit;}select:not([multiple]).bk-input,select:not([size]).bk-input{height:auto;appearance:none;-webkit-appearance:none;background-image:url(\'data:image/svg+xml;utf8,<svg version="1.1" viewBox="0 0 25 20" xmlns="http://www.w3.org/2000/svg"><path d="M 0,0 25,0 12.5,20 Z" fill="black" /></svg>\');background-position:right 0.5em center;background-size:8px 6px;background-repeat:no-repeat;padding-right:calc(var(--padding-horizontal) + 8px);}option{padding:0;}select[multiple].bk-input,select[size].bk-input,textarea.bk-input{height:auto;}.bk-input-group{position:relative;width:100%;height:100%;display:inline-flex;flex-wrap:nowrap;align-items:start;flex-direction:column;white-space:nowrap;}.bk-input-group.bk-inline{flex-direction:row;}.bk-input-group.bk-inline > *:not(:first-child){margin-left:5px;}.bk-input-group > .bk-spin-wrapper{display:inherit;width:inherit;height:inherit;position:relative;overflow:hidden;padding:0;vertical-align:middle;}.bk-input-group > .bk-spin-wrapper input{padding-right:20px;}.bk-input-group > .bk-spin-wrapper > .bk-spin-btn{position:absolute;display:block;height:50%;min-height:0;min-width:0;width:30px;padding:0;margin:0;right:0;border:none;background:none;cursor:pointer;}.bk-input-group > .bk-spin-wrapper > .bk-spin-btn:before{content:"";display:inline-block;transform:translateY(-50%);border-left:5px solid transparent;border-right:5px solid transparent;}.bk-input-group > .bk-spin-wrapper > .bk-spin-btn.bk-spin-btn-up{top:0;}.bk-input-group > .bk-spin-wrapper > .bk-spin-btn.bk-spin-btn-up:before{border-bottom:5px solid black;}.bk-input-group > .bk-spin-wrapper > .bk-spin-btn.bk-spin-btn-up:disabled:before{border-bottom-color:grey;}.bk-input-group > .bk-spin-wrapper > .bk-spin-btn.bk-spin-btn-down{bottom:0;}.bk-input-group > .bk-spin-wrapper > .bk-spin-btn.bk-spin-btn-down:before{border-top:5px solid black;}.bk-input-group > .bk-spin-wrapper > .bk-spin-btn.bk-spin-btn-down:disabled:before{border-top-color:grey;}.bk-description{position:relative;display:inline-block;margin-left:0.25em;vertical-align:middle;margin-top:-2px;cursor:pointer;}.bk-description > .bk-icon{opacity:0.5;width:18px;height:18px;background-color:gray;mask-image:var(--bokeh-icon-help);mask-size:contain;mask-repeat:no-repeat;-webkit-mask-image:var(--bokeh-icon-help);-webkit-mask-size:contain;-webkit-mask-repeat:no-repeat;}label:hover > .bk-description > .bk-icon,.bk-icon.bk-opaque{opacity:1;}'},
616: function _(e,o,i,b,r){b(),i.menu="bk-menu",i.above="bk-above",i.below="bk-below",i.divider="bk-divider",i.active="bk-active",i.default=":host{position:relative;}.bk-menu{position:absolute;left:0;width:100%;z-index:var(--bokeh-top-level);cursor:pointer;font-size:var(--font-size);background-color:#fff;border:1px solid #ccc;border-radius:var(--border-radius);box-shadow:0 6px 12px rgba(0, 0, 0, 0.175);}.bk-menu.bk-above{bottom:100%;}.bk-menu.bk-below{top:100%;}.bk-menu > .bk-divider{height:1px;margin:calc(var(--line-height-computed)/2 - 1px) 0;overflow:hidden;background-color:#e5e5e5;}.bk-menu > :not(.bk-divider){padding:var(--padding-vertical) var(--padding-horizontal);}.bk-menu > :not(.bk-divider):hover,.bk-menu > :not(.bk-divider).bk-active{background-color:#e6e6e6;}"},
617: function _(t,e,n,o,c){var i;o();const s=t(607),u=t(53);class _ extends s.AbstractButtonView{click(){this.model.trigger_event(new u.ButtonClick),super.click()}}n.ButtonView=_,_.__name__="ButtonView";class r extends s.AbstractButton{constructor(t){super(t)}on_click(t){this.on_event(u.ButtonClick,t)}}n.Button=r,i=r,r.__name__="Button",i.prototype.default_view=_,i.override({label:"Button"})},
618: function _(t,e,o,c,s){var a;c();const i=t(1),n=t(619),u=i.__importStar(t(610));class r extends n.ToggleButtonGroupView{get active(){return new Set(this.model.active)}change_active(t){const{active:e}=this;e.has(t)?e.delete(t):e.add(t),this.model.active=[...e].sort()}_update_active(){const{active:t}=this;this._buttons.forEach(((e,o)=>{e.classList.toggle(u.active,t.has(o))}))}}o.CheckboxButtonGroupView=r,r.__name__="CheckboxButtonGroupView";class _ extends n.ToggleButtonGroup{constructor(t){super(t)}}o.CheckboxButtonGroup=_,a=_,_.__name__="CheckboxButtonGroup",a.prototype.default_view=r,a.define((({Int:t,List:e})=>({active:[e(t),[]]})))},
619: function _(t,e,n,s,o){var i;s();const r=t(1),l=t(620),a=t(53),_=t(21),d=t(63),u=r.__importStar(t(610)),c=u;class h extends l.OrientedControlView{*controls(){yield*this._buttons}connect_signals(){super.connect_signals();const t=this.model.properties;this.on_change(t.button_type,(()=>this.rerender())),this.on_change(t.labels,(()=>this.rerender())),this.on_change(t.active,(()=>this._update_active()))}stylesheets(){return[...super.stylesheets(),u.default]}render(){super.render(),this._buttons=this.model.labels.map(((t,e)=>{const n=(0,d.button)({class:[c.btn,c[`btn_${this.model.button_type}`]],disabled:this.model.disabled},t);return n.addEventListener("click",(()=>{this.change_active(e),this.model.trigger_event(new a.ButtonClick)})),n})),this._update_active();const t="horizontal"==this.model.orientation?c.horizontal:c.vertical,e=(0,d.div)({class:[c.btn_group,t]},this._buttons);this.shadow_el.appendChild(e)}}n.ToggleButtonGroupView=h,h.__name__="ToggleButtonGroupView";class p extends l.OrientedControl{constructor(t){super(t)}}n.ToggleButtonGroup=p,i=p,p.__name__="ToggleButtonGroup",i.define((({Str:t,List:e})=>({labels:[e(t),[]],button_type:[_.ButtonType,"default"]})))},
620: function _(n,e,o,t,r){var i;t();const l=n(608),s=n(21);class _ extends l.ControlView{}o.OrientedControlView=_,_.__name__="OrientedControlView";class a extends l.Control{constructor(n){super(n)}}o.OrientedControl=a,i=a,a.__name__="OrientedControl",i.define((()=>({orientation:[s.Orientation,"horizontal"]})))},
621: function _(e,t,s,n,i){var c;n();const o=e(1),a=e(622),h=e(63),l=e(10),d=e(14),p=o.__importStar(e(615));class r extends a.ToggleInputGroupView{get active(){return new Set(this.model.active)}connect_signals(){super.connect_signals();const{active:e}=this.model.properties;this.on_change(e,(()=>{const{active:e}=this;for(const[t,s]of(0,d.enumerate)(this._inputs))t.checked=e.has(s)}))}render(){super.render();const e=(0,h.div)({class:[p.input_group,this.model.inline?p.inline:null]});this.shadow_el.appendChild(e);const{active:t,labels:s}=this.model;this._inputs=[];for(let n=0;n<s.length;n++){const i=(0,h.input)({type:"checkbox",value:`${n}`});i.addEventListener("change",(()=>this.change_active(n))),this._inputs.push(i),this.model.disabled&&(i.disabled=!0),(0,l.includes)(t,n)&&(i.checked=!0);const c=(0,h.label)(i,(0,h.span)(s[n]));e.appendChild(c)}}change_active(e){const{active:t}=this;t.has(e)?t.delete(e):t.add(e),this.model.active=[...t].sort()}}s.CheckboxGroupView=r,r.__name__="CheckboxGroupView";class u extends a.ToggleInputGroup{constructor(e){super(e)}}s.CheckboxGroup=u,c=u,u.__name__="CheckboxGroup",c.prototype.default_view=r,c.define((({Int:e,List:t})=>({active:[t(e),[]]})))},
622: function _(e,t,n,s,o){var l;s();const r=e(1),i=e(608),u=r.__importDefault(e(615)),_=r.__importDefault(e(623));class p extends i.ControlView{*controls(){yield*this._inputs}connect_signals(){super.connect_signals();const{labels:e,inline:t}=this.model.properties;this.on_change([e,t],(()=>this.rerender()))}stylesheets(){return[...super.stylesheets(),u.default,_.default]}}n.ToggleInputGroupView=p,p.__name__="ToggleInputGroupView";class a extends i.Control{constructor(e){super(e)}}n.ToggleInputGroup=a,l=a,a.__name__="ToggleInputGroup",l.define((({Bool:e,Str:t,List:n})=>({labels:[n(t),[]],inline:[e,!1]})))},
623: function _(t,i,p,e,n){e(),p.default='input[type="checkbox"],input[type="radio"]{margin:0;}input[type="checkbox"] + *,input[type="radio"] + *{position:relative;top:-2px;margin-left:3px;}'},
624: function _(e,t,s,_,i){var c;_();const h=e(1),l=e(625),a=e(63),d=h.__importDefault(e(623));class o extends l.ToggleInputView{stylesheets(){return[...super.stylesheets(),d.default]}render(){super.render(),this.checkbox_el=(0,a.input)({type:"checkbox"}),this.checkbox_el.addEventListener("change",(()=>this._toggle_active())),this._update_label(),this._update_active(),this._update_disabled(),this.shadow_el.append(this.checkbox_el,this.label_el)}_update_active(){this.checkbox_el.checked=this.model.active}_update_disabled(){this.checkbox_el.toggleAttribute("disabled",this.model.disabled)}}s.CheckboxView=o,o.__name__="CheckboxView";class n extends l.ToggleInput{constructor(e){super(e)}}s.Checkbox=n,c=n,n.__name__="Checkbox",c.prototype.default_view=o},
625: function _(e,t,l,s,a){var i;s();const n=e(1),_=e(718),o=e(63),d=n.__importStar(e(626));class c extends _.WidgetView{stylesheets(){return[...super.stylesheets(),d.default]}connect_signals(){super.connect_signals();const{active:e,disabled:t,label:l}=this.model.properties;this.on_change(e,(()=>this._update_active())),this.on_change(t,(()=>this._update_disabled())),this.on_change(l,(()=>this._update_label()))}_toggle_active(){this.model.disabled||(this.model.active=!this.model.active)}render(){super.render(),this.label_el=(0,o.div)({class:d.label},this.model.label)}_update_label(){this.label_el.textContent=this.model.label}}l.ToggleInputView=c,c.__name__="ToggleInputView";class h extends _.Widget{constructor(e){super(e)}}l.ToggleInput=h,i=h,h.__name__="ToggleInput",i.define((({Bool:e,Str:t})=>({active:[e,!1],label:[t,""]})))},
626: function _(l,e,a,i,b){i(),a.label="bk-label",a.default=".bk-label{position:relative;display:inline;}.bk-label:empty{display:none;}"},
627: function _(e,t,i,n,o){var s;n();const r=e(1),l=e(614),c=e(63),a=e(23),d=r.__importStar(e(615));class h extends l.InputWidgetView{connect_signals(){super.connect_signals(),this.connect(this.model.properties.name.change,(()=>this.input_el.name=this.model.name??"")),this.connect(this.model.properties.color.change,(()=>this.input_el.value=(0,a.color2hexrgb)(this.model.color))),this.connect(this.model.properties.disabled.change,(()=>this.input_el.disabled=this.model.disabled))}_render_input(){return this.input_el=(0,c.input)({type:"color",class:d.input,name:this.model.name,value:(0,a.color2hexrgb)(this.model.color),disabled:this.model.disabled})}render(){super.render(),this.input_el.addEventListener("change",(()=>this.change_input()))}change_input(){this.model.color=this.input_el.value,super.change_input()}}i.ColorPickerView=h,h.__name__="ColorPickerView";class p extends l.InputWidget{constructor(e){super(e)}}i.ColorPicker=p,s=p,p.__name__="ColorPicker",s.prototype.default_view=h,s.define((({Color:e})=>({color:[e,"#000000"]})))},
628: function _(e,t,a,n,r){var i;n();const s=e(629),l=e(12);class c extends s.BaseDatePickerView{get flatpickr_options(){return{...super.flatpickr_options,mode:"single"}}_on_change(e){(0,l.assert)(e.length<=1),this.model.value=(()=>{if(0==e.length)return null;{const[t]=e;return this._format_date(t)}})()}}a.DatePickerView=c,c.__name__="DatePickerView";class _ extends s.BaseDatePicker{constructor(e){super(e)}}a.DatePicker=_,i=_,_.__name__="DatePicker",i.prototype.default_view=c,i.define((({Nullable:e})=>({value:[e(s.DateLike),null]})))},
629: function _(e,t,a,i,s){var n;i();const l=e(630),c=e(8),r=e(22);a.DateLike=(0,r.Or)((0,r.Ref)(Date),r.Str,r.Float),a.DateLikeList=(0,r.List)((0,r.Or)(a.DateLike,(0,r.Tuple)(a.DateLike,a.DateLike),(0,r.Struct)({from:a.DateLike,to:a.DateLike})));class d extends l.PickerBaseView{_format_date(e){const{picker:t}=this;return t.formatDate(e,t.config.dateFormat)}connect_signals(){super.connect_signals();const{value:e,min_date:t,max_date:a,disabled_dates:i,enabled_dates:s,date_format:n}=this.model.properties;this.connect(e.change,(()=>{const{value:e}=this.model;null!=e?this.picker.setDate(e):this.picker.clear()})),this.connect(t.change,(()=>this.picker.set("minDate",this.model.min_date))),this.connect(a.change,(()=>this.picker.set("maxDate",this.model.max_date))),this.connect(i.change,(()=>{const{disabled_dates:e}=this.model;this.picker.set("disable",null!=e?this._convert_date_list(e):[])})),this.connect(s.change,(()=>{const{enabled_dates:e}=this.model;null!=e?this.picker.set("enable",this._convert_date_list(e)):(this.picker.config._enable=void 0,this.picker.redraw(),this.picker.updateValue(!0))})),this.connect(n.change,(()=>this.picker.set("altFormat",this.model.date_format)))}get flatpickr_options(){const{value:e,min_date:t,max_date:a,disabled_dates:i,enabled_dates:s,date_format:n}=this.model,l=super.flatpickr_options;return l.altInput=!0,l.altFormat=n,l.dateFormat="Y-m-d",null!=e&&(l.defaultDate=e),null!=t&&(l.minDate=t),null!=a&&(l.maxDate=a),null!=i&&(l.disable=this._convert_date_list(i)),null!=s&&(l.enable=this._convert_date_list(s)),l}_convert_date_list(e){const t=[];for(const a of e)if((0,c.isArray)(a)){const[e,i]=a;t.push({from:e,to:i})}else t.push(a);return t}}a.BaseDatePickerView=d,d.__name__="BaseDatePickerView";class o extends l.PickerBase{constructor(e){super(e)}}a.BaseDatePicker=o,n=o,o.__name__="BaseDatePicker",n.define((({Nullable:e})=>({min_date:[e(a.DateLike),null],max_date:[e(a.DateLike),null],disabled_dates:[e(a.DateLikeList),null],enabled_dates:[e(a.DateLikeList),null],date_format:[r.Str,"Y-m-d"]})))},
630: function _(e,t,i,n,o){var s;n();const r=e(1),a=r.__importDefault(e(631)),l=e(614),c=e(63),d=e(21),h=e(63),p=e(12),f=r.__importDefault(e(639)),g=r.__importStar(e(615));class u extends l.InputWidgetView{get picker(){return(0,p.assert)(null!=this._picker),this._picker}*controls(){yield this.picker.altInput??this.input_el}remove(){this._picker?.destroy(),super.remove()}stylesheets(){return[...super.stylesheets(),f.default]}connect_signals(){super.connect_signals();const{inline:e}=this.model.properties;this.connect(e.change,(()=>this.picker.set("inline",this.model.inline)))}get flatpickr_options(){return{appendTo:this.group_el,inline:this.model.inline,position:this._position.bind(this),onChange:e=>{this._on_change(e),this.change_input()}}}_render_input(){return this.input_el=(0,c.input)({type:"text",class:g.input,disabled:this.model.disabled})}render(){super.render(),this._picker?.destroy();const e=this.flatpickr_options;this._picker=(0,a.default)(this.input_el,e)}_position(e,t){const i=t??e._positionElement,n=[...e.calendarContainer.children].reduce(((e,t)=>e+(0,h.bounding_box)(t).height),0),o=e.calendarContainer.offsetWidth,s=this.model.position.split(" "),r=s[0],a=s.length>1?s[1]:null,l=i.offsetTop,c=i.offsetTop+i.offsetHeight,d=i.offsetLeft,p=i.offsetLeft+i.offsetWidth,f=i.offsetWidth,g=window.innerHeight-c,u="above"===r||"below"!==r&&g<n&&l>n,_=null!=e.config.appendTo?l+(u?-n-2:i.offsetHeight+2):window.scrollY+l+(u?-n-2:i.offsetHeight+2);if(e.calendarContainer.classList.toggle("arrowTop",!u),e.calendarContainer.classList.toggle("arrowBottom",u),e.config.inline)return;let w=window.scrollX+d,C=!1,m=!1;"center"===a?(w-=(o-f)/2,C=!0):"right"===a&&(w-=o-f,m=!0),e.calendarContainer.classList.toggle("arrowLeft",!C&&!m),e.calendarContainer.classList.toggle("arrowCenter",C),e.calendarContainer.classList.toggle("arrowRight",m);const y=window.document.body.offsetWidth-(window.scrollX+p),k=w+o>window.document.body.offsetWidth,b=y+o>window.document.body.offsetWidth;if(e.calendarContainer.classList.toggle("rightMost",k),!e.config.static)if(e.calendarContainer.style.top=`${_}px`,k)if(b){const t=this.shadow_el.styleSheets[0],i=window.document.body.offsetWidth,n=Math.max(0,i/2-o/2),s=".flatpickr-calendar.centerMost:before",r=".flatpickr-calendar.centerMost:after",a=t.cssRules.length,l=`{left:${d}px;right:auto;}`;e.calendarContainer.classList.toggle("rightMost",!1),e.calendarContainer.classList.toggle("centerMost",!0),t.insertRule(`${s},${r}${l}`,a),e.calendarContainer.style.left=`${n}px`,e.calendarContainer.style.right="auto"}else e.calendarContainer.style.left="auto",e.calendarContainer.style.right=`${y}px`;else e.calendarContainer.style.left=`${w}px`,e.calendarContainer.style.right="auto"}}i.PickerBaseView=u,u.__name__="PickerBaseView";class _ extends l.InputWidget{constructor(e){super(e)}}i.PickerBase=_,s=_,_.__name__="PickerBase",s.define((({Bool:e})=>({position:[d.CalendarPosition,"auto"],inline:[e,!1]})))},
631: function _(e,t,n,a,i){a();const o=e(1);var r=this&&this.__assign||function(){return r=Object.assign||function(e){for(var t,n=1,a=arguments.length;n<a;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},r.apply(this,arguments)},l=this&&this.__spreadArrays||function(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var a=Array(e),i=0;for(t=0;t<n;t++)for(var o=arguments[t],r=0,l=o.length;r<l;r++,i++)a[i]=o[r];return a};const c=e(632),s=o.__importDefault(e(633)),d=e(634),u=e(635),f=e(636),m=e(637);e(638);var g=300;function p(e,t){var n={config:r(r({},c.defaults),v.defaultConfig),l10n:s.default};function a(){var e;return(null===(e=n.calendarContainer)||void 0===e?void 0:e.getRootNode()).activeElement||document.activeElement}function i(e){return e.bind(n)}function o(){var e=n.config;!1===e.weekNumbers&&1===e.showMonths||!0!==e.noCalendar&&window.requestAnimationFrame((function(){if(void 0!==n.calendarContainer&&(n.calendarContainer.style.visibility="hidden",n.calendarContainer.style.display="block"),void 0!==n.daysContainer){var t=(n.days.offsetWidth+1)*e.showMonths;n.daysContainer.style.width=t+"px",n.calendarContainer.style.width=t+(void 0!==n.weekWrapper?n.weekWrapper.offsetWidth:0)+"px",n.calendarContainer.style.removeProperty("visibility"),n.calendarContainer.style.removeProperty("display")}}))}function p(e){if(0===n.selectedDates.length){var t=void 0===n.config.minDate||(0,f.compareDates)(new Date,n.config.minDate)>=0?new Date:new Date(n.config.minDate.getTime()),a=(0,f.getDefaultHours)(n.config);t.setHours(a.hours,a.minutes,a.seconds,t.getMilliseconds()),n.selectedDates=[t],n.latestSelectedDateObj=t}void 0!==e&&"blur"!==e.type&&function(e){e.preventDefault();var t="keydown"===e.type,a=(0,u.getEventTarget)(e),i=a;void 0!==n.amPM&&a===n.amPM&&(n.amPM.textContent=n.l10n.amPM[(0,d.int)(n.amPM.textContent===n.l10n.amPM[0])]);var o=parseFloat(i.getAttribute("min")),r=parseFloat(i.getAttribute("max")),l=parseFloat(i.getAttribute("step")),c=parseInt(i.value,10),s=e.delta||(t?38===e.which?1:-1:0),f=c+l*s;if(void 0!==i.value&&2===i.value.length){var m=i===n.hourElement,g=i===n.minuteElement;f<o?(f=r+f+(0,d.int)(!m)+((0,d.int)(m)&&(0,d.int)(!n.amPM)),g&&x(void 0,-1,n.hourElement)):f>r&&(f=i===n.hourElement?f-r-(0,d.int)(!n.amPM):o,g&&x(void 0,1,n.hourElement)),n.amPM&&m&&(1===l?f+c===23:Math.abs(f-c)>l)&&(n.amPM.textContent=n.l10n.amPM[(0,d.int)(n.amPM.textContent===n.l10n.amPM[0])]),i.value=(0,d.pad)(f)}}(e);var i=n._input.value;h(),se(),n._input.value!==i&&n._debouncedChange()}function h(){if(void 0!==n.hourElement&&void 0!==n.minuteElement){var e,t,a=(parseInt(n.hourElement.value.slice(-2),10)||0)%24,i=(parseInt(n.minuteElement.value,10)||0)%60,o=void 0!==n.secondElement?(parseInt(n.secondElement.value,10)||0)%60:0;void 0!==n.amPM&&(e=a,t=n.amPM.textContent,a=e%12+12*(0,d.int)(t===n.l10n.amPM[1]));var r=void 0!==n.config.minTime||n.config.minDate&&n.minDateHasTime&&n.latestSelectedDateObj&&0===(0,f.compareDates)(n.latestSelectedDateObj,n.config.minDate,!0),l=void 0!==n.config.maxTime||n.config.maxDate&&n.maxDateHasTime&&n.latestSelectedDateObj&&0===(0,f.compareDates)(n.latestSelectedDateObj,n.config.maxDate,!0);if(void 0!==n.config.maxTime&&void 0!==n.config.minTime&&n.config.minTime>n.config.maxTime){var c=(0,f.calculateSecondsSinceMidnight)(n.config.minTime.getHours(),n.config.minTime.getMinutes(),n.config.minTime.getSeconds()),s=(0,f.calculateSecondsSinceMidnight)(n.config.maxTime.getHours(),n.config.maxTime.getMinutes(),n.config.maxTime.getSeconds()),u=(0,f.calculateSecondsSinceMidnight)(a,i,o);if(u>s&&u<c){var m=(0,f.parseSeconds)(c);a=m[0],i=m[1],o=m[2]}}else{if(l){var g=void 0!==n.config.maxTime?n.config.maxTime:n.config.maxDate;(a=Math.min(a,g.getHours()))===g.getHours()&&(i=Math.min(i,g.getMinutes())),i===g.getMinutes()&&(o=Math.min(o,g.getSeconds()))}if(r){var p=void 0!==n.config.minTime?n.config.minTime:n.config.minDate;(a=Math.max(a,p.getHours()))===p.getHours()&&i<p.getMinutes()&&(i=p.getMinutes()),i===p.getMinutes()&&(o=Math.max(o,p.getSeconds()))}}C(a,i,o)}}function D(e){var t=e||n.latestSelectedDateObj;t&&t instanceof Date&&C(t.getHours(),t.getMinutes(),t.getSeconds())}function C(e,t,a){void 0!==n.latestSelectedDateObj&&n.latestSelectedDateObj.setHours(e%24,t,a||0,0),n.hourElement&&n.minuteElement&&!n.isMobile&&(n.hourElement.value=(0,d.pad)(n.config.time_24hr?e:(12+e)%12+12*(0,d.int)(e%12==0)),n.minuteElement.value=(0,d.pad)(t),void 0!==n.amPM&&(n.amPM.textContent=n.l10n.amPM[(0,d.int)(e>=12)]),void 0!==n.secondElement&&(n.secondElement.value=(0,d.pad)(a)))}function b(e){var t=(0,u.getEventTarget)(e),n=parseInt(t.value)+(e.delta||0);(n/1e3>1||"Enter"===e.key&&!/[^\d]/.test(n.toString()))&&R(n)}function M(e,t,a,i){return t instanceof Array?t.forEach((function(t){return M(e,t,a,i)})):e instanceof Array?e.forEach((function(e){return M(e,t,a,i)})):(e.addEventListener(t,a,i),void n._handlers.push({remove:function(){return e.removeEventListener(t,a,i)}}))}function y(){ie("onChange")}function w(e,t){var a=void 0!==e?n.parseDate(e):n.latestSelectedDateObj||(n.config.minDate&&n.config.minDate>n.now?n.config.minDate:n.config.maxDate&&n.config.maxDate<n.now?n.config.maxDate:n.now),i=n.currentYear,o=n.currentMonth;try{void 0!==a&&(n.currentYear=a.getFullYear(),n.currentMonth=a.getMonth())}catch(e){e.message="Invalid date supplied: "+a,n.config.errorHandler(e)}t&&n.currentYear!==i&&(ie("onYearChange"),N()),!t||n.currentYear===i&&n.currentMonth===o||ie("onMonthChange"),n.redraw()}function E(e){var t=(0,u.getEventTarget)(e);~t.className.indexOf("arrow")&&x(e,t.classList.contains("arrowUp")?1:-1)}function x(e,t,n){var a=e&&(0,u.getEventTarget)(e),i=n||a&&a.parentNode&&a.parentNode.firstChild,o=oe("increment");o.delta=t,i&&i.dispatchEvent(o)}function k(e,t,a,i){var o=W(t,!0),r=(0,u.createElement)("span",e,t.getDate().toString());return r.dateObj=t,r.$i=i,r.setAttribute("aria-label",n.formatDate(t,n.config.ariaDateFormat)),-1===e.indexOf("hidden")&&0===(0,f.compareDates)(t,n.now)&&(n.todayDateElem=r,r.classList.add("today"),r.setAttribute("aria-current","date")),o?(r.tabIndex=-1,re(t)&&(r.classList.add("selected"),n.selectedDateElem=r,"range"===n.config.mode&&((0,u.toggleClass)(r,"startRange",n.selectedDates[0]&&0===(0,f.compareDates)(t,n.selectedDates[0],!0)),(0,u.toggleClass)(r,"endRange",n.selectedDates[1]&&0===(0,f.compareDates)(t,n.selectedDates[1],!0)),"nextMonthDay"===e&&r.classList.add("inRange")))):r.classList.add("flatpickr-disabled"),"range"===n.config.mode&&function(e){return!("range"!==n.config.mode||n.selectedDates.length<2)&&((0,f.compareDates)(e,n.selectedDates[0])>=0&&(0,f.compareDates)(e,n.selectedDates[1])<=0)}(t)&&!re(t)&&r.classList.add("inRange"),n.weekNumbers&&1===n.config.showMonths&&"prevMonthDay"!==e&&i%7==6&&n.weekNumbers.insertAdjacentHTML("beforeend","<span class='flatpickr-day'>"+n.config.getWeek(t)+"</span>"),ie("onDayCreate",r),r}function T(e){e.focus(),"range"===n.config.mode&&J(e)}function _(e){for(var t=e>0?0:n.config.showMonths-1,a=e>0?n.config.showMonths:-1,i=t;i!=a;i+=e)for(var o=n.daysContainer.children[i],r=e>0?0:o.children.length-1,l=e>0?o.children.length:-1,c=r;c!=l;c+=e){var s=o.children[c];if(-1===s.className.indexOf("hidden")&&W(s.dateObj))return s}}function I(e,t){var i=a(),o=B(i||document.body),r=void 0!==e?e:o?i:void 0!==n.selectedDateElem&&B(n.selectedDateElem)?n.selectedDateElem:void 0!==n.todayDateElem&&B(n.todayDateElem)?n.todayDateElem:_(t>0?1:-1);void 0===r?n._input.focus():o?function(e,t){for(var a=-1===e.className.indexOf("Month")?e.dateObj.getMonth():n.currentMonth,i=t>0?n.config.showMonths:-1,o=t>0?1:-1,r=a-n.currentMonth;r!=i;r+=o)for(var l=n.daysContainer.children[r],c=a-n.currentMonth===r?e.$i+t:t<0?l.children.length-1:0,s=l.children.length,d=c;d>=0&&d<s&&d!=(t>0?s:-1);d+=o){var u=l.children[d];if(-1===u.className.indexOf("hidden")&&W(u.dateObj)&&Math.abs(e.$i-d)>=Math.abs(t))return T(u)}n.changeMonth(o),I(_(o),0)}(r,t):T(r)}function S(e,t){for(var a=(new Date(e,t,1).getDay()-n.l10n.firstDayOfWeek+7)%7,i=n.utils.getDaysInMonth((t-1+12)%12,e),o=n.utils.getDaysInMonth(t,e),r=window.document.createDocumentFragment(),l=n.config.showMonths>1,c=l?"prevMonthDay hidden":"prevMonthDay",s=l?"nextMonthDay hidden":"nextMonthDay",d=i+1-a,f=0;d<=i;d++,f++)r.appendChild(k("flatpickr-day "+c,new Date(e,t-1,d),0,f));for(d=1;d<=o;d++,f++)r.appendChild(k("flatpickr-day",new Date(e,t,d),0,f));for(var m=o+1;m<=42-a&&(1===n.config.showMonths||f%7!=0);m++,f++)r.appendChild(k("flatpickr-day "+s,new Date(e,t+1,m%o),0,f));var g=(0,u.createElement)("div","dayContainer");return g.appendChild(r),g}function O(){if(void 0!==n.daysContainer){(0,u.clearNode)(n.daysContainer),n.weekNumbers&&(0,u.clearNode)(n.weekNumbers);for(var e=document.createDocumentFragment(),t=0;t<n.config.showMonths;t++){var a=new Date(n.currentYear,n.currentMonth,1);a.setMonth(n.currentMonth+t),e.appendChild(S(a.getFullYear(),a.getMonth()))}n.daysContainer.appendChild(e),n.days=n.daysContainer.firstChild,"range"===n.config.mode&&1===n.selectedDates.length&&J()}}function N(){if(!(n.config.showMonths>1||"dropdown"!==n.config.monthSelectorType)){var e=function(e){return!(void 0!==n.config.minDate&&n.currentYear===n.config.minDate.getFullYear()&&e<n.config.minDate.getMonth())&&!(void 0!==n.config.maxDate&&n.currentYear===n.config.maxDate.getFullYear()&&e>n.config.maxDate.getMonth())};n.monthsDropdownContainer.tabIndex=-1,n.monthsDropdownContainer.innerHTML="";for(var t=0;t<12;t++)if(e(t)){var a=(0,u.createElement)("option","flatpickr-monthDropdown-month");a.value=new Date(n.currentYear,t).getMonth().toString(),a.textContent=(0,m.monthToStr)(t,n.config.shorthandCurrentMonth,n.l10n),a.tabIndex=-1,n.currentMonth===t&&(a.selected=!0),n.monthsDropdownContainer.appendChild(a)}}}function A(){var e,t=(0,u.createElement)("div","flatpickr-month"),a=window.document.createDocumentFragment();n.config.showMonths>1||"static"===n.config.monthSelectorType?e=(0,u.createElement)("span","cur-month"):(n.monthsDropdownContainer=(0,u.createElement)("select","flatpickr-monthDropdown-months"),n.monthsDropdownContainer.setAttribute("aria-label",n.l10n.monthAriaLabel),M(n.monthsDropdownContainer,"change",(function(e){var t=(0,u.getEventTarget)(e),a=parseInt(t.value,10);n.changeMonth(a-n.currentMonth),ie("onMonthChange")})),N(),e=n.monthsDropdownContainer);var i=(0,u.createNumberInput)("cur-year",{tabindex:"-1"}),o=i.getElementsByTagName("input")[0];o.setAttribute("aria-label",n.l10n.yearAriaLabel),n.config.minDate&&o.setAttribute("min",n.config.minDate.getFullYear().toString()),n.config.maxDate&&(o.setAttribute("max",n.config.maxDate.getFullYear().toString()),o.disabled=!!n.config.minDate&&n.config.minDate.getFullYear()===n.config.maxDate.getFullYear());var r=(0,u.createElement)("div","flatpickr-current-month");return r.appendChild(e),r.appendChild(i),a.appendChild(r),t.appendChild(a),{container:t,yearElement:o,monthElement:e}}function P(){(0,u.clearNode)(n.monthNav),n.monthNav.appendChild(n.prevMonthNav),n.config.showMonths&&(n.yearElements=[],n.monthElements=[]);for(var e=n.config.showMonths;e--;){var t=A();n.yearElements.push(t.yearElement),n.monthElements.push(t.monthElement),n.monthNav.appendChild(t.container)}n.monthNav.appendChild(n.nextMonthNav)}function Y(){n.weekdayContainer?(0,u.clearNode)(n.weekdayContainer):n.weekdayContainer=(0,u.createElement)("div","flatpickr-weekdays");for(var e=n.config.showMonths;e--;){var t=(0,u.createElement)("div","flatpickr-weekdaycontainer");n.weekdayContainer.appendChild(t)}return F(),n.weekdayContainer}function F(){if(n.weekdayContainer){var e=n.l10n.firstDayOfWeek,t=l(n.l10n.weekdays.shorthand);e>0&&e<t.length&&(t=l(t.splice(e,t.length),t.splice(0,e)));for(var a=n.config.showMonths;a--;)n.weekdayContainer.children[a].innerHTML="\n      <span class='flatpickr-weekday'>\n        "+t.join("</span><span class='flatpickr-weekday'>")+"\n      </span>\n      "}}function j(e,t){void 0===t&&(t=!0);var a=t?e:e-n.currentMonth;a<0&&!0===n._hidePrevMonthArrow||a>0&&!0===n._hideNextMonthArrow||(n.currentMonth+=a,(n.currentMonth<0||n.currentMonth>11)&&(n.currentYear+=n.currentMonth>11?1:-1,n.currentMonth=(n.currentMonth+12)%12,ie("onYearChange"),N()),O(),ie("onMonthChange"),le())}function L(e){return n.calendarContainer.contains(e)}function H(e){if(n.isOpen&&!n.config.inline){var t=(0,u.getEventTarget)(e),a=L(t),i=!(t===n.input||t===n.altInput||n.element.contains(t)||e.path&&e.path.indexOf&&(~e.path.indexOf(n.input)||~e.path.indexOf(n.altInput)))&&!a&&!L(e.relatedTarget),o=!n.config.ignoredFocusElements.some((function(e){return e.contains(t)}));i&&o&&(n.config.allowInput&&n.setDate(n._input.value,!1,n.config.altInput?n.config.altFormat:n.config.dateFormat),void 0!==n.timeContainer&&void 0!==n.minuteElement&&void 0!==n.hourElement&&""!==n.input.value&&void 0!==n.input.value&&p(),n.close(),n.config&&"range"===n.config.mode&&1===n.selectedDates.length&&n.clear(!1))}}function R(e){if(!(!e||n.config.minDate&&e<n.config.minDate.getFullYear()||n.config.maxDate&&e>n.config.maxDate.getFullYear())){var t=e,a=n.currentYear!==t;n.currentYear=t||n.currentYear,n.config.maxDate&&n.currentYear===n.config.maxDate.getFullYear()?n.currentMonth=Math.min(n.config.maxDate.getMonth(),n.currentMonth):n.config.minDate&&n.currentYear===n.config.minDate.getFullYear()&&(n.currentMonth=Math.max(n.config.minDate.getMonth(),n.currentMonth)),a&&(n.redraw(),ie("onYearChange"),N())}}function W(e,t){var a;void 0===t&&(t=!0);var i=n.parseDate(e,void 0,t);if(n.config.minDate&&i&&(0,f.compareDates)(i,n.config.minDate,void 0!==t?t:!n.minDateHasTime)<0||n.config.maxDate&&i&&(0,f.compareDates)(i,n.config.maxDate,void 0!==t?t:!n.maxDateHasTime)>0)return!1;if(!n.config.enable&&0===n.config.disable.length)return!0;if(void 0===i)return!1;for(var o=!!n.config.enable,r=null!==(a=n.config.enable)&&void 0!==a?a:n.config.disable,l=0,c=void 0;l<r.length;l++){if("function"==typeof(c=r[l])&&c(i))return o;if(c instanceof Date&&void 0!==i&&c.getTime()===i.getTime())return o;if("string"==typeof c){var s=n.parseDate(c,void 0,!0);return s&&s.getTime()===i.getTime()?o:!o}if("object"==typeof c&&void 0!==i&&c.from&&c.to&&i.getTime()>=c.from.getTime()&&i.getTime()<=c.to.getTime())return o}return!o}function B(e){return void 0!==n.daysContainer&&(-1===e.className.indexOf("hidden")&&-1===e.className.indexOf("flatpickr-disabled")&&n.daysContainer.contains(e))}function K(e){var t=e.target===n._input,a=n._input.value.trimEnd()!==ce();!t||!a||e.relatedTarget&&L(e.relatedTarget)||n.setDate(n._input.value,!0,e.target===n.altInput?n.config.altFormat:n.config.dateFormat)}function q(t){var i=(0,u.getEventTarget)(t),o=n.config.wrap?e.contains(i):i===n._input,r=n.config.allowInput,l=n.isOpen&&(!r||!o),c=n.config.inline&&o&&!r;if(13===t.keyCode&&o){if(r)return n.setDate(n._input.value,!0,i===n.altInput?n.config.altFormat:n.config.dateFormat),n.close(),i.blur();n.open()}else if(L(i)||l||c){var s=!!n.timeContainer&&n.timeContainer.contains(i);switch(t.keyCode){case 13:s?(t.preventDefault(),p(),G()):Z(t);break;case 27:t.preventDefault(),G();break;case 8:case 46:o&&!n.config.allowInput&&(t.preventDefault(),n.clear());break;case 37:case 39:if(s||o)n.hourElement&&n.hourElement.focus();else{t.preventDefault();var d=a();if(void 0!==n.daysContainer&&(!1===r||d&&B(d))){var f=39===t.keyCode?1:-1;t.ctrlKey?(t.stopPropagation(),j(f),I(_(1),0)):I(void 0,f)}}break;case 38:case 40:t.preventDefault();var m=40===t.keyCode?1:-1;n.daysContainer&&void 0!==i.$i||i===n.input||i===n.altInput?t.ctrlKey?(t.stopPropagation(),R(n.currentYear-m),I(_(1),0)):s||I(void 0,7*m):i===n.currentYearElement?R(n.currentYear-m):n.config.enableTime&&(!s&&n.hourElement&&n.hourElement.focus(),p(t),n._debouncedChange());break;case 9:if(s){var g=[n.hourElement,n.minuteElement,n.secondElement,n.amPM].concat(n.pluginElements).filter((function(e){return e})),v=g.indexOf(i);if(-1!==v){var D=g[v+(t.shiftKey?-1:1)];t.preventDefault(),(D||n._input).focus()}}else!n.config.noCalendar&&n.daysContainer&&n.daysContainer.contains(i)&&t.shiftKey&&(t.preventDefault(),n._input.focus())}}if(void 0!==n.amPM&&i===n.amPM)switch(t.key){case n.l10n.amPM[0].charAt(0):case n.l10n.amPM[0].charAt(0).toLowerCase():n.amPM.textContent=n.l10n.amPM[0],h(),se();break;case n.l10n.amPM[1].charAt(0):case n.l10n.amPM[1].charAt(0).toLowerCase():n.amPM.textContent=n.l10n.amPM[1],h(),se()}(o||L(i))&&ie("onKeyDown",t)}function J(e,t){if(void 0===t&&(t="flatpickr-day"),1===n.selectedDates.length&&(!e||e.classList.contains(t)&&!e.classList.contains("flatpickr-disabled"))){for(var a=e?e.dateObj.getTime():n.days.firstElementChild.dateObj.getTime(),i=n.parseDate(n.selectedDates[0],void 0,!0).getTime(),o=Math.min(a,n.selectedDates[0].getTime()),r=Math.max(a,n.selectedDates[0].getTime()),l=!1,c=0,s=0,d=o;d<r;d+=f.duration.DAY)W(new Date(d),!0)||(l=l||d>o&&d<r,d<i&&(!c||d>c)?c=d:d>i&&(!s||d<s)&&(s=d));Array.from(n.rContainer.querySelectorAll("*:nth-child(-n+"+n.config.showMonths+") > ."+t)).forEach((function(t){var o=t.dateObj.getTime(),r=c>0&&o<c||s>0&&o>s;if(r)return t.classList.add("notAllowed"),void["inRange","startRange","endRange"].forEach((function(e){t.classList.remove(e)}));l&&!r||(["startRange","inRange","endRange","notAllowed"].forEach((function(e){t.classList.remove(e)})),void 0!==e&&(e.classList.add(a<=n.selectedDates[0].getTime()?"startRange":"endRange"),i<a&&o===i?t.classList.add("startRange"):i>a&&o===i&&t.classList.add("endRange"),o>=c&&(0===s||o<=s)&&(0,f.isBetween)(o,i,a)&&t.classList.add("inRange")))}))}}function U(){!n.isOpen||n.config.static||n.config.inline||X()}function $(e){return function(t){var a=n.config["_"+e+"Date"]=n.parseDate(t,n.config.dateFormat),i=n.config["_"+("min"===e?"max":"min")+"Date"];void 0!==a&&(n["min"===e?"minDateHasTime":"maxDateHasTime"]=a.getHours()>0||a.getMinutes()>0||a.getSeconds()>0),n.selectedDates&&(n.selectedDates=n.selectedDates.filter((function(e){return W(e)})),n.selectedDates.length||"min"!==e||D(a),se()),n.daysContainer&&(z(),void 0!==a?n.currentYearElement[e]=a.getFullYear().toString():n.currentYearElement.removeAttribute(e),n.currentYearElement.disabled=!!i&&void 0!==a&&i.getFullYear()===a.getFullYear())}}function Q(){return n.config.wrap?e.querySelector("[data-input]"):e}function V(){"object"!=typeof n.config.locale&&void 0===v.l10ns[n.config.locale]&&n.config.errorHandler(new Error("flatpickr: invalid locale "+n.config.locale)),n.l10n=r(r({},v.l10ns.default),"object"==typeof n.config.locale?n.config.locale:"default"!==n.config.locale?v.l10ns[n.config.locale]:void 0),m.tokenRegex.D="("+n.l10n.weekdays.shorthand.join("|")+")",m.tokenRegex.l="("+n.l10n.weekdays.longhand.join("|")+")",m.tokenRegex.M="("+n.l10n.months.shorthand.join("|")+")",m.tokenRegex.F="("+n.l10n.months.longhand.join("|")+")",m.tokenRegex.K="("+n.l10n.amPM[0]+"|"+n.l10n.amPM[1]+"|"+n.l10n.amPM[0].toLowerCase()+"|"+n.l10n.amPM[1].toLowerCase()+")",void 0===r(r({},t),JSON.parse(JSON.stringify(e.dataset||{}))).time_24hr&&void 0===v.defaultConfig.time_24hr&&(n.config.time_24hr=n.l10n.time_24hr),n.formatDate=(0,f.createDateFormatter)(n),n.parseDate=(0,f.createDateParser)({config:n.config,l10n:n.l10n})}function X(e){if("function"!=typeof n.config.position){if(void 0!==n.calendarContainer){ie("onPreCalendarPosition");var t=e||n._positionElement,a=Array.prototype.reduce.call(n.calendarContainer.children,(function(e,t){return e+t.offsetHeight}),0),i=n.calendarContainer.offsetWidth,o=n.config.position.split(" "),r=o[0],l=o.length>1?o[1]:null,c=t.getBoundingClientRect(),s=window.innerHeight-c.bottom,d="above"===r||"below"!==r&&s<a&&c.top>a,f=window.pageYOffset+c.top+(d?-a-2:t.offsetHeight+2);if((0,u.toggleClass)(n.calendarContainer,"arrowTop",!d),(0,u.toggleClass)(n.calendarContainer,"arrowBottom",d),!n.config.inline){var m=window.pageXOffset+c.left,g=!1,p=!1;"center"===l?(m-=(i-c.width)/2,g=!0):"right"===l&&(m-=i-c.width,p=!0),(0,u.toggleClass)(n.calendarContainer,"arrowLeft",!g&&!p),(0,u.toggleClass)(n.calendarContainer,"arrowCenter",g),(0,u.toggleClass)(n.calendarContainer,"arrowRight",p);var h=window.document.body.offsetWidth-(window.pageXOffset+c.right),v=m+i>window.document.body.offsetWidth,D=h+i>window.document.body.offsetWidth;if((0,u.toggleClass)(n.calendarContainer,"rightMost",v),!n.config.static)if(n.calendarContainer.style.top=f+"px",v)if(D){var C=function(){for(var e=null,t=0;t<document.styleSheets.length;t++){var n=document.styleSheets[t];if(n.cssRules){try{n.cssRules}catch(e){continue}e=n;break}}return null!=e?e:(a=document.createElement("style"),document.head.appendChild(a),a.sheet);var a}();if(void 0===C)return;var b=window.document.body.offsetWidth,M=Math.max(0,b/2-i/2),y=C.cssRules.length,w="{left:"+c.left+"px;right:auto;}";(0,u.toggleClass)(n.calendarContainer,"rightMost",!1),(0,u.toggleClass)(n.calendarContainer,"centerMost",!0),C.insertRule(".flatpickr-calendar.centerMost:before,.flatpickr-calendar.centerMost:after"+w,y),n.calendarContainer.style.left=M+"px",n.calendarContainer.style.right="auto"}else n.calendarContainer.style.left="auto",n.calendarContainer.style.right=h+"px";else n.calendarContainer.style.left=m+"px",n.calendarContainer.style.right="auto"}}}else n.config.position(n,e)}function z(){n.config.noCalendar||n.isMobile||(N(),le(),O())}function G(){n._input.focus(),-1!==window.navigator.userAgent.indexOf("MSIE")||void 0!==navigator.msMaxTouchPoints?setTimeout(n.close,0):n.close()}function Z(e){e.preventDefault(),e.stopPropagation();var t=(0,u.findParent)((0,u.getEventTarget)(e),(function(e){return e.classList&&e.classList.contains("flatpickr-day")&&!e.classList.contains("flatpickr-disabled")&&!e.classList.contains("notAllowed")}));if(void 0!==t){var a=t,i=n.latestSelectedDateObj=new Date(a.dateObj.getTime()),o=(i.getMonth()<n.currentMonth||i.getMonth()>n.currentMonth+n.config.showMonths-1)&&"range"!==n.config.mode;if(n.selectedDateElem=a,"single"===n.config.mode)n.selectedDates=[i];else if("multiple"===n.config.mode){var r=re(i);r?n.selectedDates.splice(parseInt(r),1):n.selectedDates.push(i)}else"range"===n.config.mode&&(2===n.selectedDates.length&&n.clear(!1,!1),n.latestSelectedDateObj=i,n.selectedDates.push(i),0!==(0,f.compareDates)(i,n.selectedDates[0],!0)&&n.selectedDates.sort((function(e,t){return e.getTime()-t.getTime()})));if(h(),o){var l=n.currentYear!==i.getFullYear();n.currentYear=i.getFullYear(),n.currentMonth=i.getMonth(),l&&(ie("onYearChange"),N()),ie("onMonthChange")}if(le(),O(),se(),o||"range"===n.config.mode||1!==n.config.showMonths?void 0!==n.selectedDateElem&&void 0===n.hourElement&&n.selectedDateElem&&n.selectedDateElem.focus():T(a),void 0!==n.hourElement&&void 0!==n.hourElement&&n.hourElement.focus(),n.config.closeOnSelect){var c="single"===n.config.mode&&!n.config.enableTime,s="range"===n.config.mode&&2===n.selectedDates.length&&!n.config.enableTime;(c||s)&&G()}y()}}n.parseDate=(0,f.createDateParser)({config:n.config,l10n:n.l10n}),n._handlers=[],n.pluginElements=[],n.loadedPlugins=[],n._bind=M,n._setHoursFromDate=D,n._positionCalendar=X,n.changeMonth=j,n.changeYear=R,n.clear=function(e,t){void 0===e&&(e=!0);void 0===t&&(t=!0);n.input.value="",void 0!==n.altInput&&(n.altInput.value="");void 0!==n.mobileInput&&(n.mobileInput.value="");n.selectedDates=[],n.latestSelectedDateObj=void 0,!0===t&&(n.currentYear=n._initialDate.getFullYear(),n.currentMonth=n._initialDate.getMonth());if(!0===n.config.enableTime){var a=(0,f.getDefaultHours)(n.config);C(a.hours,a.minutes,a.seconds)}n.redraw(),e&&ie("onChange")},n.close=function(){n.isOpen=!1,n.isMobile||(void 0!==n.calendarContainer&&n.calendarContainer.classList.remove("open"),void 0!==n._input&&n._input.classList.remove("active"));ie("onClose")},n.onMouseOver=J,n._createElement=u.createElement,n.createDay=k,n.destroy=function(){void 0!==n.config&&ie("onDestroy");for(var e=n._handlers.length;e--;)n._handlers[e].remove();if(n._handlers=[],n.mobileInput)n.mobileInput.parentNode&&n.mobileInput.parentNode.removeChild(n.mobileInput),n.mobileInput=void 0;else if(n.calendarContainer&&n.calendarContainer.parentNode)if(n.config.static&&n.calendarContainer.parentNode){var t=n.calendarContainer.parentNode;if(t.lastChild&&t.removeChild(t.lastChild),t.parentNode){for(;t.firstChild;)t.parentNode.insertBefore(t.firstChild,t);t.parentNode.removeChild(t)}}else n.calendarContainer.parentNode.removeChild(n.calendarContainer);n.altInput&&(n.input.type="text",n.altInput.parentNode&&n.altInput.parentNode.removeChild(n.altInput),delete n.altInput);n.input&&(n.input.type=n.input._type,n.input.classList.remove("flatpickr-input"),n.input.removeAttribute("readonly"));["_showTimeInput","latestSelectedDateObj","_hideNextMonthArrow","_hidePrevMonthArrow","__hideNextMonthArrow","__hidePrevMonthArrow","isMobile","isOpen","selectedDateElem","minDateHasTime","maxDateHasTime","days","daysContainer","_input","_positionElement","innerContainer","rContainer","monthNav","todayDateElem","calendarContainer","weekdayContainer","prevMonthNav","nextMonthNav","monthsDropdownContainer","currentMonthElement","currentYearElement","navigationCurrentMonth","selectedDateElem","config"].forEach((function(e){try{delete n[e]}catch(e){}}))},n.isEnabled=W,n.jumpToDate=w,n.updateValue=se,n.open=function(e,t){void 0===t&&(t=n._positionElement);if(!0===n.isMobile){if(e){e.preventDefault();var a=(0,u.getEventTarget)(e);a&&a.blur()}return void 0!==n.mobileInput&&(n.mobileInput.focus(),n.mobileInput.click()),void ie("onOpen")}if(n._input.disabled||n.config.inline)return;var i=n.isOpen;n.isOpen=!0,i||(n.calendarContainer.classList.add("open"),n._input.classList.add("active"),ie("onOpen"),X(t));!0===n.config.enableTime&&!0===n.config.noCalendar&&(!1!==n.config.allowInput||void 0!==e&&n.timeContainer.contains(e.relatedTarget)||setTimeout((function(){return n.hourElement.select()}),50))},n.redraw=z,n.set=function(e,t){if(null!==e&&"object"==typeof e)for(var a in Object.assign(n.config,e),e)void 0!==ee[a]&&ee[a].forEach((function(e){return e()}));else n.config[e]=t,void 0!==ee[e]?ee[e].forEach((function(e){return e()})):c.HOOKS.indexOf(e)>-1&&(n.config[e]=(0,d.arrayify)(t));n.redraw(),se(!0)},n.setDate=function(e,t,a){void 0===t&&(t=!1);void 0===a&&(a=n.config.dateFormat);if(0!==e&&!e||e instanceof Array&&0===e.length)return n.clear(t);te(e,a),n.latestSelectedDateObj=n.selectedDates[n.selectedDates.length-1],n.redraw(),w(void 0,t),D(),0===n.selectedDates.length&&n.clear(!1);se(t),t&&ie("onChange")},n.toggle=function(e){if(!0===n.isOpen)return n.close();n.open(e)};var ee={locale:[V,F],showMonths:[P,o,Y],minDate:[w],maxDate:[w],positionElement:[ae],clickOpens:[function(){!0===n.config.clickOpens?(M(n._input,"focus",n.open),M(n._input,"click",n.open)):(n._input.removeEventListener("focus",n.open),n._input.removeEventListener("click",n.open))}]};function te(e,t){var a=[];if(e instanceof Array)a=e.map((function(e){return n.parseDate(e,t)}));else if(e instanceof Date||"number"==typeof e)a=[n.parseDate(e,t)];else if("string"==typeof e)switch(n.config.mode){case"single":case"time":a=[n.parseDate(e,t)];break;case"multiple":a=e.split(n.config.conjunction).map((function(e){return n.parseDate(e,t)}));break;case"range":a=e.split(n.l10n.rangeSeparator).map((function(e){return n.parseDate(e,t)}))}else n.config.errorHandler(new Error("Invalid date supplied: "+JSON.stringify(e)));n.selectedDates=n.config.allowInvalidPreload?a:a.filter((function(e){return e instanceof Date&&W(e,!1)})),"range"===n.config.mode&&n.selectedDates.sort((function(e,t){return e.getTime()-t.getTime()}))}function ne(e){return e.slice().map((function(e){return"string"==typeof e||"number"==typeof e||e instanceof Date?n.parseDate(e,void 0,!0):e&&"object"==typeof e&&e.from&&e.to?{from:n.parseDate(e.from,void 0),to:n.parseDate(e.to,void 0)}:e})).filter((function(e){return e}))}function ae(){n._positionElement=n.config.positionElement||n._input}function ie(e,t){if(void 0!==n.config){var a=n.config[e];if(void 0!==a&&a.length>0)for(var i=0;a[i]&&i<a.length;i++)a[i](n.selectedDates,n.input.value,n,t);"onChange"===e&&(n.input.dispatchEvent(oe("change")),n.input.dispatchEvent(oe("input")))}}function oe(e){var t=document.createEvent("Event");return t.initEvent(e,!0,!0),t}function re(e){for(var t=0;t<n.selectedDates.length;t++){var a=n.selectedDates[t];if(a instanceof Date&&0===(0,f.compareDates)(a,e))return""+t}return!1}function le(){n.config.noCalendar||n.isMobile||!n.monthNav||(n.yearElements.forEach((function(e,t){var a=new Date(n.currentYear,n.currentMonth,1);a.setMonth(n.currentMonth+t),n.config.showMonths>1||"static"===n.config.monthSelectorType?n.monthElements[t].textContent=(0,m.monthToStr)(a.getMonth(),n.config.shorthandCurrentMonth,n.l10n)+" ":n.monthsDropdownContainer.value=a.getMonth().toString(),e.value=a.getFullYear().toString()})),n._hidePrevMonthArrow=void 0!==n.config.minDate&&(n.currentYear===n.config.minDate.getFullYear()?n.currentMonth<=n.config.minDate.getMonth():n.currentYear<n.config.minDate.getFullYear()),n._hideNextMonthArrow=void 0!==n.config.maxDate&&(n.currentYear===n.config.maxDate.getFullYear()?n.currentMonth+1>n.config.maxDate.getMonth():n.currentYear>n.config.maxDate.getFullYear()))}function ce(e){var t=e||(n.config.altInput?n.config.altFormat:n.config.dateFormat);return n.selectedDates.map((function(e){return n.formatDate(e,t)})).filter((function(e,t,a){return"range"!==n.config.mode||n.config.enableTime||a.indexOf(e)===t})).join("range"!==n.config.mode?n.config.conjunction:n.l10n.rangeSeparator)}function se(e){void 0===e&&(e=!0),void 0!==n.mobileInput&&n.mobileFormatStr&&(n.mobileInput.value=void 0!==n.latestSelectedDateObj?n.formatDate(n.latestSelectedDateObj,n.mobileFormatStr):""),n.input.value=ce(n.config.dateFormat),void 0!==n.altInput&&(n.altInput.value=ce(n.config.altFormat)),!1!==e&&ie("onValueUpdate")}function de(e){var t=(0,u.getEventTarget)(e),a=n.prevMonthNav.contains(t),i=n.nextMonthNav.contains(t);a||i?j(a?-1:1):n.yearElements.indexOf(t)>=0?t.select():t.classList.contains("arrowUp")?n.changeYear(n.currentYear+1):t.classList.contains("arrowDown")&&n.changeYear(n.currentYear-1)}return function(){n.element=n.input=e,n.isOpen=!1,function(){var a=["wrap","weekNumbers","allowInput","allowInvalidPreload","clickOpens","time_24hr","enableTime","noCalendar","altInput","shorthandCurrentMonth","inline","static","enableSeconds","disableMobile"],o=r(r({},JSON.parse(JSON.stringify(e.dataset||{}))),t),l={};n.config.parseDate=o.parseDate,n.config.formatDate=o.formatDate,Object.defineProperty(n.config,"enable",{get:function(){return n.config._enable},set:function(e){n.config._enable=ne(e)}}),Object.defineProperty(n.config,"disable",{get:function(){return n.config._disable},set:function(e){n.config._disable=ne(e)}});var s="time"===o.mode;if(!o.dateFormat&&(o.enableTime||s)){var u=v.defaultConfig.dateFormat||c.defaults.dateFormat;l.dateFormat=o.noCalendar||s?"H:i"+(o.enableSeconds?":S":""):u+" H:i"+(o.enableSeconds?":S":"")}if(o.altInput&&(o.enableTime||s)&&!o.altFormat){var f=v.defaultConfig.altFormat||c.defaults.altFormat;l.altFormat=o.noCalendar||s?"h:i"+(o.enableSeconds?":S K":" K"):f+" h:i"+(o.enableSeconds?":S":"")+" K"}Object.defineProperty(n.config,"minDate",{get:function(){return n.config._minDate},set:$("min")}),Object.defineProperty(n.config,"maxDate",{get:function(){return n.config._maxDate},set:$("max")});var m=function(e){return function(t){n.config["min"===e?"_minTime":"_maxTime"]=n.parseDate(t,"H:i:S")}};Object.defineProperty(n.config,"minTime",{get:function(){return n.config._minTime},set:m("min")}),Object.defineProperty(n.config,"maxTime",{get:function(){return n.config._maxTime},set:m("max")}),"time"===o.mode&&(n.config.noCalendar=!0,n.config.enableTime=!0);Object.assign(n.config,l,o);for(var g=0;g<a.length;g++)n.config[a[g]]=!0===n.config[a[g]]||"true"===n.config[a[g]];c.HOOKS.filter((function(e){return void 0!==n.config[e]})).forEach((function(e){n.config[e]=(0,d.arrayify)(n.config[e]||[]).map(i)})),n.isMobile=!n.config.disableMobile&&!n.config.inline&&"single"===n.config.mode&&!n.config.disable.length&&!n.config.enable&&!n.config.weekNumbers&&/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);for(g=0;g<n.config.plugins.length;g++){var p=n.config.plugins[g](n)||{};for(var h in p)c.HOOKS.indexOf(h)>-1?n.config[h]=(0,d.arrayify)(p[h]).map(i).concat(n.config[h]):void 0===o[h]&&(n.config[h]=p[h])}o.altInputClass||(n.config.altInputClass=Q().className+" "+n.config.altInputClass);ie("onParseConfig")}(),V(),function(){if(n.input=Q(),!n.input)return void n.config.errorHandler(new Error("Invalid input element specified"));n.input._type=n.input.type,n.input.type="text",n.input.classList.add("flatpickr-input"),n._input=n.input,n.config.altInput&&(n.altInput=(0,u.createElement)(n.input.nodeName,n.config.altInputClass),n._input=n.altInput,n.altInput.placeholder=n.input.placeholder,n.altInput.disabled=n.input.disabled,n.altInput.required=n.input.required,n.altInput.tabIndex=n.input.tabIndex,n.altInput.type="text",n.input.setAttribute("type","hidden"),!n.config.static&&n.input.parentNode&&n.input.parentNode.insertBefore(n.altInput,n.input.nextSibling));n.config.allowInput||n._input.setAttribute("readonly","readonly");ae()}(),function(){n.selectedDates=[],n.now=n.parseDate(n.config.now)||new Date;var e=n.config.defaultDate||("INPUT"!==n.input.nodeName&&"TEXTAREA"!==n.input.nodeName||!n.input.placeholder||n.input.value!==n.input.placeholder?n.input.value:null);e&&te(e,n.config.dateFormat);n._initialDate=n.selectedDates.length>0?n.selectedDates[0]:n.config.minDate&&n.config.minDate.getTime()>n.now.getTime()?n.config.minDate:n.config.maxDate&&n.config.maxDate.getTime()<n.now.getTime()?n.config.maxDate:n.now,n.currentYear=n._initialDate.getFullYear(),n.currentMonth=n._initialDate.getMonth(),n.selectedDates.length>0&&(n.latestSelectedDateObj=n.selectedDates[0]);void 0!==n.config.minTime&&(n.config.minTime=n.parseDate(n.config.minTime,"H:i"));void 0!==n.config.maxTime&&(n.config.maxTime=n.parseDate(n.config.maxTime,"H:i"));n.minDateHasTime=!!n.config.minDate&&(n.config.minDate.getHours()>0||n.config.minDate.getMinutes()>0||n.config.minDate.getSeconds()>0),n.maxDateHasTime=!!n.config.maxDate&&(n.config.maxDate.getHours()>0||n.config.maxDate.getMinutes()>0||n.config.maxDate.getSeconds()>0)}(),n.utils={getDaysInMonth:function(e,t){return void 0===e&&(e=n.currentMonth),void 0===t&&(t=n.currentYear),1===e&&(t%4==0&&t%100!=0||t%400==0)?29:n.l10n.daysInMonth[e]}},n.isMobile||function(){var e=window.document.createDocumentFragment();if(n.calendarContainer=(0,u.createElement)("div","flatpickr-calendar"),n.calendarContainer.tabIndex=-1,!n.config.noCalendar){if(e.appendChild((n.monthNav=(0,u.createElement)("div","flatpickr-months"),n.yearElements=[],n.monthElements=[],n.prevMonthNav=(0,u.createElement)("span","flatpickr-prev-month"),n.prevMonthNav.innerHTML=n.config.prevArrow,n.nextMonthNav=(0,u.createElement)("span","flatpickr-next-month"),n.nextMonthNav.innerHTML=n.config.nextArrow,P(),Object.defineProperty(n,"_hidePrevMonthArrow",{get:function(){return n.__hidePrevMonthArrow},set:function(e){n.__hidePrevMonthArrow!==e&&((0,u.toggleClass)(n.prevMonthNav,"flatpickr-disabled",e),n.__hidePrevMonthArrow=e)}}),Object.defineProperty(n,"_hideNextMonthArrow",{get:function(){return n.__hideNextMonthArrow},set:function(e){n.__hideNextMonthArrow!==e&&((0,u.toggleClass)(n.nextMonthNav,"flatpickr-disabled",e),n.__hideNextMonthArrow=e)}}),n.currentYearElement=n.yearElements[0],le(),n.monthNav)),n.innerContainer=(0,u.createElement)("div","flatpickr-innerContainer"),n.config.weekNumbers){var t=function(){n.calendarContainer.classList.add("hasWeeks");var e=(0,u.createElement)("div","flatpickr-weekwrapper");e.appendChild((0,u.createElement)("span","flatpickr-weekday",n.l10n.weekAbbreviation));var t=(0,u.createElement)("div","flatpickr-weeks");return e.appendChild(t),{weekWrapper:e,weekNumbers:t}}(),a=t.weekWrapper,i=t.weekNumbers;n.innerContainer.appendChild(a),n.weekNumbers=i,n.weekWrapper=a}n.rContainer=(0,u.createElement)("div","flatpickr-rContainer"),n.rContainer.appendChild(Y()),n.daysContainer||(n.daysContainer=(0,u.createElement)("div","flatpickr-days"),n.daysContainer.tabIndex=-1),O(),n.rContainer.appendChild(n.daysContainer),n.innerContainer.appendChild(n.rContainer),e.appendChild(n.innerContainer)}n.config.enableTime&&e.appendChild(function(){n.calendarContainer.classList.add("hasTime"),n.config.noCalendar&&n.calendarContainer.classList.add("noCalendar");var e=(0,f.getDefaultHours)(n.config);n.timeContainer=(0,u.createElement)("div","flatpickr-time"),n.timeContainer.tabIndex=-1;var t=(0,u.createElement)("span","flatpickr-time-separator",":"),a=(0,u.createNumberInput)("flatpickr-hour",{"aria-label":n.l10n.hourAriaLabel});n.hourElement=a.getElementsByTagName("input")[0];var i=(0,u.createNumberInput)("flatpickr-minute",{"aria-label":n.l10n.minuteAriaLabel});n.minuteElement=i.getElementsByTagName("input")[0],n.hourElement.tabIndex=n.minuteElement.tabIndex=-1,n.hourElement.value=(0,d.pad)(n.latestSelectedDateObj?n.latestSelectedDateObj.getHours():n.config.time_24hr?e.hours:function(e){switch(e%24){case 0:case 12:return 12;default:return e%12}}(e.hours)),n.minuteElement.value=(0,d.pad)(n.latestSelectedDateObj?n.latestSelectedDateObj.getMinutes():e.minutes),n.hourElement.setAttribute("step",n.config.hourIncrement.toString()),n.minuteElement.setAttribute("step",n.config.minuteIncrement.toString()),n.hourElement.setAttribute("min",n.config.time_24hr?"0":"1"),n.hourElement.setAttribute("max",n.config.time_24hr?"23":"12"),n.hourElement.setAttribute("maxlength","2"),n.minuteElement.setAttribute("min","0"),n.minuteElement.setAttribute("max","59"),n.minuteElement.setAttribute("maxlength","2"),n.timeContainer.appendChild(a),n.timeContainer.appendChild(t),n.timeContainer.appendChild(i),n.config.time_24hr&&n.timeContainer.classList.add("time24hr");if(n.config.enableSeconds){n.timeContainer.classList.add("hasSeconds");var o=(0,u.createNumberInput)("flatpickr-second");n.secondElement=o.getElementsByTagName("input")[0],n.secondElement.value=(0,d.pad)(n.latestSelectedDateObj?n.latestSelectedDateObj.getSeconds():e.seconds),n.secondElement.setAttribute("step",n.minuteElement.getAttribute("step")),n.secondElement.setAttribute("min","0"),n.secondElement.setAttribute("max","59"),n.secondElement.setAttribute("maxlength","2"),n.timeContainer.appendChild((0,u.createElement)("span","flatpickr-time-separator",":")),n.timeContainer.appendChild(o)}n.config.time_24hr||(n.amPM=(0,u.createElement)("span","flatpickr-am-pm",n.l10n.amPM[(0,d.int)((n.latestSelectedDateObj?n.hourElement.value:n.config.defaultHour)>11)]),n.amPM.title=n.l10n.toggleTitle,n.amPM.tabIndex=-1,n.timeContainer.appendChild(n.amPM));return n.timeContainer}());(0,u.toggleClass)(n.calendarContainer,"rangeMode","range"===n.config.mode),(0,u.toggleClass)(n.calendarContainer,"animate",!0===n.config.animate),(0,u.toggleClass)(n.calendarContainer,"multiMonth",n.config.showMonths>1),n.calendarContainer.appendChild(e);var o=void 0!==n.config.appendTo&&void 0!==n.config.appendTo.nodeType;if((n.config.inline||n.config.static)&&(n.calendarContainer.classList.add(n.config.inline?"inline":"static"),n.config.inline&&(!o&&n.element.parentNode?n.element.parentNode.insertBefore(n.calendarContainer,n._input.nextSibling):void 0!==n.config.appendTo&&n.config.appendTo.appendChild(n.calendarContainer)),n.config.static)){var r=(0,u.createElement)("div","flatpickr-wrapper");n.element.parentNode&&n.element.parentNode.insertBefore(r,n.element),r.appendChild(n.element),n.altInput&&r.appendChild(n.altInput),r.appendChild(n.calendarContainer)}n.config.static||n.config.inline||(void 0!==n.config.appendTo?n.config.appendTo:window.document.body).appendChild(n.calendarContainer)}(),function(){n.config.wrap&&["open","close","toggle","clear"].forEach((function(e){Array.prototype.forEach.call(n.element.querySelectorAll("[data-"+e+"]"),(function(t){return M(t,"click",n[e])}))}));if(n.isMobile)return void function(){var e=n.config.enableTime?n.config.noCalendar?"time":"datetime-local":"date";n.mobileInput=(0,u.createElement)("input",n.input.className+" flatpickr-mobile"),n.mobileInput.tabIndex=1,n.mobileInput.type=e,n.mobileInput.disabled=n.input.disabled,n.mobileInput.required=n.input.required,n.mobileInput.placeholder=n.input.placeholder,n.mobileFormatStr="datetime-local"===e?"Y-m-d\\TH:i:S":"date"===e?"Y-m-d":"H:i:S",n.selectedDates.length>0&&(n.mobileInput.defaultValue=n.mobileInput.value=n.formatDate(n.selectedDates[0],n.mobileFormatStr));n.config.minDate&&(n.mobileInput.min=n.formatDate(n.config.minDate,"Y-m-d"));n.config.maxDate&&(n.mobileInput.max=n.formatDate(n.config.maxDate,"Y-m-d"));n.input.getAttribute("step")&&(n.mobileInput.step=String(n.input.getAttribute("step")));n.input.type="hidden",void 0!==n.altInput&&(n.altInput.type="hidden");try{n.input.parentNode&&n.input.parentNode.insertBefore(n.mobileInput,n.input.nextSibling)}catch(e){}M(n.mobileInput,"change",(function(e){n.setDate((0,u.getEventTarget)(e).value,!1,n.mobileFormatStr),ie("onChange"),ie("onClose")}))}();var e=(0,d.debounce)(U,50);n._debouncedChange=(0,d.debounce)(y,g),n.daysContainer&&!/iPhone|iPad|iPod/i.test(navigator.userAgent)&&M(n.daysContainer,"mouseover",(function(e){"range"===n.config.mode&&J((0,u.getEventTarget)(e))}));M(n._input,"keydown",q),void 0!==n.calendarContainer&&M(n.calendarContainer,"keydown",q);n.config.inline||n.config.static||M(window,"resize",e);void 0!==window.ontouchstart?M(window.document,"touchstart",H):M(window.document,"mousedown",H);M(window.document,"focus",H,{capture:!0}),!0===n.config.clickOpens&&(M(n._input,"focus",n.open),M(n._input,"click",n.open));void 0!==n.daysContainer&&(M(n.monthNav,"click",de),M(n.monthNav,["keyup","increment"],b),M(n.daysContainer,"click",Z));if(void 0!==n.timeContainer&&void 0!==n.minuteElement&&void 0!==n.hourElement){var t=function(e){return(0,u.getEventTarget)(e).select()};M(n.timeContainer,["increment"],p),M(n.timeContainer,"blur",p,{capture:!0}),M(n.timeContainer,"click",E),M([n.hourElement,n.minuteElement],["focus","click"],t),void 0!==n.secondElement&&M(n.secondElement,"focus",(function(){return n.secondElement&&n.secondElement.select()})),void 0!==n.amPM&&M(n.amPM,"click",(function(e){p(e)}))}n.config.allowInput&&M(n._input,"blur",K)}(),(n.selectedDates.length||n.config.noCalendar)&&(n.config.enableTime&&D(n.config.noCalendar?n.latestSelectedDateObj:void 0),se(!1)),o();var a=/^((?!chrome|android).)*safari/i.test(navigator.userAgent);!n.isMobile&&a&&X(),ie("onReady")}(),n}function h(e,t){for(var n=Array.prototype.slice.call(e).filter((function(e){return e instanceof HTMLElement})),a=[],i=0;i<n.length;i++){var o=n[i];try{if(null!==o.getAttribute("data-fp-omit"))continue;void 0!==o._flatpickr&&(o._flatpickr.destroy(),o._flatpickr=void 0),o._flatpickr=p(o,t||{}),a.push(o._flatpickr)}catch(e){console.error(e)}}return 1===a.length?a[0]:a}"undefined"!=typeof HTMLElement&&"undefined"!=typeof HTMLCollection&&"undefined"!=typeof NodeList&&(HTMLCollection.prototype.flatpickr=NodeList.prototype.flatpickr=function(e){return h(this,e)},HTMLElement.prototype.flatpickr=function(e){return h([this],e)});var v=function(e,t){return"string"==typeof e?h(window.document.querySelectorAll(e),t):e instanceof Node?h([e],t):h(e,t)};v.defaultConfig={},v.l10ns={en:r({},s.default),default:r({},s.default)},v.localize=function(e){v.l10ns.default=r(r({},v.l10ns.default),e)},v.setDefaults=function(e){v.defaultConfig=r(r({},v.defaultConfig),e)},v.parseDate=(0,f.createDateParser)({}),v.formatDate=(0,f.createDateFormatter)({}),v.compareDates=f.compareDates,"undefined"!=typeof jQuery&&void 0!==jQuery.fn&&(jQuery.fn.flatpickr=function(e){return h(this,e)}),Date.prototype.fp_incr=function(e){return new Date(this.getFullYear(),this.getMonth(),this.getDate()+("string"==typeof e?parseInt(e,10):e))},"undefined"!=typeof window&&(window.flatpickr=v),n.default=v},
632: function _(e,n,o,t,a){t(),o.HOOKS=["onChange","onClose","onDayCreate","onDestroy","onKeyDown","onMonthChange","onOpen","onParseConfig","onReady","onValueUpdate","onYearChange","onPreCalendarPosition"],o.defaults={_disable:[],allowInput:!1,allowInvalidPreload:!1,altFormat:"F j, Y",altInput:!1,altInputClass:"form-control input",animate:"object"==typeof window&&-1===window.navigator.userAgent.indexOf("MSIE"),ariaDateFormat:"F j, Y",autoFillDefaultTime:!0,clickOpens:!0,closeOnSelect:!0,conjunction:", ",dateFormat:"Y-m-d",defaultHour:12,defaultMinute:0,defaultSeconds:0,disable:[],disableMobile:!1,enableSeconds:!1,enableTime:!1,errorHandler:function(e){return"undefined"!=typeof console&&console.warn(e)},getWeek:function(e){var n=new Date(e.getTime());n.setHours(0,0,0,0),n.setDate(n.getDate()+3-(n.getDay()+6)%7);var o=new Date(n.getFullYear(),0,4);return 1+Math.round(((n.getTime()-o.getTime())/864e5-3+(o.getDay()+6)%7)/7)},hourIncrement:1,ignoredFocusElements:[],inline:!1,locale:"default",minuteIncrement:5,mode:"single",monthSelectorType:"dropdown",nextArrow:"<svg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' viewBox='0 0 17 17'><g></g><path d='M13.207 8.472l-7.854 7.854-0.707-0.707 7.146-7.146-7.146-7.148 0.707-0.707 7.854 7.854z' /></svg>",noCalendar:!1,now:new Date,onChange:[],onClose:[],onDayCreate:[],onDestroy:[],onKeyDown:[],onMonthChange:[],onOpen:[],onParseConfig:[],onReady:[],onValueUpdate:[],onYearChange:[],onPreCalendarPosition:[],plugins:[],position:"auto",positionElement:void 0,prevArrow:"<svg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' viewBox='0 0 17 17'><g></g><path d='M5.207 8.471l7.146 7.147-0.707 0.707-7.853-7.854 7.854-7.853 0.707 0.707-7.147 7.146z' /></svg>",shorthandCurrentMonth:!1,showMonths:1,static:!1,time_24hr:!1,weekNumbers:!1,wrap:!1}},
633: function _(e,r,a,n,t){n(),a.english={weekdays:{shorthand:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],longhand:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},months:{shorthand:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],longhand:["January","February","March","April","May","June","July","August","September","October","November","December"]},daysInMonth:[31,28,31,30,31,30,31,31,30,31,30,31],firstDayOfWeek:0,ordinal:function(e){var r=e%100;if(r>3&&r<21)return"th";switch(r%10){case 1:return"st";case 2:return"nd";case 3:return"rd";default:return"th"}},rangeSeparator:" to ",weekAbbreviation:"Wk",scrollTitle:"Scroll to increment",toggleTitle:"Click to toggle",amPM:["AM","PM"],yearAriaLabel:"Year",monthAriaLabel:"Month",hourAriaLabel:"Hour",minuteAriaLabel:"Minute",time_24hr:!1},a.default=a.english},
634: function _(n,t,r,i,u){i(),r.debounce=function(n,t){var r;return function(){var i=this,u=arguments;clearTimeout(r),r=setTimeout((function(){return n.apply(i,u)}),t)}};r.pad=function(n,t){return void 0===t&&(t=2),("000"+n).slice(-1*t)};r.int=function(n){return!0===n?1:0};r.arrayify=function(n){return n instanceof Array?n:[n]}},
635: function _(t,e,n,r,a){function i(t,e,n){var r=window.document.createElement(t);return e=e||"",n=n||"",r.className=e,void 0!==n&&(r.textContent=n),r}r(),n.toggleClass=function(t,e,n){if(!0===n)return t.classList.add(e);t.classList.remove(e)},n.createElement=i,n.clearNode=function(t){for(;t.firstChild;)t.removeChild(t.firstChild)},n.findParent=function t(e,n){if(n(e))return e;if(e.parentNode)return t(e.parentNode,n);return},n.createNumberInput=function(t,e){var n=i("div","numInputWrapper"),r=i("input","numInput "+t),a=i("span","arrowUp"),o=i("span","arrowDown");-1===navigator.userAgent.indexOf("MSIE 9.0")?r.type="number":(r.type="text",r.pattern="\\d*");if(void 0!==e)for(var u in e)r.setAttribute(u,e[u]);return n.appendChild(r),n.appendChild(a),n.appendChild(o),n},n.getEventTarget=function(t){try{return"function"==typeof t.composedPath?t.composedPath()[0]:t.target}catch(e){return t.target}}},
636: function _(e,t,n,a,r){a(),n.compareDates=function(e,t,n){void 0===n&&(n=!0);if(!1!==n)return new Date(e.getTime()).setHours(0,0,0,0)-new Date(t.getTime()).setHours(0,0,0,0);return e.getTime()-t.getTime()},n.compareTimes=function(e,t){return 3600*(e.getHours()-t.getHours())+60*(e.getMinutes()-t.getMinutes())+e.getSeconds()-t.getSeconds()},n.getDefaultHours=function(e){var t=e.defaultHour,n=e.defaultMinute,a=e.defaultSeconds;if(void 0!==e.minDate){var r=e.minDate.getHours(),i=e.minDate.getMinutes(),o=e.minDate.getSeconds();t<r&&(t=r),t===r&&n<i&&(n=i),t===r&&n===i&&a<o&&(a=e.minDate.getSeconds())}if(void 0!==e.maxDate){var s=e.maxDate.getHours(),u=e.maxDate.getMinutes();(t=Math.min(t,s))===s&&(n=Math.min(u,n)),t===s&&n===u&&(a=e.maxDate.getSeconds())}return{hours:t,minutes:n,seconds:a}};const i=e(637),o=e(632),s=e(633);n.createDateFormatter=function(e){var t=e.config,n=void 0===t?o.defaults:t,a=e.l10n,r=void 0===a?s.english:a,u=e.isMobile,f=void 0!==u&&u;return function(e,t,a){var o=a||r;return void 0===n.formatDate||f?t.split("").map((function(t,a,r){return i.formats[t]&&"\\"!==r[a-1]?i.formats[t](e,o,n):"\\"!==t?t:""})).join(""):n.formatDate(e,t,o)}};n.createDateParser=function(e){var t=e.config,n=void 0===t?o.defaults:t,a=e.l10n,r=void 0===a?s.english:a;return function(e,t,a,s){if(0===e||e){var u,f=s||r,d=e;if(e instanceof Date)u=new Date(e.getTime());else if("string"!=typeof e&&void 0!==e.toFixed)u=new Date(e);else if("string"==typeof e){var c=t||(n||o.defaults).dateFormat,g=String(e).trim();if("today"===g)u=new Date,a=!0;else if(n&&n.parseDate)u=n.parseDate(e,c);else if(/Z$/.test(g)||/GMT$/.test(g))u=new Date(e);else{for(var m=void 0,l=[],v=0,D=0,h="";v<c.length;v++){var w=c[v],M="\\"===w,p="\\"===c[v-1]||M;if(i.tokenRegex[w]&&!p){h+=i.tokenRegex[w];var H=new RegExp(h).exec(e);H&&(m=!0)&&l["Y"!==w?"push":"unshift"]({fn:i.revFormat[w],val:H[++D]})}else M||(h+=".")}u=n&&n.noCalendar?new Date((new Date).setHours(0,0,0,0)):new Date((new Date).getFullYear(),0,1,0,0,0,0),l.forEach((function(e){var t=e.fn,n=e.val;return u=t(u,n,f)||u})),u=m?u:void 0}}if(u instanceof Date&&!isNaN(u.getTime()))return!0===a&&u.setHours(0,0,0,0),u;n.errorHandler(new Error("Invalid date provided: "+d))}}};n.isBetween=function(e,t,n){return e>Math.min(t,n)&&e<Math.max(t,n)};n.calculateSecondsSinceMidnight=function(e,t,n){return 3600*e+60*t+n};n.parseSeconds=function(e){var t=Math.floor(e/3600),n=(e-3600*t)/60;return[t,n,e-3600*t-60*n]},n.duration={DAY:864e5}},
637: function _(t,n,e,o,r){o();const u=t(634);var a=function(){};e.monthToStr=function(t,n,e){return e.months[n?"shorthand":"longhand"][t]},e.revFormat={D:a,F:function(t,n,e){t.setMonth(e.months.longhand.indexOf(n))},G:function(t,n){t.setHours((t.getHours()>=12?12:0)+parseFloat(n))},H:function(t,n){t.setHours(parseFloat(n))},J:function(t,n){t.setDate(parseFloat(n))},K:function(t,n,e){t.setHours(t.getHours()%12+12*(0,u.int)(new RegExp(e.amPM[1],"i").test(n)))},M:function(t,n,e){t.setMonth(e.months.shorthand.indexOf(n))},S:function(t,n){t.setSeconds(parseFloat(n))},U:function(t,n){return new Date(1e3*parseFloat(n))},W:function(t,n,e){var o=parseInt(n),r=new Date(t.getFullYear(),0,2+7*(o-1),0,0,0,0);return r.setDate(r.getDate()-r.getDay()+e.firstDayOfWeek),r},Y:function(t,n){t.setFullYear(parseFloat(n))},Z:function(t,n){return new Date(n)},d:function(t,n){t.setDate(parseFloat(n))},h:function(t,n){t.setHours((t.getHours()>=12?12:0)+parseFloat(n))},i:function(t,n){t.setMinutes(parseFloat(n))},j:function(t,n){t.setDate(parseFloat(n))},l:a,m:function(t,n){t.setMonth(parseFloat(n)-1)},n:function(t,n){t.setMonth(parseFloat(n)-1)},s:function(t,n){t.setSeconds(parseFloat(n))},u:function(t,n){return new Date(parseFloat(n))},w:a,y:function(t,n){t.setFullYear(2e3+parseFloat(n))}},e.tokenRegex={D:"",F:"",G:"(\\d\\d|\\d)",H:"(\\d\\d|\\d)",J:"(\\d\\d|\\d)\\w+",K:"",M:"",S:"(\\d\\d|\\d)",U:"(.+)",W:"(\\d\\d|\\d)",Y:"(\\d{4})",Z:"(.+)",d:"(\\d\\d|\\d)",h:"(\\d\\d|\\d)",i:"(\\d\\d|\\d)",j:"(\\d\\d|\\d)",l:"",m:"(\\d\\d|\\d)",n:"(\\d\\d|\\d)",s:"(\\d\\d|\\d)",u:"(.+)",w:"(\\d\\d|\\d)",y:"(\\d{2})"},e.formats={Z:function(t){return t.toISOString()},D:function(t,n,o){return n.weekdays.shorthand[e.formats.w(t,n,o)]},F:function(t,n,o){return(0,e.monthToStr)(e.formats.n(t,n,o)-1,!1,n)},G:function(t,n,o){return(0,u.pad)(e.formats.h(t,n,o))},H:function(t){return(0,u.pad)(t.getHours())},J:function(t,n){return void 0!==n.ordinal?t.getDate()+n.ordinal(t.getDate()):t.getDate()},K:function(t,n){return n.amPM[(0,u.int)(t.getHours()>11)]},M:function(t,n){return(0,e.monthToStr)(t.getMonth(),!0,n)},S:function(t){return(0,u.pad)(t.getSeconds())},U:function(t){return t.getTime()/1e3},W:function(t,n,e){return e.getWeek(t)},Y:function(t){return(0,u.pad)(t.getFullYear(),4)},d:function(t){return(0,u.pad)(t.getDate())},h:function(t){return t.getHours()%12?t.getHours()%12:12},i:function(t){return(0,u.pad)(t.getMinutes())},j:function(t){return t.getDate()},l:function(t,n){return n.weekdays.longhand[t.getDay()]},m:function(t){return(0,u.pad)(t.getMonth()+1)},n:function(t){return t.getMonth()+1},s:function(t){return t.getSeconds()},u:function(t){return t.getTime()},w:function(t){return t.getDay()},y:function(t){return String(t.getFullYear()).substring(2)}}},
638: function _(n,t,o,r,e){"function"!=typeof Object.assign&&(Object.assign=function(n){for(var t=[],o=1;o<arguments.length;o++)t[o-1]=arguments[o];if(!n)throw TypeError("Cannot convert undefined or null to object");for(var r=function(t){t&&Object.keys(t).forEach((function(o){return n[o]=t[o]}))},e=0,c=t;e<c.length;e++){r(c[e])}return n})},
639: function _(e,t,a,r,i){r(),a.default='.flatpickr-calendar{background:transparent;opacity:0;display:none;text-align:center;visibility:hidden;padding:0;-webkit-animation:none;animation:none;direction:ltr;border:0;font-size:14px;line-height:24px;border-radius:5px;position:absolute;width:307.875px;-webkit-box-sizing:border-box;box-sizing:border-box;-ms-touch-action:manipulation;touch-action:manipulation;background:#fff;-webkit-box-shadow:1px 0 0 #e6e6e6, -1px 0 0 #e6e6e6, 0 1px 0 #e6e6e6, 0 -1px 0 #e6e6e6, 0 3px 13px rgba(0, 0, 0, 0.08);box-shadow:1px 0 0 #e6e6e6, -1px 0 0 #e6e6e6, 0 1px 0 #e6e6e6, 0 -1px 0 #e6e6e6, 0 3px 13px rgba(0, 0, 0, 0.08);}.flatpickr-calendar.open,.flatpickr-calendar.inline{opacity:1;max-height:640px;visibility:visible;}.flatpickr-calendar.open{display:inline-block;z-index:99999;}.flatpickr-calendar.animate.open{-webkit-animation:fpFadeInDown 300ms cubic-bezier(0.23, 1, 0.32, 1);animation:fpFadeInDown 300ms cubic-bezier(0.23, 1, 0.32, 1);}.flatpickr-calendar.inline{display:block;position:relative;top:2px;}.flatpickr-calendar.static{position:absolute;top:calc(100% + 2px);}.flatpickr-calendar.static.open{z-index:999;display:block;}.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+1) .flatpickr-day.inRange:nth-child(7n+7){-webkit-box-shadow:none !important;box-shadow:none !important;}.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+2) .flatpickr-day.inRange:nth-child(7n+1){-webkit-box-shadow:-2px 0 0 #e6e6e6, 5px 0 0 #e6e6e6;box-shadow:-2px 0 0 #e6e6e6, 5px 0 0 #e6e6e6;}.flatpickr-calendar .hasWeeks .dayContainer,.flatpickr-calendar .hasTime .dayContainer{border-bottom:0;border-bottom-right-radius:0;border-bottom-left-radius:0;}.flatpickr-calendar .hasWeeks .dayContainer{border-left:0;}.flatpickr-calendar.hasTime .flatpickr-time{height:40px;border-top:1px solid #e6e6e6;}.flatpickr-calendar.noCalendar.hasTime .flatpickr-time{height:auto;}.flatpickr-calendar:before,.flatpickr-calendar:after{position:absolute;display:block;pointer-events:none;border:solid transparent;content:\'\';height:0;width:0;left:22px;}.flatpickr-calendar.rightMost:before,.flatpickr-calendar.arrowRight:before,.flatpickr-calendar.rightMost:after,.flatpickr-calendar.arrowRight:after{left:auto;right:22px;}.flatpickr-calendar.arrowCenter:before,.flatpickr-calendar.arrowCenter:after{left:50%;right:50%;}.flatpickr-calendar:before{border-width:5px;margin:0 -5px;}.flatpickr-calendar:after{border-width:4px;margin:0 -4px;}.flatpickr-calendar.arrowTop:before,.flatpickr-calendar.arrowTop:after{bottom:100%;}.flatpickr-calendar.arrowTop:before{border-bottom-color:#e6e6e6;}.flatpickr-calendar.arrowTop:after{border-bottom-color:#fff;}.flatpickr-calendar.arrowBottom:before,.flatpickr-calendar.arrowBottom:after{top:100%;}.flatpickr-calendar.arrowBottom:before{border-top-color:#e6e6e6;}.flatpickr-calendar.arrowBottom:after{border-top-color:#fff;}.flatpickr-calendar:focus{outline:0;}.flatpickr-wrapper{position:relative;display:inline-block;}.flatpickr-months{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;}.flatpickr-months .flatpickr-month{background:transparent;color:rgba(0, 0, 0, 0.9);fill:rgba(0, 0, 0, 0.9);height:34px;line-height:1;text-align:center;position:relative;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;overflow:hidden;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;}.flatpickr-months .flatpickr-prev-month,.flatpickr-months .flatpickr-next-month{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;text-decoration:none;cursor:pointer;position:absolute;top:0;height:34px;padding:10px;z-index:3;color:rgba(0, 0, 0, 0.9);fill:rgba(0, 0, 0, 0.9);}.flatpickr-months .flatpickr-prev-month.flatpickr-disabled,.flatpickr-months .flatpickr-next-month.flatpickr-disabled{display:none;}.flatpickr-months .flatpickr-prev-month i,.flatpickr-months .flatpickr-next-month i{position:relative;}.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month,.flatpickr-months .flatpickr-next-month.flatpickr-prev-month{left:0;}.flatpickr-months .flatpickr-prev-month.flatpickr-next-month,.flatpickr-months .flatpickr-next-month.flatpickr-next-month{right:0;}.flatpickr-months .flatpickr-prev-month:hover,.flatpickr-months .flatpickr-next-month:hover{color:#959ea9;}.flatpickr-months .flatpickr-prev-month:hover svg,.flatpickr-months .flatpickr-next-month:hover svg{fill:#f64747;}.flatpickr-months .flatpickr-prev-month svg,.flatpickr-months .flatpickr-next-month svg{width:14px;height:14px;}.flatpickr-months .flatpickr-prev-month svg path,.flatpickr-months .flatpickr-next-month svg path{-webkit-transition:fill 0.1s;transition:fill 0.1s;fill:inherit;}.numInputWrapper{position:relative;height:auto;}.numInputWrapper input,.numInputWrapper span{display:inline-block;}.numInputWrapper input{width:100%;}.numInputWrapper input::-ms-clear{display:none;}.numInputWrapper input::-webkit-outer-spin-button,.numInputWrapper input::-webkit-inner-spin-button{margin:0;-webkit-appearance:none;}.numInputWrapper span{position:absolute;right:0;width:14px;padding:0 4px 0 2px;height:50%;line-height:50%;opacity:0;cursor:pointer;border:1px solid rgba(57, 57, 57, 0.15);-webkit-box-sizing:border-box;box-sizing:border-box;}.numInputWrapper span:hover{background:rgba(0, 0, 0, 0.1);}.numInputWrapper span:active{background:rgba(0, 0, 0, 0.2);}.numInputWrapper span:after{display:block;content:"";position:absolute;}.numInputWrapper span.arrowUp{top:0;border-bottom:0;}.numInputWrapper span.arrowUp:after{border-left:4px solid transparent;border-right:4px solid transparent;border-bottom:4px solid rgba(57, 57, 57, 0.6);top:26%;}.numInputWrapper span.arrowDown{top:50%;}.numInputWrapper span.arrowDown:after{border-left:4px solid transparent;border-right:4px solid transparent;border-top:4px solid rgba(57, 57, 57, 0.6);top:40%;}.numInputWrapper span svg{width:inherit;height:auto;}.numInputWrapper span svg path{fill:rgba(0, 0, 0, 0.5);}.numInputWrapper:hover{background:rgba(0, 0, 0, 0.05);}.numInputWrapper:hover span{opacity:1;}.flatpickr-current-month{font-size:135%;line-height:inherit;font-weight:300;color:inherit;position:absolute;width:75%;left:12.5%;padding:7.48px 0 0 0;line-height:1;height:34px;display:inline-block;text-align:center;-webkit-transform:translate3d(0px, 0px, 0px);transform:translate3d(0px, 0px, 0px);}.flatpickr-current-month span.cur-month{font-family:inherit;font-weight:700;color:inherit;display:inline-block;margin-left:0.5ch;padding:0;}.flatpickr-current-month span.cur-month:hover{background:rgba(0, 0, 0, 0.05);}.flatpickr-current-month .numInputWrapper{width:6ch;width:7ch\0;display:inline-block;}.flatpickr-current-month .numInputWrapper span.arrowUp:after{border-bottom-color:rgba(0, 0, 0, 0.9);}.flatpickr-current-month .numInputWrapper span.arrowDown:after{border-top-color:rgba(0, 0, 0, 0.9);}.flatpickr-current-month input.cur-year{background:transparent;-webkit-box-sizing:border-box;box-sizing:border-box;color:inherit;cursor:text;padding:0 0 0 0.5ch;margin:0;display:inline-block;font-size:inherit;font-family:inherit;font-weight:300;line-height:inherit;height:auto;border:0;border-radius:0;vertical-align:initial;-webkit-appearance:textfield;-moz-appearance:textfield;appearance:textfield;}.flatpickr-current-month input.cur-year:focus{outline:0;}.flatpickr-current-month input.cur-year[disabled],.flatpickr-current-month input.cur-year[disabled]:hover{font-size:100%;color:rgba(0, 0, 0, 0.5);background:transparent;pointer-events:none;}.flatpickr-current-month .flatpickr-monthDropdown-months{appearance:menulist;background:transparent;border:none;border-radius:0;box-sizing:border-box;color:inherit;cursor:pointer;font-size:inherit;font-family:inherit;font-weight:300;height:auto;line-height:inherit;margin:-1px 0 0 0;outline:none;padding:0 0 0 0.5ch;position:relative;vertical-align:initial;-webkit-box-sizing:border-box;-webkit-appearance:menulist;-moz-appearance:menulist;width:auto;}.flatpickr-current-month .flatpickr-monthDropdown-months:focus,.flatpickr-current-month .flatpickr-monthDropdown-months:active{outline:none;}.flatpickr-current-month .flatpickr-monthDropdown-months:hover{background:rgba(0, 0, 0, 0.05);}.flatpickr-current-month .flatpickr-monthDropdown-months .flatpickr-monthDropdown-month{background-color:transparent;outline:none;padding:0;}.flatpickr-weekdays{background:transparent;text-align:center;overflow:hidden;width:100%;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;height:28px;}.flatpickr-weekdays .flatpickr-weekdaycontainer{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;}span.flatpickr-weekday{cursor:default;font-size:90%;background:transparent;color:rgba(0, 0, 0, 0.54);line-height:1;margin:0;text-align:center;display:block;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;font-weight:bolder;}.dayContainer,.flatpickr-weeks{padding:1px 0 0 0;}.flatpickr-days{position:relative;overflow:hidden;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:start;-webkit-align-items:flex-start;-ms-flex-align:start;align-items:flex-start;width:307.875px;}.flatpickr-days:focus{outline:0;}.dayContainer{padding:0;outline:0;text-align:left;width:307.875px;min-width:307.875px;max-width:307.875px;-webkit-box-sizing:border-box;box-sizing:border-box;display:inline-block;display:-ms-flexbox;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-wrap:wrap;-ms-flex-pack:justify;-webkit-justify-content:space-around;justify-content:space-around;-webkit-transform:translate3d(0px, 0px, 0px);transform:translate3d(0px, 0px, 0px);opacity:1;}.dayContainer + .dayContainer{-webkit-box-shadow:-1px 0 0 #e6e6e6;box-shadow:-1px 0 0 #e6e6e6;}.flatpickr-day{background:none;border:1px solid transparent;border-radius:150px;-webkit-box-sizing:border-box;box-sizing:border-box;color:#393939;cursor:pointer;font-weight:400;width:14.2857143%;-webkit-flex-basis:14.2857143%;-ms-flex-preferred-size:14.2857143%;flex-basis:14.2857143%;max-width:39px;height:39px;line-height:39px;margin:0;display:inline-block;position:relative;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;text-align:center;}.flatpickr-day.inRange,.flatpickr-day.prevMonthDay.inRange,.flatpickr-day.nextMonthDay.inRange,.flatpickr-day.today.inRange,.flatpickr-day.prevMonthDay.today.inRange,.flatpickr-day.nextMonthDay.today.inRange,.flatpickr-day:hover,.flatpickr-day.prevMonthDay:hover,.flatpickr-day.nextMonthDay:hover,.flatpickr-day:focus,.flatpickr-day.prevMonthDay:focus,.flatpickr-day.nextMonthDay:focus{cursor:pointer;outline:0;background:#e6e6e6;border-color:#e6e6e6;}.flatpickr-day.today{border-color:#959ea9;}.flatpickr-day.today:hover,.flatpickr-day.today:focus{border-color:#959ea9;background:#959ea9;color:#fff;}.flatpickr-day.selected,.flatpickr-day.startRange,.flatpickr-day.endRange,.flatpickr-day.selected.inRange,.flatpickr-day.startRange.inRange,.flatpickr-day.endRange.inRange,.flatpickr-day.selected:focus,.flatpickr-day.startRange:focus,.flatpickr-day.endRange:focus,.flatpickr-day.selected:hover,.flatpickr-day.startRange:hover,.flatpickr-day.endRange:hover,.flatpickr-day.selected.prevMonthDay,.flatpickr-day.startRange.prevMonthDay,.flatpickr-day.endRange.prevMonthDay,.flatpickr-day.selected.nextMonthDay,.flatpickr-day.startRange.nextMonthDay,.flatpickr-day.endRange.nextMonthDay{background:#569ff7;-webkit-box-shadow:none;box-shadow:none;color:#fff;border-color:#569ff7;}.flatpickr-day.selected.startRange,.flatpickr-day.startRange.startRange,.flatpickr-day.endRange.startRange{border-radius:50px 0 0 50px;}.flatpickr-day.selected.endRange,.flatpickr-day.startRange.endRange,.flatpickr-day.endRange.endRange{border-radius:0 50px 50px 0;}.flatpickr-day.selected.startRange + .endRange:not(:nth-child(7n+1)),.flatpickr-day.startRange.startRange + .endRange:not(:nth-child(7n+1)),.flatpickr-day.endRange.startRange + .endRange:not(:nth-child(7n+1)){-webkit-box-shadow:-10px 0 0 #569ff7;box-shadow:-10px 0 0 #569ff7;}.flatpickr-day.selected.startRange.endRange,.flatpickr-day.startRange.startRange.endRange,.flatpickr-day.endRange.startRange.endRange{border-radius:50px;}.flatpickr-day.inRange{border-radius:0;-webkit-box-shadow:-5px 0 0 #e6e6e6, 5px 0 0 #e6e6e6;box-shadow:-5px 0 0 #e6e6e6, 5px 0 0 #e6e6e6;}.flatpickr-day.flatpickr-disabled,.flatpickr-day.flatpickr-disabled:hover,.flatpickr-day.prevMonthDay,.flatpickr-day.nextMonthDay,.flatpickr-day.notAllowed,.flatpickr-day.notAllowed.prevMonthDay,.flatpickr-day.notAllowed.nextMonthDay{color:rgba(57, 57, 57, 0.3);background:transparent;border-color:transparent;cursor:default;}.flatpickr-day.flatpickr-disabled,.flatpickr-day.flatpickr-disabled:hover{cursor:not-allowed;color:rgba(57, 57, 57, 0.1);}.flatpickr-day.week.selected{border-radius:0;-webkit-box-shadow:-5px 0 0 #569ff7, 5px 0 0 #569ff7;box-shadow:-5px 0 0 #569ff7, 5px 0 0 #569ff7;}.flatpickr-day.hidden{visibility:hidden;}.rangeMode .flatpickr-day{margin-top:1px;}.flatpickr-weekwrapper{float:left;}.flatpickr-weekwrapper .flatpickr-weeks{padding:0 12px;-webkit-box-shadow:1px 0 0 #e6e6e6;box-shadow:1px 0 0 #e6e6e6;}.flatpickr-weekwrapper .flatpickr-weekday{float:none;width:100%;line-height:28px;}.flatpickr-weekwrapper span.flatpickr-day,.flatpickr-weekwrapper span.flatpickr-day:hover{display:block;width:100%;max-width:none;color:rgba(57, 57, 57, 0.3);background:transparent;cursor:default;border:none;}.flatpickr-innerContainer{display:block;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden;}.flatpickr-rContainer{display:inline-block;padding:0;-webkit-box-sizing:border-box;box-sizing:border-box;}.flatpickr-time{text-align:center;outline:0;display:block;height:0;line-height:40px;max-height:40px;-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;}.flatpickr-time:after{content:"";display:table;clear:both;}.flatpickr-time .numInputWrapper{-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;width:40%;height:40px;float:left;}.flatpickr-time .numInputWrapper span.arrowUp:after{border-bottom-color:#393939;}.flatpickr-time .numInputWrapper span.arrowDown:after{border-top-color:#393939;}.flatpickr-time.hasSeconds .numInputWrapper{width:26%;}.flatpickr-time.time24hr .numInputWrapper{width:49%;}.flatpickr-time input{background:transparent;-webkit-box-shadow:none;box-shadow:none;border:0;border-radius:0;text-align:center;margin:0;padding:0;height:inherit;line-height:inherit;color:#393939;font-size:14px;position:relative;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:textfield;-moz-appearance:textfield;appearance:textfield;}.flatpickr-time input.flatpickr-hour{font-weight:bold;}.flatpickr-time input.flatpickr-minute,.flatpickr-time input.flatpickr-second{font-weight:400;}.flatpickr-time input:focus{outline:0;border:0;}.flatpickr-time .flatpickr-time-separator,.flatpickr-time .flatpickr-am-pm{height:inherit;float:left;line-height:inherit;color:#393939;font-weight:bold;width:2%;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-align-self:center;-ms-flex-item-align:center;align-self:center;}.flatpickr-time .flatpickr-am-pm{outline:0;width:18%;cursor:pointer;text-align:center;font-weight:400;}.flatpickr-time input:hover,.flatpickr-time .flatpickr-am-pm:hover,.flatpickr-time input:focus,.flatpickr-time .flatpickr-am-pm:focus{background:#eee;}.flatpickr-input[readonly]{cursor:pointer;}@-webkit-keyframes fpFadeInDown{from{opacity:0;-webkit-transform:translate3d(0, -20px, 0);transform:translate3d(0, -20px, 0);}to{opacity:1;-webkit-transform:translate3d(0, 0, 0);transform:translate3d(0, 0, 0);}}@keyframes fpFadeInDown{from{opacity:0;-webkit-transform:translate3d(0, -20px, 0);transform:translate3d(0, -20px, 0);}to{opacity:1;-webkit-transform:translate3d(0, 0, 0);transform:translate3d(0, 0, 0);}}.flatpickr-calendar{background:transparent;opacity:0;display:none;text-align:center;visibility:hidden;padding:0;-webkit-animation:none;animation:none;direction:ltr;border:0;font-size:14px;line-height:24px;border-radius:5px;position:absolute;width:307.875px;-webkit-box-sizing:border-box;box-sizing:border-box;-ms-touch-action:manipulation;touch-action:manipulation;-webkit-box-shadow:0 3px 13px rgba(0, 0, 0, 0.08);box-shadow:0 3px 13px rgba(0, 0, 0, 0.08);}.flatpickr-calendar.open,.flatpickr-calendar.inline{opacity:1;max-height:640px;visibility:visible;}.flatpickr-calendar.open{display:inline-block;z-index:99999;}.flatpickr-calendar.animate.open{-webkit-animation:fpFadeInDown 300ms cubic-bezier(0.23, 1, 0.32, 1);animation:fpFadeInDown 300ms cubic-bezier(0.23, 1, 0.32, 1);}.flatpickr-calendar.inline{display:block;position:relative;top:2px;}.flatpickr-calendar.static{position:absolute;top:calc(100% + 2px);}.flatpickr-calendar.static.open{z-index:999;display:block;}.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+1) .flatpickr-day.inRange:nth-child(7n+7){-webkit-box-shadow:none !important;box-shadow:none !important;}.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+2) .flatpickr-day.inRange:nth-child(7n+1){-webkit-box-shadow:-2px 0 0 #e6e6e6, 5px 0 0 #e6e6e6;box-shadow:-2px 0 0 #e6e6e6, 5px 0 0 #e6e6e6;}.flatpickr-calendar .hasWeeks .dayContainer,.flatpickr-calendar .hasTime .dayContainer{border-bottom:0;border-bottom-right-radius:0;border-bottom-left-radius:0;}.flatpickr-calendar .hasWeeks .dayContainer{border-left:0;}.flatpickr-calendar.hasTime .flatpickr-time{height:40px;border-top:1px solid #eceef1;}.flatpickr-calendar.hasTime .flatpickr-innerContainer{border-bottom:0;}.flatpickr-calendar.hasTime .flatpickr-time{border:1px solid #eceef1;}.flatpickr-calendar.noCalendar.hasTime .flatpickr-time{height:auto;}.flatpickr-calendar:before,.flatpickr-calendar:after{position:absolute;display:block;pointer-events:none;border:solid transparent;content:\'\';height:0;width:0;left:22px;}.flatpickr-calendar.rightMost:before,.flatpickr-calendar.arrowRight:before,.flatpickr-calendar.rightMost:after,.flatpickr-calendar.arrowRight:after{left:auto;right:22px;}.flatpickr-calendar.arrowCenter:before,.flatpickr-calendar.arrowCenter:after{left:50%;right:50%;}.flatpickr-calendar:before{border-width:5px;margin:0 -5px;}.flatpickr-calendar:after{border-width:4px;margin:0 -4px;}.flatpickr-calendar.arrowTop:before,.flatpickr-calendar.arrowTop:after{bottom:100%;}.flatpickr-calendar.arrowTop:before{border-bottom-color:#eceef1;}.flatpickr-calendar.arrowTop:after{border-bottom-color:#eceef1;}.flatpickr-calendar.arrowBottom:before,.flatpickr-calendar.arrowBottom:after{top:100%;}.flatpickr-calendar.arrowBottom:before{border-top-color:#eceef1;}.flatpickr-calendar.arrowBottom:after{border-top-color:#eceef1;}.flatpickr-calendar:focus{outline:0;}.flatpickr-wrapper{position:relative;display:inline-block;}.flatpickr-months{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;}.flatpickr-months .flatpickr-month{border-radius:5px 5px 0 0;background:#eceef1;color:#5a6171;fill:#5a6171;height:34px;line-height:1;text-align:center;position:relative;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;overflow:hidden;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;}.flatpickr-months .flatpickr-prev-month,.flatpickr-months .flatpickr-next-month{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;text-decoration:none;cursor:pointer;position:absolute;top:0;height:34px;padding:10px;z-index:3;color:#5a6171;fill:#5a6171;}.flatpickr-months .flatpickr-prev-month.flatpickr-disabled,.flatpickr-months .flatpickr-next-month.flatpickr-disabled{display:none;}.flatpickr-months .flatpickr-prev-month i,.flatpickr-months .flatpickr-next-month i{position:relative;}.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month,.flatpickr-months .flatpickr-next-month.flatpickr-prev-month{left:0;}.flatpickr-months .flatpickr-prev-month.flatpickr-next-month,.flatpickr-months .flatpickr-next-month.flatpickr-next-month{right:0;}.flatpickr-months .flatpickr-prev-month:hover,.flatpickr-months .flatpickr-next-month:hover{color:#bbb;}.flatpickr-months .flatpickr-prev-month:hover svg,.flatpickr-months .flatpickr-next-month:hover svg{fill:#f64747;}.flatpickr-months .flatpickr-prev-month svg,.flatpickr-months .flatpickr-next-month svg{width:14px;height:14px;}.flatpickr-months .flatpickr-prev-month svg path,.flatpickr-months .flatpickr-next-month svg path{-webkit-transition:fill 0.1s;transition:fill 0.1s;fill:inherit;}.numInputWrapper{position:relative;height:auto;}.numInputWrapper input,.numInputWrapper span{display:inline-block;}.numInputWrapper input{width:100%;}.numInputWrapper input::-ms-clear{display:none;}.numInputWrapper input::-webkit-outer-spin-button,.numInputWrapper input::-webkit-inner-spin-button{margin:0;-webkit-appearance:none;}.numInputWrapper span{position:absolute;right:0;width:14px;padding:0 4px 0 2px;height:50%;line-height:50%;opacity:0;cursor:pointer;border:1px solid rgba(72, 72, 72, 0.15);-webkit-box-sizing:border-box;box-sizing:border-box;}.numInputWrapper span:hover{background:rgba(0, 0, 0, 0.1);}.numInputWrapper span:active{background:rgba(0, 0, 0, 0.2);}.numInputWrapper span:after{display:block;content:"";position:absolute;}.numInputWrapper span.arrowUp{top:0;border-bottom:0;}.numInputWrapper span.arrowUp:after{border-left:4px solid transparent;border-right:4px solid transparent;border-bottom:4px solid rgba(72, 72, 72, 0.6);top:26%;}.numInputWrapper span.arrowDown{top:50%;}.numInputWrapper span.arrowDown:after{border-left:4px solid transparent;border-right:4px solid transparent;border-top:4px solid rgba(72, 72, 72, 0.6);top:40%;}.numInputWrapper span svg{width:inherit;height:auto;}.numInputWrapper span svg path{fill:rgba(90, 97, 113, 0.5);}.numInputWrapper:hover{background:rgba(0, 0, 0, 0.05);}.numInputWrapper:hover span{opacity:1;}.flatpickr-current-month{font-size:135%;line-height:inherit;font-weight:300;color:inherit;position:absolute;width:75%;left:12.5%;padding:7.48px 0 0 0;line-height:1;height:34px;display:inline-block;text-align:center;-webkit-transform:translate3d(0px, 0px, 0px);transform:translate3d(0px, 0px, 0px);}.flatpickr-current-month span.cur-month{font-family:inherit;font-weight:700;color:inherit;display:inline-block;margin-left:0.5ch;padding:0;}.flatpickr-current-month span.cur-month:hover{background:rgba(0, 0, 0, 0.05);}.flatpickr-current-month .numInputWrapper{width:6ch;width:7ch\0;display:inline-block;}.flatpickr-current-month .numInputWrapper span.arrowUp:after{border-bottom-color:#5a6171;}.flatpickr-current-month .numInputWrapper span.arrowDown:after{border-top-color:#5a6171;}.flatpickr-current-month input.cur-year{background:transparent;-webkit-box-sizing:border-box;box-sizing:border-box;color:inherit;cursor:text;padding:0 0 0 0.5ch;margin:0;display:inline-block;font-size:inherit;font-family:inherit;font-weight:300;line-height:inherit;height:auto;border:0;border-radius:0;vertical-align:initial;-webkit-appearance:textfield;-moz-appearance:textfield;appearance:textfield;}.flatpickr-current-month input.cur-year:focus{outline:0;}.flatpickr-current-month input.cur-year[disabled],.flatpickr-current-month input.cur-year[disabled]:hover{font-size:100%;color:rgba(90, 97, 113, 0.5);background:transparent;pointer-events:none;}.flatpickr-current-month .flatpickr-monthDropdown-months{appearance:menulist;background:#eceef1;border:none;border-radius:0;box-sizing:border-box;color:inherit;cursor:pointer;font-size:inherit;font-family:inherit;font-weight:300;height:auto;line-height:inherit;margin:-1px 0 0 0;outline:none;padding:0 0 0 0.5ch;position:relative;vertical-align:initial;-webkit-box-sizing:border-box;-webkit-appearance:menulist;-moz-appearance:menulist;width:auto;}.flatpickr-current-month .flatpickr-monthDropdown-months:focus,.flatpickr-current-month .flatpickr-monthDropdown-months:active{outline:none;}.flatpickr-current-month .flatpickr-monthDropdown-months:hover{background:rgba(0, 0, 0, 0.05);}.flatpickr-current-month .flatpickr-monthDropdown-months .flatpickr-monthDropdown-month{background-color:#eceef1;outline:none;padding:0;}.flatpickr-weekdays{background:#eceef1;text-align:center;overflow:hidden;width:100%;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;height:28px;}.flatpickr-weekdays .flatpickr-weekdaycontainer{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;}span.flatpickr-weekday{cursor:default;font-size:90%;background:#eceef1;color:#5a6171;line-height:1;margin:0;text-align:center;display:block;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;font-weight:bolder;}.dayContainer,.flatpickr-weeks{padding:1px 0 0 0;}.flatpickr-days{position:relative;overflow:hidden;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:start;-webkit-align-items:flex-start;-ms-flex-align:start;align-items:flex-start;width:307.875px;border-left:1px solid #eceef1;border-right:1px solid #eceef1;}.flatpickr-days:focus{outline:0;}.dayContainer{padding:0;outline:0;text-align:left;width:307.875px;min-width:307.875px;max-width:307.875px;-webkit-box-sizing:border-box;box-sizing:border-box;display:inline-block;display:-ms-flexbox;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-wrap:wrap;-ms-flex-pack:justify;-webkit-justify-content:space-around;justify-content:space-around;-webkit-transform:translate3d(0px, 0px, 0px);transform:translate3d(0px, 0px, 0px);opacity:1;}.dayContainer + .dayContainer{-webkit-box-shadow:-1px 0 0 #eceef1;box-shadow:-1px 0 0 #eceef1;}.flatpickr-day{background:none;border:1px solid transparent;border-radius:150px;-webkit-box-sizing:border-box;box-sizing:border-box;color:#484848;cursor:pointer;font-weight:400;width:14.2857143%;-webkit-flex-basis:14.2857143%;-ms-flex-preferred-size:14.2857143%;flex-basis:14.2857143%;max-width:39px;height:39px;line-height:39px;margin:0;display:inline-block;position:relative;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;text-align:center;}.flatpickr-day.inRange,.flatpickr-day.prevMonthDay.inRange,.flatpickr-day.nextMonthDay.inRange,.flatpickr-day.today.inRange,.flatpickr-day.prevMonthDay.today.inRange,.flatpickr-day.nextMonthDay.today.inRange,.flatpickr-day:hover,.flatpickr-day.prevMonthDay:hover,.flatpickr-day.nextMonthDay:hover,.flatpickr-day:focus,.flatpickr-day.prevMonthDay:focus,.flatpickr-day.nextMonthDay:focus{cursor:pointer;outline:0;background:#e2e2e2;border-color:#e2e2e2;}.flatpickr-day.today{border-color:#bbb;}.flatpickr-day.today:hover,.flatpickr-day.today:focus{border-color:#bbb;background:#bbb;color:#fff;}.flatpickr-day.selected,.flatpickr-day.startRange,.flatpickr-day.endRange,.flatpickr-day.selected.inRange,.flatpickr-day.startRange.inRange,.flatpickr-day.endRange.inRange,.flatpickr-day.selected:focus,.flatpickr-day.startRange:focus,.flatpickr-day.endRange:focus,.flatpickr-day.selected:hover,.flatpickr-day.startRange:hover,.flatpickr-day.endRange:hover,.flatpickr-day.selected.prevMonthDay,.flatpickr-day.startRange.prevMonthDay,.flatpickr-day.endRange.prevMonthDay,.flatpickr-day.selected.nextMonthDay,.flatpickr-day.startRange.nextMonthDay,.flatpickr-day.endRange.nextMonthDay{background:#ff5a5f;-webkit-box-shadow:none;box-shadow:none;color:#fff;border-color:#ff5a5f;}.flatpickr-day.selected.startRange,.flatpickr-day.startRange.startRange,.flatpickr-day.endRange.startRange{border-radius:50px 0 0 50px;}.flatpickr-day.selected.endRange,.flatpickr-day.startRange.endRange,.flatpickr-day.endRange.endRange{border-radius:0 50px 50px 0;}.flatpickr-day.selected.startRange + .endRange:not(:nth-child(7n+1)),.flatpickr-day.startRange.startRange + .endRange:not(:nth-child(7n+1)),.flatpickr-day.endRange.startRange + .endRange:not(:nth-child(7n+1)){-webkit-box-shadow:-10px 0 0 #ff5a5f;box-shadow:-10px 0 0 #ff5a5f;}.flatpickr-day.selected.startRange.endRange,.flatpickr-day.startRange.startRange.endRange,.flatpickr-day.endRange.startRange.endRange{border-radius:50px;}.flatpickr-day.inRange{border-radius:0;-webkit-box-shadow:-5px 0 0 #e2e2e2, 5px 0 0 #e2e2e2;box-shadow:-5px 0 0 #e2e2e2, 5px 0 0 #e2e2e2;}.flatpickr-day.flatpickr-disabled,.flatpickr-day.flatpickr-disabled:hover,.flatpickr-day.prevMonthDay,.flatpickr-day.nextMonthDay,.flatpickr-day.notAllowed,.flatpickr-day.notAllowed.prevMonthDay,.flatpickr-day.notAllowed.nextMonthDay{color:rgba(72, 72, 72, 0.3);background:transparent;border-color:transparent;cursor:default;}.flatpickr-day.flatpickr-disabled,.flatpickr-day.flatpickr-disabled:hover{cursor:not-allowed;color:rgba(72, 72, 72, 0.1);}.flatpickr-day.week.selected{border-radius:0;-webkit-box-shadow:-5px 0 0 #ff5a5f, 5px 0 0 #ff5a5f;box-shadow:-5px 0 0 #ff5a5f, 5px 0 0 #ff5a5f;}.flatpickr-day.hidden{visibility:hidden;}.rangeMode .flatpickr-day{margin-top:1px;}.flatpickr-weekwrapper{float:left;}.flatpickr-weekwrapper .flatpickr-weeks{padding:0 12px;border-left:1px solid #eceef1;}.flatpickr-weekwrapper .flatpickr-weekday{float:none;width:100%;line-height:28px;}.flatpickr-weekwrapper span.flatpickr-day,.flatpickr-weekwrapper span.flatpickr-day:hover{display:block;width:100%;max-width:none;color:rgba(72, 72, 72, 0.3);background:transparent;cursor:default;border:none;}.flatpickr-innerContainer{display:block;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden;background:#fff;border-bottom:1px solid #eceef1;}.flatpickr-rContainer{display:inline-block;padding:0;-webkit-box-sizing:border-box;box-sizing:border-box;}.flatpickr-time{text-align:center;outline:0;display:block;height:0;line-height:40px;max-height:40px;-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;background:#fff;border-radius:0 0 5px 5px;}.flatpickr-time:after{content:"";display:table;clear:both;}.flatpickr-time .numInputWrapper{-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;width:40%;height:40px;float:left;}.flatpickr-time .numInputWrapper span.arrowUp:after{border-bottom-color:#484848;}.flatpickr-time .numInputWrapper span.arrowDown:after{border-top-color:#484848;}.flatpickr-time.hasSeconds .numInputWrapper{width:26%;}.flatpickr-time.time24hr .numInputWrapper{width:49%;}.flatpickr-time input{background:transparent;-webkit-box-shadow:none;box-shadow:none;border:0;border-radius:0;text-align:center;margin:0;padding:0;height:inherit;line-height:inherit;color:#484848;font-size:14px;position:relative;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:textfield;-moz-appearance:textfield;appearance:textfield;}.flatpickr-time input.flatpickr-hour{font-weight:bold;}.flatpickr-time input.flatpickr-minute,.flatpickr-time input.flatpickr-second{font-weight:400;}.flatpickr-time input:focus{outline:0;border:0;}.flatpickr-time .flatpickr-time-separator,.flatpickr-time .flatpickr-am-pm{height:inherit;float:left;line-height:inherit;color:#484848;font-weight:bold;width:2%;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-align-self:center;-ms-flex-item-align:center;align-self:center;}.flatpickr-time .flatpickr-am-pm{outline:0;width:18%;cursor:pointer;text-align:center;font-weight:400;}.flatpickr-time input:hover,.flatpickr-time .flatpickr-am-pm:hover,.flatpickr-time input:focus,.flatpickr-time .flatpickr-am-pm:focus{background:#eaeaea;}.flatpickr-input[readonly]{cursor:pointer;}@-webkit-keyframes fpFadeInDown{from{opacity:0;-webkit-transform:translate3d(0, -20px, 0);transform:translate3d(0, -20px, 0);}to{opacity:1;-webkit-transform:translate3d(0, 0, 0);transform:translate3d(0, 0, 0);}}@keyframes fpFadeInDown{from{opacity:0;-webkit-transform:translate3d(0, -20px, 0);transform:translate3d(0, -20px, 0);}to{opacity:1;-webkit-transform:translate3d(0, 0, 0);transform:translate3d(0, 0, 0);}}span.flatpickr-day.selected{font-weight:bold;}'},
640: function _(e,a,t,i,n){var s;i();const r=e(629),l=e(12);class c extends r.BaseDatePickerView{get flatpickr_options(){return{...super.flatpickr_options,mode:"range"}}_on_change(e){switch(e.length){case 0:this.model.value=null;break;case 1:break;case 2:{const[a,t]=e,i=this._format_date(a),n=this._format_date(t);this.model.value=[i,n];break}default:(0,l.assert)(!1,"invalid length")}}}t.DateRangePickerView=c,c.__name__="DateRangePickerView";class _ extends r.BaseDatePicker{constructor(e){super(e)}}t.DateRangePicker=_,s=_,_.__name__="DateRangePicker",s.prototype.default_view=c,s.define((({Tuple:e,Nullable:a})=>({value:[a(e(r.DateLike,r.DateLike)),null]})))},
641: function _(e,t,i,a,n){var r;a();const s=e(642),l=e(629),c=e(12);class _ extends s.BaseDatetimePickerView{get flatpickr_options(){return{...super.flatpickr_options,mode:"single"}}_on_change(e){(0,c.assert)(e.length<=1),this.model.value=(()=>{if(0==e.length)return null;{const[t]=e;return this._format_date(t)}})()}}i.DatetimePickerView=_,_.__name__="DatetimePickerView";class o extends s.BaseDatetimePicker{constructor(e){super(e)}}i.DatetimePicker=o,r=o,o.__name__="DatetimePicker",r.prototype.default_view=_,r.define((({Nullable:e})=>({value:[e(l.DateLike),null]})))},
642: function _(e,t,n,c,i){var s;c();const r=e(629),o=e(21);class m extends r.BaseDatePickerView{connect_signals(){super.connect_signals();const{value:e,hour_increment:t,minute_increment:n,second_increment:c,seconds:i,clock:s}=this.model.properties;this.connect(e.change,(()=>{const{value:e}=this.model;null!=e?this.picker.setDate(e):this.picker.clear()})),this.connect(t.change,(()=>this.picker.set("hourIncrement",this.model.hour_increment))),this.connect(n.change,(()=>this.picker.set("minuteIncrement",this.model.minute_increment))),this.connect(c.change,(()=>this._update_second_increment())),this.connect(i.change,(()=>this.picker.set("enableSeconds",this.model.seconds))),this.connect(s.change,(()=>this.picker.set("time_24hr","24h"==this.model.clock)))}get flatpickr_options(){const{hour_increment:e,minute_increment:t,seconds:n,clock:c}=this.model,i=super.flatpickr_options;return i.enableTime=!0,i.dateFormat="Y-m-dTH:i:S",i.hourIncrement=e,i.minuteIncrement=t,i.enableSeconds=n,i.time_24hr="24h"==c,i}render(){super.render(),this._update_second_increment()}_update_second_increment(){const{second_increment:e}=this.model;this.picker.secondElement?.setAttribute("step",e.toString())}}n.BaseDatetimePickerView=m,m.__name__="BaseDatetimePickerView";class a extends r.BaseDatePicker{constructor(e){super(e)}}n.BaseDatetimePicker=a,s=a,a.__name__="BaseDatetimePicker",s.define((({Bool:e,Positive:t,Int:n})=>({hour_increment:[t(n),1],minute_increment:[t(n),1],second_increment:[t(n),1],seconds:[e,!1],clock:[o.Clock,"24h"]}))),s.override({date_format:"Y-m-d H:i"})},
643: function _(e,t,a,i,n){var s;i();const r=e(642),l=e(629),c=e(12);class _ extends r.BaseDatetimePickerView{get flatpickr_options(){return{...super.flatpickr_options,mode:"range"}}_on_change(e){switch(e.length){case 0:this.model.value=null;break;case 1:break;case 2:{const[t,a]=e,i=this._format_date(t),n=this._format_date(a);this.model.value=[i,n];break}default:(0,c.assert)(!1,"invalid length")}}}a.DatetimeRangePickerView=_,_.__name__="DatetimeRangePickerView";class o extends r.BaseDatetimePicker{constructor(e){super(e)}}a.DatetimeRangePicker=o,s=o,o.__name__="DatetimeRangePicker",s.prototype.default_view=_,s.define((({Nullable:e,Tuple:t})=>({value:[e(t(l.DateLike,l.DateLike)),null]})))},
644: function _(e,t,s,i,r){var _;i();const n=e(645);class a extends n.MarkupView{render(){super.render(),this.model.render_as_text?this.markup_el.textContent=this.model.text:this.markup_el.innerHTML=this.has_math_disabled()?this.model.text:this.process_tex(this.model.text)}}s.DivView=a,a.__name__="DivView";class d extends n.Markup{constructor(e){super(e)}}s.Div=d,_=d,d.__name__="Div",_.prototype.default_view=a,_.define((({Bool:e})=>({render_as_text:[e,!1]})))},
645: function _(t,e,s,i,a){var r;i();const n=t(1),d=t(63),h=t(718),o=n.__importStar(t(646));class _ extends h.WidgetView{constructor(){super(...arguments),this._auto_width="fit-content",this._auto_height="auto"}async lazy_initialize(){await super.lazy_initialize(),"not_started"!=this.provider.status&&"loading"!=this.provider.status||this.provider.ready.connect((()=>{this.contains_tex_string(this.model.text)&&this.rerender()}))}has_math_disabled(){return this.model.disable_math||!this.contains_tex_string(this.model.text)}connect_signals(){super.connect_signals(),this.connect(this.model.change,(()=>{this.rerender()}))}stylesheets(){return[...super.stylesheets(),o.default,"p { margin: 0; }"]}render(){super.render(),this.markup_el=(0,d.div)({class:o.clearfix,style:{display:"inline-block"}}),this.shadow_el.appendChild(this.markup_el),"failed"!=this.provider.status&&"loaded"!=this.provider.status||(this._has_finished=!0)}}s.MarkupView=_,_.__name__="MarkupView";class l extends h.Widget{constructor(t){super(t)}}s.Markup=l,r=l,l.__name__="Markup",r.define((({Bool:t,Str:e})=>({text:[e,""],disable_math:[t,!1]})))},
646: function _(e,a,f,l,r){l(),f.clearfix="bk-clearfix",f.default='.bk-clearfix:before,.bk-clearfix:after{content:"";display:table;}.bk-clearfix:after{clear:both;}'},
647: function _(e,t,i,s,n){var o;s();const l=e(1),_=e(607),r=e(53),d=e(63),u=e(8),c=e(50),h=l.__importStar(e(610)),m=l.__importStar(e(616)),p=m,a=l.__importStar(e(648)),g=a;class w extends _.AbstractButtonView{constructor(){super(...arguments),this._open=!1}stylesheets(){return[...super.stylesheets(),m.default,a.default]}connect_signals(){super.connect_signals();const{menu:e}=this.model.properties;this.on_change(e,(()=>this.rebuild_menu()))}render(){super.render();const e=(0,d.div)({class:[g.caret,g.down]});if(this.model.is_split){const t=this._render_button(e);t.classList.add(h.dropdown_toggle),t.addEventListener("click",(()=>this._toggle_menu())),this.group_el.append(t)}else this.button_el.append(e);this.menu_el=(0,d.div)({class:[p.menu,p.below]}),this.shadow_el.append(this.menu_el),this.rebuild_menu(),(0,d.undisplay)(this.menu_el)}_show_menu(){if(!this._open){this._open=!0,(0,d.display)(this.menu_el);const e=t=>{t.composedPath().includes(this.el)||(document.removeEventListener("click",e),this._hide_menu())};document.addEventListener("click",e)}}_hide_menu(){this._open&&(this._open=!1,(0,d.undisplay)(this.menu_el))}_toggle_menu(){this._open?this._hide_menu():this._show_menu()}click(){this.model.is_split?(this._hide_menu(),this.model.trigger_event(new r.ButtonClick),super.click()):this._toggle_menu()}_item_click(e){this._hide_menu();const t=this.model.menu[e];if(null!=t){const i=(0,u.isString)(t)?t:t[1];(0,u.isString)(i)?this.model.trigger_event(new r.MenuItemClick(i)):(0,c.execute)(i,this.model,{index:e})}}rebuild_menu(){(0,d.empty)(this.menu_el);const e=this.model.menu.map(((e,t)=>{if(null==e)return(0,d.div)({class:p.divider});{const i=(0,u.isString)(e)?e:e[0],s=(0,d.div)(i);return s.addEventListener("click",(()=>this._item_click(t))),s}}));this.menu_el.append(...e)}}i.DropdownView=w,w.__name__="DropdownView";class v extends _.AbstractButton{constructor(e){super(e)}get is_split(){return this.split}}i.Dropdown=v,o=v,v.__name__="Dropdown",o.prototype.default_view=w,o.define((({Null:e,Bool:t,Str:i,List:s,Tuple:n,Or:o})=>({split:[t,!1],menu:[s(o(i,n(i,o(i)),e)),[]]}))),o.override({label:"Dropdown"})},
648: function _(t,r,e,a,d){a(),e.caret="bk-caret",e.down="bk-down",e.up="bk-up",e.left="bk-left",e.right="bk-right",e.default=":host{--caret-width:4px;}.bk-caret{display:inline-block;vertical-align:middle;width:0;height:0;margin:0 5px;}.bk-caret.bk-down{border-top:var(--caret-width) solid;}.bk-caret.bk-up{border-bottom:var(--caret-width) solid;}.bk-caret.bk-down,.bk-caret.bk-up{border-right:var(--caret-width) solid transparent;border-left:var(--caret-width) solid transparent;}.bk-caret.bk-left{border-right:var(--caret-width) solid;}.bk-caret.bk-right{border-left:var(--caret-width) solid;}.bk-caret.bk-left,.bk-caret.bk-right{border-top:var(--caret-width) solid transparent;border-bottom:var(--caret-width) solid transparent;}"},
649: function _(e,t,n,i,s){var l;i();const r=e(1),a=e(614),o=e(63),u=e(8),p=r.__importStar(e(19)),d=r.__importStar(e(615)),c=r.__importDefault(e(610));class _ extends a.InputWidgetView{connect_signals(){super.connect_signals(),this.model.on_event(a.ClearInput,(()=>{this.model.setv({value:"",mime_type:"",filename:""}),this.input_el.value=""}))}stylesheets(){return[...super.stylesheets(),c.default]}_render_input(){const{multiple:e,disabled:t,directory:n}=this.model,i=(()=>{const{accept:e}=this.model;return(0,u.isString)(e)?e:e.join(",")})();return this.input_el=(0,o.input)({type:"file",class:d.input,multiple:e,accept:i,disabled:t,webkitdirectory:n})}render(){super.render(),this.input_el.addEventListener("change",(async()=>{const{files:e}=this.input_el;null!=e&&await this.load_files(e)}))}async load_files(e){const t=[],n=[],i=[],{directory:s,multiple:l}=this.model,r=(()=>{const{accept:e}=this.model;return(0,u.isString)(e)?e:e.join(",")})();for(const l of e){const e=await this._read_file(l),[,a="",,o=""]=e.split(/[:;,]/,4);if(s){const e=l.name.split(".").pop();r.length>0&&(0,u.isString)(e)&&!r.includes(`.${e}`)||(n.push(l.webkitRelativePath),t.push(o),i.push(a))}else n.push(l.name),t.push(o),i.push(a)}const[a,o,p]=s||l?[t,n,i]:0!=e.length?[t[0],n[0],i[0]]:["","",""];this.model.setv({value:a,filename:o,mime_type:p})}_read_file(e){return new Promise(((t,n)=>{const i=new FileReader;i.onload=()=>{const{result:s}=i;null!=s?t(s):n(i.error??new Error(`unable to read '${e.name}'`))},i.readAsDataURL(e)}))}}n.FileInputView=_,_.__name__="FileInputView";class m extends a.InputWidget{constructor(e){super(e)}}n.FileInput=m,l=m,m.__name__="FileInput",l.prototype.default_view=_,l.define((({Bool:e,Str:t,List:n,Or:i})=>({value:[i(t,n(t)),p.unset,{readonly:!0}],mime_type:[i(t,n(t)),p.unset,{readonly:!0}],filename:[i(t,n(t)),p.unset,{readonly:!0}],accept:[i(t,n(t)),""],multiple:[e,!1],directory:[e,!1]})))},
650: function _(e,t,i,o,n){var s;o();const l=e(607),d=e(425),r=e(492),a=e(56);class p extends l.AbstractButtonView{*children(){yield*super.children(),yield this.tooltip}async lazy_initialize(){await super.lazy_initialize();const{tooltip:e}=this.model;this.tooltip=await(0,a.build_view)(e,{parent:this})}remove(){this.tooltip.remove(),super.remove()}render(){super.render();let e=!1;const t=t=>{this.tooltip.model.setv({visible:t,closable:e})};this.on_change(this.tooltip.model.properties.visible,(()=>{const{visible:i}=this.tooltip.model;i||(e=!1),t(i)})),this.el.addEventListener("mouseenter",(()=>{t(!0)})),this.el.addEventListener("mouseleave",(()=>{e||t(!1)})),document.addEventListener("mousedown",(i=>{const o=i.composedPath();o.includes(this.tooltip.el)||(o.includes(this.el)?(e=!e,t(e)):(e=!1,t(!1)))})),window.addEventListener("blur",(()=>{e=!1,t(!1)}))}}i.HelpButtonView=p,p.__name__="HelpButtonView";class u extends l.AbstractButton{constructor(e){super(e)}}i.HelpButton=u,s=u,u.__name__="HelpButton",s.prototype.default_view=p,s.define((({Ref:e})=>({tooltip:[e(d.Tooltip)]}))),s.override({label:"",icon:()=>new r.BuiltinIcon({icon_name:"help",size:18}),button_type:"default"})},
651: function _(e,t,i,s,o){var l;s();const n=e(1),u=n.__importDefault(e(652)),_=e(63),r=e(8),a=e(27),c=n.__importStar(e(615)),h=n.__importDefault(e(653)),d=e(614);function p(e){return Object.defineProperty(e,"target",{get:()=>e.composedPath()[0]??null,configurable:!0}),e}class m extends u.default{_onFocus(e){super._onFocus(p(e))}_onBlur(e){super._onBlur(p(e))}_onKeyUp(e){super._onKeyUp(p(e))}_onKeyDown(e){super._onKeyDown(p(e))}_onClick(e){super._onClick(p(e))}_onTouchEnd(e){super._onTouchEnd(p(e))}_onMouseDown(e){super._onMouseDown(p(e))}_onMouseOver(e){super._onMouseOver(p(e))}}m.__name__="OurChoices";class g extends d.InputWidgetView{connect_signals(){super.connect_signals(),this.connect(this.model.properties.disabled.change,(()=>this.set_disabled()));const{value:e,max_items:t,option_limit:i,search_option_limit:s,delete_button:o,placeholder:l,options:n,name:u,title:_}=this.model.properties;this.on_change([t,i,s,o,l,n,u,_],(()=>this.rerender())),this.on_change(e,(()=>{(0,a.is_equal)(this.model.value,this._current_values)||this.rerender()}))}stylesheets(){return[...super.stylesheets(),h.default]}_render_input(){return this.input_el=(0,_.select)({multiple:!0,class:c.input,name:this.model.name,disabled:this.model.disabled})}render(){super.render();const e=new Set(this.model.value),t=this.model.options.map((t=>{let i,s;return(0,r.isString)(t)?i=s=t:[i,s]=t,{value:i,label:s,selected:e.has(i)}})),i=this.model.solid?"solid":"light",s=`choices__item ${i}`,o=`choices__button ${i}`,l={choices:t,itemSelectText:"",duplicateItemsAllowed:!1,shouldSort:!1,removeItemButton:this.model.delete_button,classNames:{item:s,button:o},placeholderValue:this.model.placeholder,maxItemCount:this.model.max_items??-1,renderChoiceLimit:this.model.option_limit??-1,searchResultLimit:this.model.search_option_limit??4};this.choice_el=new m(this.input_el,l),this.input_el.addEventListener("change",(()=>this.change_input()))}set_disabled(){this.model.disabled?this.choice_el.disable():this.choice_el.enable()}get _current_values(){return this.choice_el.getValue().map((e=>e.value))}change_input(){this.model.value=this._current_values,super.change_input()}}i.MultiChoiceView=g,g.__name__="MultiChoiceView";class b extends d.InputWidget{constructor(e){super(e)}}i.MultiChoice=b,l=b,b.__name__="MultiChoice",l.prototype.default_view=g,l.define((({Bool:e,Int:t,Str:i,List:s,Tuple:o,Or:l,Nullable:n})=>({value:[s(i),[]],options:[s(l(i,o(i,i))),[]],max_items:[n(t),null],delete_button:[e,!0],placeholder:[n(i),null],option_limit:[n(t),null],search_option_limit:[n(t),null],solid:[e,!0]})))},
652: function _(e,t,i,n,r){
/*! choices.js v10.2.0 | © 2022 Josh Johnson | https://github.com/jshjohnson/Choices#readme */
var s,o;s=window,o=function(){return function(){"use strict";var e={282:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.clearChoices=t.activateChoices=t.filterChoices=t.addChoice=void 0;var n=i(883);t.addChoice=function(e){var t=e.value,i=e.label,r=e.id,s=e.groupId,o=e.disabled,a=e.elementId,c=e.customProperties,l=e.placeholder,h=e.keyCode;return{type:n.ACTION_TYPES.ADD_CHOICE,value:t,label:i,id:r,groupId:s,disabled:o,elementId:a,customProperties:c,placeholder:l,keyCode:h}},t.filterChoices=function(e){return{type:n.ACTION_TYPES.FILTER_CHOICES,results:e}},t.activateChoices=function(e){return void 0===e&&(e=!0),{type:n.ACTION_TYPES.ACTIVATE_CHOICES,active:e}},t.clearChoices=function(){return{type:n.ACTION_TYPES.CLEAR_CHOICES}}},783:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.addGroup=void 0;var n=i(883);t.addGroup=function(e){var t=e.value,i=e.id,r=e.active,s=e.disabled;return{type:n.ACTION_TYPES.ADD_GROUP,value:t,id:i,active:r,disabled:s}}},464:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.highlightItem=t.removeItem=t.addItem=void 0;var n=i(883);t.addItem=function(e){var t=e.value,i=e.label,r=e.id,s=e.choiceId,o=e.groupId,a=e.customProperties,c=e.placeholder,l=e.keyCode;return{type:n.ACTION_TYPES.ADD_ITEM,value:t,label:i,id:r,choiceId:s,groupId:o,customProperties:a,placeholder:c,keyCode:l}},t.removeItem=function(e,t){return{type:n.ACTION_TYPES.REMOVE_ITEM,id:e,choiceId:t}},t.highlightItem=function(e,t){return{type:n.ACTION_TYPES.HIGHLIGHT_ITEM,id:e,highlighted:t}}},137:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.setIsLoading=t.resetTo=t.clearAll=void 0;var n=i(883);t.clearAll=function(){return{type:n.ACTION_TYPES.CLEAR_ALL}},t.resetTo=function(e){return{type:n.ACTION_TYPES.RESET_TO,state:e}},t.setIsLoading=function(e){return{type:n.ACTION_TYPES.SET_IS_LOADING,isLoading:e}}},373:function(e,t,i){var n=this&&this.__spreadArray||function(e,t,i){if(i||2===arguments.length)for(var n,r=0,s=t.length;r<s;r++)!n&&r in t||(n||(n=Array.prototype.slice.call(t,0,r)),n[r]=t[r]);return e.concat(n||Array.prototype.slice.call(t))},r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var s=r(i(996)),o=r(i(221)),a=i(282),c=i(783),l=i(464),h=i(137),u=i(520),d=i(883),p=i(789),f=i(799),m=i(655),v=r(i(744)),g=r(i(686)),_="-ms-scroll-limit"in document.documentElement.style&&"-ms-ime-align"in document.documentElement.style,y={},E=function(){function e(t,i){void 0===t&&(t="[data-choice]"),void 0===i&&(i={});var r=this;void 0===i.allowHTML&&console.warn("Deprecation warning: allowHTML will default to false in a future release. To render HTML in Choices, you will need to set it to true. Setting allowHTML will suppress this message."),this.config=s.default.all([p.DEFAULT_CONFIG,e.defaults.options,i],{arrayMerge:function(e,t){return n([],t,!0)}});var o=(0,f.diff)(this.config,p.DEFAULT_CONFIG);o.length&&console.warn("Unknown config option(s) passed",o.join(", "));var a="string"==typeof t?document.querySelector(t):t;if(!(a instanceof HTMLInputElement||a instanceof HTMLSelectElement))throw TypeError("Expected one of the following types text|select-one|select-multiple");if(this._isTextElement=a.type===d.TEXT_TYPE,this._isSelectOneElement=a.type===d.SELECT_ONE_TYPE,this._isSelectMultipleElement=a.type===d.SELECT_MULTIPLE_TYPE,this._isSelectElement=this._isSelectOneElement||this._isSelectMultipleElement,this.config.searchEnabled=this._isSelectMultipleElement||this.config.searchEnabled,["auto","always"].includes("".concat(this.config.renderSelectedChoices))||(this.config.renderSelectedChoices="auto"),i.addItemFilter&&"function"!=typeof i.addItemFilter){var c=i.addItemFilter instanceof RegExp?i.addItemFilter:new RegExp(i.addItemFilter);this.config.addItemFilter=c.test.bind(c)}if(this._isTextElement?this.passedElement=new u.WrappedInput({element:a,classNames:this.config.classNames,delimiter:this.config.delimiter}):this.passedElement=new u.WrappedSelect({element:a,classNames:this.config.classNames,template:function(e){return r._templates.option(e)}}),this.initialised=!1,this._store=new v.default,this._initialState=m.defaultState,this._currentState=m.defaultState,this._prevState=m.defaultState,this._currentValue="",this._canSearch=!!this.config.searchEnabled,this._isScrollingOnIe=!1,this._highlightPosition=0,this._wasTap=!0,this._placeholderValue=this._generatePlaceholderValue(),this._baseId=(0,f.generateId)(this.passedElement.element,"choices-"),this._direction=this.passedElement.dir,!this._direction){var l=window.getComputedStyle(this.passedElement.element).direction;l!==window.getComputedStyle(document.documentElement).direction&&(this._direction=l)}if(this._idNames={itemChoice:"item-choice"},this._isSelectElement&&(this._presetGroups=this.passedElement.optionGroups,this._presetOptions=this.passedElement.options),this._presetChoices=this.config.choices,this._presetItems=this.config.items,this.passedElement.value&&this._isTextElement){var h=this.passedElement.value.split(this.config.delimiter);this._presetItems=this._presetItems.concat(h)}if(this.passedElement.options&&this.passedElement.options.forEach((function(e){r._presetChoices.push({value:e.value,label:e.innerHTML,selected:!!e.selected,disabled:e.disabled||e.parentNode.disabled,placeholder:""===e.value||e.hasAttribute("placeholder"),customProperties:(0,f.parseCustomProperties)(e.dataset.customProperties)})})),this._render=this._render.bind(this),this._onFocus=this._onFocus.bind(this),this._onBlur=this._onBlur.bind(this),this._onKeyUp=this._onKeyUp.bind(this),this._onKeyDown=this._onKeyDown.bind(this),this._onClick=this._onClick.bind(this),this._onTouchMove=this._onTouchMove.bind(this),this._onTouchEnd=this._onTouchEnd.bind(this),this._onMouseDown=this._onMouseDown.bind(this),this._onMouseOver=this._onMouseOver.bind(this),this._onFormReset=this._onFormReset.bind(this),this._onSelectKey=this._onSelectKey.bind(this),this._onEnterKey=this._onEnterKey.bind(this),this._onEscapeKey=this._onEscapeKey.bind(this),this._onDirectionKey=this._onDirectionKey.bind(this),this._onDeleteKey=this._onDeleteKey.bind(this),this.passedElement.isActive)return this.config.silent||console.warn("Trying to initialise Choices on element already initialised",{element:t}),void(this.initialised=!0);this.init()}return Object.defineProperty(e,"defaults",{get:function(){return Object.preventExtensions({get options(){return y},get templates(){return g.default}})},enumerable:!1,configurable:!0}),e.prototype.init=function(){if(!this.initialised){this._createTemplates(),this._createElements(),this._createStructure(),this._store.subscribe(this._render),this._render(),this._addEventListeners(),(!this.config.addItems||this.passedElement.element.hasAttribute("disabled"))&&this.disable(),this.initialised=!0;var e=this.config.callbackOnInit;e&&"function"==typeof e&&e.call(this)}},e.prototype.destroy=function(){this.initialised&&(this._removeEventListeners(),this.passedElement.reveal(),this.containerOuter.unwrap(this.passedElement.element),this.clearStore(),this._isSelectElement&&(this.passedElement.options=this._presetOptions),this._templates=g.default,this.initialised=!1)},e.prototype.enable=function(){return this.passedElement.isDisabled&&this.passedElement.enable(),this.containerOuter.isDisabled&&(this._addEventListeners(),this.input.enable(),this.containerOuter.enable()),this},e.prototype.disable=function(){return this.passedElement.isDisabled||this.passedElement.disable(),this.containerOuter.isDisabled||(this._removeEventListeners(),this.input.disable(),this.containerOuter.disable()),this},e.prototype.highlightItem=function(e,t){if(void 0===t&&(t=!0),!e||!e.id)return this;var i=e.id,n=e.groupId,r=void 0===n?-1:n,s=e.value,o=void 0===s?"":s,a=e.label,c=void 0===a?"":a,h=r>=0?this._store.getGroupById(r):null;return this._store.dispatch((0,l.highlightItem)(i,!0)),t&&this.passedElement.triggerEvent(d.EVENTS.highlightItem,{id:i,value:o,label:c,groupValue:h&&h.value?h.value:null}),this},e.prototype.unhighlightItem=function(e){if(!e||!e.id)return this;var t=e.id,i=e.groupId,n=void 0===i?-1:i,r=e.value,s=void 0===r?"":r,o=e.label,a=void 0===o?"":o,c=n>=0?this._store.getGroupById(n):null;return this._store.dispatch((0,l.highlightItem)(t,!1)),this.passedElement.triggerEvent(d.EVENTS.highlightItem,{id:t,value:s,label:a,groupValue:c&&c.value?c.value:null}),this},e.prototype.highlightAll=function(){var e=this;return this._store.items.forEach((function(t){return e.highlightItem(t)})),this},e.prototype.unhighlightAll=function(){var e=this;return this._store.items.forEach((function(t){return e.unhighlightItem(t)})),this},e.prototype.removeActiveItemsByValue=function(e){var t=this;return this._store.activeItems.filter((function(t){return t.value===e})).forEach((function(e){return t._removeItem(e)})),this},e.prototype.removeActiveItems=function(e){var t=this;return this._store.activeItems.filter((function(t){return t.id!==e})).forEach((function(e){return t._removeItem(e)})),this},e.prototype.removeHighlightedItems=function(e){var t=this;return void 0===e&&(e=!1),this._store.highlightedActiveItems.forEach((function(i){t._removeItem(i),e&&t._triggerChange(i.value)})),this},e.prototype.showDropdown=function(e){var t=this;return this.dropdown.isActive||requestAnimationFrame((function(){t.dropdown.show(),t.containerOuter.open(t.dropdown.distanceFromTopWindow),!e&&t._canSearch&&t.input.focus(),t.passedElement.triggerEvent(d.EVENTS.showDropdown,{})})),this},e.prototype.hideDropdown=function(e){var t=this;return this.dropdown.isActive?(requestAnimationFrame((function(){t.dropdown.hide(),t.containerOuter.close(),!e&&t._canSearch&&(t.input.removeActiveDescendant(),t.input.blur()),t.passedElement.triggerEvent(d.EVENTS.hideDropdown,{})})),this):this},e.prototype.getValue=function(e){void 0===e&&(e=!1);var t=this._store.activeItems.reduce((function(t,i){var n=e?i.value:i;return t.push(n),t}),[]);return this._isSelectOneElement?t[0]:t},e.prototype.setValue=function(e){var t=this;return this.initialised?(e.forEach((function(e){return t._setChoiceOrItem(e)})),this):this},e.prototype.setChoiceByValue=function(e){var t=this;return!this.initialised||this._isTextElement||(Array.isArray(e)?e:[e]).forEach((function(e){return t._findAndSelectChoiceByValue(e)})),this},e.prototype.setChoices=function(e,t,i,n){var r=this;if(void 0===e&&(e=[]),void 0===t&&(t="value"),void 0===i&&(i="label"),void 0===n&&(n=!1),!this.initialised)throw new ReferenceError("setChoices was called on a non-initialized instance of Choices");if(!this._isSelectElement)throw new TypeError("setChoices can't be used with INPUT based Choices");if("string"!=typeof t||!t)throw new TypeError("value parameter must be a name of 'value' field in passed objects");if(n&&this.clearChoices(),"function"==typeof e){var s=e(this);if("function"==typeof Promise&&s instanceof Promise)return new Promise((function(e){return requestAnimationFrame(e)})).then((function(){return r._handleLoadingState(!0)})).then((function(){return s})).then((function(e){return r.setChoices(e,t,i,n)})).catch((function(e){r.config.silent||console.error(e)})).then((function(){return r._handleLoadingState(!1)})).then((function(){return r}));if(!Array.isArray(s))throw new TypeError(".setChoices first argument function must return either array of choices or Promise, got: ".concat(typeof s));return this.setChoices(s,t,i,!1)}if(!Array.isArray(e))throw new TypeError(".setChoices must be called either with array of choices with a function resulting into Promise of array of choices");return this.containerOuter.removeLoadingState(),this._startLoading(),e.forEach((function(e){if(e.choices)r._addGroup({id:e.id?parseInt("".concat(e.id),10):null,group:e,valueKey:t,labelKey:i});else{var n=e;r._addChoice({value:n[t],label:n[i],isSelected:!!n.selected,isDisabled:!!n.disabled,placeholder:!!n.placeholder,customProperties:n.customProperties})}})),this._stopLoading(),this},e.prototype.clearChoices=function(){return this._store.dispatch((0,a.clearChoices)()),this},e.prototype.clearStore=function(){return this._store.dispatch((0,h.clearAll)()),this},e.prototype.clearInput=function(){var e=!this._isSelectOneElement;return this.input.clear(e),!this._isTextElement&&this._canSearch&&(this._isSearching=!1,this._store.dispatch((0,a.activateChoices)(!0))),this},e.prototype._render=function(){if(!this._store.isLoading()){this._currentState=this._store.state;var e=this._currentState.choices!==this._prevState.choices||this._currentState.groups!==this._prevState.groups||this._currentState.items!==this._prevState.items,t=this._isSelectElement,i=this._currentState.items!==this._prevState.items;e&&(t&&this._renderChoices(),i&&this._renderItems(),this._prevState=this._currentState)}},e.prototype._renderChoices=function(){var e=this,t=this._store,i=t.activeGroups,n=t.activeChoices,r=document.createDocumentFragment();if(this.choiceList.clear(),this.config.resetScrollPosition&&requestAnimationFrame((function(){return e.choiceList.scrollToTop()})),i.length>=1&&!this._isSearching){var s=n.filter((function(e){return!0===e.placeholder&&-1===e.groupId}));s.length>=1&&(r=this._createChoicesFragment(s,r)),r=this._createGroupsFragment(i,n,r)}else n.length>=1&&(r=this._createChoicesFragment(n,r));if(r.childNodes&&r.childNodes.length>0){var o=this._store.activeItems,a=this._canAddItem(o,this.input.value);if(a.response)this.choiceList.append(r),this._highlightChoice();else{var c=this._getTemplate("notice",a.notice);this.choiceList.append(c)}}else{var l=void 0;c=void 0,this._isSearching?(c="function"==typeof this.config.noResultsText?this.config.noResultsText():this.config.noResultsText,l=this._getTemplate("notice",c,"no-results")):(c="function"==typeof this.config.noChoicesText?this.config.noChoicesText():this.config.noChoicesText,l=this._getTemplate("notice",c,"no-choices")),this.choiceList.append(l)}},e.prototype._renderItems=function(){var e=this._store.activeItems||[];this.itemList.clear();var t=this._createItemsFragment(e);t.childNodes&&this.itemList.append(t)},e.prototype._createGroupsFragment=function(e,t,i){var n=this;return void 0===i&&(i=document.createDocumentFragment()),this.config.shouldSort&&e.sort(this.config.sorter),e.forEach((function(e){var r=function(e){return t.filter((function(t){return n._isSelectOneElement?t.groupId===e.id:t.groupId===e.id&&("always"===n.config.renderSelectedChoices||!t.selected)}))}(e);if(r.length>=1){var s=n._getTemplate("choiceGroup",e);i.appendChild(s),n._createChoicesFragment(r,i,!0)}})),i},e.prototype._createChoicesFragment=function(e,t,i){var r=this;void 0===t&&(t=document.createDocumentFragment()),void 0===i&&(i=!1);var s=this.config,o=s.renderSelectedChoices,a=s.searchResultLimit,c=s.renderChoiceLimit,l=this._isSearching?f.sortByScore:this.config.sorter,h=function(e){if("auto"!==o||r._isSelectOneElement||!e.selected){var i=r._getTemplate("choice",e,r.config.itemSelectText);t.appendChild(i)}},u=e;"auto"!==o||this._isSelectOneElement||(u=e.filter((function(e){return!e.selected})));var d=u.reduce((function(e,t){return t.placeholder?e.placeholderChoices.push(t):e.normalChoices.push(t),e}),{placeholderChoices:[],normalChoices:[]}),p=d.placeholderChoices,m=d.normalChoices;(this.config.shouldSort||this._isSearching)&&m.sort(l);var v=u.length,g=this._isSelectOneElement?n(n([],p,!0),m,!0):m;this._isSearching?v=a:c&&c>0&&!i&&(v=c);for(var _=0;_<v;_+=1)g[_]&&h(g[_]);return t},e.prototype._createItemsFragment=function(e,t){var i=this;void 0===t&&(t=document.createDocumentFragment());var n=this.config,r=n.shouldSortItems,s=n.sorter,o=n.removeItemButton;return r&&!this._isSelectOneElement&&e.sort(s),this._isTextElement?this.passedElement.value=e.map((function(e){return e.value})).join(this.config.delimiter):this.passedElement.options=e,e.forEach((function(e){var n=i._getTemplate("item",e,o);t.appendChild(n)})),t},e.prototype._triggerChange=function(e){null!=e&&this.passedElement.triggerEvent(d.EVENTS.change,{value:e})},e.prototype._selectPlaceholderChoice=function(e){this._addItem({value:e.value,label:e.label,choiceId:e.id,groupId:e.groupId,placeholder:e.placeholder}),this._triggerChange(e.value)},e.prototype._handleButtonAction=function(e,t){if(e&&t&&this.config.removeItems&&this.config.removeItemButton){var i=t.parentNode&&t.parentNode.dataset.id,n=i&&e.find((function(e){return e.id===parseInt(i,10)}));n&&(this._removeItem(n),this._triggerChange(n.value),this._isSelectOneElement&&this._store.placeholderChoice&&this._selectPlaceholderChoice(this._store.placeholderChoice))}},e.prototype._handleItemAction=function(e,t,i){var n=this;if(void 0===i&&(i=!1),e&&t&&this.config.removeItems&&!this._isSelectOneElement){var r=t.dataset.id;e.forEach((function(e){e.id!==parseInt("".concat(r),10)||e.highlighted?!i&&e.highlighted&&n.unhighlightItem(e):n.highlightItem(e)})),this.input.focus()}},e.prototype._handleChoiceAction=function(e,t){if(e&&t){var i=t.dataset.id,n=i&&this._store.getChoiceById(i);if(n){var r=e[0]&&e[0].keyCode?e[0].keyCode:void 0,s=this.dropdown.isActive;n.keyCode=r,this.passedElement.triggerEvent(d.EVENTS.choice,{choice:n}),n.selected||n.disabled||this._canAddItem(e,n.value).response&&(this._addItem({value:n.value,label:n.label,choiceId:n.id,groupId:n.groupId,customProperties:n.customProperties,placeholder:n.placeholder,keyCode:n.keyCode}),this._triggerChange(n.value)),this.clearInput(),s&&this._isSelectOneElement&&(this.hideDropdown(!0),this.containerOuter.focus())}}},e.prototype._handleBackspace=function(e){if(this.config.removeItems&&e){var t=e[e.length-1],i=e.some((function(e){return e.highlighted}));this.config.editItems&&!i&&t?(this.input.value=t.value,this.input.setWidth(),this._removeItem(t),this._triggerChange(t.value)):(i||this.highlightItem(t,!1),this.removeHighlightedItems(!0))}},e.prototype._startLoading=function(){this._store.dispatch((0,h.setIsLoading)(!0))},e.prototype._stopLoading=function(){this._store.dispatch((0,h.setIsLoading)(!1))},e.prototype._handleLoadingState=function(e){void 0===e&&(e=!0);var t=this.itemList.getChild(".".concat(this.config.classNames.placeholder));e?(this.disable(),this.containerOuter.addLoadingState(),this._isSelectOneElement?t?t.innerHTML=this.config.loadingText:(t=this._getTemplate("placeholder",this.config.loadingText))&&this.itemList.append(t):this.input.placeholder=this.config.loadingText):(this.enable(),this.containerOuter.removeLoadingState(),this._isSelectOneElement?t&&(t.innerHTML=this._placeholderValue||""):this.input.placeholder=this._placeholderValue||"")},e.prototype._handleSearch=function(e){if(this.input.isFocussed){var t=this._store.choices,i=this.config,n=i.searchFloor,r=i.searchChoices,s=t.some((function(e){return!e.active}));if(null!=e&&e.length>=n){var o=r?this._searchChoices(e):0;this.passedElement.triggerEvent(d.EVENTS.search,{value:e,resultCount:o})}else s&&(this._isSearching=!1,this._store.dispatch((0,a.activateChoices)(!0)))}},e.prototype._canAddItem=function(e,t){var i=!0,n="function"==typeof this.config.addItemText?this.config.addItemText(t):this.config.addItemText;if(!this._isSelectOneElement){var r=(0,f.existsInArray)(e,t);this.config.maxItemCount>0&&this.config.maxItemCount<=e.length&&(i=!1,n="function"==typeof this.config.maxItemText?this.config.maxItemText(this.config.maxItemCount):this.config.maxItemText),!this.config.duplicateItemsAllowed&&r&&i&&(i=!1,n="function"==typeof this.config.uniqueItemText?this.config.uniqueItemText(t):this.config.uniqueItemText),this._isTextElement&&this.config.addItems&&i&&"function"==typeof this.config.addItemFilter&&!this.config.addItemFilter(t)&&(i=!1,n="function"==typeof this.config.customAddItemText?this.config.customAddItemText(t):this.config.customAddItemText)}return{response:i,notice:n}},e.prototype._searchChoices=function(e){var t="string"==typeof e?e.trim():e,i="string"==typeof this._currentValue?this._currentValue.trim():this._currentValue;if(t.length<1&&t==="".concat(i," "))return 0;var r=this._store.searchableChoices,s=t,c=Object.assign(this.config.fuseOptions,{keys:n([],this.config.searchFields,!0),includeMatches:!0}),l=new o.default(r,c).search(s);return this._currentValue=t,this._highlightPosition=0,this._isSearching=!0,this._store.dispatch((0,a.filterChoices)(l)),l.length},e.prototype._addEventListeners=function(){var e=document.documentElement;e.addEventListener("touchend",this._onTouchEnd,!0),this.containerOuter.element.addEventListener("keydown",this._onKeyDown,!0),this.containerOuter.element.addEventListener("mousedown",this._onMouseDown,!0),e.addEventListener("click",this._onClick,{passive:!0}),e.addEventListener("touchmove",this._onTouchMove,{passive:!0}),this.dropdown.element.addEventListener("mouseover",this._onMouseOver,{passive:!0}),this._isSelectOneElement&&(this.containerOuter.element.addEventListener("focus",this._onFocus,{passive:!0}),this.containerOuter.element.addEventListener("blur",this._onBlur,{passive:!0})),this.input.element.addEventListener("keyup",this._onKeyUp,{passive:!0}),this.input.element.addEventListener("focus",this._onFocus,{passive:!0}),this.input.element.addEventListener("blur",this._onBlur,{passive:!0}),this.input.element.form&&this.input.element.form.addEventListener("reset",this._onFormReset,{passive:!0}),this.input.addEventListeners()},e.prototype._removeEventListeners=function(){var e=document.documentElement;e.removeEventListener("touchend",this._onTouchEnd,!0),this.containerOuter.element.removeEventListener("keydown",this._onKeyDown,!0),this.containerOuter.element.removeEventListener("mousedown",this._onMouseDown,!0),e.removeEventListener("click",this._onClick),e.removeEventListener("touchmove",this._onTouchMove),this.dropdown.element.removeEventListener("mouseover",this._onMouseOver),this._isSelectOneElement&&(this.containerOuter.element.removeEventListener("focus",this._onFocus),this.containerOuter.element.removeEventListener("blur",this._onBlur)),this.input.element.removeEventListener("keyup",this._onKeyUp),this.input.element.removeEventListener("focus",this._onFocus),this.input.element.removeEventListener("blur",this._onBlur),this.input.element.form&&this.input.element.form.removeEventListener("reset",this._onFormReset),this.input.removeEventListeners()},e.prototype._onKeyDown=function(e){var t=e.keyCode,i=this._store.activeItems,n=this.input.isFocussed,r=this.dropdown.isActive,s=this.itemList.hasChildren(),o=String.fromCharCode(t),a=/[^\x00-\x1F]/.test(o),c=d.KEY_CODES.BACK_KEY,l=d.KEY_CODES.DELETE_KEY,h=d.KEY_CODES.ENTER_KEY,u=d.KEY_CODES.A_KEY,p=d.KEY_CODES.ESC_KEY,f=d.KEY_CODES.UP_KEY,m=d.KEY_CODES.DOWN_KEY,v=d.KEY_CODES.PAGE_UP_KEY,g=d.KEY_CODES.PAGE_DOWN_KEY;switch(this._isTextElement||r||!a||(this.showDropdown(),this.input.isFocussed||(this.input.value+=e.key.toLowerCase())),t){case u:return this._onSelectKey(e,s);case h:return this._onEnterKey(e,i,r);case p:return this._onEscapeKey(r);case f:case v:case m:case g:return this._onDirectionKey(e,r);case l:case c:return this._onDeleteKey(e,i,n)}},e.prototype._onKeyUp=function(e){var t=e.target,i=e.keyCode,n=this.input.value,r=this._store.activeItems,s=this._canAddItem(r,n),o=d.KEY_CODES.BACK_KEY,c=d.KEY_CODES.DELETE_KEY;if(this._isTextElement)if(s.notice&&n){var l=this._getTemplate("notice",s.notice);this.dropdown.element.innerHTML=l.outerHTML,this.showDropdown(!0)}else this.hideDropdown(!0);else{var h=(i===o||i===c)&&t&&!t.value,u=!this._isTextElement&&this._isSearching,p=this._canSearch&&s.response;h&&u?(this._isSearching=!1,this._store.dispatch((0,a.activateChoices)(!0))):p&&this._handleSearch(this.input.rawValue)}this._canSearch=this.config.searchEnabled},e.prototype._onSelectKey=function(e,t){var i=e.ctrlKey,n=e.metaKey;(i||n)&&t&&(this._canSearch=!1,this.config.removeItems&&!this.input.value&&this.input.element===document.activeElement&&this.highlightAll())},e.prototype._onEnterKey=function(e,t,i){var n=e.target,r=d.KEY_CODES.ENTER_KEY,s=n&&n.hasAttribute("data-button");if(this._isTextElement&&n&&n.value){var o=this.input.value;this._canAddItem(t,o).response&&(this.hideDropdown(!0),this._addItem({value:o}),this._triggerChange(o),this.clearInput())}if(s&&(this._handleButtonAction(t,n),e.preventDefault()),i){var a=this.dropdown.getChild(".".concat(this.config.classNames.highlightedState));a&&(t[0]&&(t[0].keyCode=r),this._handleChoiceAction(t,a)),e.preventDefault()}else this._isSelectOneElement&&(this.showDropdown(),e.preventDefault())},e.prototype._onEscapeKey=function(e){e&&(this.hideDropdown(!0),this.containerOuter.focus())},e.prototype._onDirectionKey=function(e,t){var i=e.keyCode,n=e.metaKey,r=d.KEY_CODES.DOWN_KEY,s=d.KEY_CODES.PAGE_UP_KEY,o=d.KEY_CODES.PAGE_DOWN_KEY;if(t||this._isSelectOneElement){this.showDropdown(),this._canSearch=!1;var a=i===r||i===o?1:-1,c="[data-choice-selectable]",l=void 0;if(n||i===o||i===s)l=a>0?this.dropdown.element.querySelector("".concat(c,":last-of-type")):this.dropdown.element.querySelector(c);else{var h=this.dropdown.element.querySelector(".".concat(this.config.classNames.highlightedState));l=h?(0,f.getAdjacentEl)(h,c,a):this.dropdown.element.querySelector(c)}l&&((0,f.isScrolledIntoView)(l,this.choiceList.element,a)||this.choiceList.scrollToChildElement(l,a),this._highlightChoice(l)),e.preventDefault()}},e.prototype._onDeleteKey=function(e,t,i){var n=e.target;this._isSelectOneElement||n.value||!i||(this._handleBackspace(t),e.preventDefault())},e.prototype._onTouchMove=function(){this._wasTap&&(this._wasTap=!1)},e.prototype._onTouchEnd=function(e){var t=(e||e.touches[0]).target;this._wasTap&&this.containerOuter.element.contains(t)&&((t===this.containerOuter.element||t===this.containerInner.element)&&(this._isTextElement?this.input.focus():this._isSelectMultipleElement&&this.showDropdown()),e.stopPropagation()),this._wasTap=!0},e.prototype._onMouseDown=function(e){var t=e.target;if(t instanceof HTMLElement){if(_&&this.choiceList.element.contains(t)){var i=this.choiceList.element.firstElementChild,n="ltr"===this._direction?e.offsetX>=i.offsetWidth:e.offsetX<i.offsetLeft;this._isScrollingOnIe=n}if(t!==this.input.element){var r=t.closest("[data-button],[data-item],[data-choice]");if(r instanceof HTMLElement){var s=e.shiftKey,o=this._store.activeItems,a=r.dataset;"button"in a?this._handleButtonAction(o,r):"item"in a?this._handleItemAction(o,r,s):"choice"in a&&this._handleChoiceAction(o,r)}e.preventDefault()}}},e.prototype._onMouseOver=function(e){var t=e.target;t instanceof HTMLElement&&"choice"in t.dataset&&this._highlightChoice(t)},e.prototype._onClick=function(e){var t=e.target;this.containerOuter.element.contains(t)?this.dropdown.isActive||this.containerOuter.isDisabled?this._isSelectOneElement&&t!==this.input.element&&!this.dropdown.element.contains(t)&&this.hideDropdown():this._isTextElement?document.activeElement!==this.input.element&&this.input.focus():(this.showDropdown(),this.containerOuter.focus()):(this._store.highlightedActiveItems.length>0&&this.unhighlightAll(),this.containerOuter.removeFocusState(),this.hideDropdown(!0))},e.prototype._onFocus=function(e){var t,i=this,n=e.target;n&&this.containerOuter.element.contains(n)&&((t={})[d.TEXT_TYPE]=function(){n===i.input.element&&i.containerOuter.addFocusState()},t[d.SELECT_ONE_TYPE]=function(){i.containerOuter.addFocusState(),n===i.input.element&&i.showDropdown(!0)},t[d.SELECT_MULTIPLE_TYPE]=function(){n===i.input.element&&(i.showDropdown(!0),i.containerOuter.addFocusState())},t)[this.passedElement.element.type]()},e.prototype._onBlur=function(e){var t,i=this,n=e.target;if(n&&this.containerOuter.element.contains(n)&&!this._isScrollingOnIe){var r=this._store.activeItems.some((function(e){return e.highlighted}));((t={})[d.TEXT_TYPE]=function(){n===i.input.element&&(i.containerOuter.removeFocusState(),r&&i.unhighlightAll(),i.hideDropdown(!0))},t[d.SELECT_ONE_TYPE]=function(){i.containerOuter.removeFocusState(),(n===i.input.element||n===i.containerOuter.element&&!i._canSearch)&&i.hideDropdown(!0)},t[d.SELECT_MULTIPLE_TYPE]=function(){n===i.input.element&&(i.containerOuter.removeFocusState(),i.hideDropdown(!0),r&&i.unhighlightAll())},t)[this.passedElement.element.type]()}else this._isScrollingOnIe=!1,this.input.element.focus()},e.prototype._onFormReset=function(){this._store.dispatch((0,h.resetTo)(this._initialState))},e.prototype._highlightChoice=function(e){var t=this;void 0===e&&(e=null);var i=Array.from(this.dropdown.element.querySelectorAll("[data-choice-selectable]"));if(i.length){var n=e;Array.from(this.dropdown.element.querySelectorAll(".".concat(this.config.classNames.highlightedState))).forEach((function(e){e.classList.remove(t.config.classNames.highlightedState),e.setAttribute("aria-selected","false")})),n?this._highlightPosition=i.indexOf(n):(n=i.length>this._highlightPosition?i[this._highlightPosition]:i[i.length-1])||(n=i[0]),n.classList.add(this.config.classNames.highlightedState),n.setAttribute("aria-selected","true"),this.passedElement.triggerEvent(d.EVENTS.highlightChoice,{el:n}),this.dropdown.isActive&&(this.input.setActiveDescendant(n.id),this.containerOuter.setActiveDescendant(n.id))}},e.prototype._addItem=function(e){var t=e.value,i=e.label,n=void 0===i?null:i,r=e.choiceId,s=void 0===r?-1:r,o=e.groupId,a=void 0===o?-1:o,c=e.customProperties,h=void 0===c?{}:c,u=e.placeholder,p=void 0!==u&&u,f=e.keyCode,m=void 0===f?-1:f,v="string"==typeof t?t.trim():t,g=this._store.items,_=n||v,y=s||-1,E=a>=0?this._store.getGroupById(a):null,b=g?g.length+1:1;this.config.prependValue&&(v=this.config.prependValue+v.toString()),this.config.appendValue&&(v+=this.config.appendValue.toString()),this._store.dispatch((0,l.addItem)({value:v,label:_,id:b,choiceId:y,groupId:a,customProperties:h,placeholder:p,keyCode:m})),this._isSelectOneElement&&this.removeActiveItems(b),this.passedElement.triggerEvent(d.EVENTS.addItem,{id:b,value:v,label:_,customProperties:h,groupValue:E&&E.value?E.value:null,keyCode:m})},e.prototype._removeItem=function(e){var t=e.id,i=e.value,n=e.label,r=e.customProperties,s=e.choiceId,o=e.groupId,a=o&&o>=0?this._store.getGroupById(o):null;t&&s&&(this._store.dispatch((0,l.removeItem)(t,s)),this.passedElement.triggerEvent(d.EVENTS.removeItem,{id:t,value:i,label:n,customProperties:r,groupValue:a&&a.value?a.value:null}))},e.prototype._addChoice=function(e){var t=e.value,i=e.label,n=void 0===i?null:i,r=e.isSelected,s=void 0!==r&&r,o=e.isDisabled,c=void 0!==o&&o,l=e.groupId,h=void 0===l?-1:l,u=e.customProperties,d=void 0===u?{}:u,p=e.placeholder,f=void 0!==p&&p,m=e.keyCode,v=void 0===m?-1:m;if(null!=t){var g=this._store.choices,_=n||t,y=g?g.length+1:1,E="".concat(this._baseId,"-").concat(this._idNames.itemChoice,"-").concat(y);this._store.dispatch((0,a.addChoice)({id:y,groupId:h,elementId:E,value:t,label:_,disabled:c,customProperties:d,placeholder:f,keyCode:v})),s&&this._addItem({value:t,label:_,choiceId:y,customProperties:d,placeholder:f,keyCode:v})}},e.prototype._addGroup=function(e){var t=this,i=e.group,n=e.id,r=e.valueKey,s=void 0===r?"value":r,o=e.labelKey,a=void 0===o?"label":o,l=(0,f.isType)("Object",i)?i.choices:Array.from(i.getElementsByTagName("OPTION")),h=n||Math.floor((new Date).valueOf()*Math.random()),u=!!i.disabled&&i.disabled;l?(this._store.dispatch((0,c.addGroup)({value:i.label,id:h,active:!0,disabled:u})),l.forEach((function(e){var i=e.disabled||e.parentNode&&e.parentNode.disabled;t._addChoice({value:e[s],label:(0,f.isType)("Object",e)?e[a]:e.innerHTML,isSelected:e.selected,isDisabled:i,groupId:h,customProperties:e.customProperties,placeholder:e.placeholder})}))):this._store.dispatch((0,c.addGroup)({value:i.label,id:i.id,active:!1,disabled:i.disabled}))},e.prototype._getTemplate=function(e){for(var t,i=[],r=1;r<arguments.length;r++)i[r-1]=arguments[r];return(t=this._templates[e]).call.apply(t,n([this,this.config],i,!1))},e.prototype._createTemplates=function(){var e=this.config.callbackOnCreateTemplates,t={};e&&"function"==typeof e&&(t=e.call(this,f.strToEl)),this._templates=(0,s.default)(g.default,t)},e.prototype._createElements=function(){this.containerOuter=new u.Container({element:this._getTemplate("containerOuter",this._direction,this._isSelectElement,this._isSelectOneElement,this.config.searchEnabled,this.passedElement.element.type,this.config.labelId),classNames:this.config.classNames,type:this.passedElement.element.type,position:this.config.position}),this.containerInner=new u.Container({element:this._getTemplate("containerInner"),classNames:this.config.classNames,type:this.passedElement.element.type,position:this.config.position}),this.input=new u.Input({element:this._getTemplate("input",this._placeholderValue),classNames:this.config.classNames,type:this.passedElement.element.type,preventPaste:!this.config.paste}),this.choiceList=new u.List({element:this._getTemplate("choiceList",this._isSelectOneElement)}),this.itemList=new u.List({element:this._getTemplate("itemList",this._isSelectOneElement)}),this.dropdown=new u.Dropdown({element:this._getTemplate("dropdown"),classNames:this.config.classNames,type:this.passedElement.element.type})},e.prototype._createStructure=function(){this.passedElement.conceal(),this.containerInner.wrap(this.passedElement.element),this.containerOuter.wrap(this.containerInner.element),this._isSelectOneElement?this.input.placeholder=this.config.searchPlaceholderValue||"":this._placeholderValue&&(this.input.placeholder=this._placeholderValue,this.input.setWidth()),this.containerOuter.element.appendChild(this.containerInner.element),this.containerOuter.element.appendChild(this.dropdown.element),this.containerInner.element.appendChild(this.itemList.element),this._isTextElement||this.dropdown.element.appendChild(this.choiceList.element),this._isSelectOneElement?this.config.searchEnabled&&this.dropdown.element.insertBefore(this.input.element,this.dropdown.element.firstChild):this.containerInner.element.appendChild(this.input.element),this._isSelectElement&&(this._highlightPosition=0,this._isSearching=!1,this._startLoading(),this._presetGroups.length?this._addPredefinedGroups(this._presetGroups):this._addPredefinedChoices(this._presetChoices),this._stopLoading()),this._isTextElement&&this._addPredefinedItems(this._presetItems)},e.prototype._addPredefinedGroups=function(e){var t=this,i=this.passedElement.placeholderOption;i&&i.parentNode&&"SELECT"===i.parentNode.tagName&&this._addChoice({value:i.value,label:i.innerHTML,isSelected:i.selected,isDisabled:i.disabled,placeholder:!0}),e.forEach((function(e){return t._addGroup({group:e,id:e.id||null})}))},e.prototype._addPredefinedChoices=function(e){var t=this;this.config.shouldSort&&e.sort(this.config.sorter);var i=e.some((function(e){return e.selected})),n=e.findIndex((function(e){return void 0===e.disabled||!e.disabled}));e.forEach((function(e,r){var s=e.value,o=void 0===s?"":s,a=e.label,c=e.customProperties,l=e.placeholder;if(t._isSelectElement)if(e.choices)t._addGroup({group:e,id:e.id||null});else{var h=!(!t._isSelectOneElement||i||r!==n)||e.selected,u=e.disabled;t._addChoice({value:o,label:a,isSelected:!!h,isDisabled:!!u,placeholder:!!l,customProperties:c})}else t._addChoice({value:o,label:a,isSelected:!!e.selected,isDisabled:!!e.disabled,placeholder:!!e.placeholder,customProperties:c})}))},e.prototype._addPredefinedItems=function(e){var t=this;e.forEach((function(e){"object"==typeof e&&e.value&&t._addItem({value:e.value,label:e.label,choiceId:e.id,customProperties:e.customProperties,placeholder:e.placeholder}),"string"==typeof e&&t._addItem({value:e})}))},e.prototype._setChoiceOrItem=function(e){var t=this;({object:function(){e.value&&(t._isTextElement?t._addItem({value:e.value,label:e.label,choiceId:e.id,customProperties:e.customProperties,placeholder:e.placeholder}):t._addChoice({value:e.value,label:e.label,isSelected:!0,isDisabled:!1,customProperties:e.customProperties,placeholder:e.placeholder}))},string:function(){t._isTextElement?t._addItem({value:e}):t._addChoice({value:e,label:e,isSelected:!0,isDisabled:!1})}})[(0,f.getType)(e).toLowerCase()]()},e.prototype._findAndSelectChoiceByValue=function(e){var t=this,i=this._store.choices.find((function(i){return t.config.valueComparer(i.value,e)}));i&&!i.selected&&this._addItem({value:i.value,label:i.label,choiceId:i.id,groupId:i.groupId,customProperties:i.customProperties,placeholder:i.placeholder,keyCode:i.keyCode})},e.prototype._generatePlaceholderValue=function(){if(this._isSelectElement&&this.passedElement.placeholderOption){var e=this.passedElement.placeholderOption;return e?e.text:null}var t=this.config,i=t.placeholder,n=t.placeholderValue,r=this.passedElement.element.dataset;if(i){if(n)return n;if(r.placeholder)return r.placeholder}return null},e}();t.default=E},613:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0});var n=i(799),r=i(883),s=function(){function e(e){var t=e.element,i=e.type,n=e.classNames,r=e.position;this.element=t,this.classNames=n,this.type=i,this.position=r,this.isOpen=!1,this.isFlipped=!1,this.isFocussed=!1,this.isDisabled=!1,this.isLoading=!1,this._onFocus=this._onFocus.bind(this),this._onBlur=this._onBlur.bind(this)}return e.prototype.addEventListeners=function(){this.element.addEventListener("focus",this._onFocus),this.element.addEventListener("blur",this._onBlur)},e.prototype.removeEventListeners=function(){this.element.removeEventListener("focus",this._onFocus),this.element.removeEventListener("blur",this._onBlur)},e.prototype.shouldFlip=function(e){if("number"!=typeof e)return!1;var t=!1;return"auto"===this.position?t=!window.matchMedia("(min-height: ".concat(e+1,"px)")).matches:"top"===this.position&&(t=!0),t},e.prototype.setActiveDescendant=function(e){this.element.setAttribute("aria-activedescendant",e)},e.prototype.removeActiveDescendant=function(){this.element.removeAttribute("aria-activedescendant")},e.prototype.open=function(e){this.element.classList.add(this.classNames.openState),this.element.setAttribute("aria-expanded","true"),this.isOpen=!0,this.shouldFlip(e)&&(this.element.classList.add(this.classNames.flippedState),this.isFlipped=!0)},e.prototype.close=function(){this.element.classList.remove(this.classNames.openState),this.element.setAttribute("aria-expanded","false"),this.removeActiveDescendant(),this.isOpen=!1,this.isFlipped&&(this.element.classList.remove(this.classNames.flippedState),this.isFlipped=!1)},e.prototype.focus=function(){this.isFocussed||this.element.focus()},e.prototype.addFocusState=function(){this.element.classList.add(this.classNames.focusState)},e.prototype.removeFocusState=function(){this.element.classList.remove(this.classNames.focusState)},e.prototype.enable=function(){this.element.classList.remove(this.classNames.disabledState),this.element.removeAttribute("aria-disabled"),this.type===r.SELECT_ONE_TYPE&&this.element.setAttribute("tabindex","0"),this.isDisabled=!1},e.prototype.disable=function(){this.element.classList.add(this.classNames.disabledState),this.element.setAttribute("aria-disabled","true"),this.type===r.SELECT_ONE_TYPE&&this.element.setAttribute("tabindex","-1"),this.isDisabled=!0},e.prototype.wrap=function(e){(0,n.wrap)(e,this.element)},e.prototype.unwrap=function(e){this.element.parentNode&&(this.element.parentNode.insertBefore(e,this.element),this.element.parentNode.removeChild(this.element))},e.prototype.addLoadingState=function(){this.element.classList.add(this.classNames.loadingState),this.element.setAttribute("aria-busy","true"),this.isLoading=!0},e.prototype.removeLoadingState=function(){this.element.classList.remove(this.classNames.loadingState),this.element.removeAttribute("aria-busy"),this.isLoading=!1},e.prototype._onFocus=function(){this.isFocussed=!0},e.prototype._onBlur=function(){this.isFocussed=!1},e}();t.default=s},217:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function e(e){var t=e.element,i=e.type,n=e.classNames;this.element=t,this.classNames=n,this.type=i,this.isActive=!1}return Object.defineProperty(e.prototype,"distanceFromTopWindow",{get:function(){return this.element.getBoundingClientRect().bottom},enumerable:!1,configurable:!0}),e.prototype.getChild=function(e){return this.element.querySelector(e)},e.prototype.show=function(){return this.element.classList.add(this.classNames.activeState),this.element.setAttribute("aria-expanded","true"),this.isActive=!0,this},e.prototype.hide=function(){return this.element.classList.remove(this.classNames.activeState),this.element.setAttribute("aria-expanded","false"),this.isActive=!1,this},e}();t.default=i},520:function(e,t,i){var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.WrappedSelect=t.WrappedInput=t.List=t.Input=t.Container=t.Dropdown=void 0;var r=n(i(217));t.Dropdown=r.default;var s=n(i(613));t.Container=s.default;var o=n(i(11));t.Input=o.default;var a=n(i(624));t.List=a.default;var c=n(i(541));t.WrappedInput=c.default;var l=n(i(982));t.WrappedSelect=l.default},11:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0});var n=i(799),r=i(883),s=function(){function e(e){var t=e.element,i=e.type,n=e.classNames,r=e.preventPaste;this.element=t,this.type=i,this.classNames=n,this.preventPaste=r,this.isFocussed=this.element.isEqualNode(document.activeElement),this.isDisabled=t.disabled,this._onPaste=this._onPaste.bind(this),this._onInput=this._onInput.bind(this),this._onFocus=this._onFocus.bind(this),this._onBlur=this._onBlur.bind(this)}return Object.defineProperty(e.prototype,"placeholder",{set:function(e){this.element.placeholder=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"value",{get:function(){return(0,n.sanitise)(this.element.value)},set:function(e){this.element.value=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"rawValue",{get:function(){return this.element.value},enumerable:!1,configurable:!0}),e.prototype.addEventListeners=function(){this.element.addEventListener("paste",this._onPaste),this.element.addEventListener("input",this._onInput,{passive:!0}),this.element.addEventListener("focus",this._onFocus,{passive:!0}),this.element.addEventListener("blur",this._onBlur,{passive:!0})},e.prototype.removeEventListeners=function(){this.element.removeEventListener("input",this._onInput),this.element.removeEventListener("paste",this._onPaste),this.element.removeEventListener("focus",this._onFocus),this.element.removeEventListener("blur",this._onBlur)},e.prototype.enable=function(){this.element.removeAttribute("disabled"),this.isDisabled=!1},e.prototype.disable=function(){this.element.setAttribute("disabled",""),this.isDisabled=!0},e.prototype.focus=function(){this.isFocussed||this.element.focus()},e.prototype.blur=function(){this.isFocussed&&this.element.blur()},e.prototype.clear=function(e){return void 0===e&&(e=!0),this.element.value&&(this.element.value=""),e&&this.setWidth(),this},e.prototype.setWidth=function(){var e=this.element,t=e.style,i=e.value,n=e.placeholder;t.minWidth="".concat(n.length+1,"ch"),t.width="".concat(i.length+1,"ch")},e.prototype.setActiveDescendant=function(e){this.element.setAttribute("aria-activedescendant",e)},e.prototype.removeActiveDescendant=function(){this.element.removeAttribute("aria-activedescendant")},e.prototype._onInput=function(){this.type!==r.SELECT_ONE_TYPE&&this.setWidth()},e.prototype._onPaste=function(e){this.preventPaste&&e.preventDefault()},e.prototype._onFocus=function(){this.isFocussed=!0},e.prototype._onBlur=function(){this.isFocussed=!1},e}();t.default=s},624:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0});var n=i(883),r=function(){function e(e){var t=e.element;this.element=t,this.scrollPos=this.element.scrollTop,this.height=this.element.offsetHeight}return e.prototype.clear=function(){this.element.innerHTML=""},e.prototype.append=function(e){this.element.appendChild(e)},e.prototype.getChild=function(e){return this.element.querySelector(e)},e.prototype.hasChildren=function(){return this.element.hasChildNodes()},e.prototype.scrollToTop=function(){this.element.scrollTop=0},e.prototype.scrollToChildElement=function(e,t){var i=this;if(e){var n=this.element.offsetHeight,r=this.element.scrollTop+n,s=e.offsetHeight,o=e.offsetTop+s,a=t>0?this.element.scrollTop+o-r:e.offsetTop;requestAnimationFrame((function(){i._animateScroll(a,t)}))}},e.prototype._scrollDown=function(e,t,i){var n=(i-e)/t,r=n>1?n:1;this.element.scrollTop=e+r},e.prototype._scrollUp=function(e,t,i){var n=(e-i)/t,r=n>1?n:1;this.element.scrollTop=e-r},e.prototype._animateScroll=function(e,t){var i=this,r=n.SCROLLING_SPEED,s=this.element.scrollTop,o=!1;t>0?(this._scrollDown(s,r,e),s<e&&(o=!0)):(this._scrollUp(s,r,e),s>e&&(o=!0)),o&&requestAnimationFrame((function(){i._animateScroll(e,t)}))},e}();t.default=r},730:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0});var n=i(799),r=function(){function e(e){var t=e.element,i=e.classNames;if(this.element=t,this.classNames=i,!(t instanceof HTMLInputElement||t instanceof HTMLSelectElement))throw new TypeError("Invalid element passed");this.isDisabled=!1}return Object.defineProperty(e.prototype,"isActive",{get:function(){return"active"===this.element.dataset.choice},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"dir",{get:function(){return this.element.dir},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"value",{get:function(){return this.element.value},set:function(e){this.element.value=e},enumerable:!1,configurable:!0}),e.prototype.conceal=function(){this.element.classList.add(this.classNames.input),this.element.hidden=!0,this.element.tabIndex=-1;var e=this.element.getAttribute("style");e&&this.element.setAttribute("data-choice-orig-style",e),this.element.setAttribute("data-choice","active")},e.prototype.reveal=function(){this.element.classList.remove(this.classNames.input),this.element.hidden=!1,this.element.removeAttribute("tabindex");var e=this.element.getAttribute("data-choice-orig-style");e?(this.element.removeAttribute("data-choice-orig-style"),this.element.setAttribute("style",e)):this.element.removeAttribute("style"),this.element.removeAttribute("data-choice"),this.element.value=this.element.value},e.prototype.enable=function(){this.element.removeAttribute("disabled"),this.element.disabled=!1,this.isDisabled=!1},e.prototype.disable=function(){this.element.setAttribute("disabled",""),this.element.disabled=!0,this.isDisabled=!0},e.prototype.triggerEvent=function(e,t){(0,n.dispatchEvent)(this.element,e,t)},e}();t.default=r},541:function(e,t,i){var n,r=this&&this.__extends||(n=function(e,t){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])},n(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function i(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(i.prototype=t.prototype,new i)}),s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=function(e){function t(t){var i=t.element,n=t.classNames,r=t.delimiter,s=e.call(this,{element:i,classNames:n})||this;return s.delimiter=r,s}return r(t,e),Object.defineProperty(t.prototype,"value",{get:function(){return this.element.value},set:function(e){this.element.setAttribute("value",e),this.element.value=e},enumerable:!1,configurable:!0}),t}(s(i(730)).default);t.default=o},982:function(e,t,i){var n,r=this&&this.__extends||(n=function(e,t){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])},n(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function i(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(i.prototype=t.prototype,new i)}),s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=function(e){function t(t){var i=t.element,n=t.classNames,r=t.template,s=e.call(this,{element:i,classNames:n})||this;return s.template=r,s}return r(t,e),Object.defineProperty(t.prototype,"placeholderOption",{get:function(){return this.element.querySelector('option[value=""]')||this.element.querySelector("option[placeholder]")},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"optionGroups",{get:function(){return Array.from(this.element.getElementsByTagName("OPTGROUP"))},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"options",{get:function(){return Array.from(this.element.options)},set:function(e){var t=this,i=document.createDocumentFragment();e.forEach((function(e){return n=e,r=t.template(n),void i.appendChild(r);var n,r})),this.appendDocFragment(i)},enumerable:!1,configurable:!0}),t.prototype.appendDocFragment=function(e){this.element.innerHTML="",this.element.appendChild(e)},t}(s(i(730)).default);t.default=o},883:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.SCROLLING_SPEED=t.SELECT_MULTIPLE_TYPE=t.SELECT_ONE_TYPE=t.TEXT_TYPE=t.KEY_CODES=t.ACTION_TYPES=t.EVENTS=void 0,t.EVENTS={showDropdown:"showDropdown",hideDropdown:"hideDropdown",change:"change",choice:"choice",search:"search",addItem:"addItem",removeItem:"removeItem",highlightItem:"highlightItem",highlightChoice:"highlightChoice",unhighlightItem:"unhighlightItem"},t.ACTION_TYPES={ADD_CHOICE:"ADD_CHOICE",FILTER_CHOICES:"FILTER_CHOICES",ACTIVATE_CHOICES:"ACTIVATE_CHOICES",CLEAR_CHOICES:"CLEAR_CHOICES",ADD_GROUP:"ADD_GROUP",ADD_ITEM:"ADD_ITEM",REMOVE_ITEM:"REMOVE_ITEM",HIGHLIGHT_ITEM:"HIGHLIGHT_ITEM",CLEAR_ALL:"CLEAR_ALL",RESET_TO:"RESET_TO",SET_IS_LOADING:"SET_IS_LOADING"},t.KEY_CODES={BACK_KEY:46,DELETE_KEY:8,ENTER_KEY:13,A_KEY:65,ESC_KEY:27,UP_KEY:38,DOWN_KEY:40,PAGE_UP_KEY:33,PAGE_DOWN_KEY:34},t.TEXT_TYPE="text",t.SELECT_ONE_TYPE="select-one",t.SELECT_MULTIPLE_TYPE="select-multiple",t.SCROLLING_SPEED=4},789:function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.DEFAULT_CONFIG=t.DEFAULT_CLASSNAMES=void 0;var n=i(799);t.DEFAULT_CLASSNAMES={containerOuter:"choices",containerInner:"choices__inner",input:"choices__input",inputCloned:"choices__input--cloned",list:"choices__list",listItems:"choices__list--multiple",listSingle:"choices__list--single",listDropdown:"choices__list--dropdown",item:"choices__item",itemSelectable:"choices__item--selectable",itemDisabled:"choices__item--disabled",itemChoice:"choices__item--choice",placeholder:"choices__placeholder",group:"choices__group",groupHeading:"choices__heading",button:"choices__button",activeState:"is-active",focusState:"is-focused",openState:"is-open",disabledState:"is-disabled",highlightedState:"is-highlighted",selectedState:"is-selected",flippedState:"is-flipped",loadingState:"is-loading",noResults:"has-no-results",noChoices:"has-no-choices"},t.DEFAULT_CONFIG={items:[],choices:[],silent:!1,renderChoiceLimit:-1,maxItemCount:-1,addItems:!0,addItemFilter:null,removeItems:!0,removeItemButton:!1,editItems:!1,allowHTML:!0,duplicateItemsAllowed:!0,delimiter:",",paste:!0,searchEnabled:!0,searchChoices:!0,searchFloor:1,searchResultLimit:4,searchFields:["label","value"],position:"auto",resetScrollPosition:!0,shouldSort:!0,shouldSortItems:!1,sorter:n.sortByAlpha,placeholder:!0,placeholderValue:null,searchPlaceholderValue:null,prependValue:null,appendValue:null,renderSelectedChoices:"auto",loadingText:"Loading...",noResultsText:"No results found",noChoicesText:"No choices to choose from",itemSelectText:"Press to select",uniqueItemText:"Only unique values can be added",customAddItemText:"Only values matching specific conditions can be added",addItemText:function(e){return'Press Enter to add <b>"'.concat((0,n.sanitise)(e),'"</b>')},maxItemText:function(e){return"Only ".concat(e," values can be added")},valueComparer:function(e,t){return e===t},fuseOptions:{includeScore:!0},labelId:"",callbackOnInit:null,callbackOnCreateTemplates:null,classNames:t.DEFAULT_CLASSNAMES}},18:function(e,t){Object.defineProperty(t,"__esModule",{value:!0})},978:function(e,t){Object.defineProperty(t,"__esModule",{value:!0})},948:function(e,t){Object.defineProperty(t,"__esModule",{value:!0})},359:function(e,t){Object.defineProperty(t,"__esModule",{value:!0})},285:function(e,t){Object.defineProperty(t,"__esModule",{value:!0})},533:function(e,t){Object.defineProperty(t,"__esModule",{value:!0})},187:function(e,t,i){var n=this&&this.__createBinding||(Object.create?function(e,t,i,n){void 0===n&&(n=i);var r=Object.getOwnPropertyDescriptor(t,i);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[i]}}),Object.defineProperty(e,n,r)}:function(e,t,i,n){void 0===n&&(n=i),e[n]=t[i]}),r=this&&this.__exportStar||function(e,t){for(var i in e)"default"===i||Object.prototype.hasOwnProperty.call(t,i)||n(t,e,i)};Object.defineProperty(t,"__esModule",{value:!0}),r(i(18),t),r(i(978),t),r(i(948),t),r(i(359),t),r(i(285),t),r(i(533),t),r(i(287),t),r(i(132),t),r(i(837),t),r(i(598),t),r(i(369),t),r(i(37),t),r(i(47),t),r(i(923),t),r(i(876),t)},287:function(e,t){Object.defineProperty(t,"__esModule",{value:!0})},132:function(e,t){Object.defineProperty(t,"__esModule",{value:!0})},837:function(e,t){Object.defineProperty(t,"__esModule",{value:!0})},598:function(e,t){Object.defineProperty(t,"__esModule",{value:!0})},37:function(e,t){Object.defineProperty(t,"__esModule",{value:!0})},369:function(e,t){Object.defineProperty(t,"__esModule",{value:!0})},47:function(e,t){Object.defineProperty(t,"__esModule",{value:!0})},923:function(e,t){Object.defineProperty(t,"__esModule",{value:!0})},876:function(e,t){Object.defineProperty(t,"__esModule",{value:!0})},799:function(e,t){var i;Object.defineProperty(t,"__esModule",{value:!0}),t.parseCustomProperties=t.diff=t.cloneObject=t.existsInArray=t.dispatchEvent=t.sortByScore=t.sortByAlpha=t.strToEl=t.sanitise=t.isScrolledIntoView=t.getAdjacentEl=t.wrap=t.isType=t.getType=t.generateId=t.generateChars=t.getRandomNumber=void 0,t.getRandomNumber=function(e,t){return Math.floor(Math.random()*(t-e)+e)},t.generateChars=function(e){return Array.from({length:e},(function(){return(0,t.getRandomNumber)(0,36).toString(36)})).join("")},t.generateId=function(e,i){var n=e.id||e.name&&"".concat(e.name,"-").concat((0,t.generateChars)(2))||(0,t.generateChars)(4);return n=n.replace(/(:|\.|\[|\]|,)/g,""),n="".concat(i,"-").concat(n)},t.getType=function(e){return Object.prototype.toString.call(e).slice(8,-1)},t.isType=function(e,i){return null!=i&&(0,t.getType)(i)===e},t.wrap=function(e,t){return void 0===t&&(t=document.createElement("div")),e.parentNode&&(e.nextSibling?e.parentNode.insertBefore(t,e.nextSibling):e.parentNode.appendChild(t)),t.appendChild(e)},t.getAdjacentEl=function(e,t,i){void 0===i&&(i=1);for(var n="".concat(i>0?"next":"previous","ElementSibling"),r=e[n];r;){if(r.matches(t))return r;r=r[n]}return r},t.isScrolledIntoView=function(e,t,i){return void 0===i&&(i=1),!!e&&(i>0?t.scrollTop+t.offsetHeight>=e.offsetTop+e.offsetHeight:e.offsetTop>=t.scrollTop)},t.sanitise=function(e){return"string"!=typeof e?e:e.replace(/&/g,"&amp;").replace(/>/g,"&gt;").replace(/</g,"&lt;").replace(/"/g,"&quot;")},t.strToEl=(i=document.createElement("div"),function(e){var t=e.trim();i.innerHTML=t;for(var n=i.children[0];i.firstChild;)i.removeChild(i.firstChild);return n}),t.sortByAlpha=function(e,t){var i=e.value,n=e.label,r=void 0===n?i:n,s=t.value,o=t.label,a=void 0===o?s:o;return r.localeCompare(a,[],{sensitivity:"base",ignorePunctuation:!0,numeric:!0})},t.sortByScore=function(e,t){var i=e.score,n=void 0===i?0:i,r=t.score;return n-(void 0===r?0:r)},t.dispatchEvent=function(e,t,i){void 0===i&&(i=null);var n=new CustomEvent(t,{detail:i,bubbles:!0,cancelable:!0});return e.dispatchEvent(n)},t.existsInArray=function(e,t,i){return void 0===i&&(i="value"),e.some((function(e){return"string"==typeof t?e[i]===t.trim():e[i]===t}))},t.cloneObject=function(e){return JSON.parse(JSON.stringify(e))},t.diff=function(e,t){var i=Object.keys(e).sort(),n=Object.keys(t).sort();return i.filter((function(e){return n.indexOf(e)<0}))},t.parseCustomProperties=function(e){if(void 0!==e)try{return JSON.parse(e)}catch(t){return e}return{}}},273:function(e,t){var i=this&&this.__spreadArray||function(e,t,i){if(i||2===arguments.length)for(var n,r=0,s=t.length;r<s;r++)!n&&r in t||(n||(n=Array.prototype.slice.call(t,0,r)),n[r]=t[r]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.defaultState=void 0,t.defaultState=[],t.default=function(e,n){switch(void 0===e&&(e=t.defaultState),void 0===n&&(n={}),n.type){case"ADD_CHOICE":var r=n,s={id:r.id,elementId:r.elementId,groupId:r.groupId,value:r.value,label:r.label||r.value,disabled:r.disabled||!1,selected:!1,active:!0,score:9999,customProperties:r.customProperties,placeholder:r.placeholder||!1};return i(i([],e,!0),[s],!1);case"ADD_ITEM":var o=n;return o.choiceId>-1?e.map((function(e){var t=e;return t.id===parseInt("".concat(o.choiceId),10)&&(t.selected=!0),t})):e;case"REMOVE_ITEM":var a=n;return a.choiceId&&a.choiceId>-1?e.map((function(e){var t=e;return t.id===parseInt("".concat(a.choiceId),10)&&(t.selected=!1),t})):e;case"FILTER_CHOICES":var c=n;return e.map((function(e){var t=e;return t.active=c.results.some((function(e){var i=e.item,n=e.score;return i.id===t.id&&(t.score=n,!0)})),t}));case"ACTIVATE_CHOICES":var l=n;return e.map((function(e){var t=e;return t.active=l.active,t}));case"CLEAR_CHOICES":return t.defaultState;default:return e}}},871:function(e,t){var i=this&&this.__spreadArray||function(e,t,i){if(i||2===arguments.length)for(var n,r=0,s=t.length;r<s;r++)!n&&r in t||(n||(n=Array.prototype.slice.call(t,0,r)),n[r]=t[r]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.defaultState=void 0,t.defaultState=[],t.default=function(e,n){switch(void 0===e&&(e=t.defaultState),void 0===n&&(n={}),n.type){case"ADD_GROUP":var r=n;return i(i([],e,!0),[{id:r.id,value:r.value,active:r.active,disabled:r.disabled}],!1);case"CLEAR_CHOICES":return[];default:return e}}},655:function(e,t,i){var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.defaultState=void 0;var r=i(791),s=n(i(52)),o=n(i(871)),a=n(i(273)),c=n(i(502)),l=i(799);t.defaultState={groups:[],items:[],choices:[],loading:!1};var h=(0,r.combineReducers)({items:s.default,groups:o.default,choices:a.default,loading:c.default});t.default=function(e,i){var n=e;if("CLEAR_ALL"===i.type)n=t.defaultState;else if("RESET_TO"===i.type)return(0,l.cloneObject)(i.state);return h(n,i)}},52:function(e,t){var i=this&&this.__spreadArray||function(e,t,i){if(i||2===arguments.length)for(var n,r=0,s=t.length;r<s;r++)!n&&r in t||(n||(n=Array.prototype.slice.call(t,0,r)),n[r]=t[r]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.defaultState=void 0,t.defaultState=[],t.default=function(e,n){switch(void 0===e&&(e=t.defaultState),void 0===n&&(n={}),n.type){case"ADD_ITEM":var r=n;return i(i([],e,!0),[{id:r.id,choiceId:r.choiceId,groupId:r.groupId,value:r.value,label:r.label,active:!0,highlighted:!1,customProperties:r.customProperties,placeholder:r.placeholder||!1,keyCode:null}],!1).map((function(e){var t=e;return t.highlighted=!1,t}));case"REMOVE_ITEM":return e.map((function(e){var t=e;return t.id===n.id&&(t.active=!1),t}));case"HIGHLIGHT_ITEM":var s=n;return e.map((function(e){var t=e;return t.id===s.id&&(t.highlighted=s.highlighted),t}));default:return e}}},502:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.defaultState=void 0,t.defaultState=!1,t.default=function(e,i){return void 0===e&&(e=t.defaultState),void 0===i&&(i={}),"SET_IS_LOADING"===i.type?i.isLoading:e}},744:function(e,t,i){var n=this&&this.__spreadArray||function(e,t,i){if(i||2===arguments.length)for(var n,r=0,s=t.length;r<s;r++)!n&&r in t||(n||(n=Array.prototype.slice.call(t,0,r)),n[r]=t[r]);return e.concat(n||Array.prototype.slice.call(t))},r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var s=i(791),o=r(i(655)),a=function(){function e(){this._store=(0,s.createStore)(o.default,window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__())}return e.prototype.subscribe=function(e){this._store.subscribe(e)},e.prototype.dispatch=function(e){this._store.dispatch(e)},Object.defineProperty(e.prototype,"state",{get:function(){return this._store.getState()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"items",{get:function(){return this.state.items},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"activeItems",{get:function(){return this.items.filter((function(e){return!0===e.active}))},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"highlightedActiveItems",{get:function(){return this.items.filter((function(e){return e.active&&e.highlighted}))},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"choices",{get:function(){return this.state.choices},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"activeChoices",{get:function(){return this.choices.filter((function(e){return!0===e.active}))},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"selectableChoices",{get:function(){return this.choices.filter((function(e){return!0!==e.disabled}))},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"searchableChoices",{get:function(){return this.selectableChoices.filter((function(e){return!0!==e.placeholder}))},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"placeholderChoice",{get:function(){return n([],this.choices,!0).reverse().find((function(e){return!0===e.placeholder}))},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"groups",{get:function(){return this.state.groups},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"activeGroups",{get:function(){var e=this.groups,t=this.choices;return e.filter((function(e){var i=!0===e.active&&!1===e.disabled,n=t.some((function(e){return!0===e.active&&!1===e.disabled}));return i&&n}),[])},enumerable:!1,configurable:!0}),e.prototype.isLoading=function(){return this.state.loading},e.prototype.getChoiceById=function(e){return this.activeChoices.find((function(t){return t.id===parseInt(e,10)}))},e.prototype.getGroupById=function(e){return this.groups.find((function(t){return t.id===e}))},e}();t.default=a},686:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var i={containerOuter:function(e,t,i,n,r,s,o){var a=e.classNames.containerOuter,c=Object.assign(document.createElement("div"),{className:a});return c.dataset.type=s,t&&(c.dir=t),n&&(c.tabIndex=0),i&&(c.setAttribute("role",r?"combobox":"listbox"),r&&c.setAttribute("aria-autocomplete","list")),c.setAttribute("aria-haspopup","true"),c.setAttribute("aria-expanded","false"),o&&c.setAttribute("aria-labelledby",o),c},containerInner:function(e){var t=e.classNames.containerInner;return Object.assign(document.createElement("div"),{className:t})},itemList:function(e,t){var i=e.classNames,n=i.list,r=i.listSingle,s=i.listItems;return Object.assign(document.createElement("div"),{className:"".concat(n," ").concat(t?r:s)})},placeholder:function(e,t){var i,n=e.allowHTML,r=e.classNames.placeholder;return Object.assign(document.createElement("div"),((i={className:r})[n?"innerHTML":"innerText"]=t,i))},item:function(e,t,i){var n,r,s=e.allowHTML,o=e.classNames,a=o.item,c=o.button,l=o.highlightedState,h=o.itemSelectable,u=o.placeholder,d=t.id,p=t.value,f=t.label,m=t.customProperties,v=t.active,g=t.disabled,_=t.highlighted,y=t.placeholder,E=Object.assign(document.createElement("div"),((n={className:a})[s?"innerHTML":"innerText"]=f,n));if(Object.assign(E.dataset,{item:"",id:d,value:p,customProperties:m}),v&&E.setAttribute("aria-selected","true"),g&&E.setAttribute("aria-disabled","true"),y&&E.classList.add(u),E.classList.add(_?l:h),i){g&&E.classList.remove(h),E.dataset.deletable="";var b="Remove item",S=Object.assign(document.createElement("button"),((r={type:"button",className:c})[s?"innerHTML":"innerText"]=b,r));S.setAttribute("aria-label","".concat(b,": '").concat(p,"'")),S.dataset.button="",E.appendChild(S)}return E},choiceList:function(e,t){var i=e.classNames.list,n=Object.assign(document.createElement("div"),{className:i});return t||n.setAttribute("aria-multiselectable","true"),n.setAttribute("role","listbox"),n},choiceGroup:function(e,t){var i,n=e.allowHTML,r=e.classNames,s=r.group,o=r.groupHeading,a=r.itemDisabled,c=t.id,l=t.value,h=t.disabled,u=Object.assign(document.createElement("div"),{className:"".concat(s," ").concat(h?a:"")});return u.setAttribute("role","group"),Object.assign(u.dataset,{group:"",id:c,value:l}),h&&u.setAttribute("aria-disabled","true"),u.appendChild(Object.assign(document.createElement("div"),((i={className:o})[n?"innerHTML":"innerText"]=l,i))),u},choice:function(e,t,i){var n,r=e.allowHTML,s=e.classNames,o=s.item,a=s.itemChoice,c=s.itemSelectable,l=s.selectedState,h=s.itemDisabled,u=s.placeholder,d=t.id,p=t.value,f=t.label,m=t.groupId,v=t.elementId,g=t.disabled,_=t.selected,y=t.placeholder,E=Object.assign(document.createElement("div"),((n={id:v})[r?"innerHTML":"innerText"]=f,n.className="".concat(o," ").concat(a),n));return _&&E.classList.add(l),y&&E.classList.add(u),E.setAttribute("role",m&&m>0?"treeitem":"option"),Object.assign(E.dataset,{choice:"",id:d,value:p,selectText:i}),g?(E.classList.add(h),E.dataset.choiceDisabled="",E.setAttribute("aria-disabled","true")):(E.classList.add(c),E.dataset.choiceSelectable=""),E},input:function(e,t){var i=e.classNames,n=i.input,r=i.inputCloned,s=Object.assign(document.createElement("input"),{type:"search",name:"search_terms",className:"".concat(n," ").concat(r),autocomplete:"off",autocapitalize:"off",spellcheck:!1});return s.setAttribute("role","textbox"),s.setAttribute("aria-autocomplete","list"),s.setAttribute("aria-label",t),s},dropdown:function(e){var t=e.classNames,i=t.list,n=t.listDropdown,r=document.createElement("div");return r.classList.add(i,n),r.setAttribute("aria-expanded","false"),r},notice:function(e,t,i){var n,r=e.allowHTML,s=e.classNames,o=s.item,a=s.itemChoice,c=s.noResults,l=s.noChoices;void 0===i&&(i="");var h=[o,a];return"no-choices"===i?h.push(l):"no-results"===i&&h.push(c),Object.assign(document.createElement("div"),((n={})[r?"innerHTML":"innerText"]=t,n.className=h.join(" "),n))},option:function(e){var t=e.label,i=e.value,n=e.customProperties,r=e.active,s=e.disabled,o=new Option(t,i,!1,r);return n&&(o.dataset.customProperties="".concat(n)),o.disabled=!!s,o}};t.default=i},996:function(e){var t=function(e){return function(e){return!!e&&"object"==typeof e}(e)&&!function(e){var t=Object.prototype.toString.call(e);return"[object RegExp]"===t||"[object Date]"===t||function(e){return e.$$typeof===i}(e)}(e)},i="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function n(e,t){return!1!==t.clone&&t.isMergeableObject(e)?c((i=e,Array.isArray(i)?[]:{}),e,t):e;var i}function r(e,t,i){return e.concat(t).map((function(e){return n(e,i)}))}function s(e){return Object.keys(e).concat(function(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter((function(t){return e.propertyIsEnumerable(t)})):[]}(e))}function o(e,t){try{return t in e}catch(e){return!1}}function a(e,t,i){var r={};return i.isMergeableObject(e)&&s(e).forEach((function(t){r[t]=n(e[t],i)})),s(t).forEach((function(s){(function(e,t){return o(e,t)&&!(Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))})(e,s)||(o(e,s)&&i.isMergeableObject(t[s])?r[s]=function(e,t){if(!t.customMerge)return c;var i=t.customMerge(e);return"function"==typeof i?i:c}(s,i)(e[s],t[s],i):r[s]=n(t[s],i))})),r}function c(e,i,s){(s=s||{}).arrayMerge=s.arrayMerge||r,s.isMergeableObject=s.isMergeableObject||t,s.cloneUnlessOtherwiseSpecified=n;var o=Array.isArray(i);return o===Array.isArray(e)?o?s.arrayMerge(e,i,s):a(e,i,s):n(i,s)}c.all=function(e,t){if(!Array.isArray(e))throw new Error("first argument should be an array");return e.reduce((function(e,i){return c(e,i,t)}),{})};var l=c;e.exports=l},221:function(e,t,i){
/**
                     * Fuse.js v6.6.2 - Lightweight fuzzy-search (http://fusejs.io)
                     *
                     * Copyright (c) 2022 Kiro Risk (http://kiro.me)
                     * All Rights Reserved. Apache Software License 2.0
                     *
                     * http://www.apache.org/licenses/LICENSE-2.0
                     */
function n(e){return Array.isArray?Array.isArray(e):"[object Array]"===d(e)}i.r(t),i.d(t,{default:function(){return X}});const r=1/0;function s(e){return null==e?"":function(e){if("string"==typeof e)return e;let t=e+"";return"0"==t&&1/e==-r?"-0":t}(e)}function o(e){return"string"==typeof e}function a(e){return"number"==typeof e}function c(e){return!0===e||!1===e||function(e){return l(e)&&null!==e}(e)&&"[object Boolean]"==d(e)}function l(e){return"object"==typeof e}function h(e){return null!=e}function u(e){return!e.trim().length}function d(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}const p=e=>`Missing ${e} property in key`,f=e=>`Property 'weight' in key '${e}' must be a positive integer`,m=Object.prototype.hasOwnProperty;class v{constructor(e){this._keys=[],this._keyMap={};let t=0;e.forEach((e=>{let i=g(e);t+=i.weight,this._keys.push(i),this._keyMap[i.id]=i,t+=i.weight})),this._keys.forEach((e=>{e.weight/=t}))}get(e){return this._keyMap[e]}keys(){return this._keys}toJSON(){return JSON.stringify(this._keys)}}function g(e){let t=null,i=null,r=null,s=1,a=null;if(o(e)||n(e))r=e,t=_(e),i=y(e);else{if(!m.call(e,"name"))throw new Error(p("name"));const n=e.name;if(r=n,m.call(e,"weight")&&(s=e.weight,s<=0))throw new Error(f(n));t=_(n),i=y(n),a=e.getFn}return{path:t,id:i,weight:s,src:r,getFn:a}}function _(e){return n(e)?e:e.split(".")}function y(e){return n(e)?e.join("."):e}var E={isCaseSensitive:!1,includeScore:!1,keys:[],shouldSort:!0,sortFn:(e,t)=>e.score===t.score?e.idx<t.idx?-1:1:e.score<t.score?-1:1,includeMatches:!1,findAllMatches:!1,minMatchCharLength:1,location:0,threshold:.6,distance:100,useExtendedSearch:!1,getFn:function(e,t){let i=[],r=!1;const l=(e,t,u)=>{if(h(e))if(t[u]){const d=e[t[u]];if(!h(d))return;if(u===t.length-1&&(o(d)||a(d)||c(d)))i.push(s(d));else if(n(d)){r=!0;for(let e=0,i=d.length;e<i;e+=1)l(d[e],t,u+1)}else t.length&&l(d,t,u+1)}else i.push(e)};return l(e,o(t)?t.split("."):t,0),r?i:i[0]},ignoreLocation:!1,ignoreFieldNorm:!1,fieldNormWeight:1};const b=/[^ ]+/g;class S{constructor({getFn:e=E.getFn,fieldNormWeight:t=E.fieldNormWeight}={}){this.norm=function(e=1,t=3){const i=new Map,n=Math.pow(10,t);return{get(t){const r=t.match(b).length;if(i.has(r))return i.get(r);const s=1/Math.pow(r,.5*e),o=parseFloat(Math.round(s*n)/n);return i.set(r,o),o},clear(){i.clear()}}}(t,3),this.getFn=e,this.isCreated=!1,this.setIndexRecords()}setSources(e=[]){this.docs=e}setIndexRecords(e=[]){this.records=e}setKeys(e=[]){this.keys=e,this._keysMap={},e.forEach(((e,t)=>{this._keysMap[e.id]=t}))}create(){!this.isCreated&&this.docs.length&&(this.isCreated=!0,o(this.docs[0])?this.docs.forEach(((e,t)=>{this._addString(e,t)})):this.docs.forEach(((e,t)=>{this._addObject(e,t)})),this.norm.clear())}add(e){const t=this.size();o(e)?this._addString(e,t):this._addObject(e,t)}removeAt(e){this.records.splice(e,1);for(let t=e,i=this.size();t<i;t+=1)this.records[t].i-=1}getValueForItemAtKeyId(e,t){return e[this._keysMap[t]]}size(){return this.records.length}_addString(e,t){if(!h(e)||u(e))return;let i={v:e,i:t,n:this.norm.get(e)};this.records.push(i)}_addObject(e,t){let i={i:t,$:{}};this.keys.forEach(((t,r)=>{let s=t.getFn?t.getFn(e):this.getFn(e,t.path);if(h(s))if(n(s)){let e=[];const t=[{nestedArrIndex:-1,value:s}];for(;t.length;){const{nestedArrIndex:i,value:r}=t.pop();if(h(r))if(o(r)&&!u(r)){let t={v:r,i,n:this.norm.get(r)};e.push(t)}else n(r)&&r.forEach(((e,i)=>{t.push({nestedArrIndex:i,value:e})}))}i.$[r]=e}else if(o(s)&&!u(s)){let e={v:s,n:this.norm.get(s)};i.$[r]=e}})),this.records.push(i)}toJSON(){return{keys:this.keys,records:this.records}}}function O(e,t,{getFn:i=E.getFn,fieldNormWeight:n=E.fieldNormWeight}={}){const r=new S({getFn:i,fieldNormWeight:n});return r.setKeys(e.map(g)),r.setSources(t),r.create(),r}function I(e,{errors:t=0,currentLocation:i=0,expectedLocation:n=0,distance:r=E.distance,ignoreLocation:s=E.ignoreLocation}={}){const o=t/e.length;if(s)return o;const a=Math.abs(n-i);return r?o+a/r:a?1:o}const C=32;function T(e,t,i,{location:n=E.location,distance:r=E.distance,threshold:s=E.threshold,findAllMatches:o=E.findAllMatches,minMatchCharLength:a=E.minMatchCharLength,includeMatches:c=E.includeMatches,ignoreLocation:l=E.ignoreLocation}={}){if(t.length>C)throw new Error("Pattern length exceeds max of 32.");const h=t.length,u=e.length,d=Math.max(0,Math.min(n,u));let p=s,f=d;const m=a>1||c,v=m?Array(u):[];let g;for(;(g=e.indexOf(t,f))>-1;){let e=I(t,{currentLocation:g,expectedLocation:d,distance:r,ignoreLocation:l});if(p=Math.min(e,p),f=g+h,m){let e=0;for(;e<h;)v[g+e]=1,e+=1}}f=-1;let _=[],y=1,b=h+u;const S=1<<h-1;for(let n=0;n<h;n+=1){let s=0,a=b;for(;s<a;)I(t,{errors:n,currentLocation:d+a,expectedLocation:d,distance:r,ignoreLocation:l})<=p?s=a:b=a,a=Math.floor((b-s)/2+s);b=a;let c=Math.max(1,d-a+1),g=o?u:Math.min(d+a,u)+h,E=Array(g+2);E[g+1]=(1<<n)-1;for(let s=g;s>=c;s-=1){let o=s-1,a=i[e.charAt(o)];if(m&&(v[o]=+!!a),E[s]=(E[s+1]<<1|1)&a,n&&(E[s]|=(_[s+1]|_[s])<<1|1|_[s+1]),E[s]&S&&(y=I(t,{errors:n,currentLocation:o,expectedLocation:d,distance:r,ignoreLocation:l}),y<=p)){if(p=y,f=o,f<=d)break;c=Math.max(1,2*d-f)}}if(I(t,{errors:n+1,currentLocation:d,expectedLocation:d,distance:r,ignoreLocation:l})>p)break;_=E}const O={isMatch:f>=0,score:Math.max(.001,y)};if(m){const e=function(e=[],t=E.minMatchCharLength){let i=[],n=-1,r=-1,s=0;for(let o=e.length;s<o;s+=1){let o=e[s];o&&-1===n?n=s:o||-1===n||(r=s-1,r-n+1>=t&&i.push([n,r]),n=-1)}return e[s-1]&&s-n>=t&&i.push([n,s-1]),i}(v,a);e.length?c&&(O.indices=e):O.isMatch=!1}return O}function L(e){let t={};for(let i=0,n=e.length;i<n;i+=1){const r=e.charAt(i);t[r]=(t[r]||0)|1<<n-i-1}return t}class w{constructor(e,{location:t=E.location,threshold:i=E.threshold,distance:n=E.distance,includeMatches:r=E.includeMatches,findAllMatches:s=E.findAllMatches,minMatchCharLength:o=E.minMatchCharLength,isCaseSensitive:a=E.isCaseSensitive,ignoreLocation:c=E.ignoreLocation}={}){if(this.options={location:t,threshold:i,distance:n,includeMatches:r,findAllMatches:s,minMatchCharLength:o,isCaseSensitive:a,ignoreLocation:c},this.pattern=a?e:e.toLowerCase(),this.chunks=[],!this.pattern.length)return;const l=(e,t)=>{this.chunks.push({pattern:e,alphabet:L(e),startIndex:t})},h=this.pattern.length;if(h>C){let e=0;const t=h%C,i=h-t;for(;e<i;)l(this.pattern.substr(e,C),e),e+=C;if(t){const e=h-C;l(this.pattern.substr(e),e)}}else l(this.pattern,0)}searchIn(e){const{isCaseSensitive:t,includeMatches:i}=this.options;if(t||(e=e.toLowerCase()),this.pattern===e){let t={isMatch:!0,score:0};return i&&(t.indices=[[0,e.length-1]]),t}const{location:n,distance:r,threshold:s,findAllMatches:o,minMatchCharLength:a,ignoreLocation:c}=this.options;let l=[],h=0,u=!1;this.chunks.forEach((({pattern:t,alphabet:d,startIndex:p})=>{const{isMatch:f,score:m,indices:v}=T(e,t,d,{location:n+p,distance:r,threshold:s,findAllMatches:o,minMatchCharLength:a,includeMatches:i,ignoreLocation:c});f&&(u=!0),h+=m,f&&v&&(l=[...l,...v])}));let d={isMatch:u,score:u?h/this.chunks.length:1};return u&&i&&(d.indices=l),d}}class A{constructor(e){this.pattern=e}static isMultiMatch(e){return M(e,this.multiRegex)}static isSingleMatch(e){return M(e,this.singleRegex)}search(){}}function M(e,t){const i=e.match(t);return i?i[1]:null}class P extends A{constructor(e,{location:t=E.location,threshold:i=E.threshold,distance:n=E.distance,includeMatches:r=E.includeMatches,findAllMatches:s=E.findAllMatches,minMatchCharLength:o=E.minMatchCharLength,isCaseSensitive:a=E.isCaseSensitive,ignoreLocation:c=E.ignoreLocation}={}){super(e),this._bitapSearch=new w(e,{location:t,threshold:i,distance:n,includeMatches:r,findAllMatches:s,minMatchCharLength:o,isCaseSensitive:a,ignoreLocation:c})}static get type(){return"fuzzy"}static get multiRegex(){return/^"(.*)"$/}static get singleRegex(){return/^(.*)$/}search(e){return this._bitapSearch.searchIn(e)}}class x extends A{constructor(e){super(e)}static get type(){return"include"}static get multiRegex(){return/^'"(.*)"$/}static get singleRegex(){return/^'(.*)$/}search(e){let t,i=0;const n=[],r=this.pattern.length;for(;(t=e.indexOf(this.pattern,i))>-1;)i=t+r,n.push([t,i-1]);const s=!!n.length;return{isMatch:s,score:s?0:1,indices:n}}}const N=[class extends A{constructor(e){super(e)}static get type(){return"exact"}static get multiRegex(){return/^="(.*)"$/}static get singleRegex(){return/^=(.*)$/}search(e){const t=e===this.pattern;return{isMatch:t,score:t?0:1,indices:[0,this.pattern.length-1]}}},x,class extends A{constructor(e){super(e)}static get type(){return"prefix-exact"}static get multiRegex(){return/^\^"(.*)"$/}static get singleRegex(){return/^\^(.*)$/}search(e){const t=e.startsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,this.pattern.length-1]}}},class extends A{constructor(e){super(e)}static get type(){return"inverse-prefix-exact"}static get multiRegex(){return/^!\^"(.*)"$/}static get singleRegex(){return/^!\^(.*)$/}search(e){const t=!e.startsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,e.length-1]}}},class extends A{constructor(e){super(e)}static get type(){return"inverse-suffix-exact"}static get multiRegex(){return/^!"(.*)"\$$/}static get singleRegex(){return/^!(.*)\$$/}search(e){const t=!e.endsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,e.length-1]}}},class extends A{constructor(e){super(e)}static get type(){return"suffix-exact"}static get multiRegex(){return/^"(.*)"\$$/}static get singleRegex(){return/^(.*)\$$/}search(e){const t=e.endsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[e.length-this.pattern.length,e.length-1]}}},class extends A{constructor(e){super(e)}static get type(){return"inverse-exact"}static get multiRegex(){return/^!"(.*)"$/}static get singleRegex(){return/^!(.*)$/}search(e){const t=-1===e.indexOf(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,e.length-1]}}},P],D=N.length,j=/ +(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)/,F=new Set([P.type,x.type]);class k{constructor(e,{isCaseSensitive:t=E.isCaseSensitive,includeMatches:i=E.includeMatches,minMatchCharLength:n=E.minMatchCharLength,ignoreLocation:r=E.ignoreLocation,findAllMatches:s=E.findAllMatches,location:o=E.location,threshold:a=E.threshold,distance:c=E.distance}={}){this.query=null,this.options={isCaseSensitive:t,includeMatches:i,minMatchCharLength:n,findAllMatches:s,ignoreLocation:r,location:o,threshold:a,distance:c},this.pattern=t?e:e.toLowerCase(),this.query=function(e,t={}){return e.split("|").map((e=>{let i=e.trim().split(j).filter((e=>e&&!!e.trim())),n=[];for(let e=0,r=i.length;e<r;e+=1){const r=i[e];let s=!1,o=-1;for(;!s&&++o<D;){const e=N[o];let i=e.isMultiMatch(r);i&&(n.push(new e(i,t)),s=!0)}if(!s)for(o=-1;++o<D;){const e=N[o];let i=e.isSingleMatch(r);if(i){n.push(new e(i,t));break}}}return n}))}(this.pattern,this.options)}static condition(e,t){return t.useExtendedSearch}searchIn(e){const t=this.query;if(!t)return{isMatch:!1,score:1};const{includeMatches:i,isCaseSensitive:n}=this.options;e=n?e:e.toLowerCase();let r=0,s=[],o=0;for(let n=0,a=t.length;n<a;n+=1){const a=t[n];s.length=0,r=0;for(let t=0,n=a.length;t<n;t+=1){const n=a[t],{isMatch:c,indices:l,score:h}=n.search(e);if(!c){o=0,r=0,s.length=0;break}if(r+=1,o+=h,i){const e=n.constructor.type;F.has(e)?s=[...s,...l]:s.push(l)}}if(r){let e={isMatch:!0,score:o/r};return i&&(e.indices=s),e}}return{isMatch:!1,score:1}}}const K=[];function R(e,t){for(let i=0,n=K.length;i<n;i+=1){let n=K[i];if(n.condition(e,t))return new n(e,t)}return new w(e,t)}const Y="$and",H="$or",V="$path",B="$val",G=e=>!(!e[Y]&&!e[H]),U=e=>({[Y]:Object.keys(e).map((t=>({[t]:e[t]})))});function W(e,t,{auto:i=!0}={}){const r=e=>{let s=Object.keys(e);const a=(e=>!!e[V])(e);if(!a&&s.length>1&&!G(e))return r(U(e));if((e=>!n(e)&&l(e)&&!G(e))(e)){const n=a?e[V]:s[0],r=a?e[B]:e[n];if(!o(r))throw new Error((e=>`Invalid value for key ${e}`)(n));const c={keyId:y(n),pattern:r};return i&&(c.searcher=R(r,t)),c}let c={children:[],operator:s[0]};return s.forEach((t=>{const i=e[t];n(i)&&i.forEach((e=>{c.children.push(r(e))}))})),c};return G(e)||(e=U(e)),r(e)}function $(e,t){const i=e.matches;t.matches=[],h(i)&&i.forEach((e=>{if(!h(e.indices)||!e.indices.length)return;const{indices:i,value:n}=e;let r={indices:i,value:n};e.key&&(r.key=e.key.src),e.idx>-1&&(r.refIndex=e.idx),t.matches.push(r)}))}function q(e,t){t.score=e.score}class X{constructor(e,t={},i){this.options={...E,...t},this.options.useExtendedSearch,this._keyStore=new v(this.options.keys),this.setCollection(e,i)}setCollection(e,t){if(this._docs=e,t&&!(t instanceof S))throw new Error("Incorrect 'index' type");this._myIndex=t||O(this.options.keys,this._docs,{getFn:this.options.getFn,fieldNormWeight:this.options.fieldNormWeight})}add(e){h(e)&&(this._docs.push(e),this._myIndex.add(e))}remove(e=()=>!1){const t=[];for(let i=0,n=this._docs.length;i<n;i+=1){const r=this._docs[i];e(r,i)&&(this.removeAt(i),i-=1,n-=1,t.push(r))}return t}removeAt(e){this._docs.splice(e,1),this._myIndex.removeAt(e)}getIndex(){return this._myIndex}search(e,{limit:t=-1}={}){const{includeMatches:i,includeScore:n,shouldSort:r,sortFn:s,ignoreFieldNorm:c}=this.options;let l=o(e)?o(this._docs[0])?this._searchStringList(e):this._searchObjectList(e):this._searchLogical(e);return function(e,{ignoreFieldNorm:t=E.ignoreFieldNorm}){e.forEach((e=>{let i=1;e.matches.forEach((({key:e,norm:n,score:r})=>{const s=e?e.weight:null;i*=Math.pow(0===r&&s?Number.EPSILON:r,(s||1)*(t?1:n))})),e.score=i}))}(l,{ignoreFieldNorm:c}),r&&l.sort(s),a(t)&&t>-1&&(l=l.slice(0,t)),function(e,t,{includeMatches:i=E.includeMatches,includeScore:n=E.includeScore}={}){const r=[];return i&&r.push($),n&&r.push(q),e.map((e=>{const{idx:i}=e,n={item:t[i],refIndex:i};return r.length&&r.forEach((t=>{t(e,n)})),n}))}(l,this._docs,{includeMatches:i,includeScore:n})}_searchStringList(e){const t=R(e,this.options),{records:i}=this._myIndex,n=[];return i.forEach((({v:e,i,n:r})=>{if(!h(e))return;const{isMatch:s,score:o,indices:a}=t.searchIn(e);s&&n.push({item:e,idx:i,matches:[{score:o,value:e,norm:r,indices:a}]})})),n}_searchLogical(e){const t=W(e,this.options),i=(e,t,n)=>{if(!e.children){const{keyId:i,searcher:r}=e,s=this._findMatches({key:this._keyStore.get(i),value:this._myIndex.getValueForItemAtKeyId(t,i),searcher:r});return s&&s.length?[{idx:n,item:t,matches:s}]:[]}const r=[];for(let s=0,o=e.children.length;s<o;s+=1){const o=e.children[s],a=i(o,t,n);if(a.length)r.push(...a);else if(e.operator===Y)return[]}return r},n=this._myIndex.records,r={},s=[];return n.forEach((({$:e,i:n})=>{if(h(e)){let o=i(t,e,n);o.length&&(r[n]||(r[n]={idx:n,item:e,matches:[]},s.push(r[n])),o.forEach((({matches:e})=>{r[n].matches.push(...e)})))}})),s}_searchObjectList(e){const t=R(e,this.options),{keys:i,records:n}=this._myIndex,r=[];return n.forEach((({$:e,i:n})=>{if(!h(e))return;let s=[];i.forEach(((i,n)=>{s.push(...this._findMatches({key:i,value:e[n],searcher:t}))})),s.length&&r.push({idx:n,item:e,matches:s})})),r}_findMatches({key:e,value:t,searcher:i}){if(!h(t))return[];let r=[];if(n(t))t.forEach((({v:t,i:n,n:s})=>{if(!h(t))return;const{isMatch:o,score:a,indices:c}=i.searchIn(t);o&&r.push({score:a,key:e,value:t,idx:n,norm:s,indices:c})}));else{const{v:n,n:s}=t,{isMatch:o,score:a,indices:c}=i.searchIn(n);o&&r.push({score:a,key:e,value:n,norm:s,indices:c})}return r}}X.version="6.6.2",X.createIndex=O,X.parseIndex=function(e,{getFn:t=E.getFn,fieldNormWeight:i=E.fieldNormWeight}={}){const{keys:n,records:r}=e,s=new S({getFn:t,fieldNormWeight:i});return s.setKeys(n),s.setIndexRecords(r),s},X.config=E,X.parseQuery=W,function(...e){K.push(...e)}(k)},791:function(e,t,i){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function r(e){var t=function(e,t){if("object"!==n(e)||null===e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var r=i.call(e,t||"default");if("object"!==n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===n(t)?t:String(t)}function s(e,t,i){return(t=r(t))in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function o(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,n)}return i}function a(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?o(Object(i),!0).forEach((function(t){s(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):o(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}function c(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}i.r(t),i.d(t,{__DO_NOT_USE__ActionTypes:function(){return u},applyMiddleware:function(){return y},bindActionCreators:function(){return g},combineReducers:function(){return m},compose:function(){return _},createStore:function(){return p},legacy_createStore:function(){return f}});var l="function"==typeof Symbol&&Symbol.observable||"@@observable",h=function(){return Math.random().toString(36).substring(7).split("").join(".")},u={INIT:"@@redux/INIT"+h(),REPLACE:"@@redux/REPLACE"+h(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+h()}};function d(e){if("object"!=typeof e||null===e)return!1;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function p(e,t,i){var n;if("function"==typeof t&&"function"==typeof i||"function"==typeof i&&"function"==typeof arguments[3])throw new Error(c(0));if("function"==typeof t&&void 0===i&&(i=t,t=void 0),void 0!==i){if("function"!=typeof i)throw new Error(c(1));return i(p)(e,t)}if("function"!=typeof e)throw new Error(c(2));var r=e,s=t,o=[],a=o,h=!1;function f(){a===o&&(a=o.slice())}function m(){if(h)throw new Error(c(3));return s}function v(e){if("function"!=typeof e)throw new Error(c(4));if(h)throw new Error(c(5));var t=!0;return f(),a.push(e),function(){if(t){if(h)throw new Error(c(6));t=!1,f();var i=a.indexOf(e);a.splice(i,1),o=null}}}function g(e){if(!d(e))throw new Error(c(7));if(void 0===e.type)throw new Error(c(8));if(h)throw new Error(c(9));try{h=!0,s=r(s,e)}finally{h=!1}for(var t=o=a,i=0;i<t.length;i++)(0,t[i])();return e}return g({type:u.INIT}),(n={dispatch:g,subscribe:v,getState:m,replaceReducer:function(e){if("function"!=typeof e)throw new Error(c(10));r=e,g({type:u.REPLACE})}})[l]=function(){var e,t=v;return(e={subscribe:function(e){if("object"!=typeof e||null===e)throw new Error(c(11));function i(){e.next&&e.next(m())}return i(),{unsubscribe:t(i)}}})[l]=function(){return this},e},n}var f=p;function m(e){for(var t=Object.keys(e),i={},n=0;n<t.length;n++){var r=t[n];"function"==typeof e[r]&&(i[r]=e[r])}var s,o=Object.keys(i);try{!function(e){Object.keys(e).forEach((function(t){var i=e[t];if(void 0===i(void 0,{type:u.INIT}))throw new Error(c(12));if(void 0===i(void 0,{type:u.PROBE_UNKNOWN_ACTION()}))throw new Error(c(13))}))}(i)}catch(e){s=e}return function(e,t){if(void 0===e&&(e={}),s)throw s;for(var n=!1,r={},a=0;a<o.length;a++){var l=o[a],h=i[l],u=e[l],d=h(u,t);if(void 0===d)throw t&&t.type,new Error(c(14));r[l]=d,n=n||d!==u}return(n=n||o.length!==Object.keys(e).length)?r:e}}function v(e,t){return function(){return t(e.apply(this,arguments))}}function g(e,t){if("function"==typeof e)return v(e,t);if("object"!=typeof e||null===e)throw new Error(c(16));var i={};for(var n in e){var r=e[n];"function"==typeof r&&(i[n]=v(r,t))}return i}function _(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];return 0===t.length?function(e){return e}:1===t.length?t[0]:t.reduce((function(e,t){return function(){return e(t.apply(void 0,arguments))}}))}function y(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];return function(e){return function(){var i=e.apply(void 0,arguments),n=function(){throw new Error(c(15))},r={getState:i.getState,dispatch:function(){return n.apply(void 0,arguments)}},s=t.map((function(e){return e(r)}));return n=_.apply(void 0,s)(i.dispatch),a(a({},i),{},{dispatch:n})}}}}},t={};function i(n){var r=t[n];if(void 0!==r)return r.exports;var s=t[n]={exports:{}};return e[n].call(s.exports,s,s.exports,i),s.exports}i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,{a:t}),t},i.d=function(e,t){for(var n in t)i.o(t,n)&&!i.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n,r,s={};return n=i(373),r=i.n(n),i(187),i(883),i(789),i(686),s.default=r(),s=s.default}()},"object"==typeof i&&"object"==typeof t?t.exports=o():"function"==typeof define&&define.amd?define([],o):"object"==typeof i?i.Choices=o():s.Choices=o()},
653: function _(e,i,o,t,c){t(),o.default='.choices{position:relative;overflow:hidden;margin-bottom:24px;font-size:16px;}.choices:focus{outline:none;}.choices:last-child{margin-bottom:0;}.choices.is-open{overflow:visible;}.choices.is-disabled .choices__inner,.choices.is-disabled .choices__input{background-color:#eaeaea;cursor:not-allowed;-webkit-user-select:none;user-select:none;}.choices.is-disabled .choices__item{cursor:not-allowed;}.choices [hidden]{display:none !important;}.choices[data-type*=select-one]{cursor:pointer;}.choices[data-type*=select-one] .choices__inner{padding-bottom:7.5px;}.choices[data-type*=select-one] .choices__input{display:block;width:100%;padding:10px;border-bottom:1px solid #ddd;background-color:#fff;margin:0;}.choices[data-type*=select-one] .choices__button{background-image:url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMjEiIHZpZXdCb3g9IjAgMCAyMSAyMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjMDAwIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0yLjU5Mi4wNDRsMTguMzY0IDE4LjM2NC0yLjU0OCAyLjU0OEwuMDQ0IDIuNTkyeiIvPjxwYXRoIGQ9Ik0wIDE4LjM2NEwxOC4zNjQgMGwyLjU0OCAyLjU0OEwyLjU0OCAyMC45MTJ6Ii8+PC9nPjwvc3ZnPg==");padding:0;background-size:8px;position:absolute;top:50%;right:0;margin-top:-10px;margin-right:25px;height:20px;width:20px;border-radius:10em;opacity:0.25;}.choices[data-type*=select-one] .choices__button:hover,.choices[data-type*=select-one] .choices__button:focus{opacity:1;}.choices[data-type*=select-one] .choices__button:focus{box-shadow:0 0 0 2px #00bcd4;}.choices[data-type*=select-one] .choices__item[data-value=""] .choices__button{display:none;}.choices[data-type*=select-one]::after{content:"";height:0;width:0;border-style:solid;border-color:#333 transparent transparent transparent;border-width:5px;position:absolute;right:11.5px;top:50%;margin-top:-2.5px;pointer-events:none;}.choices[data-type*=select-one].is-open::after{border-color:transparent transparent #333 transparent;margin-top:-7.5px;}.choices[data-type*=select-one][dir=rtl]::after{left:11.5px;right:auto;}.choices[data-type*=select-one][dir=rtl] .choices__button{right:auto;left:0;margin-left:25px;margin-right:0;}.choices[data-type*=select-multiple] .choices__inner,.choices[data-type*=text] .choices__inner{cursor:text;}.choices[data-type*=select-multiple] .choices__button,.choices[data-type*=text] .choices__button{position:relative;display:inline-block;margin-top:0;margin-right:-4px;margin-bottom:0;margin-left:8px;padding-left:16px;border-left:1px solid #008fa1;background-image:url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMjEiIHZpZXdCb3g9IjAgMCAyMSAyMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjRkZGIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0yLjU5Mi4wNDRsMTguMzY0IDE4LjM2NC0yLjU0OCAyLjU0OEwuMDQ0IDIuNTkyeiIvPjxwYXRoIGQ9Ik0wIDE4LjM2NEwxOC4zNjQgMGwyLjU0OCAyLjU0OEwyLjU0OCAyMC45MTJ6Ii8+PC9nPjwvc3ZnPg==");background-size:8px;width:8px;line-height:1;opacity:0.75;border-radius:0;}.choices[data-type*=select-multiple] .choices__button:hover,.choices[data-type*=select-multiple] .choices__button:focus,.choices[data-type*=text] .choices__button:hover,.choices[data-type*=text] .choices__button:focus{opacity:1;}.choices__inner{display:inline-block;vertical-align:top;width:100%;background-color:#f9f9f9;padding:7.5px 7.5px 3.75px;border:1px solid #ddd;border-radius:2.5px;font-size:14px;min-height:44px;overflow:hidden;}.is-focused .choices__inner,.is-open .choices__inner{border-color:#b7b7b7;}.is-open .choices__inner{border-radius:2.5px 2.5px 0 0;}.is-flipped.is-open .choices__inner{border-radius:0 0 2.5px 2.5px;}.choices__list{margin:0;padding-left:0;list-style:none;}.choices__list--single{display:inline-block;padding:4px 16px 4px 4px;width:100%;}[dir=rtl] .choices__list--single{padding-right:4px;padding-left:16px;}.choices__list--single .choices__item{width:100%;}.choices__list--multiple{display:inline;}.choices__list--multiple .choices__item{display:inline-block;vertical-align:middle;border-radius:20px;padding:4px 10px;font-size:12px;font-weight:500;margin-right:3.75px;margin-bottom:3.75px;background-color:#00bcd4;border:1px solid #00a5bb;color:#fff;word-break:break-all;box-sizing:border-box;}.choices__list--multiple .choices__item[data-deletable]{padding-right:5px;}[dir=rtl] .choices__list--multiple .choices__item{margin-right:0;margin-left:3.75px;}.choices__list--multiple .choices__item.is-highlighted{background-color:#00a5bb;border:1px solid #008fa1;}.is-disabled .choices__list--multiple .choices__item{background-color:#aaaaaa;border:1px solid #919191;}.choices__list--dropdown,.choices__list[aria-expanded]{visibility:hidden;z-index:1;position:absolute;width:100%;background-color:#fff;border:1px solid #ddd;top:100%;margin-top:-1px;border-bottom-left-radius:2.5px;border-bottom-right-radius:2.5px;overflow:hidden;word-break:break-all;will-change:visibility;}.is-active.choices__list--dropdown,.is-active.choices__list[aria-expanded]{visibility:visible;}.is-open .choices__list--dropdown,.is-open .choices__list[aria-expanded]{border-color:#b7b7b7;}.is-flipped .choices__list--dropdown,.is-flipped .choices__list[aria-expanded]{top:auto;bottom:100%;margin-top:0;margin-bottom:-1px;border-radius:0.25rem 0.25rem 0 0;}.choices__list--dropdown .choices__list,.choices__list[aria-expanded] .choices__list{position:relative;max-height:300px;overflow:auto;-webkit-overflow-scrolling:touch;will-change:scroll-position;}.choices__list--dropdown .choices__item,.choices__list[aria-expanded] .choices__item{position:relative;padding:10px;font-size:14px;}[dir=rtl] .choices__list--dropdown .choices__item,[dir=rtl] .choices__list[aria-expanded] .choices__item{text-align:right;}@media (min-width: 640px){.choices__list--dropdown .choices__item--selectable,.choices__list[aria-expanded] .choices__item--selectable{padding-right:100px;}.choices__list--dropdown .choices__item--selectable::after,.choices__list[aria-expanded] .choices__item--selectable::after{content:attr(data-select-text);font-size:12px;opacity:0;position:absolute;right:10px;top:50%;transform:translateY(-50%);}[dir=rtl] .choices__list--dropdown .choices__item--selectable,[dir=rtl] .choices__list[aria-expanded] .choices__item--selectable{text-align:right;padding-left:100px;padding-right:10px;}[dir=rtl] .choices__list--dropdown .choices__item--selectable::after,[dir=rtl] .choices__list[aria-expanded] .choices__item--selectable::after{right:auto;left:10px;}}.choices__list--dropdown .choices__item--selectable.is-highlighted,.choices__list[aria-expanded] .choices__item--selectable.is-highlighted{background-color:#f2f2f2;}.choices__list--dropdown .choices__item--selectable.is-highlighted::after,.choices__list[aria-expanded] .choices__item--selectable.is-highlighted::after{opacity:0.5;}.choices__item{cursor:default;}.choices__item--selectable{cursor:pointer;}.choices__item--disabled{cursor:not-allowed;-webkit-user-select:none;user-select:none;opacity:0.5;}.choices__heading{font-weight:600;font-size:12px;padding:10px;border-bottom:1px solid #f7f7f7;color:gray;}.choices__button{text-indent:-9999px;-webkit-appearance:none;appearance:none;border:0;background-color:transparent;background-repeat:no-repeat;background-position:center;cursor:pointer;}.choices__button:focus{outline:none;}.choices__input{display:inline-block;vertical-align:baseline;background-color:#f9f9f9;font-size:14px;margin-bottom:5px;border:0;border-radius:0;max-width:100%;padding:4px 0 4px 2px;}.choices__input:focus{outline:0;}.choices__input::-webkit-search-decoration,.choices__input::-webkit-search-cancel-button,.choices__input::-webkit-search-results-button,.choices__input::-webkit-search-results-decoration{display:none;}.choices__input::-ms-clear,.choices__input::-ms-reveal{display:none;width:0;height:0;}[dir=rtl] .choices__input{padding-right:2px;padding-left:0;}.choices__placeholder{opacity:0.5;}.choices{width:100%;}.choices{box-sizing:border-box;}.choices *,.choices *:before,.choices *:after{box-sizing:inherit;}input[type="search"]{margin:0;}.choices__inner .choices__item.light{background-color:rgba(0, 126, 255, 0.08);border-radius:5px;border:1px solid rgba(0, 126, 255, 0.24);color:#007eff;}.choices__inner .choices__item.solid{background-color:#1f77b4;border:none;border-radius:5px;color:white;}.choices__inner .choices__item.solid .is-highlighted{background-color:#1f77b4;border:none;}.choices__input{background-color:transparent;}.choices__inner{background:transparent;border:1px solid darkgray;border-radius:5px;min-height:0;padding:calc(var(--padding-vertical) / 2) var(--padding-horizontal);}.choices__list{white-space:initial;}.choices__list--dropdown,.choices__list[aria-expanded]{z-index:var(--bokeh-top-level);}.choices__list--dropdown .choices__item--selectable,.choices__list[aria-expanded] .choices__item--selectable{padding-right:0;}.choices[data-type*=select-multiple] .choices__button.light{background-image:url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMjEiIHZpZXdCb3g9IjAgMCAyMSAyMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjMDA3ZWZmIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0yLjU5Mi4wNDRsMTguMzY0IDE4LjM2NC0yLjU0OCAyLjU0OEwuMDQ0IDIuNTkyeiIvPjxwYXRoIGQ9Ik0wIDE4LjM2NEwxOC4zNjQgMGwyLjU0OCAyLjU0OEwyLjU0OCAyMC45MTJ6Ii8+PC9nPjwvc3ZnPg==);}.choices[data-type*=select-multiple] .choices__button.solid{background-image:url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMjEiIHZpZXdCb3g9IjAgMCAyMSAyMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjZmZmZmZmIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0yLjU5Mi4wNDRsMTguMzY0IDE4LjM2NC0yLjU0OCAyLjU0OEwuMDQ0IDIuNTkyeiIvPjxwYXRoIGQ9Ik0wIDE4LjM2NEwxOC4zNjQgMGwyLjU0OCAyLjU0OEwyLjU0OCAyMC45MTJ6Ii8+PC9nPjwvc3ZnPg==);border-left:1px solid white;opacity:1;}'},
654: function _(e,t,s,i,n){var l;i();const o=e(1),r=e(63),c=e(8),h=e(614),d=o.__importStar(e(615));class p extends h.InputWidgetView{connect_signals(){super.connect_signals(),this.connect(this.model.properties.value.change,(()=>this.render_selection())),this.connect(this.model.properties.options.change,(()=>this.rerender())),this.connect(this.model.properties.name.change,(()=>this.rerender())),this.connect(this.model.properties.title.change,(()=>this.rerender())),this.connect(this.model.properties.size.change,(()=>this.rerender())),this.connect(this.model.properties.disabled.change,(()=>this.rerender()))}_render_input(){const e=this.model.options.map((e=>{let t,s;return(0,c.isString)(e)?t=s=e:[t,s]=e,(0,r.option)({value:t},s)}));return this.input_el=(0,r.select)({multiple:!0,class:d.input,name:this.model.name,disabled:this.model.disabled},e),this.input_el.addEventListener("change",(()=>this.change_input())),this.input_el}render(){super.render(),this.render_selection()}render_selection(){const e=new Set(this.model.value);for(const t of this.shadow_el.querySelectorAll("option"))t.selected=e.has(t.value);this.input_el.size=this.model.size}change_input(){const e=null!=this.shadow_el.querySelector("select:focus"),t=[];for(const e of this.shadow_el.querySelectorAll("option"))e.selected&&t.push(e.value);this.model.value=t,super.change_input(),e&&this.input_el.focus()}}s.MultiSelectView=p,p.__name__="MultiSelectView";class u extends h.InputWidget{constructor(e){super(e)}}s.MultiSelect=u,l=u,u.__name__="MultiSelect",l.prototype.default_view=p,l.define((({Int:e,Str:t,List:s,Tuple:i,Or:n})=>({value:[s(t),[]],options:[s(n(t,i(t,t))),[]],size:[e,4]})))},
655: function _(e,t,a,i,r){var s;i();const l=e(629);class o extends l.BaseDatePickerView{get flatpickr_options(){return{...super.flatpickr_options,mode:"multiple",conjunction:this.model.separator}}_on_change(e){this.model.value=e.map((e=>this._format_date(e)))}}a.MultipleDatePickerView=o,o.__name__="MultipleDatePickerView";class c extends l.BaseDatePicker{constructor(e){super(e)}}a.MultipleDatePicker=c,s=c,c.__name__="MultipleDatePicker",s.prototype.default_view=o,s.define((({Str:e,List:t})=>({value:[t(l.DateLike),[]],separator:[e,", "]})))},
656: function _(e,t,i,a,r){var s;a();const l=e(642),o=e(629);class c extends l.BaseDatetimePickerView{get flatpickr_options(){return{...super.flatpickr_options,mode:"multiple",conjunction:this.model.separator}}_on_change(e){this.model.value=e.map((e=>this._format_date(e)))}}i.MultipleDatetimePickerView=c,c.__name__="MultipleDatetimePickerView";class n extends l.BaseDatetimePicker{constructor(e){super(e)}}i.MultipleDatetimePicker=n,s=n,n.__name__="MultipleDatetimePicker",s.prototype.default_view=c,s.define((({Str:e,List:t})=>({value:[t(o.DateLike),[]],separator:[e,", "]})))},
657: function _(e,t,l,i,n){var s;i();const u=e(1),h=u.__importStar(e(246)),o=e(614),a=e(192),r=e(63),d=e(8),p=e(12),_=u.__importStar(e(615)),m=/^[-+]?\d*$/,c=/^[-+]?\d*\.?\d*(?:(?:\d|\d.)[eE][-+]?)*\d*$/;class v extends o.InputWidgetView{connect_signals(){super.connect_signals(),this.connect(this.model.properties.name.change,(()=>this.input_el.name=this.model.name??"")),this.connect(this.model.properties.value.change,(()=>{this.input_el.value=this.format_value,this.old_value=this.input_el.value})),this.connect(this.model.properties.low.change,(()=>{const{value:e,low:t,high:l}=this.model;null!=t&&null!=l&&(0,p.assert)(t<=l,"Invalid bounds, low must be inferior to high"),null!=e&&null!=t&&e<t&&(this.model.value=t)})),this.connect(this.model.properties.high.change,(()=>{const{value:e,low:t,high:l}=this.model;null!=t&&null!=l&&(0,p.assert)(l>=t,"Invalid bounds, high must be superior to low"),null!=e&&null!=l&&e>l&&(this.model.value=l)})),this.connect(this.model.properties.high.change,(()=>this.input_el.placeholder=this.model.placeholder)),this.connect(this.model.properties.disabled.change,(()=>this.input_el.disabled=this.model.disabled)),this.connect(this.model.properties.placeholder.change,(()=>this.input_el.placeholder=this.model.placeholder))}get format_value(){return null!=this.model.value?this.model.pretty(this.model.value):""}_set_input_filter(e){this.input_el.addEventListener("input",(()=>{const{selectionStart:t,selectionEnd:l}=this.input_el;if(e(this.input_el.value))this.old_value=this.input_el.value;else{const e=this.old_value.length-this.input_el.value.length;this.input_el.value=this.old_value,null!=t&&null!=l&&this.input_el.setSelectionRange(t-1,l+e)}}))}_render_input(){return this.input_el=(0,r.input)({type:"text",class:_.input,name:this.model.name,value:this.format_value,disabled:this.model.disabled,placeholder:this.model.placeholder})}render(){super.render(),this.old_value=this.format_value,this.set_input_filter(),this.input_el.addEventListener("change",(()=>this.change_input())),this.input_el.addEventListener("focusout",(()=>this.input_el.value=this.format_value))}set_input_filter(){const e="int"==this.model.mode?m:c;this._set_input_filter((t=>e.test(t)))}bound_value(e){let t=e;const{low:l,high:i}=this.model;return t=null!=l?Math.max(l,t):t,t=null!=i?Math.min(i,t):t,t}get value(){let e=""!=this.input_el.value?Number(this.input_el.value):null;return null!=e&&(e=this.bound_value(e)),e}change_input(){null==this.value?this.model.value=null:Number.isNaN(this.value)||(this.model.value=this.value)}}l.NumericInputView=v,v.__name__="NumericInputView";class g extends o.InputWidget{constructor(e){super(e)}_formatter(e,t){return(0,d.isString)(t)?h.format(e,t):t.doFormat([e],{loc:0})[0]}pretty(e){return null!=this.format?this._formatter(e,this.format):`${e}`}}l.NumericInput=g,s=g,g.__name__="NumericInput",s.prototype.default_view=v,s.define((({Float:e,Str:t,Enum:l,Ref:i,Or:n,Nullable:s})=>({value:[s(e),null],placeholder:[t,""],mode:[l("int","float"),"int"],format:[s(n(t,i(a.TickFormatter))),null],low:[s(e),null],high:[s(e),null]})))},
658: function _(e,t,s,n,i){var l;n();const r=e(1),a=e(63),o=e(659),c=e(14),h=e(23),u=e(11),_=e(10),d=e(12),p=e(614),m=r.__importStar(e(615)),v=r.__importStar(e(661)),w=r.__importStar(e(662)),g=r.__importStar(e(663)),y=r.__importStar(e(123)),f=e(22),S=(0,f.Tuple)(f.Str,(0,f.Arrayable)(f.Color));class k extends p.InputWidgetView{constructor(){super(...arguments),this._style=new a.InlineStyleSheet("","select"),this._style_menu=new a.InlineStyleSheet("","menu")}stylesheets(){return[...super.stylesheets(),v.default,w.default,y.default,this._style]}connect_signals(){super.connect_signals();const{value:e,items:t,ncols:s,swatch_width:n,swatch_height:i}=this.model.properties;this.on_change([t,n,i],(()=>this.rerender())),this.on_change(e,(()=>this._update_value())),this.on_change(s,(()=>this._update_ncols()))}_update_value(){(0,a.empty)(this._value_el);const e=this._render_value();null!=e&&this._value_el.append(e)}_update_ncols(){const{ncols:e}=this.model;this._pane.el.style.setProperty("--number-of-columns",`${e}`)}_render_item(e){const[t]=e,s=this.model.items.indexOf(e);(0,d.assert)(-1!=s);const n=(0,a.div)({class:w.swatch,id:`item_${s}`});return(0,a.div)({class:w.entry},n,(0,a.div)(t))}_render_value(){const{value:e,items:t}=this.model,s=t.find((([t])=>t==e));return null!=s?this._render_item(s):null}_render_input(){this._value_el=(0,a.div)({class:[v.value,w.entry]},this._render_value());const e=(0,a.div)({class:[v.chevron,y.tool_icon_chevron_down]}),t=(0,a.div)({class:[m.input,v.value_input]},this._value_el,e);return this.model.disabled?t.classList.add(m.disabled):t.tabIndex=0,this.input_el=t,this.input_el}render(){super.render();const{swatch_width:e,swatch_height:t}=this.model;this._style.replace(`\n      .${w.swatch} {\n        width: ${e}px;\n        height: ${"auto"==t?"auto":(0,a.px)(t)};\n      }\n    `);for(const[e,t]of(0,c.enumerate)(this.model.items)){const[,s]=e,n=s.length,i=(0,_.linspace)(0,100,n+1),l=[];for(const[e,t]of(0,c.enumerate)(s)){const[s,n]=[i[t],i[t+1]];l.push(`${(0,h.color2css)(e)} ${s}% ${n}%`)}const r=l.join(", ");this._style.append(`\n        #item_${t} {\n          background: linear-gradient(to right, ${r});\n        }\n      `)}this._style_menu.replace(this._style.css);const s=[];for(const[e,t]of(0,c.enumerate)(this.model.items)){const n=this._render_item(e),i=(0,a.div)({class:w.item,tabIndex:0},n);i.addEventListener("pointerup",(()=>{this.select(e)})),i.addEventListener("keyup",(t=>{switch(t.key){case"Enter":this.select(e);break;case"Escape":this.hide()}}));const l=e=>{const{items:n}=this.model,i=(0,u.cycle)(t+e,0,n.length-1);s[i].focus()};i.addEventListener("keydown",(e=>{const t=(()=>{switch(e.key){case"ArrowUp":return-this.model.ncols;case"ArrowDown":return+this.model.ncols;case"ArrowLeft":return-1;case"ArrowRight":return 1;default:return null}})();null!=t&&(e.preventDefault(),l(t))})),s.push(i)}this._pane=new o.DropPane(s,{target:this.group_el,prevent_hide:this.input_el,extra_stylesheets:[w.default,g.default,this._style_menu]}),this._update_ncols(),this.input_el.addEventListener("pointerup",(()=>{this.toggle()})),this.input_el.addEventListener("keyup",(e=>{switch(e.key){case"Enter":this.toggle();break;case"Escape":this.hide()}}));const n=e=>{const{items:t,value:s}=this.model,n=t.findIndex((([e])=>s==e));if(-1!=n){const s=(0,u.cycle)(n+e,0,t.length-1);this.select(t[s])}};this.input_el.addEventListener("keydown",(e=>{const t=(()=>{switch(e.key){case"ArrowUp":return-1;case"ArrowDown":return 1;default:return null}})();null!=t&&(e.preventDefault(),n(t))}))}select(e){this.hide();const[t]=e;this.model.value=t,super.change_input(),this.input_el.focus()}toggle(){this.model.disabled||this._pane.toggle()}hide(){this._pane.hide()}}s.PaletteSelectView=k,k.__name__="PaletteSelectView";class b extends p.InputWidget{constructor(e){super(e)}}s.PaletteSelect=b,l=b,b.__name__="PaletteSelect",l.prototype.default_view=k,l.define((({Int:e,Str:t,List:s,NonNegative:n,Positive:i,Or:l,Auto:r})=>({value:[t],items:[s(S)],ncols:[i(e),1],swatch_width:[n(e),100],swatch_height:[l(r,n(e)),"auto"]})))},
659: function _(e,t,s,i,n){i();const o=e(1),h=e(63),_=e(8),l=o.__importDefault(e(660)),r=o.__importDefault(e(66));class d{get is_open(){return this._open}constructor(e,t){this.el=(0,h.div)(),this._open=!1,this._on_mousedown=e=>{if(e.composedPath().includes(this.el))return;const{prevent_hide:t}=this;if(t instanceof HTMLElement){if(e.composedPath().includes(t))return}else if(null!=t&&t(e))return;this.hide()},this._on_keydown=e=>{if("Escape"===e.key)this.hide()},this._on_blur=()=>{this.hide()},this.contents=e,this.target=t.target,this.prevent_hide=t.prevent_hide,this.extra_stylesheets=t.extra_stylesheets??[],this.shadow_el=this.el.attachShadow({mode:"open"}),this.class_list=new h.ClassList(this.el.classList)}remove(){this._unlisten(),this.el.remove()}_listen(){document.addEventListener("mousedown",this._on_mousedown),document.addEventListener("keydown",this._on_keydown),window.addEventListener("blur",this._on_blur)}_unlisten(){document.removeEventListener("mousedown",this._on_mousedown),document.removeEventListener("keydown",this._on_keydown),window.removeEventListener("blur",this._on_blur)}stylesheets(){return[r.default,l.default,...this.extra_stylesheets]}empty(){(0,h.empty)(this.shadow_el),this.class_list.clear()}render(){this.empty();for(const e of this.stylesheets()){((0,_.isString)(e)?new h.InlineStyleSheet(e):e).install(this.shadow_el)}this.shadow_el.append(...this.contents)}show(){if(!this._open){this.render();(this.target.shadowRoot??this.target).appendChild(this.el),this._listen(),this._open=!0}}hide(){this._open&&(this._open=!1,this._unlisten(),this.el.remove())}toggle(){this._open?this.hide():this.show()}}s.DropPane=d,d.__name__="DropPane"},
660: function _(o,e,r,t,i){t(),r.default=":host{position:absolute;top:100%;min-width:100%;width:auto;height:auto;user-select:none;-webkit-user-select:none;z-index:var(--bokeh-top-level);cursor:pointer;font-size:var(--font-size);background-color:#fff;border:1px solid #ccc;border-radius:var(--border-radius);box-shadow:2px 4px 8px rgba(0, 0, 0, 0.175);}"},
661: function _(e,t,a,i,n){i(),a.value_input="bk-value-input",a.value="bk-value",a.chevron="bk-chevron",a.default=".bk-value-input{display:flex;flex-direction:row;flex-wrap:nowrap;align-items:center;gap:1em;cursor:pointer;}.bk-value{flex-grow:1;}.bk-chevron{width:16px;height:16px;mask-size:100% 100%;-webkit-mask-size:100% 100%;mask-position:center center;-webkit-mask-position:center center;mask-repeat:no-repeat;-webkit-mask-repeat:no-repeat;}"},
662: function _(e,t,i,o,r){o(),i.entry="bk-entry",i.swatch="bk-swatch",i.item="bk-item",i.active="bk-active",i.default=".bk-entry{display:flex;flex-direction:row;flex-wrap:nowrap;align-items:center;gap:0.5em;}.bk-swatch{width:100px;height:auto;align-self:stretch;}.bk-item{border:1px solid transparent;}.bk-item.bk-active{border-color:var(--highlight-color);}.bk-item:hover{background-color:#f9f9f9;}.bk-item:focus,.bk-item:focus-visible{outline:1px dotted var(--highlight-color);outline-offset:-1px;}.bk-item::-moz-focus-inner{border:0;}"},
663: function _(e,n,a,m,o){m(),a.default=":host{--number-of-columns:1;padding:5px;display:grid;grid-template-columns:repeat(var(--number-of-columns), 1fr);gap:0.25em;}"},
664: function _(e,a,t,r,s){var n;r();const p=e(645),i=e(63);class _ extends p.MarkupView{render(){super.render();const e=(0,i.p)({style:{margin:"0px"}});this.has_math_disabled()?e.textContent=this.model.text:e.innerHTML=this.process_tex(this.model.text),this.markup_el.appendChild(e)}}t.ParagraphView=_,_.__name__="ParagraphView";class h extends p.Markup{constructor(e){super(e)}}t.Paragraph=h,n=h,h.__name__="Paragraph",n.prototype.default_view=_},
665: function _(e,t,s,l,n){var o;l();const p=e(1),r=e(612),a=e(63),i=p.__importDefault(e(666)),_=p.__importDefault(e(123));class u extends r.TextInputView{stylesheets(){return[...super.stylesheets(),i.default,_.default]}render(){super.render(),this.input_el.type="password",this.toggle_el=(0,a.div)({class:"bk-toggle"}),this.toggle_el.addEventListener("click",(()=>{const{input_el:e,toggle_el:t}=this,s="text"==e.type;t.classList.toggle("bk-visible",!s),e.type=s?"password":"text"})),this.shadow_el.append(this.toggle_el)}}s.PasswordInputView=u,u.__name__="PasswordInputView";class d extends r.TextInput{constructor(e){super(e)}}s.PasswordInput=d,o=d,d.__name__="PasswordInput",o.prototype.default_view=u},
666: function _(e,i,o,t,g){t(),o.input="bk-input",o.toggle="bk-toggle",o.visible="bk-visible",o.default=":host{--toggle-size:14px;--toggle-padding:4px;--toggle-width:calc(var(--toggle-size) + 2*var(--toggle-padding));}.bk-input{padding-right:max();}.bk-toggle{position:absolute;right:0;top:0;width:var(--toggle-width);height:100%;padding:0 var(--toggle-padding);background-color:var(--icon-color);mask-image:var(--bokeh-icon-see-off);-webkit-mask-image:var(--bokeh-icon-see-off);mask-size:var(--toggle-size) var(--toggle-size);-webkit-mask-size:var(--toggle-size) var(--toggle-size);mask-position:center center;-webkit-mask-position:center center;mask-repeat:no-repeat;-webkit-mask-repeat:no-repeat;cursor:pointer;}.bk-toggle.bk-visible{mask-image:var(--bokeh-icon-see-on);-webkit-mask-image:var(--bokeh-icon-see-on);}"},
667: function _(e,t,r,s,n){var a;s();const o=e(645),_=e(63);class p extends o.MarkupView{render(){super.render();const e=(0,_.pre)({style:{overflow:"auto"}},this.model.text);this.markup_el.appendChild(e)}}r.PreTextView=p,p.__name__="PreTextView";class u extends o.Markup{constructor(e){super(e)}}r.PreText=u,a=u,u.__name__="PreText",a.prototype.default_view=p},
668: function _(e,t,s,i,l){var n;i();const a=e(1),r=e(669),o=e(16),d=e(63),h=e(21),_=e(22),u=e(11),c=e(245),p=a.__importStar(e(670)),m=(0,_.Enum)("determinate","indeterminate"),g=(0,_.Enum)("none","inline");class v extends r.IndicatorView{connect_signals(){super.connect_signals();const{mode:e,value:t,min:s,max:i,label:l,reversed:n,orientation:a,disabled:r,label_location:o,description:d}=this.model.properties;this.on_change([e,t,s,i,l,d],(()=>this._update_value())),this.on_change(n,(()=>this._update_reversed())),this.on_change(a,(()=>this._update_orientation())),this.on_change(r,(()=>this._update_disabled())),this.on_change(o,(()=>this._update_label_location()))}stylesheets(){return[...super.stylesheets(),p.default]}render(){super.render(),this.el.role="progress",this.label_el=(0,d.div)({class:p.label}),this.value_el=(0,d.div)({class:p.value}),this.bar_el=(0,d.div)({class:p.bar},this.value_el,this.label_el),this._update_value(),this._update_disabled(),this._update_reversed(),this._update_orientation(),this._update_label_location(),this.shadow_el.append(this.bar_el)}_update_value(){const{value:e,min:t,max:s,label:i}=this.model,l=Math.abs(s-t),n=(0,u.clamp)(e,t,s)-t,a=n/l*100,r=this.model.indeterminate||!isFinite(a);this.class_list.toggle(p.indeterminate,r),this.value_el.style.setProperty("--progress",`${r?0:a}%`);const o=(i,r,o)=>{const d=(()=>{switch(r){case"min":return t;case"max":return s;case"total":return l;case"value":return e;case"index":return n;case"percent":return a;default:return null}})();return null==d?d:null!=o?(0,c.sprintf)(o,d):d.toFixed(0)};this.label_el.textContent=null==i||r?"0%":(0,c.process_placeholders)(i,o),this.bar_el.title=(()=>{const{description:e}=this.model;return null==e||r?"":(0,c.process_placeholders)(e,o)})()}_update_disabled(){const{disabled:e}=this.model;this.class_list.toggle(p.disabled,e)}_update_reversed(){const{reversed:e}=this.model;this.class_list.toggle(p.reversed,e)}_update_orientation(){const{orientation:e}=this.model;this.class_list.toggle(p.horizontal,"horizontal"==e),this.class_list.toggle(p.vertical,"vertical"==e)}_update_label_location(){const{label:e,label_location:t}=this.model;this.label_el.classList.toggle(p.hidden,null==e||"none"==t)}}s.ProgressView=v,v.__name__="ProgressView";class b extends r.Indicator{constructor(e){super(e),this.finished=new o.Signal0(this,"finished")}get indeterminate(){return"indeterminate"==this.mode}get has_finished(){return!this.indeterminate&&this.value==this.max}update(e){if(this.indeterminate)return!1;const{value:t,min:s,max:i}=this;this.value=(0,u.clamp)(t+e,s,i);const{has_finished:l}=this;return l&&this.finished.emit(),l}increment(e=1){return this.update(e)}decrement(e=1){this.update(-e)}}s.Progress=b,n=b,b.__name__="Progress",n.prototype.default_view=v,n.define((({Bool:e,Int:t,Str:s,Nullable:i})=>({mode:[m,"determinate"],value:[t,0],min:[t,0],max:[t,100],reversed:[e,!1],orientation:[h.Orientation,"horizontal"],label:[i(s),"@{percent}%"],label_location:[g,"inline"],description:[i(s),null]})))},
669: function _(e,n,t,c,i){c();const s=e(718);class _ extends s.WidgetView{}t.IndicatorView=_,_.__name__="IndicatorView";class a extends s.Widget{constructor(e){super(e)}}t.Indicator=a,a.__name__="Indicator"},
670: function _(e,i,t,a,r){a(),t.bar="bk-bar",t.value="bk-value",t.disabled="bk-disabled",t.horizontal="bk-horizontal",t.vertical="bk-vertical",t.reversed="bk-reversed",t.label="bk-label",t.hidden="bk-hidden",t.indeterminate="bk-indeterminate",t.default=":host{--progress:0%;}.bk-bar{position:relative;display:flex;flex-wrap:nowrap;align-items:center;justify-content:center;background-color:var(--active-bg);border:1px solid var(--border-color);border-radius:4px;overflow:hidden;}.bk-value{position:absolute;background-color:var(--active-fg);}:host(.bk-disabled) .bk-bar{background-color:var(--inactive-bg);}:host(.bk-disabled) .bk-value{background-color:var(--inactive-fg);}:host(.bk-horizontal) .bk-bar{width:100%;height:max-content;min-width:3em;min-height:0.5em;}:host(.bk-horizontal) .bk-value{left:0;width:var(--progress);height:100%;}:host(.bk-vertical) .bk-bar{width:max-content;height:100%;min-width:0.5em;min-height:3em;}:host(.bk-vertical) .bk-value{bottom:0;width:100%;height:var(--progress);}:host(.bk-horizontal.bk-reversed) .bk-value{left:unset;right:0;}:host(.bk-vertical.bk-reversed) .bk-value{top:0;bottom:unset;}.bk-label{position:relative;padding:0.25em;}.bk-label.bk-hidden{display:none;}:host(.bk-vertical) .bk-label{writing-mode:vertical-rl;rotate:180deg;}:host(.bk-indeterminate){--indeterminate-size:10%;}:host(.bk-indeterminate) .bk-label{visibility:hidden;}:host(.bk-indeterminate) .bk-value{animation-duration:1.5s;animation-direction:alternate;animation-iteration-count:infinite;animation-timing-function:linear;}:host(.bk-indeterminate.bk-horizontal:not(.bk-disabled)) .bk-value{width:var(--indeterminate-size);animation-name:bk-progress-animation-horizontal;}:host(.bk-indeterminate.bk-vertical:not(.bk-disabled)) .bk-value{height:var(--indeterminate-size);animation-name:bk-progress-animation-vertical;}@keyframes bk-progress-animation-horizontal{from{left:calc(-1 * var(--indeterminate-size));}to{left:100%;}}@keyframes bk-progress-animation-vertical{from{bottom:calc(-1 * var(--indeterminate-size));}to{bottom:100%;}}"},
671: function _(t,o,e,a,i){var n;a();const u=t(1),s=t(619),c=u.__importStar(t(610));class _ extends s.ToggleButtonGroupView{change_active(t){this.model.active!==t&&(this.model.active=t)}_update_active(){const{active:t}=this.model;this._buttons.forEach(((o,e)=>{o.classList.toggle(c.active,t===e)}))}}e.RadioButtonGroupView=_,_.__name__="RadioButtonGroupView";class l extends s.ToggleButtonGroup{constructor(t){super(t)}}e.RadioButtonGroup=l,n=l,l.__name__="RadioButtonGroup",n.prototype.default_view=_,n.define((({Int:t,Nullable:o})=>({active:[o(t),null]})))},
672: function _(e,t,n,i,s){var o;i();const a=e(1),l=e(622),c=e(63),d=e(40),p=e(14),u=a.__importStar(e(615));class r extends l.ToggleInputGroupView{connect_signals(){super.connect_signals();const{active:e}=this.model.properties;this.on_change(e,(()=>{const{active:e}=this.model;for(const[t,n]of(0,p.enumerate)(this._inputs))t.checked=e==n}))}render(){super.render();const e=(0,c.div)({class:[u.input_group,this.model.inline?u.inline:null]});this.shadow_el.appendChild(e);const t=(0,d.unique_id)(),{active:n,labels:i}=this.model;this._inputs=[];for(let s=0;s<i.length;s++){const o=(0,c.input)({type:"radio",name:t,value:`${s}`});o.addEventListener("change",(()=>this.change_active(s))),this._inputs.push(o),this.model.disabled&&(o.disabled=!0),s==n&&(o.checked=!0);const a=(0,c.label)(o,(0,c.span)(i[s]));e.appendChild(a)}}change_active(e){this.model.active=e}}n.RadioGroupView=r,r.__name__="RadioGroupView";class h extends l.ToggleInputGroup{constructor(e){super(e)}}n.RadioGroup=h,o=h,h.__name__="RadioGroup",o.prototype.default_view=r,o.define((({Int:e,Nullable:t})=>({active:[t(e),null]})))},
673: function _(e,t,n,i,s){var u;i();const l=e(1),o=e(63),_=e(8),a=e(9),p=e(614),r=l.__importStar(e(615)),c=e(22),h=c.Unknown,d=c.Str,v=(0,c.List)((0,c.Or)(d,(0,c.Tuple)(h,d))),m=(0,c.Dict)(v);class g extends p.InputWidgetView{constructor(){super(...arguments),this._known_values=new Map}connect_signals(){super.connect_signals();const{value:e,options:t}=this.model.properties;this.on_change(e,(()=>{this._update_value()})),this.on_change(t,(()=>{(0,o.empty)(this.input_el),this.input_el.append(...this.options_el()),this._update_value()}))}options_el(){const{_known_values:e}=this;function t(t){return t.map((t=>{let n,i;return(0,_.isString)(t)?n=i=t:[n,i]=t,e.set(n,i),(0,o.option)({value:i},i)}))}e.clear();const{options:n}=this.model;return(0,_.isArray)(n)?t(n):(0,a.entries)(n).map((([e,n])=>(0,o.optgroup)({label:e},t(n))))}_render_input(){return this.input_el=(0,o.select)({class:r.input,name:this.model.name,disabled:this.model.disabled},this.options_el()),this.input_el.addEventListener("change",(()=>this.change_input())),this.input_el}render(){super.render(),this._update_value()}change_input(){const e=this.input_el.value,t=[...this._known_values].find((([t,n])=>e==n)),n=(()=>{if(null==t)return"";{const[e,n]=t;return e}})();this.model.value=n,super.change_input()}_update_value(){const{value:e}=this.model,t=this._known_values.get(e);void 0!==t?this.input_el.value=t:(this.input_el.removeAttribute("value"),this.input_el.selectedIndex=-1)}}n.SelectView=g,g.__name__="SelectView";class w extends p.InputWidget{constructor(e){super(e)}}n.Select=w,u=w,w.__name__="Select",u.prototype.default_view=g,u.define((()=>({value:[h,""],options:[(0,c.Or)(v,m),[]]})))},
674: function _(e,t,n,i,s){var l;i();const o=e(1),r=e(657),a=o.__importStar(e(19)),_=e(63),{min:u,max:h}=Math;class d extends r.NumericInputView{*buttons(){yield this.btn_up_el,yield this.btn_down_el}initialize(){super.initialize(),this._handles={interval:void 0,timeout:void 0},this._interval=200}connect_signals(){super.connect_signals();const e=this.model.properties;this.on_change(e.disabled,(()=>{for(const e of this.buttons())(0,_.toggle_attribute)(e,"disabled",this.model.disabled)}))}_render_input(){super._render_input(),this.btn_up_el=(0,_.button)({class:"bk-spin-btn bk-spin-btn-up"}),this.btn_down_el=(0,_.button)({class:"bk-spin-btn bk-spin-btn-down"});const{input_el:e,btn_up_el:t,btn_down_el:n}=this;return this.wrapper_el=(0,_.div)({class:"bk-spin-wrapper"},e,t,n),this.wrapper_el}render(){super.render();for(const e of this.buttons())(0,_.toggle_attribute)(e,"disabled",this.model.disabled),e.addEventListener("mousedown",(e=>this._btn_mouse_down(e))),e.addEventListener("mouseup",(()=>this._btn_mouse_up())),e.addEventListener("mouseleave",(()=>this._btn_mouse_leave()));this.input_el.addEventListener("keydown",(e=>{this._input_key_down(e)})),this.input_el.addEventListener("keyup",(()=>{this.model.value_throttled=this.model.value})),this.input_el.addEventListener("wheel",(e=>{this._input_mouse_wheel(e)})),this.input_el.addEventListener("wheel",function(e,t,n=!1){let i;return function(...s){const l=this,o=n&&void 0===i;void 0!==i&&clearTimeout(i),i=setTimeout((function(){i=void 0,n||e.apply(l,s)}),t),o&&e.apply(l,s)}}((()=>{this.model.value_throttled=this.model.value}),this.model.wheel_wait,!1))}remove(){this._stop_incrementation(),super.remove()}_start_incrementation(e){clearInterval(this._handles.interval),this._counter=0;const{step:t}=this.model,n=e=>{if(this._counter+=1,this._counter%5==0){const t=Math.floor(this._counter/5);t<10?(clearInterval(this._handles.interval),this._handles.interval=setInterval((()=>n(e)),this._interval/(t+1))):t>=10&&t<=13&&(clearInterval(this._handles.interval),this._handles.interval=setInterval((()=>n(2*e)),this._interval/10))}this.increment(e)};this._handles.interval=setInterval((()=>n(e*t)),this._interval)}_stop_incrementation(){clearTimeout(this._handles.timeout),this._handles.timeout=void 0,clearInterval(this._handles.interval),this._handles.interval=void 0,this.model.value_throttled=this.model.value}_btn_mouse_down(e){e.preventDefault();const t=e.currentTarget===this.btn_up_el?1:-1;this.increment(t*this.model.step),this.input_el.focus(),this._handles.timeout=setTimeout((()=>this._start_incrementation(t)),this._interval)}_btn_mouse_up(){this._stop_incrementation()}_btn_mouse_leave(){this._stop_incrementation()}_input_mouse_wheel(e){if(this.shadow_el.activeElement===this.input_el){e.preventDefault();const t=e.deltaY>0?-1:1;this.increment(t*this.model.step)}}_input_key_down(e){const t=(()=>{const{step:t,page_step_multiplier:n}=this.model;switch(e.key){case"ArrowUp":return t;case"ArrowDown":return-t;case"PageUp":return n*t;case"PageDown":return-n*t;default:return null}})();null!=t&&(e.preventDefault(),this.increment(t))}increment(e){const{low:t,high:n}=this.model;null==this.model.value?e>0?this.model.value=null!=t?t:null!=n?u(0,n):0:e<0&&(this.model.value=null!=n?n:null!=t?h(t,0):0):this.model.value=this.bound_value(this.model.value+e)}change_input(){super.change_input(),this.model.value_throttled=this.model.value}bound_value(e){const{low:t,high:n}=this.model;return null!=t&&e<t||null!=n&&e>n?this.model.value??0:e}}n.SpinnerView=d,d.__name__="SpinnerView";class p extends r.NumericInput{constructor(e){super(e)}}n.Spinner=p,l=p,p.__name__="Spinner",l.prototype.default_view=d,l.define((({Float:e,Nullable:t})=>({value_throttled:[t(e),a.unset,{readonly:!0}],step:[e,1],page_step_multiplier:[e,10],wheel_wait:[e,100]}))),l.override({mode:"float"})},
675: function _(e,i,t,s,l){var n;s();const _=e(1),o=e(625),a=e(118),c=e(121),d=e(63),h=_.__importStar(e(123)),r=_.__importStar(e(676));class p extends o.ToggleInputView{stylesheets(){return[...super.stylesheets(),h.default,r.default]}_intrinsic_display(){return{inner:this.model.flow_mode,outer:"flex"}}render(){super.render(),this.bar_el=(0,d.div)({class:r.bar}),this.knob_el=(0,d.div)({class:r.knob,tabIndex:0}),this.icon_el=(0,d.div)({class:r.icon}),this.body_el=(0,d.div)({class:r.body},this.bar_el,this.knob_el),this.shadow_el.append(this.label_el,this.icon_el,this.body_el),this._update_label(),this._update_active(),this._update_disabled(),this.body_el.addEventListener("click",(()=>this._toggle_active())),this.knob_el.addEventListener("keydown",(e=>{switch(e.key){case"Enter":case" ":e.preventDefault(),this._toggle_active()}}))}_apply_icon(e){if(null!=e){const i=(0,d.div)({class:r.icon});this.icon_el.replaceWith(i),this.icon_el=i,(0,c.apply_icon)(this.icon_el,e)}else(0,d.undisplay)(this.icon_el)}_update_active(){const{active:e,on_icon:i,off_icon:t}=this.model;this.el.classList.toggle(r.active,e),this._apply_icon(e?i:t)}_update_disabled(){this.el.classList.toggle(r.disabled,this.model.disabled)}}t.SwitchView=p,p.__name__="SwitchView";class u extends o.ToggleInput{constructor(e){super(e)}}t.Switch=u,n=u,u.__name__="Switch",n.prototype.default_view=p,n.define((({Nullable:e})=>({on_icon:[e(a.IconLike),null],off_icon:[e(a.IconLike),null]})))},
676: function _(i,t,e,a,r){a(),e.icon="bk-icon",e.body="bk-body",e.disabled="bk-disabled",e.bar="bk-bar",e.knob="bk-knob",e.active="bk-active",e.default=":host{--switch-width:32px;--switch-size:16px;--bar-height:10px;}:host{display:flex;flex-direction:row;flex-wrap:nowrap;gap:0.5em;}.bk-icon{position:relative;width:var(--switch-size);height:var(--switch-size);mask-size:100% 100%;-webkit-mask-size:100% 100%;mask-position:center center;-webkit-mask-position:center center;mask-repeat:no-repeat;-webkit-mask-repeat:no-repeat;}.bk-body{position:relative;width:var(--switch-width);height:var(--switch-size);cursor:pointer;}:host(.bk-disabled) .bk-body{cursor:default;}.bk-bar{position:relative;top:calc(50% - var(--bar-height)/2);height:var(--bar-height);border-radius:calc(var(--bar-height)/2);background-color:var(--inactive-bg);transition-property:background-color;}.bk-knob{position:absolute;top:0;left:0;width:var(--switch-size);height:var(--switch-size);border-radius:8px;background-color:var(--inactive-fg);transition-property:left, background-color;}:host(.bk-active) .bk-bar{background-color:var(--active-bg);}:host(.bk-active) .bk-knob{left:calc(100% - var(--switch-size));background-color:var(--active-fg);}"},
677: function _(e,t,s,n,i){var r;n();const o=e(1),l=e(613),c=e(63),p=o.__importStar(e(615));class _ extends l.TextLikeInputView{connect_signals(){super.connect_signals(),this.connect(this.model.properties.rows.change,(()=>this.input_el.rows=this.model.rows)),this.connect(this.model.properties.cols.change,(()=>this.input_el.cols=this.model.cols))}_render_input(){return this.input_el=(0,c.textarea)({class:p.input})}render(){super.render(),this.input_el.cols=this.model.cols,this.input_el.rows=this.model.rows}}s.TextAreaInputView=_,_.__name__="TextAreaInputView";class u extends l.TextLikeInput{constructor(e){super(e)}}s.TextAreaInput=u,r=u,u.__name__="TextAreaInput",r.prototype.default_view=_,r.define((({Int:e})=>({cols:[e,20],rows:[e,2]}))),r.override({max_length:500})},
678: function _(e,t,n,i,c){var s;i();const m=e(630),r=e(22),o=e(21),a=e(12);n.TimeLike=(0,r.Or)(r.Str,r.Float);class l extends m.PickerBaseView{_format_time(e){const{picker:t}=this;return t.formatDate(e,t.config.dateFormat)}connect_signals(){super.connect_signals();const{value:e,min_time:t,max_time:n,time_format:i,hour_increment:c,minute_increment:s,second_increment:m,seconds:r,clock:o}=this.model.properties;this.connect(e.change,(()=>{const{value:e}=this.model;null!=e?this.picker.setDate(e):this.picker.clear()})),this.connect(t.change,(()=>this.picker.set("minTime",this.model.min_time))),this.connect(n.change,(()=>this.picker.set("maxTime",this.model.max_time))),this.connect(i.change,(()=>this.picker.set("altFormat",this.model.time_format))),this.connect(c.change,(()=>this.picker.set("hourIncrement",this.model.hour_increment))),this.connect(s.change,(()=>this.picker.set("minuteIncrement",this.model.minute_increment))),this.connect(m.change,(()=>this._update_second_increment())),this.connect(r.change,(()=>this.picker.set("enableSeconds",this.model.seconds))),this.connect(o.change,(()=>this.picker.set("time_24hr","24h"==this.model.clock)))}get flatpickr_options(){const{value:e,min_time:t,max_time:n,time_format:i,hour_increment:c,minute_increment:s,seconds:m,clock:r}=this.model,o=super.flatpickr_options;return o.enableTime=!0,o.noCalendar=!0,o.altInput=!0,o.altFormat=i,o.dateFormat="H:i:S",o.hourIncrement=c,o.minuteIncrement=s,o.enableSeconds=m,o.time_24hr="24h"==r,null!=e&&(o.defaultDate=e),null!=t&&(o.minTime=t),null!=n&&(o.maxTime=n),o}render(){super.render(),this._update_second_increment()}_update_second_increment(){const{second_increment:e}=this.model;this.picker.secondElement?.setAttribute("step",e.toString())}_on_change(e){(0,a.assert)(e.length<=1),this.model.value=(()=>{if(0==e.length)return null;{const[t]=e;return this._format_time(t)}})()}}n.TimePickerView=l,l.__name__="TimePickerView";class h extends m.PickerBase{constructor(e){super(e)}}n.TimePicker=h,s=h,h.__name__="TimePicker",s.prototype.default_view=l,s.define((({Bool:e,Str:t,Nullable:i,Positive:c,Int:s})=>({value:[i(n.TimeLike),null],min_time:[i(n.TimeLike),null],max_time:[i(n.TimeLike),null],time_format:[t,"H:i"],hour_increment:[c(s),1],minute_increment:[c(s),1],second_increment:[c(s),1],seconds:[e,!1],clock:[o.Clock,"24h"]})))},
679: function _(e,t,i,s,c){var o;s();const a=e(1),n=e(607),l=e(53),r=a.__importStar(e(610));class _ extends n.AbstractButtonView{connect_signals(){super.connect_signals(),this.connect(this.model.properties.active.change,(()=>this._update_active()))}render(){super.render(),this._update_active()}click(){this.model.active=!this.model.active,this.model.trigger_event(new l.ButtonClick),super.click()}_update_active(){this.button_el.classList.toggle(r.active,this.model.active)}}i.ToggleView=_,_.__name__="ToggleView";class g extends n.AbstractButton{constructor(e){super(e)}}i.Toggle=g,o=g,g.__name__="Toggle",o.prototype.default_view=_,o.define((({Bool:e})=>({active:[e,!1]}))),o.override({label:"Toggle"})},
680: function _(e,i,a,l,r){l(),r("CategoricalSlider",e(681).CategoricalSlider),r("DateRangeSlider",e(686).DateRangeSlider),r("DateSlider",e(689).DateSlider),r("DatetimeRangeSlider",e(691).DatetimeRangeSlider),r("RangeSlider",e(692).RangeSlider),r("Slider",e(693).Slider)},
681: function _(e,t,r,s,i){var o;s();const a=e(682),c=e(8);class n extends a.AbstractSliderView{constructor(){super(...arguments),this.behaviour="tap"}connect_signals(){super.connect_signals();const{categories:e}=this.model.properties;this.on_change([e],(()=>this._update_slider()))}_calc_to(){const{categories:e}=this.model;return{range:{min:0,max:e.length-1},start:[this.model.value],step:1,format:{to:t=>e[t],from:t=>e.indexOf(t)}}}_calc_from([e]){const{categories:t}=this.model;return t[0|e]}pretty(e){return(0,c.isNumber)(e)?this.model.categories[e]:e}}r.CategoricalSliderView=n,n.__name__="CategoricalSliderView";class l extends a.AbstractSlider{constructor(e){super(e)}}r.CategoricalSlider=l,o=l,l.__name__="CategoricalSlider",o.prototype.default_view=n,o.define((({List:e,Str:t})=>({categories:[e(t)]})))},
682: function _(t,e,i,s,l){var o;s();const r=t(1),n=r.__importDefault(t(683)),_=r.__importStar(t(19)),h=t(63),d=t(10),a=t(23),c=t(620),u=r.__importStar(t(684)),p=u,m=r.__importDefault(t(685)),g=r.__importStar(t(615));class b extends c.OrientedControlView{constructor(){super(...arguments),this.connected=!1,this._auto_width="auto",this._auto_height="auto"}*controls(){yield this.slider_el}get _steps(){return this._noUiSlider.steps}_update_slider(){this._noUiSlider.updateOptions(this._calc_to(),!0)}connect_signals(){super.connect_signals();const{direction:t,orientation:e,tooltips:i}=this.model.properties;this.on_change([t,e,i],(()=>this.rerender()));const{bar_color:s}=this.model.properties;this.on_change(s,(()=>{this._set_bar_color()}));const{value:l,title:o,show_value:r}=this.model.properties;this.on_change([l,o,r],(()=>this._update_title())),this.on_change(l,(()=>this._update_slider()))}stylesheets(){return[...super.stylesheets(),m.default,u.default]}_update_title(){(0,h.empty)(this.title_el);const t=null==this.model.title||0==this.model.title.length&&!this.model.show_value;if(this.title_el.style.display=t?"none":"",!t){const{title:t}=this.model;if(null!=t&&t.length>0&&(this.contains_tex_string(t)?this.title_el.innerHTML=`${this.process_tex(t)}: `:this.title_el.textContent=`${t}: `),this.model.show_value){const{start:t}=this._calc_to(),e=t.map((t=>this.pretty(t))).join(" .. ");this.title_el.appendChild((0,h.span)({class:p.slider_value},e))}}}_set_bar_color(){if(!1!==this.connected&&!this.model.disabled&&null!=this.slider_el){this.slider_el.querySelector(".noUi-connect").style.backgroundColor=(0,a.color2css)(this.model.bar_color)}}render(){let t;if(super.render(),this.model.tooltips){const e={to:t=>this.pretty(t)},{start:i}=this._calc_to();t=(0,d.repeat)(e,i.length)}else t=null;if(null==this.slider_el){this.slider_el=(0,h.div)(),this._noUiSlider=n.default.create(this.slider_el,{...this._calc_to(),behaviour:this.behaviour,connect:this.connected,tooltips:t??!1,orientation:this.model.orientation,direction:this.model.direction}),this._noUiSlider.on("slide",((t,e,i)=>this._slide(i))),this._noUiSlider.on("change",((t,e,i)=>this._change(i)));const e=(e,i)=>{if(null==t||null==this.slider_el)return;this.slider_el.querySelectorAll(".noUi-handle")[e].querySelector(".noUi-tooltip").style.display=i?"block":""};this._noUiSlider.on("start",(()=>this._toggle_user_select(!1))),this._noUiSlider.on("end",(()=>this._toggle_user_select(!0))),this._noUiSlider.on("start",((t,i)=>e(i,!0))),this._noUiSlider.on("end",((t,i)=>e(i,!1)))}else this._update_slider();this._set_bar_color(),this.model.disabled?this.slider_el.setAttribute("disabled","true"):this.slider_el.removeAttribute("disabled"),this.title_el=(0,h.div)({class:p.slider_title}),this._update_title(),this.group_el=(0,h.div)({class:g.input_group},this.title_el,this.slider_el),this.shadow_el.appendChild(this.group_el),this._has_finished=!0}_toggle_user_select(t){const{style:e}=document.body,i=t?"":"none";e.userSelect=i,e.webkitUserSelect=i}_slide(t){this.model.value=this._calc_from(t)}_change(t){const e=this._calc_from(t);this.model.setv({value:e,value_throttled:e})}}i.AbstractSliderView=b,b.__name__="AbstractSliderView";class S extends c.OrientedControl{constructor(t){super(t)}}i.AbstractSlider=S,o=S,S.__name__="AbstractSlider",o.define((({Unknown:t,Bool:e,Str:i,Color:s,Enum:l,Nullable:o})=>({title:[o(i),""],show_value:[e,!0],value:[t],value_throttled:[t,_.unset,{readonly:!0}],direction:[l("ltr","rtl"),"ltr"],tooltips:[e,!0],bar_color:[s,"#e6e6e6"]}))),o.override({width:300})},
683: function _(t,e,r,n,i){var o,s;function a(t){return"object"==typeof t&&"function"==typeof t.to}function l(t){t.parentElement.removeChild(t)}function u(t){return null!=t}function c(t){t.preventDefault()}function p(t){return"number"==typeof t&&!isNaN(t)&&isFinite(t)}function f(t,e,r){r>0&&(g(t,e),setTimeout((function(){v(t,e)}),r))}function d(t){return Math.max(Math.min(t,100),0)}function h(t){return Array.isArray(t)?t:[t]}function m(t){var e=(t=String(t)).split(".");return e.length>1?e[1].length:0}function g(t,e){t.classList&&!/\s/.test(e)?t.classList.add(e):t.className+=" "+e}function v(t,e){t.classList&&!/\s/.test(e)?t.classList.remove(e):t.className=t.className.replace(new RegExp("(^|\\b)"+e.split(" ").join("|")+"(\\b|$)","gi")," ")}function b(t){var e=void 0!==window.pageXOffset,r="CSS1Compat"===(t.compatMode||"");return{x:e?window.pageXOffset:r?t.documentElement.scrollLeft:t.body.scrollLeft,y:e?window.pageYOffset:r?t.documentElement.scrollTop:t.body.scrollTop}}function S(t,e){return 100/(e-t)}function x(t,e,r){return 100*e/(t[r+1]-t[r])}function y(t,e){for(var r=1;t>=e[r];)r+=1;return r}function w(t,e,r){if(r>=t.slice(-1)[0])return 100;var n=y(r,t),i=t[n-1],o=t[n],s=e[n-1],a=e[n];return s+function(t,e){return x(t,t[0]<0?e+Math.abs(t[0]):e-t[0],0)}([i,o],r)/S(s,a)}function E(t,e,r,n){if(100===n)return n;var i=y(n,t),o=t[i-1],s=t[i];return r?n-o>(s-o)/2?s:o:e[i-1]?t[i-1]+function(t,e){return Math.round(t/e)*e}(n-t[i-1],e[i-1]):n}n(),r.create=tt,(o=r.PipsMode||(r.PipsMode={})).Range="range",o.Steps="steps",o.Positions="positions",o.Count="count",o.Values="values",(s=r.PipsType||(r.PipsType={}))[s.None=-1]="None",s[s.NoValue=0]="NoValue",s[s.LargeValue=1]="LargeValue",s[s.SmallValue=2]="SmallValue";var P=function(){function t(t,e,r){var n;this.xPct=[],this.xVal=[],this.xSteps=[],this.xNumSteps=[],this.xHighestCompleteStep=[],this.xSteps=[r||!1],this.xNumSteps=[!1],this.snap=e;var i=[];for(Object.keys(t).forEach((function(e){i.push([h(t[e]),e])})),i.sort((function(t,e){return t[0][0]-e[0][0]})),n=0;n<i.length;n++)this.handleEntryPoint(i[n][1],i[n][0]);for(this.xNumSteps=this.xSteps.slice(0),n=0;n<this.xNumSteps.length;n++)this.handleStepPoint(n,this.xNumSteps[n])}return t.prototype.getDistance=function(t){for(var e=[],r=0;r<this.xNumSteps.length-1;r++)e[r]=x(this.xVal,t,r);return e},t.prototype.getAbsoluteDistance=function(t,e,r){var n,i=0;if(t<this.xPct[this.xPct.length-1])for(;t>this.xPct[i+1];)i++;else t===this.xPct[this.xPct.length-1]&&(i=this.xPct.length-2);r||t!==this.xPct[i+1]||i++,null===e&&(e=[]);var o=1,s=e[i],a=0,l=0,u=0,c=0;for(n=r?(t-this.xPct[i])/(this.xPct[i+1]-this.xPct[i]):(this.xPct[i+1]-t)/(this.xPct[i+1]-this.xPct[i]);s>0;)a=this.xPct[i+1+c]-this.xPct[i+c],e[i+c]*o+100-100*n>100?(l=a*n,o=(s-100*n)/e[i+c],n=1):(l=e[i+c]*a/100*o,o=0),r?(u-=l,this.xPct.length+c>=1&&c--):(u+=l,this.xPct.length-c>=1&&c++),s=e[i+c]*o;return t+u},t.prototype.toStepping=function(t){return t=w(this.xVal,this.xPct,t)},t.prototype.fromStepping=function(t){return function(t,e,r){if(r>=100)return t.slice(-1)[0];var n=y(r,e),i=t[n-1],o=t[n],s=e[n-1];return function(t,e){return e*(t[1]-t[0])/100+t[0]}([i,o],(r-s)*S(s,e[n]))}(this.xVal,this.xPct,t)},t.prototype.getStep=function(t){return t=E(this.xPct,this.xSteps,this.snap,t)},t.prototype.getDefaultStep=function(t,e,r){var n=y(t,this.xPct);return(100===t||e&&t===this.xPct[n-1])&&(n=Math.max(n-1,1)),(this.xVal[n]-this.xVal[n-1])/r},t.prototype.getNearbySteps=function(t){var e=y(t,this.xPct);return{stepBefore:{startValue:this.xVal[e-2],step:this.xNumSteps[e-2],highestStep:this.xHighestCompleteStep[e-2]},thisStep:{startValue:this.xVal[e-1],step:this.xNumSteps[e-1],highestStep:this.xHighestCompleteStep[e-1]},stepAfter:{startValue:this.xVal[e],step:this.xNumSteps[e],highestStep:this.xHighestCompleteStep[e]}}},t.prototype.countStepDecimals=function(){var t=this.xNumSteps.map(m);return Math.max.apply(null,t)},t.prototype.hasNoSize=function(){return this.xVal[0]===this.xVal[this.xVal.length-1]},t.prototype.convert=function(t){return this.getStep(this.toStepping(t))},t.prototype.handleEntryPoint=function(t,e){var r;if(!p(r="min"===t?0:"max"===t?100:parseFloat(t))||!p(e[0]))throw new Error("noUiSlider: 'range' value isn't numeric.");this.xPct.push(r),this.xVal.push(e[0]);var n=Number(e[1]);r?this.xSteps.push(!isNaN(n)&&n):isNaN(n)||(this.xSteps[0]=n),this.xHighestCompleteStep.push(0)},t.prototype.handleStepPoint=function(t,e){if(e)if(this.xVal[t]!==this.xVal[t+1]){this.xSteps[t]=x([this.xVal[t],this.xVal[t+1]],e,0)/S(this.xPct[t],this.xPct[t+1]);var r=(this.xVal[t+1]-this.xVal[t])/this.xNumSteps[t],n=Math.ceil(Number(r.toFixed(3))-1),i=this.xVal[t]+this.xNumSteps[t]*n;this.xHighestCompleteStep[t]=i}else this.xSteps[t]=this.xHighestCompleteStep[t]=this.xVal[t]},t}(),C={to:function(t){return void 0===t?"":t.toFixed(2)},from:Number},N={target:"target",base:"base",origin:"origin",handle:"handle",handleLower:"handle-lower",handleUpper:"handle-upper",touchArea:"touch-area",horizontal:"horizontal",vertical:"vertical",background:"background",connect:"connect",connects:"connects",ltr:"ltr",rtl:"rtl",textDirectionLtr:"txt-dir-ltr",textDirectionRtl:"txt-dir-rtl",draggable:"draggable",drag:"state-drag",tap:"state-tap",active:"active",tooltip:"tooltip",pips:"pips",pipsHorizontal:"pips-horizontal",pipsVertical:"pips-vertical",marker:"marker",markerHorizontal:"marker-horizontal",markerVertical:"marker-vertical",markerNormal:"marker-normal",markerLarge:"marker-large",markerSub:"marker-sub",value:"value",valueHorizontal:"value-horizontal",valueVertical:"value-vertical",valueNormal:"value-normal",valueLarge:"value-large",valueSub:"value-sub"};r.cssClasses=N;var V={tooltips:".__tooltips",aria:".__aria"};function A(t,e){if(!p(e))throw new Error("noUiSlider: 'step' is not numeric.");t.singleStep=e}function k(t,e){if(!p(e))throw new Error("noUiSlider: 'keyboardPageMultiplier' is not numeric.");t.keyboardPageMultiplier=e}function M(t,e){if(!p(e))throw new Error("noUiSlider: 'keyboardMultiplier' is not numeric.");t.keyboardMultiplier=e}function U(t,e){if(!p(e))throw new Error("noUiSlider: 'keyboardDefaultStep' is not numeric.");t.keyboardDefaultStep=e}function D(t,e){if("object"!=typeof e||Array.isArray(e))throw new Error("noUiSlider: 'range' is not an object.");if(void 0===e.min||void 0===e.max)throw new Error("noUiSlider: Missing 'min' or 'max' in 'range'.");t.spectrum=new P(e,t.snap||!1,t.singleStep)}function O(t,e){if(e=h(e),!Array.isArray(e)||!e.length)throw new Error("noUiSlider: 'start' option is incorrect.");t.handles=e.length,t.start=e}function L(t,e){if("boolean"!=typeof e)throw new Error("noUiSlider: 'snap' option must be a boolean.");t.snap=e}function T(t,e){if("boolean"!=typeof e)throw new Error("noUiSlider: 'animate' option must be a boolean.");t.animate=e}function z(t,e){if("number"!=typeof e)throw new Error("noUiSlider: 'animationDuration' option must be a number.");t.animationDuration=e}function j(t,e){var r,n=[!1];if("lower"===e?e=[!0,!1]:"upper"===e&&(e=[!1,!0]),!0===e||!1===e){for(r=1;r<t.handles;r++)n.push(e);n.push(!1)}else{if(!Array.isArray(e)||!e.length||e.length!==t.handles+1)throw new Error("noUiSlider: 'connect' option doesn't match handle count.");n=e}t.connect=n}function H(t,e){switch(e){case"horizontal":t.ort=0;break;case"vertical":t.ort=1;break;default:throw new Error("noUiSlider: 'orientation' option is invalid.")}}function F(t,e){if(!p(e))throw new Error("noUiSlider: 'margin' option must be numeric.");0!==e&&(t.margin=t.spectrum.getDistance(e))}function R(t,e){if(!p(e))throw new Error("noUiSlider: 'limit' option must be numeric.");if(t.limit=t.spectrum.getDistance(e),!t.limit||t.handles<2)throw new Error("noUiSlider: 'limit' option is only supported on linear sliders with 2 or more handles.")}function _(t,e){var r;if(!p(e)&&!Array.isArray(e))throw new Error("noUiSlider: 'padding' option must be numeric or array of exactly 2 numbers.");if(Array.isArray(e)&&2!==e.length&&!p(e[0])&&!p(e[1]))throw new Error("noUiSlider: 'padding' option must be numeric or array of exactly 2 numbers.");if(0!==e){for(Array.isArray(e)||(e=[e,e]),t.padding=[t.spectrum.getDistance(e[0]),t.spectrum.getDistance(e[1])],r=0;r<t.spectrum.xNumSteps.length-1;r++)if(t.padding[0][r]<0||t.padding[1][r]<0)throw new Error("noUiSlider: 'padding' option must be a positive number(s).");var n=e[0]+e[1],i=t.spectrum.xVal[0];if(n/(t.spectrum.xVal[t.spectrum.xVal.length-1]-i)>1)throw new Error("noUiSlider: 'padding' option must not exceed 100% of the range.")}}function B(t,e){switch(e){case"ltr":t.dir=0;break;case"rtl":t.dir=1;break;default:throw new Error("noUiSlider: 'direction' option was not recognized.")}}function q(t,e){if("string"!=typeof e)throw new Error("noUiSlider: 'behaviour' must be a string containing options.");var r=e.indexOf("tap")>=0,n=e.indexOf("drag")>=0,i=e.indexOf("fixed")>=0,o=e.indexOf("snap")>=0,s=e.indexOf("hover")>=0,a=e.indexOf("unconstrained")>=0,l=e.indexOf("drag-all")>=0,u=e.indexOf("smooth-steps")>=0;if(i){if(2!==t.handles)throw new Error("noUiSlider: 'fixed' behaviour must be used with 2 handles");F(t,t.start[1]-t.start[0])}if(a&&(t.margin||t.limit))throw new Error("noUiSlider: 'unconstrained' behaviour cannot be used with margin or limit");t.events={tap:r||o,drag:n,dragAll:l,smoothSteps:u,fixed:i,snap:o,hover:s,unconstrained:a}}function X(t,e){if(!1!==e)if(!0===e||a(e)){t.tooltips=[];for(var r=0;r<t.handles;r++)t.tooltips.push(e)}else{if((e=h(e)).length!==t.handles)throw new Error("noUiSlider: must pass a formatter for all handles.");e.forEach((function(t){if("boolean"!=typeof t&&!a(t))throw new Error("noUiSlider: 'tooltips' must be passed a formatter or 'false'.")})),t.tooltips=e}}function Y(t,e){if(e.length!==t.handles)throw new Error("noUiSlider: must pass a attributes for all handles.");t.handleAttributes=e}function I(t,e){if(!a(e))throw new Error("noUiSlider: 'ariaFormat' requires 'to' method.");t.ariaFormat=e}function W(t,e){if(!function(t){return a(t)&&"function"==typeof t.from}(e))throw new Error("noUiSlider: 'format' requires 'to' and 'from' methods.");t.format=e}function $(t,e){if("boolean"!=typeof e)throw new Error("noUiSlider: 'keyboardSupport' option must be a boolean.");t.keyboardSupport=e}function G(t,e){t.documentElement=e}function J(t,e){if("string"!=typeof e&&!1!==e)throw new Error("noUiSlider: 'cssPrefix' must be a string or `false`.");t.cssPrefix=e}function K(t,e){if("object"!=typeof e)throw new Error("noUiSlider: 'cssClasses' must be an object.");"string"==typeof t.cssPrefix?(t.cssClasses={},Object.keys(e).forEach((function(r){t.cssClasses[r]=t.cssPrefix+e[r]}))):t.cssClasses=e}function Q(t){var e={margin:null,limit:null,padding:null,animate:!0,animationDuration:300,ariaFormat:C,format:C},r={step:{r:!1,t:A},keyboardPageMultiplier:{r:!1,t:k},keyboardMultiplier:{r:!1,t:M},keyboardDefaultStep:{r:!1,t:U},start:{r:!0,t:O},connect:{r:!0,t:j},direction:{r:!0,t:B},snap:{r:!1,t:L},animate:{r:!1,t:T},animationDuration:{r:!1,t:z},range:{r:!0,t:D},orientation:{r:!1,t:H},margin:{r:!1,t:F},limit:{r:!1,t:R},padding:{r:!1,t:_},behaviour:{r:!0,t:q},ariaFormat:{r:!1,t:I},format:{r:!1,t:W},tooltips:{r:!1,t:X},keyboardSupport:{r:!0,t:$},documentElement:{r:!1,t:G},cssPrefix:{r:!0,t:J},cssClasses:{r:!0,t:K},handleAttributes:{r:!1,t:Y}},n={connect:!1,direction:"ltr",behaviour:"tap",orientation:"horizontal",keyboardSupport:!0,cssPrefix:"noUi-",cssClasses:N,keyboardPageMultiplier:5,keyboardMultiplier:1,keyboardDefaultStep:10};t.format&&!t.ariaFormat&&(t.ariaFormat=t.format),Object.keys(r).forEach((function(i){if(u(t[i])||void 0!==n[i])r[i].t(e,u(t[i])?t[i]:n[i]);else if(r[i].r)throw new Error("noUiSlider: '"+i+"' is required.")})),e.pips=t.pips;var i=document.createElement("div"),o=void 0!==i.style.msTransform,s=void 0!==i.style.transform;e.transformRule=s?"transform":o?"msTransform":"webkitTransform";return e.style=[["left","top"],["right","bottom"]][e.dir][e.ort],e}function Z(t,e,n){var i,o,s,a,p,m,S,x=window.navigator.pointerEnabled?{start:"pointerdown",move:"pointermove",end:"pointerup"}:window.navigator.msPointerEnabled?{start:"MSPointerDown",move:"MSPointerMove",end:"MSPointerUp"}:{start:"mousedown touchstart",move:"mousemove touchmove",end:"mouseup touchend"},y=window.CSS&&CSS.supports&&CSS.supports("touch-action","none")&&function(){var t=!1;try{var e=Object.defineProperty({},"passive",{get:function(){t=!0}});window.addEventListener("test",null,e)}catch(t){}return t}(),w=t,E=e.spectrum,P=[],C=[],N=[],A=0,k={},M=t.ownerDocument,U=e.documentElement||M.documentElement,D=M.body,O="rtl"===M.dir||1===e.ort?0:100;function L(t,e){var r=M.createElement("div");return e&&g(r,e),t.appendChild(r),r}function T(t,r){var n=L(t,e.cssClasses.origin),i=L(n,e.cssClasses.handle);if(L(i,e.cssClasses.touchArea),i.setAttribute("data-handle",String(r)),e.keyboardSupport&&(i.setAttribute("tabindex","0"),i.addEventListener("keydown",(function(t){return function(t,r){if(H()||F(r))return!1;var n=["Left","Right"],i=["Down","Up"],o=["PageDown","PageUp"],s=["Home","End"];e.dir&&!e.ort?n.reverse():e.ort&&!e.dir&&(i.reverse(),o.reverse());var a,l=t.key.replace("Arrow",""),u=l===o[0],c=l===o[1],p=l===i[0]||l===n[0]||u,f=l===i[1]||l===n[1]||c,d=l===s[0],h=l===s[1];if(!(p||f||d||h))return!0;if(t.preventDefault(),f||p){var m=p?0:1,g=gt(r)[m];if(null===g)return!1;!1===g&&(g=E.getDefaultStep(C[r],p,e.keyboardDefaultStep)),g*=c||u?e.keyboardPageMultiplier:e.keyboardMultiplier,g=Math.max(g,1e-7),g*=p?-1:1,a=P[r]+g}else a=h?e.spectrum.xVal[e.spectrum.xVal.length-1]:e.spectrum.xVal[0];return pt(r,E.toStepping(a),!0,!0),ot("slide",r),ot("update",r),ot("change",r),ot("set",r),!1}(t,r)}))),void 0!==e.handleAttributes){var o=e.handleAttributes[r];Object.keys(o).forEach((function(t){i.setAttribute(t,o[t])}))}return i.setAttribute("role","slider"),i.setAttribute("aria-orientation",e.ort?"vertical":"horizontal"),0===r?g(i,e.cssClasses.handleLower):r===e.handles-1&&g(i,e.cssClasses.handleUpper),n.handle=i,n}function z(t,r){return!!r&&L(t,e.cssClasses.connect)}function j(t,r){return!(!e.tooltips||!e.tooltips[r])&&L(t.firstChild,e.cssClasses.tooltip)}function H(){return w.hasAttribute("disabled")}function F(t){return o[t].hasAttribute("disabled")}function R(){p&&(it("update"+V.tooltips),p.forEach((function(t){t&&l(t)})),p=null)}function _(){R(),p=o.map(j),nt("update"+V.tooltips,(function(t,r,n){if(p&&e.tooltips&&!1!==p[r]){var i=t[r];!0!==e.tooltips[r]&&(i=e.tooltips[r].to(n[r])),p[r].innerHTML=i}}))}function B(t,e){return t.map((function(t){return E.fromStepping(e?E.getStep(t):t)}))}function q(t){var e,n=function(t){if(t.mode===r.PipsMode.Range||t.mode===r.PipsMode.Steps)return E.xVal;if(t.mode===r.PipsMode.Count){if(t.values<2)throw new Error("noUiSlider: 'values' (>= 2) required for mode 'count'.");for(var e=t.values-1,n=100/e,i=[];e--;)i[e]=e*n;return i.push(100),B(i,t.stepped)}return t.mode===r.PipsMode.Positions?B(t.values,t.stepped):t.mode===r.PipsMode.Values?t.stepped?t.values.map((function(t){return E.fromStepping(E.getStep(E.toStepping(t)))})):t.values:[]}(t),i={},o=E.xVal[0],s=E.xVal[E.xVal.length-1],a=!1,l=!1,u=0;return e=n.slice().sort((function(t,e){return t-e})),(n=e.filter((function(t){return!this[t]&&(this[t]=!0)}),{}))[0]!==o&&(n.unshift(o),a=!0),n[n.length-1]!==s&&(n.push(s),l=!0),n.forEach((function(e,o){var s,c,p,f,d,h,m,g,v,b,S=e,x=n[o+1],y=t.mode===r.PipsMode.Steps;for(y&&(s=E.xNumSteps[o]),s||(s=x-S),void 0===x&&(x=S),s=Math.max(s,1e-7),c=S;c<=x;c=Number((c+s).toFixed(7))){for(g=(d=(f=E.toStepping(c))-u)/(t.density||1),b=d/(v=Math.round(g)),p=1;p<=v;p+=1)i[(h=u+p*b).toFixed(5)]=[E.fromStepping(h),0];m=n.indexOf(c)>-1?r.PipsType.LargeValue:y?r.PipsType.SmallValue:r.PipsType.NoValue,!o&&a&&c!==x&&(m=0),c===x&&l||(i[f.toFixed(5)]=[c,m]),u=f}})),i}function X(t,n,i){var o,s,a=M.createElement("div"),l=((o={})[r.PipsType.None]="",o[r.PipsType.NoValue]=e.cssClasses.valueNormal,o[r.PipsType.LargeValue]=e.cssClasses.valueLarge,o[r.PipsType.SmallValue]=e.cssClasses.valueSub,o),u=((s={})[r.PipsType.None]="",s[r.PipsType.NoValue]=e.cssClasses.markerNormal,s[r.PipsType.LargeValue]=e.cssClasses.markerLarge,s[r.PipsType.SmallValue]=e.cssClasses.markerSub,s),c=[e.cssClasses.valueHorizontal,e.cssClasses.valueVertical],p=[e.cssClasses.markerHorizontal,e.cssClasses.markerVertical];function f(t,r){var n=r===e.cssClasses.value,i=n?l:u;return r+" "+(n?c:p)[e.ort]+" "+i[t]}return g(a,e.cssClasses.pips),g(a,0===e.ort?e.cssClasses.pipsHorizontal:e.cssClasses.pipsVertical),Object.keys(t).forEach((function(o){!function(t,o,s){if((s=n?n(o,s):s)!==r.PipsType.None){var l=L(a,!1);l.className=f(s,e.cssClasses.marker),l.style[e.style]=t+"%",s>r.PipsType.NoValue&&((l=L(a,!1)).className=f(s,e.cssClasses.value),l.setAttribute("data-value",String(o)),l.style[e.style]=t+"%",l.innerHTML=String(i.to(o)))}}(o,t[o][0],t[o][1])})),a}function Y(){a&&(l(a),a=null)}function I(t){Y();var e=q(t),r=t.filter,n=t.format||{to:function(t){return String(Math.round(t))}};return a=w.appendChild(X(e,r,n))}function W(){var t=i.getBoundingClientRect(),r="offset"+["Width","Height"][e.ort];return 0===e.ort?t.width||i[r]:t.height||i[r]}function $(t,r,n,i){var o=function(o){var s,a,l=function(t,e,r){var n=0===t.type.indexOf("touch"),i=0===t.type.indexOf("mouse"),o=0===t.type.indexOf("pointer"),s=0,a=0;0===t.type.indexOf("MSPointer")&&(o=!0);if("mousedown"===t.type&&!t.buttons&&!t.touches)return!1;if(n){var l=function(e){var n=e.target;return n===r||r.contains(n)||t.composed&&t.composedPath().shift()===r};if("touchstart"===t.type){var u=Array.prototype.filter.call(t.touches,l);if(u.length>1)return!1;s=u[0].pageX,a=u[0].pageY}else{var c=Array.prototype.find.call(t.changedTouches,l);if(!c)return!1;s=c.pageX,a=c.pageY}}e=e||b(M),(i||o)&&(s=t.clientX+e.x,a=t.clientY+e.y);return t.pageOffset=e,t.points=[s,a],t.cursor=i||o,t}(o,i.pageOffset,i.target||r);return!!l&&(!(H()&&!i.doNotReject)&&(s=w,a=e.cssClasses.tap,!((s.classList?s.classList.contains(a):new RegExp("\\b"+a+"\\b").test(s.className))&&!i.doNotReject)&&(!(t===x.start&&void 0!==l.buttons&&l.buttons>1)&&((!i.hover||!l.buttons)&&(y||l.preventDefault(),l.calcPoint=l.points[e.ort],void n(l,i))))))},s=[];return t.split(" ").forEach((function(t){r.addEventListener(t,o,!!y&&{passive:!0}),s.push([t,o])})),s}function G(t){var r,n,o,s,a,l,u=100*(t-(r=i,n=e.ort,o=r.getBoundingClientRect(),s=r.ownerDocument,a=s.documentElement,l=b(s),/webkit.*Chrome.*Mobile/i.test(navigator.userAgent)&&(l.x=0),n?o.top+l.y-a.clientTop:o.left+l.x-a.clientLeft))/W();return u=d(u),e.dir?100-u:u}function J(t,e){"mouseout"===t.type&&"HTML"===t.target.nodeName&&null===t.relatedTarget&&Z(t,e)}function K(t,r){if(-1===navigator.appVersion.indexOf("MSIE 9")&&0===t.buttons&&0!==r.buttonsProperty)return Z(t,r);var n=(e.dir?-1:1)*(t.calcPoint-r.startCalcPoint);lt(n>0,100*n/r.baseSize,r.locations,r.handleNumbers,r.connect)}function Z(t,r){r.handle&&(v(r.handle,e.cssClasses.active),A-=1),r.listeners.forEach((function(t){U.removeEventListener(t[0],t[1])})),0===A&&(v(w,e.cssClasses.drag),ct(),t.cursor&&(D.style.cursor="",D.removeEventListener("selectstart",c))),e.events.smoothSteps&&(r.handleNumbers.forEach((function(t){pt(t,C[t],!0,!0,!1,!1)})),r.handleNumbers.forEach((function(t){ot("update",t)}))),r.handleNumbers.forEach((function(t){ot("change",t),ot("set",t),ot("end",t)}))}function tt(t,r){if(!r.handleNumbers.some(F)){var n;if(1===r.handleNumbers.length)n=o[r.handleNumbers[0]].children[0],A+=1,g(n,e.cssClasses.active);t.stopPropagation();var i=[],s=$(x.move,U,K,{target:t.target,handle:n,connect:r.connect,listeners:i,startCalcPoint:t.calcPoint,baseSize:W(),pageOffset:t.pageOffset,handleNumbers:r.handleNumbers,buttonsProperty:t.buttons,locations:C.slice()}),a=$(x.end,U,Z,{target:t.target,handle:n,listeners:i,doNotReject:!0,handleNumbers:r.handleNumbers}),l=$("mouseout",U,J,{target:t.target,handle:n,listeners:i,doNotReject:!0,handleNumbers:r.handleNumbers});i.push.apply(i,s.concat(a,l)),t.cursor&&(D.style.cursor=getComputedStyle(t.target).cursor,o.length>1&&g(w,e.cssClasses.drag),D.addEventListener("selectstart",c,!1)),r.handleNumbers.forEach((function(t){ot("start",t)}))}}function et(t){t.stopPropagation();var r=G(t.calcPoint),n=function(t){var e=100,r=!1;return o.forEach((function(n,i){if(!F(i)){var o=C[i],s=Math.abs(o-t);(s<e||s<=e&&t>o||100===s&&100===e)&&(r=i,e=s)}})),r}(r);!1!==n&&(e.events.snap||f(w,e.cssClasses.tap,e.animationDuration),pt(n,r,!0,!0),ct(),ot("slide",n,!0),ot("update",n,!0),e.events.snap?tt(t,{handleNumbers:[n]}):(ot("change",n,!0),ot("set",n,!0)))}function rt(t){var e=G(t.calcPoint),r=E.getStep(e),n=E.fromStepping(r);Object.keys(k).forEach((function(t){"hover"===t.split(".")[0]&&k[t].forEach((function(t){t.call(vt,n)}))}))}function nt(t,e){k[t]=k[t]||[],k[t].push(e),"update"===t.split(".")[0]&&o.forEach((function(t,e){ot("update",e)}))}function it(t){var e=t&&t.split(".")[0],r=e?t.substring(e.length):t;Object.keys(k).forEach((function(t){var n=t.split(".")[0],i=t.substring(n.length);e&&e!==n||r&&r!==i||function(t){return t===V.aria||t===V.tooltips}(i)&&r!==i||delete k[t]}))}function ot(t,r,n){Object.keys(k).forEach((function(i){var o=i.split(".")[0];t===o&&k[i].forEach((function(t){t.call(vt,P.map(e.format.to),r,P.slice(),n||!1,C.slice(),vt)}))}))}function st(t,r,n,i,s,a,l){var u;return o.length>1&&!e.events.unconstrained&&(i&&r>0&&(u=E.getAbsoluteDistance(t[r-1],e.margin,!1),n=Math.max(n,u)),s&&r<o.length-1&&(u=E.getAbsoluteDistance(t[r+1],e.margin,!0),n=Math.min(n,u))),o.length>1&&e.limit&&(i&&r>0&&(u=E.getAbsoluteDistance(t[r-1],e.limit,!1),n=Math.min(n,u)),s&&r<o.length-1&&(u=E.getAbsoluteDistance(t[r+1],e.limit,!0),n=Math.max(n,u))),e.padding&&(0===r&&(u=E.getAbsoluteDistance(0,e.padding[0],!1),n=Math.max(n,u)),r===o.length-1&&(u=E.getAbsoluteDistance(100,e.padding[1],!0),n=Math.min(n,u))),l||(n=E.getStep(n)),!((n=d(n))===t[r]&&!a)&&n}function at(t,r){var n=e.ort;return(n?r:t)+", "+(n?t:r)}function lt(t,r,n,i,o){var s=n.slice(),a=i[0],l=e.events.smoothSteps,u=[!t,t],c=[t,!t];i=i.slice(),t&&i.reverse(),i.length>1?i.forEach((function(t,e){var n=st(s,t,s[t]+r,u[e],c[e],!1,l);!1===n?r=0:(r=n-s[t],s[t]=n)})):u=c=[!0];var p=!1;i.forEach((function(t,e){p=pt(t,n[t]+r,u[e],c[e],!1,l)||p})),p&&(i.forEach((function(t){ot("update",t),ot("slide",t)})),null!=o&&ot("drag",a))}function ut(t,r){return e.dir?100-t-r:t}function ct(){N.forEach((function(t){var e=C[t]>50?-1:1,r=3+(o.length+e*t);o[t].style.zIndex=String(r)}))}function pt(t,r,n,i,s,a){return s||(r=st(C,t,r,n,i,!1,a)),!1!==r&&(function(t,r){C[t]=r,P[t]=E.fromStepping(r);var n="translate("+at(ut(r,0)-O+"%","0")+")";o[t].style[e.transformRule]=n,ft(t),ft(t+1)}(t,r),!0)}function ft(t){if(s[t]){var r=0,n=100;0!==t&&(r=C[t-1]),t!==s.length-1&&(n=C[t]);var i=n-r,o="translate("+at(ut(r,i)+"%","0")+")",a="scale("+at(i/100,"1")+")";s[t].style[e.transformRule]=o+" "+a}}function dt(t,r){return null===t||!1===t||void 0===t?C[r]:("number"==typeof t&&(t=String(t)),!1!==(t=e.format.from(t))&&(t=E.toStepping(t)),!1===t||isNaN(t)?C[r]:t)}function ht(t,r,n){var i=h(t),o=void 0===C[0];r=void 0===r||r,e.animate&&!o&&f(w,e.cssClasses.tap,e.animationDuration),N.forEach((function(t){pt(t,dt(i[t],t),!0,!1,n)}));var s=1===N.length?0:1;if(o&&E.hasNoSize()&&(n=!0,C[0]=0,N.length>1)){var a=100/(N.length-1);N.forEach((function(t){C[t]=t*a}))}for(;s<N.length;++s)N.forEach((function(t){pt(t,C[t],!0,!0,n)}));ct(),N.forEach((function(t){ot("update",t),null!==i[t]&&r&&ot("set",t)}))}function mt(t){if(void 0===t&&(t=!1),t)return 1===P.length?P[0]:P.slice(0);var r=P.map(e.format.to);return 1===r.length?r[0]:r}function gt(t){var r=C[t],n=E.getNearbySteps(r),i=P[t],o=n.thisStep.step,s=null;if(e.snap)return[i-n.stepBefore.startValue||null,n.stepAfter.startValue-i||null];!1!==o&&i+o>n.stepAfter.startValue&&(o=n.stepAfter.startValue-i),s=i>n.thisStep.startValue?n.thisStep.step:!1!==n.stepBefore.step&&i-n.stepBefore.highestStep,100===r?o=null:0===r&&(s=null);var a=E.countStepDecimals();return null!==o&&!1!==o&&(o=Number(o.toFixed(a))),null!==s&&!1!==s&&(s=Number(s.toFixed(a))),[s,o]}g(m=w,e.cssClasses.target),0===e.dir?g(m,e.cssClasses.ltr):g(m,e.cssClasses.rtl),0===e.ort?g(m,e.cssClasses.horizontal):g(m,e.cssClasses.vertical),g(m,"rtl"===getComputedStyle(m).direction?e.cssClasses.textDirectionRtl:e.cssClasses.textDirectionLtr),i=L(m,e.cssClasses.base),function(t,r){var n=L(r,e.cssClasses.connects);o=[],(s=[]).push(z(n,t[0]));for(var i=0;i<e.handles;i++)o.push(T(r,i)),N[i]=i,s.push(z(n,t[i+1]))}(e.connect,i),(S=e.events).fixed||o.forEach((function(t,e){$(x.start,t.children[0],tt,{handleNumbers:[e]})})),S.tap&&$(x.start,i,et,{}),S.hover&&$(x.move,i,rt,{hover:!0}),S.drag&&s.forEach((function(t,r){if(!1!==t&&0!==r&&r!==s.length-1){var n=o[r-1],i=o[r],a=[t],l=[n,i],u=[r-1,r];g(t,e.cssClasses.draggable),S.fixed&&(a.push(n.children[0]),a.push(i.children[0])),S.dragAll&&(l=o,u=N),a.forEach((function(e){$(x.start,e,tt,{handles:l,handleNumbers:u,connect:t})}))}})),ht(e.start),e.pips&&I(e.pips),e.tooltips&&_(),it("update"+V.aria),nt("update"+V.aria,(function(t,r,n,i,s){N.forEach((function(t){var r=o[t],i=st(C,t,0,!0,!0,!0),a=st(C,t,100,!0,!0,!0),l=s[t],u=String(e.ariaFormat.to(n[t]));i=E.fromStepping(i).toFixed(1),a=E.fromStepping(a).toFixed(1),l=E.fromStepping(l).toFixed(1),r.children[0].setAttribute("aria-valuemin",i),r.children[0].setAttribute("aria-valuemax",a),r.children[0].setAttribute("aria-valuenow",l),r.children[0].setAttribute("aria-valuetext",u)}))}));var vt={destroy:function(){for(it(V.aria),it(V.tooltips),Object.keys(e.cssClasses).forEach((function(t){v(w,e.cssClasses[t])}));w.firstChild;)w.removeChild(w.firstChild);delete w.noUiSlider},steps:function(){return N.map(gt)},on:nt,off:it,get:mt,set:ht,setHandle:function(t,e,r,n){if(!((t=Number(t))>=0&&t<N.length))throw new Error("noUiSlider: invalid handle number, got: "+t);pt(t,dt(e,t),!0,!0,n),ot("update",t),r&&ot("set",t)},reset:function(t){ht(e.start,t)},disable:function(t){null!=t?(o[t].setAttribute("disabled",""),o[t].handle.removeAttribute("tabindex")):(w.setAttribute("disabled",""),o.forEach((function(t){t.handle.removeAttribute("tabindex")})))},enable:function(t){null!=t?(o[t].removeAttribute("disabled"),o[t].handle.setAttribute("tabindex","0")):(w.removeAttribute("disabled"),o.forEach((function(t){t.removeAttribute("disabled"),t.handle.setAttribute("tabindex","0")})))},__moveHandles:function(t,e,r){lt(t,e,C,r)},options:n,updateOptions:function(t,r){var i=mt(),o=["margin","limit","padding","range","animate","snap","step","format","pips","tooltips"];o.forEach((function(e){void 0!==t[e]&&(n[e]=t[e])}));var s=Q(n);o.forEach((function(r){void 0!==t[r]&&(e[r]=s[r])})),E=s.spectrum,e.margin=s.margin,e.limit=s.limit,e.padding=s.padding,e.pips?I(e.pips):Y(),e.tooltips?_():R(),C=[],ht(u(t.start)?t.start:i,r)},target:w,removePips:Y,removeTooltips:R,getPositions:function(){return C.slice()},getTooltips:function(){return p},getOrigins:function(){return o},pips:I};return vt}function tt(t,e){if(!t||!t.nodeName)throw new Error("noUiSlider: create requires a single element, got: "+t);if(t.noUiSlider)throw new Error("noUiSlider: Slider was already initialized.");var r=Z(t,Q(e),e);return t.noUiSlider=r,r}r.default={__spectrum:P,cssClasses:N,create:tt}},
684: function _(e,l,i,t,d){t(),i.slider_title="bk-slider-title",i.slider_value="bk-slider-value",i.default=".bk-slider-title{white-space:nowrap;}.bk-slider-value{font-weight:600;}"},
685: function _(i,o,t,n,r){n(),t.default='.noUi-target,.noUi-target *{-webkit-touch-callout:none;-webkit-tap-highlight-color:rgba(0, 0, 0, 0);-webkit-user-select:none;-ms-touch-action:none;touch-action:none;-ms-user-select:none;-moz-user-select:none;user-select:none;-moz-box-sizing:border-box;box-sizing:border-box;}.noUi-target{position:relative;}.noUi-base,.noUi-connects{width:100%;height:100%;position:relative;z-index:1;}.noUi-connects{overflow:hidden;z-index:0;}.noUi-connect,.noUi-origin{will-change:transform;position:absolute;z-index:1;top:0;right:0;height:100%;width:100%;-ms-transform-origin:0 0;-webkit-transform-origin:0 0;-webkit-transform-style:preserve-3d;transform-origin:0 0;transform-style:flat;}.noUi-txt-dir-rtl.noUi-horizontal .noUi-origin{left:0;right:auto;}.noUi-vertical .noUi-origin{top:-100%;width:0;}.noUi-horizontal .noUi-origin{height:0;}.noUi-handle{-webkit-backface-visibility:hidden;backface-visibility:hidden;position:absolute;}.noUi-touch-area{height:100%;width:100%;}.noUi-state-tap .noUi-connect,.noUi-state-tap .noUi-origin{-webkit-transition:transform 0.3s;transition:transform 0.3s;}.noUi-state-drag *{cursor:inherit !important;}.noUi-horizontal{height:18px;}.noUi-horizontal .noUi-handle{width:34px;height:28px;right:-17px;top:-6px;}.noUi-vertical{width:18px;}.noUi-vertical .noUi-handle{width:28px;height:34px;right:-6px;bottom:-17px;}.noUi-txt-dir-rtl.noUi-horizontal .noUi-handle{left:-17px;right:auto;}.noUi-target{background:#FAFAFA;border-radius:4px;border:1px solid #D3D3D3;box-shadow:inset 0 1px 1px #F0F0F0, 0 3px 6px -5px #BBB;}.noUi-connects{border-radius:3px;}.noUi-connect{background:#3FB8AF;}.noUi-draggable{cursor:ew-resize;}.noUi-vertical .noUi-draggable{cursor:ns-resize;}.noUi-handle{border:1px solid #D9D9D9;border-radius:3px;background:#FFF;cursor:default;box-shadow:inset 0 0 1px #FFF, inset 0 1px 7px #EBEBEB, 0 3px 6px -3px #BBB;}.noUi-active{box-shadow:inset 0 0 1px #FFF, inset 0 1px 7px #DDD, 0 3px 6px -3px #BBB;}.noUi-handle:before,.noUi-handle:after{content:"";display:block;position:absolute;height:14px;width:1px;background:#E8E7E6;left:14px;top:6px;}.noUi-handle:after{left:17px;}.noUi-vertical .noUi-handle:before,.noUi-vertical .noUi-handle:after{width:14px;height:1px;left:6px;top:14px;}.noUi-vertical .noUi-handle:after{top:17px;}[disabled] .noUi-connect{background:#B8B8B8;}[disabled].noUi-target,[disabled].noUi-handle,[disabled] .noUi-handle{cursor:not-allowed;}.noUi-pips,.noUi-pips *{-moz-box-sizing:border-box;box-sizing:border-box;}.noUi-pips{position:absolute;color:#999;}.noUi-value{position:absolute;white-space:nowrap;text-align:center;}.noUi-value-sub{color:#ccc;font-size:10px;}.noUi-marker{position:absolute;background:#CCC;}.noUi-marker-sub{background:#AAA;}.noUi-marker-large{background:#AAA;}.noUi-pips-horizontal{padding:10px 0;height:80px;top:100%;left:0;width:100%;}.noUi-value-horizontal{-webkit-transform:translate(-50%, 50%);transform:translate(-50%, 50%);}.noUi-rtl .noUi-value-horizontal{-webkit-transform:translate(50%, 50%);transform:translate(50%, 50%);}.noUi-marker-horizontal.noUi-marker{margin-left:-1px;width:2px;height:5px;}.noUi-marker-horizontal.noUi-marker-sub{height:10px;}.noUi-marker-horizontal.noUi-marker-large{height:15px;}.noUi-pips-vertical{padding:0 10px;height:100%;top:0;left:100%;}.noUi-value-vertical{-webkit-transform:translate(0, -50%);transform:translate(0, -50%);padding-left:25px;}.noUi-rtl .noUi-value-vertical{-webkit-transform:translate(0, 50%);transform:translate(0, 50%);}.noUi-marker-vertical.noUi-marker{width:5px;height:2px;margin-top:-1px;}.noUi-marker-vertical.noUi-marker-sub{width:10px;}.noUi-marker-vertical.noUi-marker-large{width:15px;}.noUi-tooltip{display:block;position:absolute;border:1px solid #D9D9D9;border-radius:3px;background:#fff;color:#000;padding:5px;text-align:center;white-space:nowrap;}.noUi-horizontal .noUi-tooltip{-webkit-transform:translate(-50%, 0);transform:translate(-50%, 0);left:50%;bottom:120%;}.noUi-vertical .noUi-tooltip{-webkit-transform:translate(0, -50%);transform:translate(0, -50%);top:50%;right:120%;}.noUi-horizontal .noUi-origin > .noUi-tooltip{-webkit-transform:translate(50%, 0);transform:translate(50%, 0);left:auto;bottom:10px;}.noUi-vertical .noUi-origin > .noUi-tooltip{-webkit-transform:translate(0, -18px);transform:translate(0, -18px);top:auto;right:28px;}.noUi-handle{cursor:grab;cursor:-webkit-grab;}.noUi-handle.noUi-active{cursor:grabbing;cursor:-webkit-grabbing;}.noUi-handle:after,.noUi-handle:before{display:none;}.noUi-tooltip{display:none;white-space:nowrap;}.noUi-handle:hover .noUi-tooltip{display:block;}:host{--slider-size:10px;--handle-width:14px;--handle-height:18px;--handle-right:calc(-1*var(--handle-width)/2);--handle-top:calc(-1*(var(--handle-height) - var(--slider-size))/2 - 1px);--slider-margin:calc((var(--handle-height) - var(--slider-size))/2 + 1px);}.noUi-horizontal{width:100%;height:var(--slider-size);}.noUi-vertical{width:var(--slider-size);height:100%;}.noUi-horizontal .noUi-handle{width:var(--handle-width);height:var(--handle-height);right:var(--handle-right);top:var(--handle-top);}.noUi-vertical .noUi-handle{width:var(--handle-height);height:var(--handle-width);right:var(--handle-top);top:var(--handle-right);}.noUi-target.noUi-horizontal{margin:var(--slider-margin) 0px;}.noUi-target.noUi-vertical{margin:0px var(--slider-margin);}'},
686: function _(e,t,r,a,n){var i;a();const c=e(1).__importDefault(e(248)),o=e(687),s=e(8);class _ extends o.NumericalRangeSliderView{constructor(){super(...arguments),this.behaviour="drag",this.connected=[!1,!0,!1]}_calc_to(){const e=super._calc_to();return e.step*=864e5,e}_formatter(e,t){return(0,s.isString)(t)?(0,c.default)(e,t):t.compute(e)}}r.DateRangeSliderView=_,_.__name__="DateRangeSliderView";class l extends o.NumericalRangeSlider{constructor(e){super(e)}}r.DateRangeSlider=l,i=l,l.__name__="DateRangeSlider",i.prototype.default_view=_,i.override({format:"%d %b %Y"})},
687: function _(e,a,r,i,l){i();const s=e(688);class t extends s.BaseNumericalSliderView{_calc_to(){return{range:{min:this.model.start,max:this.model.end},start:this.model.value,step:this.model.step}}_calc_from(e){return e}}r.NumericalRangeSliderView=t,t.__name__="NumericalRangeSliderView";class n extends s.BaseNumericalSlider{constructor(e){super(e)}}r.NumericalRangeSlider=n,n.__name__="NumericalRangeSlider"},
688: function _(e,t,r,s,a){var i;s();const n=e(682),c=e(192);class l extends n.AbstractSliderView{connect_signals(){super.connect_signals();const{start:e,end:t,step:r}=this.model.properties;this.on_change([e,t,r],(()=>this._update_slider()))}pretty(e){return this._formatter(e,this.model.format)}}r.BaseNumericalSliderView=l,l.__name__="BaseNumericalSliderView";class o extends n.AbstractSlider{constructor(e){super(e)}}r.BaseNumericalSlider=o,i=o,o.__name__="BaseNumericalSlider",i.define((({Float:e,Str:t,Or:r,Ref:s})=>({start:[e],end:[e],step:[e,1],format:[r(t,s(c.TickFormatter))]})))},
689: function _(e,t,r,a,i){var c;a();const o=e(1).__importDefault(e(248)),s=e(690),_=e(8);class l extends s.NumericalSliderView{constructor(){super(...arguments),this.behaviour="tap",this.connected=[!0,!1]}_calc_to(){const e=super._calc_to();return e.step*=864e5,e}_formatter(e,t){return(0,_.isString)(t)?(0,o.default)(e,t):t.compute(e)}}r.DateSliderView=l,l.__name__="DateSliderView";class n extends s.NumericalSlider{constructor(e){super(e)}}r.DateSlider=n,c=n,n.__name__="DateSlider",c.prototype.default_view=l,c.override({format:"%d %b %Y"})},
690: function _(e,r,t,s,i){s();const a=e(688);class l extends a.BaseNumericalSliderView{_calc_to(){const{start:e,end:r,value:t,step:s}=this.model;return{range:{min:e,max:r},start:[t],step:s}}_calc_from([e]){return Number.isInteger(this.model.start)&&Number.isInteger(this.model.end)&&Number.isInteger(this.model.step)?Math.round(e):e}}t.NumericalSliderView=l,l.__name__="NumericalSliderView";class n extends a.BaseNumericalSlider{constructor(e){super(e)}}t.NumericalSlider=n,n.__name__="NumericalSlider"},
691: function _(e,t,r,a,i){var n;a();const o=e(1).__importDefault(e(248)),s=e(687),d=e(8);class c extends s.NumericalRangeSliderView{constructor(){super(...arguments),this.behaviour="drag",this.connected=[!1,!0,!1]}_formatter(e,t){return(0,d.isString)(t)?(0,o.default)(e,t):t.compute(e)}}r.DatetimeRangeSliderView=c,c.__name__="DatetimeRangeSliderView";class l extends s.NumericalRangeSlider{constructor(e){super(e)}}r.DatetimeRangeSlider=l,n=l,l.__name__="DatetimeRangeSlider",n.prototype.default_view=c,n.override({format:"%d %b %Y %H:%M:%S",step:36e5})},
692: function _(e,r,t,a,i){var n;a();const o=e(1).__importStar(e(246)),s=e(687),c=e(8);class _ extends s.NumericalRangeSliderView{constructor(){super(...arguments),this.behaviour="drag",this.connected=[!1,!0,!1]}_formatter(e,r){return(0,c.isString)(r)?o.format(e,r):r.compute(e)}}t.RangeSliderView=_,_.__name__="RangeSliderView";class d extends s.NumericalRangeSlider{constructor(e){super(e)}}t.RangeSlider=d,n=d,d.__name__="RangeSlider",n.prototype.default_view=_,n.override({format:"0[.]00"})},
693: function _(e,r,t,i,o){var a;i();const s=e(1).__importStar(e(246)),c=e(690),n=e(8);class _ extends c.NumericalSliderView{constructor(){super(...arguments),this.behaviour="tap",this.connected=[!0,!1]}_formatter(e,r){return(0,n.isString)(r)?s.format(e,r):r.compute(e)}}t.SliderView=_,_.__name__="SliderView";class d extends c.NumericalSlider{constructor(e){super(e)}}t.Slider=d,a=d,d.__name__="Slider",a.prototype.default_view=_,a.override({format:"0[.]00"})},
}, 605, {"models/widgets/main":605,"models/widgets/index":606,"models/widgets/abstract_button":607,"models/widgets/control":608,"models/widgets/widget":718,"styles/buttons.css":610,"models/widgets/autocomplete_input":611,"models/widgets/text_input":612,"models/widgets/text_like_input":613,"models/widgets/input_widget":614,"styles/widgets/inputs.css":615,"styles/dropdown.css":616,"models/widgets/button":617,"models/widgets/checkbox_button_group":618,"models/widgets/toggle_button_group":619,"models/widgets/oriented_control":620,"models/widgets/checkbox_group":621,"models/widgets/toggle_input_group":622,"styles/widgets/checkbox.css":623,"models/widgets/checkbox":624,"models/widgets/toggle_input":625,"styles/widgets/toggle_input.css":626,"models/widgets/color_picker":627,"models/widgets/date_picker":628,"models/widgets/base_date_picker":629,"models/widgets/picker_base":630,"styles/widgets/flatpickr.css":639,"models/widgets/date_range_picker":640,"models/widgets/datetime_picker":641,"models/widgets/base_datetime_picker":642,"models/widgets/datetime_range_picker":643,"models/widgets/div":644,"models/widgets/markup":645,"styles/clearfix.css":646,"models/widgets/dropdown":647,"styles/caret.css":648,"models/widgets/file_input":649,"models/widgets/help_button":650,"models/widgets/multi_choice":651,"styles/widgets/choices.css":653,"models/widgets/multiselect":654,"models/widgets/multiple_date_picker":655,"models/widgets/multiple_datetime_picker":656,"models/widgets/numeric_input":657,"models/widgets/palette_select":658,"core/util/panes":659,"styles/panes.css":660,"styles/widgets/palette_select.css":661,"styles/widgets/palette_select_item.css":662,"styles/widgets/palette_select_pane.css":663,"models/widgets/paragraph":664,"models/widgets/password_input":665,"styles/widgets/password_input.css":666,"models/widgets/pretext":667,"models/widgets/progress":668,"models/widgets/indicator":669,"styles/widgets/progress.css":670,"models/widgets/radio_button_group":671,"models/widgets/radio_group":672,"models/widgets/select":673,"models/widgets/spinner":674,"models/widgets/switch":675,"styles/widgets/switch.css":676,"models/widgets/textarea_input":677,"models/widgets/time_picker":678,"models/widgets/toggle":679,"models/widgets/sliders/index":680,"models/widgets/sliders/categorical_slider":681,"models/widgets/sliders/abstract_slider":682,"styles/widgets/sliders.css":684,"styles/widgets/nouislider.css":685,"models/widgets/sliders/date_range_slider":686,"models/widgets/sliders/numerical_range_slider":687,"models/widgets/sliders/base_numerical_slider":688,"models/widgets/sliders/date_slider":689,"models/widgets/sliders/numerical_slider":690,"models/widgets/sliders/datetime_range_slider":691,"models/widgets/sliders/range_slider":692,"models/widgets/sliders/slider":693}, {});});
