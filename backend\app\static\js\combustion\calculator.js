// 图表实例
let temperatureChart = null;
let compositionChart = null;

// 初始化图表
function initCharts() {
  // 温度分布图表
  const temperaturePlot = new Bokeh.Plotting.figure({
    title: "温度分布图",
    width: 400,
    height: 300,
    x_axis_label: "位置",
    y_axis_label: "温度 (°C)",
    tools: "pan,wheel_zoom,box_zoom,reset,save",
  });
  temperaturePlot.circle([], [], { size: 8 });
  Bokeh.Plotting.show(
    temperaturePlot,
    document.getElementById("temperature-chart")
  );

  // 组分分布图表
  const compositionPlot = new Bokeh.Plotting.figure({
    title: "组分分布图",
    width: 400,
    height: 300,
    x_axis_label: "组分",
    y_axis_label: "摩尔分数",
    tools: "pan,wheel_zoom,box_zoom,reset,save",
  });
  compositionPlot.vbar({
    x: [],
    top: [],
    width: 0.8,
  });
  Bokeh.Plotting.show(
    compositionPlot,
    document.getElementById("composition-chart")
  );

  return {
    temperatureChart: temperaturePlot,
    compositionChart: compositionPlot,
  };
}

// 更新图表数据
function updateCharts(charts, data) {
  // 更新温度图表
  charts.temperatureChart.renderers[0].data_source.data = {
    x: Array.from({ length: data.temperature.length }, (_, i) => i),
    y: data.temperature,
  };
  charts.temperatureChart.renderers[0].data_source.change.emit();

  // 更新组分图表
  const compositions = data.compositions || {};
  charts.compositionChart.renderers[0].data_source.data = {
    x: Object.keys(compositions),
    top: Object.values(compositions),
  };
  charts.compositionChart.renderers[0].data_source.change.emit();
}

// 显示计算结果
function displayResults(data) {
  // 显示燃烧特性
  const combustionProps = document.getElementById("combustion-properties");
  combustionProps.innerHTML = `
        <table class="table table-sm">
            <tr><td>当量比</td><td>${data.当量比.toFixed(4)}</td></tr>
            <tr><td>燃料流量</td><td>${data.燃料流量.toFixed(4)} kg/s</td></tr>
            <tr><td>空气流量</td><td>${data.空气流量.toFixed(4)} kg/s</td></tr>
        </table>
    `;

  // 显示气体物性
  const gasProps = document.getElementById("gas-properties");
  gasProps.innerHTML = `
        <table class="table table-sm">
            <tr><td>分子量</td><td>${data.分子量.toFixed(2)} kg/kmol</td></tr>
            <tr><td>定压比热</td><td>${data.定压比热.toFixed(
              2
            )} kJ/kg-K</td></tr>
            <tr><td>比热比</td><td>${data.比热比.toFixed(4)}</td></tr>
            <tr><td>密度</td><td>${data.密度.toFixed(2)} kg/m³</td></tr>
        </table>
    `;
}

// 显示对比分析结果
function displayComparison(data) {
  const comparisonResults = document.getElementById("comparison-results");
  const comparisonTable = document.getElementById("comparison-table");

  comparisonResults.style.display = "block";
  comparisonTable.innerHTML = `
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>燃料</th>
                    <th>喷水量 (kg/s)</th>
                    <th>燃料流量 (kg/s)</th>
                    <th>空气流量 (kg/s)</th>
                </tr>
            </thead>
            <tbody>
                ${data
                  .map(
                    (row) => `
                    <tr>
                        <td>${row.燃料}</td>
                        <td>${row.喷水量.toFixed(4)}</td>
                        <td>${row.燃料流量.toFixed(4)}</td>
                        <td>${row.空气流量.toFixed(4)}</td>
                    </tr>
                `
                  )
                  .join("")}
            </tbody>
        </table>
    `;
}

// 页面加载完成后初始化
document.addEventListener("DOMContentLoaded", function () {
  const charts = initCharts();

  // 处理表单提交
  document
    .getElementById("calculationForm")
    .addEventListener("submit", async function (e) {
      e.preventDefault();

      const formData = {
        fuel_type: document.getElementById("fuelType").value,
        temperature:
          parseFloat(document.getElementById("temperature").value) + 273.15, // 转换为K
        pressure: parseFloat(document.getElementById("pressure").value) * 1e6, // 转换为Pa
      };

      try {
        const response = await fetch("/api/combustion/calculate", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(formData),
        });

        if (!response.ok) {
          throw new Error("计算请求失败");
        }

        const result = await response.json();
        displayResults(result);
        updateCharts(charts, result);
      } catch (error) {
        console.error("Error:", error);
        alert("计算过程中发生错误：" + error.message);
      }
    });

  // 处理对比分析按钮点击
  document
    .getElementById("compareBtn")
    .addEventListener("click", async function () {
      try {
        const response = await fetch("/api/combustion/compare");
        if (!response.ok) {
          throw new Error("对比分析请求失败");
        }

        const result = await response.json();
        displayComparison(result);
      } catch (error) {
        console.error("Error:", error);
        alert("对比分析过程中发生错误：" + error.message);
      }
    });
});
