from typing import Optional, List
from datetime import datetime
import math
import os
from pathlib import Path
from cadworx_cat_parser import CADWorxCatParser
from cadworx_prj_parser import CADWorxPrjParser


def some_utility_function():
    """示例工具函数"""
    pass


# 获取当前目录和数据目录
current_dir = Path(__file__).parent
data_dir = current_dir / "dataset"

# 设置输入和输出文件路径
cat_file = data_dir / "GB-B.CAT"
prj_file = data_dir / "GB-B.PRJ"
output_dir = data_dir / "pipe_standards"  # 解析后的数据保存在dataset/pipe_standards目录下

# 确保输出目录存在
output_dir.mkdir(parents=True, exist_ok=True)

# 使用新的解析器解析CAT文件
cat_parser = CADWorxCatParser()
cat_path = cat_parser.parse_and_save(str(cat_file))
# 将文件移动到目标目录
if cat_path:
    cat_path = Path(cat_path)
    new_cat_path = output_dir / cat_path.name
    cat_path.rename(new_cat_path)
    print(f"已解析并保存到：{new_cat_path}")

# TODO: 实现PRJ文件解析器
# prj_parser = CADWorxPrjParser()
# prj_path = prj_parser.parse_and_save(str(prj_file))
# # 将文件移动到目标目录
# if prj_path:
#     prj_path = Path(prj_path)
#     new_prj_path = output_dir / prj_path.name
#     prj_path.rename(new_prj_path)
#     print(f"已解析并保存到：{new_prj_path}")
