<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管道计算选型工具</title>
    <link href="/static/bootstrap-4.6.0-dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/bokeh/bokeh.min.css" rel="stylesheet">
    <link href="/static/bokeh/bokeh-widgets.min.css" rel="stylesheet">
    <link href="/static/bokeh/bokeh-tables.min.css" rel="stylesheet">
    <link href="/static/tabulator-master/dist/css/tabulator.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --border-color: #e0e0e0;
            --hover-color: #f5f5f5;
            --header-bg: #f5f5f5;
        }

        body {
            background-color: #fff;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            padding: 15px;
        }

        .page-title {
            color: var(--primary-color);
            font-weight: 500;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }

        .basic-settings {
            background-color: var(--header-bg);
            padding: 12px;
            border: 1px solid var(--border-color);
            margin-bottom: 20px;
            border-radius: 4px;
        }

        .settings-container {
            display: flex;
            gap: 15px;
        }

        .settings-section {
            background-color: #fff;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            flex: 1;
            min-width: 0;
            display: flex;
            flex-direction: column;
        }

        .settings-section:last-child {
            margin-bottom: 0;
        }

        .settings-section h4 {
            color: var(--primary-color);
            font-size: 0.95rem;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid var(--border-color);
            white-space: nowrap;
        }

        .form-group {
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }

        .form-group label {
            flex: 0 0 100px;
            margin-right: 8px;
            font-size: 0.9rem;
            white-space: nowrap;
        }

        .form-group select,
        .form-group input {
            flex: 0 0 120px;
            font-size: 0.9rem;
            min-width: 0;
            max-width: 200px;
        }

        .form-group input[type="text"] {
            flex: 0 0 180px;
            max-width:200px;
        }

        .pipe-info {
            background-color: #fff;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            margin-top: 10px;
        }

        .pipe-info .form-group {
            margin-bottom: 8px;
            max-width: 300px;
        }

        .pipe-info label {
            display: inline-block;
            width: 120px;
            margin-right: 10px;
        }

        .pipe-info select,
        .pipe-info input {
            display: inline-block;
            width: calc(100% - 130px);
        }

        .table-container {
            margin: 15px 0;
            overflow-x: auto;
            max-width: fit-content;
        }

        .data-table {
            width: auto;
            border-collapse: collapse;
            white-space: nowrap;
        }

        .data-table th {
            background-color: var(--header-bg);
            color: var(--primary-color);
            font-weight: 500;
            padding: 8px 12px;
            text-align: left;
            font-size: 0.9rem;
            border-bottom: 1px solid var(--border-color);
        }

        .data-table td {
            padding: 8px 12px;
            border-bottom: 1px solid var(--border-color);
            font-size: 0.9rem;
        }

        .data-table td.editable {
            padding: 0;
        }

        .data-table td.editable input {
            width: 100%;
            height: 100%;
            border: none;
            padding: 8px 12px;
            font-size: 0.9rem;
            background: transparent;
        }

        .data-table td.editable input:focus {
            outline: 2px solid var(--primary-color);
            background: #fff;
        }

        .data-table td.readonly {
            background-color: var(--header-bg);
            color: var(--primary-color);
        }

        .data-table td.editable.number {
            text-align: right;
        }

        .data-table td.editable.number input {
            text-align: right;
        }

        .data-table tbody tr:hover {
            background-color: var(--hover-color);
        }

        .chart-container {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid var(--border-color);
        }

        .section-header {
            cursor: pointer;
            padding: 10px;
            background-color: var(--header-bg);
            color: var(--primary-color);
            margin-bottom: 10px;
            border: 1px solid var(--border-color);
        }

        .section-header h3 {
            margin: 0;
            font-size: 1.1rem;
            font-weight: 500;
        }

        .section-content {
            display: none;
        }

        .section-content.expanded {
            display: block;
        }

        .section-icon {
            float: right;
            transition: transform 0.3s;
            color: var(--primary-color);
        }

        .section-icon.collapsed {
            transform: rotate(-90deg);
        }

        .btn {
            padding: 6px 12px;
            font-size: 0.9rem;
            margin-right: 5px;
            margin-bottom: 5px;
        }

        .design-doc {
            padding: 15px;
        }

        .design-doc h4 {
            color: var(--primary-color);
            margin-bottom: 15px;
            font-size: 1rem;
            font-weight: 500;
        }

        .design-doc ul {
            padding-left: 20px;
            margin-bottom: 15px;
        }

        .design-doc li {
            margin-bottom: 8px;
        }

        .design-doc strong {
            color: var(--primary-color);
        }

        .bk-root {
            width: 100% !important;
        }

        .bk-root .bk-canvas {
            width: 100% !important;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .btn {
                width: 100%;
                margin-right: 0;
            }

            .data-table th,
            .data-table td {
                padding: 8px;
                font-size: 0.85rem;
            }

            .settings-container {
                flex-direction: column;
                gap: 10px;
            }
        }

        .action-icon {
            cursor: pointer;
            color: #dc3545;
            font-size: 1.1rem;
            padding: 4px 8px;
            border-radius: 4px;
            transition: all 0.2s;
        }

        .action-icon:hover {
            background-color: #f8d7da;
            color: #721c24;
        }

        /* 设置各列宽度 */
        .data-table th:nth-child(1), /* 位号 */
        .data-table td:nth-child(1) {
            width: 80px;
        }

        .data-table th:nth-child(2), /* 流体介质 */
        .data-table td:nth-child(2) {
            width: 100px;
        }

        .data-table th:nth-child(3), /* 管道材质 */
        .data-table td:nth-child(3) {
            width: 80px;
        }

        .data-table th:nth-child(4), /* 压力 */
        .data-table td:nth-child(4),
        .data-table th:nth-child(5), /* 温度 */
        .data-table td:nth-child(5),
        .data-table th:nth-child(6), /* 流量 */
        .data-table td:nth-child(6) {
            width: 100px;
        }

        .data-table th:nth-child(7), /* 管道规格 */
        .data-table td:nth-child(7),
        .data-table th:nth-child(8), /* 保温层厚 */
        .data-table td:nth-child(8) {
            width: 100px;
        }

        .data-table th:nth-child(9), /* 流速标准 */
        .data-table td:nth-child(9) {
            width: 100px;
        }

        .data-table th:nth-child(10), /* 操作 */
        .data-table td:nth-child(10) {
            width: 50px;
            text-align: center;
        }
    </style>
</head>

<body>
    <div class="container mt-4">
        <h1 class="page-title">压力管道计算选型</h1>

        <!-- 基础设置区域 -->
        <div class="section-header" onclick="toggleSection('basicSettingsSection')">
            <h3>基础设置 <i class="section-icon">▼</i></h3>
        </div>
        <div id="basicSettingsSection" class="section-content">
            <div class="basic-settings">
                <div class="settings-container">
                    <!-- 项目信息子区域 -->
                    <div class="settings-section">
                        <h4>项目信息</h4>
                        <div class="form-group">
                            <label for="projectName">项目名称：</label>
                            <input type="text" class="form-control" id="projectName" readonly>
                        </div>
                        <div class="form-group">
                            <label for="pressureStandard">公称压力标准：</label>
                            <select class="form-control" id="pressureStandard" onchange="handlePressureStandardChange()">
                                <option value="PN">PN系列</option>
                                <option value="CLASS">CLASS系列</option>
                            </select>
                        </div>
                        <!-- 管道系列选择 -->
                        <div class="form-group">
                            <label for="pipeSeries" class="form-label">管道系列:</label>
                            <select class="form-control" id="pipeSeries">
                                <option value="A">A系列</option>
                                <option value="B">B系列</option>
                            </select>
                        </div>
                    </div>

                    <!-- 管道位号子区域 -->
                    <div class="settings-section">
                        <h4>管道位号</h4>
                        <div class="form-group">
                            <label for="pipeMaterial">管道材质</label>
                            <select class="form-control" id="pipeMaterial">
                                <option value="S304">S304</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="medium">介质</label>
                            <select class="form-control" id="medium" onchange="handleMediumChange()">
                                <option value="compressed_air">压缩空气</option>
                                <option value="cooling_water">冷却水</option>
                                <option value="aviation_kerosene">航空煤油</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="velocityStandard">推荐流速 (m/s)</label>
                            <input type="number" class="form-control" id="velocityStandard" 
                                   onchange="handleVelocityChange()" step="1">
                        </div>
                    </div>

                    <!-- 单位设置子区域 -->
                    <div class="settings-section">
                        <h4>单位设置</h4>
                        <div class="form-group">
                            <label for="pressureUnit">压力单位</label>
                            <select class="form-control" id="pressureUnit">
                                <option value="MPa">MPa</option>
                                <option value="bar">bar</option>
                                <option value="kPa">kPa</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="temperatureUnit">温度单位</label>
                            <select class="form-control" id="temperatureUnit">
                                <option value="C">°C</option>
                                <option value="K">K</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="flowUnit" class="form-label">流量单位:</label>
                            <select class="form-select" id="flowUnit" onchange="handleUnitChange()">
                                <option value="kg/h">kg/h</option>
                                <option value="kg/s" selected>kg/s</option>
                                <option value="m3/h">m³/h</option>
                                <option value="m3/s">m³/s</option>
                            </select>
                        </div>
                    </div>

                    
                </div>
            </div>
        </div>

        <!-- 数据表格区域 -->
        <div class="section-header" onclick="toggleSection('tableSection')">
            <h3>数据输入区域 <i class="section-icon">▼</i></h3>
        </div>
        <div id="tableSection" class="section-content">
            <div class="table-container">
                <div id="dataTable"></div>
            </div>
            <!-- 操作按钮 -->
            <div class="mt-3">
                <button class="btn btn-primary" onclick="addRow()">添加行</button>
                <button class="btn btn-success" onclick="calculate()">计算</button>
                <button class="btn btn-primary" onclick="document.getElementById('fileInput').click()">导入数据</button>
                <button class="btn btn-primary" onclick="handleExport()">导出计算报告</button>
                <input type="file" id="fileInput" style="display: none" accept=".csv" onchange="handleFileImport()">
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="section-header" onclick="toggleSection('chartSection')">
            <h3>可视化图表区域 <i class="section-icon">▼</i></h3>
        </div>
        <div id="chartSection" class="section-content">
            <div class="row">
                <div class="col-md-4">
                    <div class="chart-container">
                        <div id="pressure-chart"></div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="chart-container">
                        <div id="temperature-chart"></div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="chart-container">
                        <div id="flow-chart"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设计说明区域 -->
        <div class="section-header" onclick="toggleSection('designSection')">
            <h3>设计说明 <i class="section-icon">▼</i></h3>
        </div>
        <div id="designSection" class="section-content">
            <div class="design-doc">
                <h4>管道设计规范说明</h4>
                <ul>
                    <li><strong>压力等级：</strong>根据GB/T 20801.2-2020标准，管道压力等级分为PN2.5、PN6、PN10、PN16、PN25、PN40、PN63、PN100等。</li>
                    <li><strong>材质选择：</strong>常用材质包括碳钢、不锈钢、合金钢等，根据介质特性、温度和压力选择合适的材质。</li>
                    <li><strong>壁厚计算：</strong>采用GB/T 20801.3-2020标准中的壁厚计算公式，考虑压力、温度、腐蚀裕量等因素。</li>
                    <li><strong>流速限制：</strong>一般液体流速控制在1-3m/s，气体流速控制在10-30m/s，具体根据介质特性确定。</li>
                </ul>

                <h4>计算依据</h4>
                <ul>
                    <li><strong>流量计算：</strong>基于连续性方程和伯努利方程</li>
                    <li><strong>压力损失：</strong>考虑沿程阻力和局部阻力</li>
                    <li><strong>温度影响：</strong>考虑介质温度对管道材料强度的影响</li>
                    <li><strong>安全系数：</strong>根据工况条件选择适当的安全系数</li>
                </ul>

                <h4>使用说明</h4>
                <ul>
                    <li>输入介质参数和工况条件</li>
                    <li>系统自动计算推荐管径和壁厚</li>
                    <li>可导出计算结果用于工程设计</li>
                    <li>图表展示关键参数的变化趋势</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 导入数据模态框 -->
    <div class="modal fade" id="importModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">导入数据</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="file" class="form-control" id="fileInput" accept=".csv">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="handleFileImport()">导入</button>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/bootstrap-4.6.0-dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/bokeh/bokeh.min.js"></script>
    <script src="/static/bokeh/bokeh-widgets.min.js"></script>
    <script src="/static/bokeh/bokeh-tables.min.js"></script>
    <script src="/static/bokeh/bokeh-api.min.js"></script>
    <script type="text/javascript" src="/static/tabulator-master/dist/js/tabulator.min.js"></script>
    <script src="/static/js/pipe_calculator.js"></script>
</body>

</html>