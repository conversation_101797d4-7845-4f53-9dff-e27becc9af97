import cantera as ct
import numpy as np


def calculate_critical_mass_flow(gas, P0, T0, d_throat):
    """
    计算拉瓦尔喷管临界质量流量（喉部达到声速）
    
    参数：
        gas : cantera.Solution 对象 
        P0  : 总压 (Pa)
        T0  : 总温 (K)
        d_throat : 喉部直径 (m)
    
    返回：
        m_dot_critical : 临界质量流量 (kg/s)
    """
    # 设置总压总温状态 (滞止状态)
    gas.TP = T0, P0  # 直接设置静压静温为总参数（假设无速度）
    # R = gas.R  # 注意：单位是 J/(kg·K)

    # 计算喉部面积
    A_throat = np.pi * (d_throat / 2) ** 2

    # 获取介质属性
    gamma = gas.cp / gas.cv  # 比热比 γ = cp/cv
    R = gas.cp - gas.cv      # 气体常数 R = cp - cv

    # 验证等熵关系（对比图片中的密度比公式）
    lambda_critical = 1.0
    rho_ratio = (1 - (gamma-1)/(gamma+1)*lambda_critical**2)**(1/(gamma-1))
    assert np.isclose(rho_ratio, (2/(gamma+1))**(1/(gamma-1))), "等熵关系验证失败"

    # 临界流量公式
    prefactor = np.sqrt((gamma / R) * (2 / (gamma + 1))
                        ** ((gamma + 1)/(gamma - 1)))
    print(f"gamma:{gamma}\t R:{R}\t C: {prefactor:.6f}")
    m_dot_critical = prefactor*A_throat * P0 / np.sqrt(T0)
    # print(f"临界质量流量: {m_dot_critical:.2f} kg/s")

    return m_dot_critical


# ---------------------------
# 执行计算
# ---------------------------
if __name__ == "__main__":
    try:
        # 加载气体介质（建议使用标准机理文件）
        # gas = ct.Solution('gri30.yaml')  # 更通用的燃气模型
        gas = ct.Solution('air.yaml')  # 更通用的燃气模型
        # gas.TPX = 300, 1e5, 'O2:1.0, N2:3.76'  # 设置空气成分
        print("气体成分:", gas.mole_fraction_dict())
        # 输入参数
        P0 = 1*1e6       # 总压 (Pa)
        T0 = 300       # 总温 (K)
        d_throat = 0.01  # 喉部直径 (m)

        # 计算临界流量
        m_dot = calculate_critical_mass_flow(
            gas=gas, P0=P0, T0=T0, d_throat=d_throat)
        print(f"临界质量流量: {m_dot:.4f} kg/s")

        # 对比Cantera内置声速计算
        gamma = gas.cp / gas.cv
        A_throat = np.pi * (d_throat / 2) ** 2

        # gas.Sonictime = 0  # 触发声速计算
        a_star = np.sqrt(gamma * ct.gas_constant * T0 * 2/(gamma+1))  # 理论临界声速
        rho_star = P0 / (ct.gas_constant * T0) * (2/(gamma+1))**(1/(gamma-1))
        m_dot_validation = A_throat * rho_star * a_star
        print(f"气体常数: {ct.gas_constant:.4f} J/(kg·K)")
        print(f"验证计算结果: {m_dot_validation:.4f} kg/s")

    except Exception as e:
        print(f"计算错误: {str(e)}")
