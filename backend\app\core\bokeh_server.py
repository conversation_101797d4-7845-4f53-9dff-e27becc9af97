"""
Bokeh服务器管理模块
统一管理Bokeh服务器的启动、停止和状态
"""
from bokeh.server.server import Server
from app.core import settings, logger
from app.visualization import TestBokehApp


class BokehServerManager:
    """Bokeh服务器管理器"""
    _instance = None
    _server = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(BokehServerManager, cls).__new__(cls)
        return cls._instance

    @property
    def server(self):
        """获取Bokeh服务器实例"""
        return self._server

    @property
    def is_running(self):
        """检查服务器是否正在运行"""
        return self._server is not None

    def start(self):
        """启动Bokeh服务器"""
        if self._server is None:
            try:
                # 创建应用实例
                test_app = TestBokehApp()

                # 启动服务器
                self._server = Server(
                    {
                        '/test_bokeh': test_app.modify_doc
                    },
                    port=settings.BOKEH_PORT,
                    address=settings.HOST,
                    allow_websocket_origin=[
                        f"{settings.HOST}:{settings.PORT}",
                        f"{settings.HOST}:{settings.BOKEH_PORT}",
                        "localhost:8000",
                        "localhost:5006",
                        "127.0.0.1:8000",
                        "127.0.0.1:5006"
                    ],
                    num_procs=1,  # 限制为单个进程
                    prefix="/bokeh"  # 添加前缀
                )

                # 启动服务器线程
                self._server.start()
                logger.info(
                    f"Bokeh服务器已启动: http://{settings.HOST}:{settings.BOKEH_PORT}/bokeh/test_bokeh")

            except Exception as e:
                logger.error(f"启动Bokeh服务器时出错: {str(e)}")
                self._server = None
                raise

    def stop(self):
        """停止Bokeh服务器"""
        if self._server is not None:
            try:
                self._server.stop()
                self._server = None
                logger.info("Bokeh服务器已停止")
            except Exception as e:
                logger.error(f"停止Bokeh服务器时出错: {str(e)}")
                raise


# 创建全局实例
bokeh_manager = BokehServerManager()
