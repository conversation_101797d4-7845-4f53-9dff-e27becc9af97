from fastapi import Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from typing import Optional
from app.database import get_db
from app.services.file_service import FileService
from app.services.task_service import TaskService
from app.core.config import settings

# 公共依赖
def get_templates(request: Request):
    """获取模板引擎"""
    return request.app.state.templates

# 服务依赖
def get_file_service(db: Session = Depends(get_db)) -> FileService:
    """获取文件服务实例"""
    return FileService(
        raw_dir=settings.RAW_DIR,
        processed_dir=settings.PROCESSED_DIR
    )

def get_task_service(db: Session = Depends(get_db)) -> TaskService:
    """获取任务服务实例"""
    return TaskService(
        raw_dir=settings.RAW_DIR,
        processed_dir=settings.PROCESSED_DIR
    )

# 权限依赖
def verify_api_key(api_key: Optional[str] = None):
    """验证API密钥（如果有）"""
    if not settings.API_KEY_REQUIRED:
        return True
    
    if not api_key or api_key != settings.API_KEY:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的API密钥",
            headers={"WWW-Authenticate": "ApiKey"},
        )
    return True 