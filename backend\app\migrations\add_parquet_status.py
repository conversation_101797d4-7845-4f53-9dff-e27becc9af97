from sqlalchemy import create_engine, text
from ..database import SQLALCHEMY_DATABASE_URL

def migrate():
    """添加 Parquet 相关字段"""
    engine = create_engine(SQLALCHEMY_DATABASE_URL)
    
    with engine.connect() as conn:
        # 添加新字段
        conn.execute(text("""
            ALTER TABLE file_uploads
            ADD COLUMN parquet_status VARCHAR DEFAULT 'pending' NOT NULL;
        """))
        
        conn.execute(text("""
            ALTER TABLE file_uploads
            ADD COLUMN parquet_path VARCHAR;
        """))
        
        conn.execute(text("""
            ALTER TABLE file_uploads
            ADD COLUMN parquet_size FLOAT;
        """))
        
        conn.execute(text("""
            ALTER TABLE file_uploads
            ADD COLUMN conversion_error VARCHAR;
        """))
        
        conn.execute(text("""
            ALTER TABLE file_uploads
            ADD COLUMN conversion_time TIMESTAMP;
        """))
        
        # 创建索引
        conn.execute(text("""
            CREATE INDEX idx_parquet_status
            ON file_uploads (parquet_status);
        """))
        
        conn.commit()

if __name__ == "__main__":
    migrate() 