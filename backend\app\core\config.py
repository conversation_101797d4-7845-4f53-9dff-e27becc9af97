"""
应用配置模块 - 集中管理应用的配置和资源
"""

# 标准库导入
import os
import logging
from contextlib import asynccontextmanager
from pathlib import Path
from typing import Optional, Dict, Any
from logging.handlers import RotatingFileHandler

# 第三方库导入
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from pydantic import Field
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    """应用配置类"""
    # 应用信息
    APP_NAME: str = "文件处理系统"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    
    # 基础路径
    BASE_DIR: Path = Path(__file__).resolve().parent.parent.parent

    # 服务器配置
    HOST: str = "127.0.0.1"
    PORT: int = 8000
    BOKEH_PORT: int = 5006
    BOKEH_URL: Optional[str] = None
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379/0"

    # 资源目录
    TEMPLATES_DIR: Path = Path(BASE_DIR) / "app" / "templates"
    STATIC_DIR: Path = Path(BASE_DIR) / "app" / "static"
    BOKEH_STATIC_DIR: Path = Path(BASE_DIR) / "app" / "static" / "bokeh"

    # 可视化目录
    VISUALIZATION_DIR: Path = Path(BASE_DIR) / "app" / "visualization"
    VISUALIZATION_COMPONENTS_DIR: Path = VISUALIZATION_DIR / "components"
    VISUALIZATION_PAGES_DIR: Path = VISUALIZATION_DIR / "pages"
    VISUALIZATION_UTILS_DIR: Path = VISUALIZATION_DIR / "utils"
    VISUALIZATION_CONFIG_DIR: Path = VISUALIZATION_DIR / "config"
    
    # 数据存储
    DATA_DIR: Path = Path(BASE_DIR) / "data"
    RAW_DATA_DIR: Path = DATA_DIR / "raw"
    PROCESSED_DATA_DIR: Path = DATA_DIR / "processed"
    RAW_DIR: Path = DATA_DIR / "raw"
    PROCESSED_DIR: Path = DATA_DIR / "processed"
    
    # 日志配置
    LOG_DIR: Path = Path(BASE_DIR) / "logs"
    LOG_LEVEL: str = "DEBUG"  # 开发阶段使用DEBUG级别，生产环境可改为INFO
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    LOG_MAX_BYTES: int = 10 * 1024 * 1024  # 10 MB
    LOG_BACKUP_COUNT: int = 5

    # 数据库配置
    DATABASE_URL: str = f"sqlite:///{DATA_DIR}/database.db"
    
    # API配置
    API_PREFIX: str = "/api/v1"
    
    # 处理配置
    MAX_WORKERS: int = 4
    CHUNK_SIZE: int = 10000

    class Config:
        env_file = ".env"
        case_sensitive = True

# 创建全局设置对象
settings = Settings()

# 配置日志系统
def setup_logging():
    """配置日志系统"""
    # 确保日志目录存在
    os.makedirs(settings.LOG_DIR, exist_ok=True)

    # 创建日志文件路径
    log_file = os.path.join(settings.LOG_DIR, "app.log")

    # 配置根日志记录器
    logging.basicConfig(
        level=getattr(logging, settings.LOG_LEVEL),
        format=settings.LOG_FORMAT,
        handlers=[
            # 控制台处理器
            logging.StreamHandler(),
            # 文件处理器 - 使用RotatingFileHandler限制日志文件大小
            RotatingFileHandler(
                log_file,
                maxBytes=settings.LOG_MAX_BYTES,
                backupCount=settings.LOG_BACKUP_COUNT,
                encoding='utf-8',  # 显式指定UTF-8编码
                mode='a'  # 追加模式
            )
        ]
    )

    # 减少某些库的日志级别
    logging.getLogger("uvicorn").setLevel(logging.WARNING)
    logging.getLogger("fastapi").setLevel(logging.WARNING)

    # 创建应用专用的日志记录器
    logger = logging.getLogger(settings.APP_NAME)
    logger.setLevel(getattr(logging, settings.LOG_LEVEL))

    # 添加初始化信息（使用info级别确保显示）
    logger.info("="*50)
    logger.info("日志系统初始化")
    logger.info(f"日志文件路径: {log_file}")
    logger.info(f"日志级别: {settings.LOG_LEVEL}")
    logger.info(f"日志格式: {settings.LOG_FORMAT}")
    logger.info(f"日志文件大小限制: {settings.LOG_MAX_BYTES} bytes")
    logger.info(f"日志备份数量: {settings.LOG_BACKUP_COUNT}")
    logger.info("="*50)

    # 返回配置好的日志记录器
    return logger

# 创建全局日志记录器
logger = setup_logging()

# 确保必要的目录存在
os.makedirs(settings.TEMPLATES_DIR, exist_ok=True)
os.makedirs(settings.STATIC_DIR, exist_ok=True)
os.makedirs(settings.DATA_DIR, exist_ok=True)
os.makedirs(settings.RAW_DIR, exist_ok=True)
os.makedirs(settings.PROCESSED_DIR, exist_ok=True)
os.makedirs(settings.LOG_DIR, exist_ok=True)

# 初始化模板和静态文件
templates = Jinja2Templates(directory=str(settings.TEMPLATES_DIR))
static_files = StaticFiles(directory=str(settings.STATIC_DIR))

# 使用后初始化而不是默认值
# 设置Bokeh URL (必须在settings实例化后设置)
settings.BOKEH_URL = f"http://{settings.HOST}:{settings.BOKEH_PORT}"

# 现在可以安全地使用logger
logger.info(f"可视化目录: {settings.TEMPLATES_DIR}")
logger.info(f"Bokeh服务地址: {settings.BOKEH_URL}")

# 导出必要的变量
__all__ = ['settings', 'templates', 'static_files']

# 确保必要的目录存在
def ensure_directories():
    """确保必要的目录存在"""
    directories = [
        settings.DATA_DIR,
        settings.RAW_DIR,
        settings.PROCESSED_DIR,
        settings.LOG_DIR,
        settings.STATIC_DIR,
        settings.TEMPLATES_DIR,
        settings.BOKEH_STATIC_DIR,
        # 添加可视化相关目录
        settings.VISUALIZATION_DIR,
        settings.VISUALIZATION_COMPONENTS_DIR,
        settings.VISUALIZATION_PAGES_DIR,
        settings.VISUALIZATION_UTILS_DIR,
        settings.VISUALIZATION_CONFIG_DIR
    ]

    for directory in directories:
        try:
            if not directory.exists():
                directory.mkdir(parents=True, exist_ok=True)
                logger.info(f"创建目录: {directory}")
        except Exception as e:
            logger.error(f"创建目录 {directory} 失败: {str(e)}")
            raise

# 自定义过滤器
def register_filters():
    """注册自定义Jinja2过滤器"""
    
    # 格式化数字
    def format_number(value):
        if value is None:
            return "0"
        try:
            if value >= 1_000_000:
                return f"{value/1_000_000:.1f}M"
            elif value >= 1_000:
                return f"{value/1_000:.1f}k"
            else:
                return str(int(value))
        except (ValueError, TypeError):
            return str(value)
    
    # 格式化文件大小
    def format_size(value):
        if value is None:
            return "0 B"
        try:
            value = float(value)
            for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
                if value < 1024:
                    return f"{value:.2f} {unit}"
                value /= 1024
            return f"{value:.2f} PB"
        except (ValueError, TypeError):
            return str(value)

    # 格式化时间
    def format_time(value):
        if value is None:
            return "0s"
        try:
            value = float(value)
            if value < 60:
                return f"{value:.1f}s"
            elif value < 3600:
                return f"{value/60:.1f}m"
            else:
                return f"{value/3600:.1f}h"
        except (ValueError, TypeError):
            return str(value)

    # 注册过滤器
    templates.env.filters["format_number"] = format_number
    templates.env.filters["format_size"] = format_size
    templates.env.filters["format_time"] = format_time


@asynccontextmanager
async def lifespan(app):
    """
    应用生命周期管理
    替代已弃用的on_event方法
    """
    # 启动前的操作
    logger.info(f"应用启动: {settings.APP_NAME} v{settings.APP_VERSION}")
    logger.info(f"API服务地址: http://{settings.HOST}:{settings.PORT}")
    logger.info(f"Bokeh服务地址: {settings.BOKEH_URL}")

    # 确保所有必要的目录存在
    ensure_directories()

    # 注册模板过滤器
    register_filters()

    yield  # 应用运行时

    # 关闭时的操作
    logger.info("应用关闭")
    # 此处可添加清理任务


def setup_app_resources(app):
    """设置应用资源"""
    # 挂载静态文件
    app.mount("/static", static_files, name="static")
    
    # 注册自定义过滤器
    register_filters()
    
    # 设置其他全局配置
    app.state.settings = settings
    app.state.logger = logger

    # 确保目录存在
    ensure_directories()

    # 设置Bokeh URL
    if not settings.BOKEH_URL:
        settings.BOKEH_URL = f"http://{settings.HOST}:{settings.BOKEH_PORT}"

    # 设置其他全局配置
    app.title = settings.APP_NAME
    app.debug = settings.DEBUG
    
    # 记录配置
    logger.info(f"应用配置加载完成: {settings.APP_NAME}")
    logger.info(f"API服务地址: http://{settings.HOST}:{settings.PORT}")
    logger.info(f"Bokeh服务地址: {settings.BOKEH_URL}")
    logger.info(f"API文档地址: http://{settings.HOST}:{settings.PORT}/docs")
    
    # 返回设置供其他模块使用
    return settings 