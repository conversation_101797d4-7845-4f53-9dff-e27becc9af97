'use strict';
/*!
 * Copyright (c) Anaconda, Inc., and Bokeh Contributors
 * All rights reserved.
 * 
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 * 
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * 
 * Redistributions in binary form must reproduce the above copyright notice,
 * this list of conditions and the following disclaimer in the documentation
 * and/or other materials provided with the distribution.
 * 
 * Neither the name of <PERSON><PERSON><PERSON> nor the names of any contributors
 * may be used to endorse or promote products derived from this software
 * without specific prior written permission.
 * 
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENT<PERSON>, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
 * THE POSSIBILITY OF SUCH DAMAGE.
 */
(function(root, factory) {
  factory(root["Bokeh"], "3.6.3");
})(this, function(Bokeh, version) {
  let define;
  return (function(modules, entry, aliases, externals) {
    const bokeh = typeof Bokeh !== "undefined" ? (version != null ? Bokeh[version] : Bokeh) : null;
    if (bokeh != null) {
      return bokeh.register_plugin(modules, entry, aliases);
    } else {
      throw new Error("Cannot find Bokeh" + (version != null ? " " + version : "") + ". You have to load it prior to loading plugins.");
    }
  })
({
546: function _(n,c,f,i,o){i(),n(547)},
547: function _(t,_,r,e,o){e();const a=t(1);o("get_regl",t(548).get_regl),a.__exportStar(t(560),r),a.__exportStar(t(567),r),a.__exportStar(t(568),r),a.__exportStar(t(563),r),a.__exportStar(t(569),r),a.__exportStar(t(571),r),a.__exportStar(t(572),r),a.__exportStar(t(573),r),a.__exportStar(t(575),r),a.__exportStar(t(576),r),a.__exportStar(t(577),r),a.__exportStar(t(578),r),a.__exportStar(t(579),r),a.__exportStar(t(574),r),a.__exportStar(t(562),r),a.__exportStar(t(580),r),a.__exportStar(t(581),r)},
548: function _(t,e,i,_,n){_(),i.get_regl=function(t){null==m&&(m=new b(t));return m};const r=t(1),a=r.__importDefault(t(549)),s=t(550),o=r.__importDefault(t(552)),f=r.__importDefault(t(553)),l=r.__importDefault(t(554)),u=r.__importDefault(t(555)),h=r.__importDefault(t(556)),c=r.__importDefault(t(557)),p=r.__importDefault(t(558)),g=r.__importDefault(t(559));let m=null;class b{constructor(t){this._marker_no_hatch_map=new Map,this._marker_hatch_map=new Map;try{this._regl=(0,a.default)({gl:t,extensions:["ANGLE_instanced_arrays","EXT_blend_minmax"]}),this._regl_available=!0,this._line_geometry=this._regl.buffer({usage:"static",type:"float",data:[[-2,0],[-1,-1],[1,-1],[1,1],[-1,1]]}),this._line_triangles=this._regl.elements({usage:"static",primitive:"triangle fan",data:[0,1,2,3,4]}),this._rect_geometry=this._regl.buffer({usage:"static",type:"float",data:[[-1,-1],[1,-1],[1,1],[-1,1]]}),this._rect_triangles=this._regl.elements({usage:"static",primitive:"triangle fan",data:[0,1,2,3]})}catch(t){this._regl_available=!1}}buffer(t){return this._regl.buffer(t)}clear(t,e){this._viewport={x:0,y:0,width:t,height:e},this._regl.clear({color:[0,0,0,0]})}clear_framebuffer(t){this._regl.clear({color:[0,0,0,0],framebuffer:t})}get framebuffer_and_texture(){const{_regl:t}=this,{_gl:e}=t,i={height:e.drawingBufferHeight,width:e.drawingBufferWidth};return null==this._framebuffer_texture?this._framebuffer_texture=t.texture(i):this._framebuffer_texture(i),null==this._framebuffer&&(this._framebuffer=t.framebuffer({color:this._framebuffer_texture,depth:!1,stencil:!1})),[this._framebuffer,this._framebuffer_texture]}get has_webgl(){return this._regl_available}get scissor(){return this._scissor}set_scissor(t,e,i,_){this._scissor={x:t,y:e,width:i,height:_}}texture(t){return this._regl.texture(t)}get viewport(){return this._viewport}accumulate(){return null==this._accumulate&&(this._accumulate=function(t,e,i){const _={vert:o.default,frag:f.default,attributes:{a_position:{buffer:e,divisor:0}},uniforms:{u_framebuffer_tex:t.prop("framebuffer_tex")},elements:i,blend:{enable:!0,func:{srcRGB:"one",srcAlpha:"one",dstRGB:"one minus src alpha",dstAlpha:"one minus src alpha"}},depth:{enable:!1},scissor:{enable:!0,box:t.prop("scissor")},viewport:t.prop("viewport")};return t(_)}(this._regl,this._rect_geometry,this._rect_triangles)),this._accumulate}dashed_line(){return null==this._dashed_line&&(this._dashed_line=function(t,e,i){const _={vert:`#define DASHED\n${h.default}\n`,frag:`#define DASHED\n${c.default}\n`,attributes:{a_position:{buffer:e,divisor:0},a_point_prev:(t,e)=>e.points.to_attribute_config(e.point_offset),a_point_start:(t,e)=>e.points.to_attribute_config(e.point_offset+2),a_point_end:(t,e)=>e.points.to_attribute_config(e.point_offset+4),a_point_next:(t,e)=>e.points.to_attribute_config(e.point_offset+6),a_show_prev:(t,e)=>e.show.to_attribute_config(e.point_offset/2-e.line_offset),a_show_curr:(t,e)=>e.show.to_attribute_config(e.point_offset/2-e.line_offset+1),a_show_next:(t,e)=>e.show.to_attribute_config(e.point_offset/2-e.line_offset+2),a_linewidth:(t,e)=>e.linewidth.to_attribute_config_nested(e.line_offset,e.nsegments+3),a_line_color:(t,e)=>e.line_color.to_attribute_config_nested(e.line_offset,e.nsegments+3),a_line_cap:(t,e)=>e.line_cap.to_attribute_config_nested(e.line_offset,e.nsegments+3),a_line_join:(t,e)=>e.line_join.to_attribute_config_nested(e.line_offset,e.nsegments+3),a_length_so_far:(t,e)=>e.length_so_far.to_attribute_config(e.point_offset/2-3*e.line_offset),a_dash_tex_info:(t,e)=>e.dash_tex_info.to_attribute_config_nested(e.line_offset,e.nsegments+3),a_dash_scale:(t,e)=>e.dash_scale.to_attribute_config_nested(e.line_offset,e.nsegments+3),a_dash_offset:(t,e)=>e.dash_offset.to_attribute_config_nested(e.line_offset,e.nsegments+3)},uniforms:{u_canvas_size:t.prop("canvas_size"),u_antialias:t.prop("antialias"),u_miter_limit:t.prop("miter_limit"),u_dash_tex:t.prop("dash_tex")},elements:i,instances:t.prop("nsegments"),blend:{enable:!0,equation:"max",func:{srcRGB:1,srcAlpha:1,dstRGB:1,dstAlpha:1}},depth:{enable:!1},framebuffer:t.prop("framebuffer"),scissor:{enable:!0,box:t.prop("scissor")},viewport:t.prop("viewport")};return t(_)}(this._regl,this._line_geometry,this._line_triangles)),this._dashed_line}get_dash(t){return null==this._dash_cache&&(this._dash_cache=new s.DashCache(this._regl)),this._dash_cache.get(t)}image(){return null==this._image&&(this._image=function(t,e,i){const _={vert:l.default,frag:u.default,attributes:{a_position:{buffer:e,divisor:0},a_bounds:(t,e)=>e.bounds.to_attribute_config()},uniforms:{u_canvas_size:t.prop("canvas_size"),u_tex:t.prop("tex"),u_global_alpha:t.prop("global_alpha")},elements:i,blend:{enable:!0,func:{srcRGB:"one",srcAlpha:"one",dstRGB:"one minus src alpha",dstAlpha:"one minus src alpha"}},depth:{enable:!1},scissor:{enable:!0,box:t.prop("scissor")},viewport:t.prop("viewport")};return t(_)}(this._regl,this._rect_geometry,this._rect_triangles)),this._image}marker_no_hatch(t){let e=this._marker_no_hatch_map.get(t);return null==e&&(e=d(this._regl,t),this._marker_no_hatch_map.set(t,e)),e}marker_hatch(t){let e=this._marker_hatch_map.get(t);return null==e&&(e=function(t,e){return d(t,e,["HATCH"],["HATCH"],{a_hatch_pattern:(t,e)=>e.hatch_pattern.to_attribute_config(0,e.nmarkers),a_hatch_scale:(t,e)=>e.hatch_scale.to_attribute_config(0,e.nmarkers),a_hatch_weight:(t,e)=>e.hatch_weight.to_attribute_config(0,e.nmarkers),a_hatch_color:(t,e)=>e.hatch_color.to_attribute_config(0,e.nmarkers)})}(this._regl,t),this._marker_hatch_map.set(t,e)),e}solid_line(){return null==this._solid_line&&(this._solid_line=function(t,e,i){const _={vert:h.default,frag:c.default,attributes:{a_position:{buffer:e,divisor:0},a_point_prev:(t,e)=>e.points.to_attribute_config(e.point_offset),a_point_start:(t,e)=>e.points.to_attribute_config(e.point_offset+2),a_point_end:(t,e)=>e.points.to_attribute_config(e.point_offset+4),a_point_next:(t,e)=>e.points.to_attribute_config(e.point_offset+6),a_show_prev:(t,e)=>e.show.to_attribute_config(e.point_offset/2-e.line_offset),a_show_curr:(t,e)=>e.show.to_attribute_config(e.point_offset/2-e.line_offset+1),a_show_next:(t,e)=>e.show.to_attribute_config(e.point_offset/2-e.line_offset+2),a_linewidth:(t,e)=>e.linewidth.to_attribute_config_nested(e.line_offset,e.nsegments+3),a_line_color:(t,e)=>e.line_color.to_attribute_config_nested(e.line_offset,e.nsegments+3),a_line_cap:(t,e)=>e.line_cap.to_attribute_config_nested(e.line_offset,e.nsegments+3),a_line_join:(t,e)=>e.line_join.to_attribute_config_nested(e.line_offset,e.nsegments+3)},uniforms:{u_canvas_size:t.prop("canvas_size"),u_antialias:t.prop("antialias"),u_miter_limit:t.prop("miter_limit")},elements:i,instances:t.prop("nsegments"),blend:{enable:!0,equation:"max",func:{srcRGB:1,srcAlpha:1,dstRGB:1,dstAlpha:1}},depth:{enable:!1},framebuffer:t.prop("framebuffer"),scissor:{enable:!0,box:t.prop("scissor")},viewport:t.prop("viewport")};return t(_)}(this._regl,this._line_geometry,this._line_triangles)),this._solid_line}}function d(t,e,i=[],_=[],n){const r=i.map((t=>`#define ${t}`)).join("\n"),a=_.map((t=>`#define ${t}`)).join("\n"),s={vert:`${r}\n#define MULTI_MARKER\n#define USE_${e.toUpperCase()}\n${p.default}\n`,frag:`${a}\n#define USE_${e.toUpperCase()}\n${g.default}\n`,attributes:{a_position:{buffer:t.buffer([[-.5,-.5],[-.5,.5],[.5,.5],[.5,-.5]]),divisor:0},a_center:(t,e)=>e.center.to_attribute_config(0,e.nmarkers),a_width:(t,e)=>e.width.to_attribute_config(0,e.nmarkers),a_height:(t,e)=>e.height.to_attribute_config(0,e.nmarkers),a_angle:(t,e)=>e.angle.to_attribute_config(0,e.nmarkers),a_aux:(t,e)=>e.aux.to_attribute_config(0,e.nmarkers),a_linewidth:(t,e)=>e.linewidth.to_attribute_config(0,e.nmarkers),a_line_color:(t,e)=>e.line_color.to_attribute_config(0,e.nmarkers),a_fill_color:(t,e)=>e.fill_color.to_attribute_config(0,e.nmarkers),a_line_cap:(t,e)=>e.line_cap.to_attribute_config(0,e.nmarkers),a_line_join:(t,e)=>e.line_join.to_attribute_config(0,e.nmarkers),a_show:(t,e)=>e.show.to_attribute_config(0,e.nmarkers),...n},uniforms:{u_canvas_size:t.prop("canvas_size"),u_antialias:t.prop("antialias"),u_size_hint:t.prop("size_hint"),u_border_radius:t.prop("border_radius")},count:4,primitive:"triangle fan",instances:t.prop("nmarkers"),blend:{enable:!0,func:{srcRGB:"one",srcAlpha:"one",dstRGB:"one minus src alpha",dstAlpha:"one minus src alpha"}},depth:{enable:!1},scissor:{enable:!0,box:t.prop("scissor")},viewport:t.prop("viewport")};return t(s)}i.ReglWrapper=b,b.__name__="ReglWrapper"},
549: function _(e,t,r,n,a){var i,o;i=this,o=function(){"use strict";var e=function(e){return e instanceof Uint8Array||e instanceof Uint16Array||e instanceof Uint32Array||e instanceof Int8Array||e instanceof Int16Array||e instanceof Int32Array||e instanceof Float32Array||e instanceof Float64Array||e instanceof Uint8ClampedArray},t=function(e,t){for(var r=Object.keys(t),n=0;n<r.length;++n)e[r[n]]=t[r[n]];return e},r="\n";function n(e){var t=new Error("(regl) "+e);throw console.error(t),t}function a(e,t){e||n(t)}function i(e){return e?": "+e:""}function o(e,t){switch(t){case"number":return"number"==typeof e;case"object":return"object"==typeof e;case"string":return"string"==typeof e;case"boolean":return"boolean"==typeof e;case"function":return"function"==typeof e;case"undefined":return void 0===e;case"symbol":return"symbol"==typeof e}}function f(e,t,r){t.indexOf(e)<0&&n("invalid value"+i(r)+". must be one of: "+t)}var u=["gl","canvas","container","attributes","pixelRatio","extensions","optionalExtensions","profile","onDone"];function s(e,t){for(e+="";e.length<t;)e=" "+e;return e}function c(){this.name="unknown",this.lines=[],this.index={},this.hasErrors=!1}function l(e,t){this.number=e,this.line=t,this.errors=[]}function d(e,t,r){this.file=e,this.line=t,this.message=r}function m(){return"unknown"}function p(){return"unknown"}function h(e,t){var r,n=e.split("\n"),a=1,i=0,o={unknown:new c,0:new c};o.unknown.name=o[0].name=t||"unknown",o.unknown.lines.push(new l(0,""));for(var f=0;f<n.length;++f){var u=n[f],s=/^\s*#\s*(\w+)\s+(.+)\s*$/.exec(u);if(s)switch(s[1]){case"line":var d=/(\d+)(\s+\d+)?/.exec(s[2]);d&&(a=0|d[1],d[2]&&((i=0|d[2])in o||(o[i]=new c)));break;case"define":var m=/SHADER_NAME(_B64)?\s+(.*)$/.exec(s[2]);m&&(o[i].name=m[1]?(r=m[2],"undefined"!=typeof atob?atob(r):"base64:"+r):m[2])}o[i].lines.push(new l(a++,u))}return Object.keys(o).forEach((function(e){var t=o[e];t.lines.forEach((function(e){t.index[e.number]=e}))})),o}function b(e){e._commandRef="unknown"}function v(e,t){n(e+" in command "+(t||"unknown"))}function g(e,t,r,n){o(e,t)||v("invalid parameter type"+i(r)+". expected "+t+", got "+typeof e,n||"unknown")}var y=33071,x=32819,w=32820,A=33635,_=34042,k={};function S(e,t){return e===w||e===x||e===A?2:e===_?4:k[e]*t}function O(e){return!(e&e-1||!e)}k[5120]=k[5121]=1,k[5122]=k[5123]=k[36193]=k[A]=k[x]=k[w]=2,k[5124]=k[5125]=k[5126]=k[_]=4;var E=t(a,{optional:function(e){e()},raise:n,commandRaise:v,command:function(e,t,r){e||v(t,r||"unknown")},parameter:function(e,t,r){e in t||n("unknown parameter ("+e+")"+i(r)+". possible values: "+Object.keys(t).join())},commandParameter:function(e,t,r,n){e in t||v("unknown parameter ("+e+")"+i(r)+". possible values: "+Object.keys(t).join(),n||"unknown")},constructor:function(e){Object.keys(e).forEach((function(e){u.indexOf(e)<0&&n('invalid regl constructor argument "'+e+'". must be one of '+u)}))},type:function(e,t,r){o(e,t)||n("invalid parameter type"+i(r)+". expected "+t+", got "+typeof e)},commandType:g,isTypedArray:function(t,r){e(t)||n("invalid parameter type"+i(r)+". must be a typed array")},nni:function(e,t){e>=0&&(0|e)===e||n("invalid parameter type, ("+e+")"+i(t)+". must be a nonnegative integer")},oneOf:f,shaderError:function(e,t,n,i,o){if(!e.getShaderParameter(t,e.COMPILE_STATUS)){var f=e.getShaderInfoLog(t),u=i===e.FRAGMENT_SHADER?"fragment":"vertex";g(n,"string",u+" shader source must be a string",o);var c=h(n,o),l=function(e){var t=[];return e.split("\n").forEach((function(e){if(!(e.length<5)){var r=/^ERROR:\s+(\d+):(\d+):\s*(.*)$/.exec(e);r?t.push(new d(0|r[1],0|r[2],r[3].trim())):e.length>0&&t.push(new d("unknown",0,e))}})),t}(f);!function(e,t){t.forEach((function(t){var r=e[t.file];if(r){var n=r.index[t.line];if(n)return n.errors.push(t),void(r.hasErrors=!0)}e.unknown.hasErrors=!0,e.unknown.lines[0].errors.push(t)}))}(c,l),Object.keys(c).forEach((function(e){var t=c[e];if(t.hasErrors){var n=[""],a=[""];i("file number "+e+": "+t.name+"\n","color:red;text-decoration:underline;font-weight:bold"),t.lines.forEach((function(e){if(e.errors.length>0){i(s(e.number,4)+"|  ","background-color:yellow; font-weight:bold"),i(e.line+r,"color:red; background-color:yellow; font-weight:bold");var t=0;e.errors.forEach((function(n){var a=n.message,o=/^\s*'(.*)'\s*:\s*(.*)$/.exec(a);if(o){var f=o[1];a=o[2],"assign"===f&&(f="="),t=Math.max(e.line.indexOf(f,t),0)}else t=0;i(s("| ",6)),i(s("^^^",t+3)+r,"font-weight:bold"),i(s("| ",6)),i(a+r,"font-weight:bold")})),i(s("| ",6)+r)}else i(s(e.number,4)+"|  "),i(e.line+r,"color:red")})),"undefined"==typeof document||window.chrome?console.log(n.join("")):(a[0]=n.join("%c"),console.log.apply(console,a))}function i(e,t){n.push(e),a.push(t||"")}})),a.raise("Error compiling "+u+" shader, "+c[0].name)}},linkError:function(e,t,n,i,o){if(!e.getProgramParameter(t,e.LINK_STATUS)){var f=e.getProgramInfoLog(t),u=h(n,o),s='Error linking program with vertex shader, "'+h(i,o)[0].name+'", and fragment shader "'+u[0].name+'"';"undefined"!=typeof document?console.log("%c"+s+"\n%c"+f,"color:red;text-decoration:underline;font-weight:bold","color:red"):console.log(s+r+f),a.raise(s)}},callSite:p,saveCommandRef:b,saveDrawInfo:function(e,t,r,n){function a(e){return e?n.id(e):0}function i(e,t){Object.keys(t).forEach((function(t){e[n.id(t)]=!0}))}b(e),e._fragId=a(e.static.frag),e._vertId=a(e.static.vert);var o=e._uniformSet={};i(o,t.static),i(o,t.dynamic);var f=e._attributeSet={};i(f,r.static),i(f,r.dynamic),e._hasCount="count"in e.static||"count"in e.dynamic||"elements"in e.static||"elements"in e.dynamic},framebufferFormat:function(e,t,r){e.texture?f(e.texture._texture.internalformat,t,"unsupported texture format for attachment"):f(e.renderbuffer._renderbuffer.format,r,"unsupported renderbuffer format for attachment")},guessCommand:m,texture2D:function(e,t,r){var n,i=t.width,o=t.height,f=t.channels;a(i>0&&i<=r.maxTextureSize&&o>0&&o<=r.maxTextureSize,"invalid texture shape"),e.wrapS===y&&e.wrapT===y||a(O(i)&&O(o),"incompatible wrap mode for texture, both width and height must be power of 2"),1===t.mipmask?1!==i&&1!==o&&a(9984!==e.minFilter&&9986!==e.minFilter&&9985!==e.minFilter&&9987!==e.minFilter,"min filter requires mipmap"):(a(O(i)&&O(o),"texture must be a square power of 2 to support mipmapping"),a(t.mipmask===(i<<1)-1,"missing or incomplete mipmap data")),5126===t.type&&(r.extensions.indexOf("oes_texture_float_linear")<0&&a(9728===e.minFilter&&9728===e.magFilter,"filter not supported, must enable oes_texture_float_linear"),a(!e.genMipmaps,"mipmap generation not supported with float textures"));var u=t.images;for(n=0;n<16;++n)if(u[n]){var s=i>>n,c=o>>n;a(t.mipmask&1<<n,"missing mipmap data");var l=u[n];if(a(l.width===s&&l.height===c,"invalid shape for mip images"),a(l.format===t.format&&l.internalformat===t.internalformat&&l.type===t.type,"incompatible type for mip image"),l.compressed);else if(l.data){var d=Math.ceil(S(l.type,f)*s/l.unpackAlignment)*l.unpackAlignment;a(l.data.byteLength===d*c,"invalid data for image, buffer size is inconsistent with image format")}else l.element||l.copy}else e.genMipmaps||a(!(t.mipmask&1<<n),"extra mipmap data");t.compressed&&a(!e.genMipmaps,"mipmap generation for compressed images not supported")},textureCube:function(e,t,r,n){var i=e.width,o=e.height,f=e.channels;a(i>0&&i<=n.maxTextureSize&&o>0&&o<=n.maxTextureSize,"invalid texture shape"),a(i===o,"cube map must be square"),a(t.wrapS===y&&t.wrapT===y,"wrap mode not supported by cube map");for(var u=0;u<r.length;++u){var s=r[u];a(s.width===i&&s.height===o,"inconsistent cube map face shape"),t.genMipmaps&&(a(!s.compressed,"can not generate mipmap for compressed textures"),a(1===s.mipmask,"can not specify mipmaps and generate mipmaps"));for(var c=s.images,l=0;l<16;++l){var d=c[l];if(d){var m=i>>l,p=o>>l;a(s.mipmask&1<<l,"missing mipmap data"),a(d.width===m&&d.height===p,"invalid shape for mip images"),a(d.format===e.format&&d.internalformat===e.internalformat&&d.type===e.type,"incompatible type for mip image"),d.compressed||(d.data?a(d.data.byteLength===m*p*Math.max(S(d.type,f),d.unpackAlignment),"invalid data for image, buffer size is inconsistent with image format"):d.element||d.copy)}}}}}),T=0;function D(e,t){this.id=T++,this.type=e,this.data=t}function j(e){return e.replace(/\\/g,"\\\\").replace(/"/g,'\\"')}function C(e){if(0===e.length)return[];var t=e.charAt(0),r=e.charAt(e.length-1);if(e.length>1&&t===r&&('"'===t||"'"===t))return['"'+j(e.substr(1,e.length-2))+'"'];var n=/\[(false|true|null|\d+|'[^']*'|"[^"]*")\]/.exec(e);if(n)return C(e.substr(0,n.index)).concat(C(n[1])).concat(C(e.substr(n.index+n[0].length)));var a=e.split(".");if(1===a.length)return['"'+j(e)+'"'];for(var i=[],o=0;o<a.length;++o)i=i.concat(C(a[o]));return i}function z(e){return"["+C(e).join("][")+"]"}var F={DynamicVariable:D,define:function(e,t){return new D(e,z(t+""))},isDynamic:function(e){return"function"==typeof e&&!e._reglType||e instanceof D},unbox:function e(t,r){return"function"==typeof t?new D(0,t):"number"==typeof t||"boolean"==typeof t?new D(5,t):Array.isArray(t)?new D(6,t.map((function(t,n){return e(t,r+"["+n+"]")}))):t instanceof D?t:void E(!1,"invalid option type in uniform "+r)},accessor:z},V={next:"function"==typeof requestAnimationFrame?function(e){return requestAnimationFrame(e)}:function(e){return setTimeout(e,16)},cancel:"function"==typeof cancelAnimationFrame?function(e){return cancelAnimationFrame(e)}:clearTimeout},B="undefined"!=typeof performance&&performance.now?function(){return performance.now()}:function(){return+new Date};function I(e){return"string"==typeof e?e.split():(E(Array.isArray(e),"invalid extension array"),e)}function P(e){return"string"==typeof e?(E("undefined"!=typeof document,"not supported outside of DOM"),document.querySelector(e)):e}function L(e){var r,n,a,i,o,f=e||{},u={},s=[],c=[],l="undefined"==typeof window?1:window.devicePixelRatio,d=!1,m=function(e){e&&E.raise(e)},p=function(){};if("string"==typeof f?(E("undefined"!=typeof document,"selector queries only supported in DOM enviroments"),r=document.querySelector(f),E(r,"invalid query string for element")):"object"==typeof f?"string"==typeof(o=f).nodeName&&"function"==typeof o.appendChild&&"function"==typeof o.getBoundingClientRect?r=f:function(e){return"function"==typeof e.drawArrays||"function"==typeof e.drawElements}(f)?a=(i=f).canvas:(E.constructor(f),"gl"in f?i=f.gl:"canvas"in f?a=P(f.canvas):"container"in f&&(n=P(f.container)),"attributes"in f&&(u=f.attributes,E.type(u,"object","invalid context attributes")),"extensions"in f&&(s=I(f.extensions)),"optionalExtensions"in f&&(c=I(f.optionalExtensions)),"onDone"in f&&(E.type(f.onDone,"function","invalid or missing onDone callback"),m=f.onDone),"profile"in f&&(d=!!f.profile),"pixelRatio"in f&&(l=+f.pixelRatio,E(l>0,"invalid pixel ratio"))):E.raise("invalid arguments to regl"),r&&("canvas"===r.nodeName.toLowerCase()?a=r:n=r),!i){if(!a){E("undefined"!=typeof document,"must manually specify webgl context outside of DOM environments");var h=function(e,r,n){var a,i=document.createElement("canvas");function o(){var t=window.innerWidth,r=window.innerHeight;if(e!==document.body){var a=i.getBoundingClientRect();t=a.right-a.left,r=a.bottom-a.top}i.width=n*t,i.height=n*r}return t(i.style,{border:0,margin:0,padding:0,top:0,left:0,width:"100%",height:"100%"}),e.appendChild(i),e===document.body&&(i.style.position="absolute",t(e.style,{margin:0,padding:0})),e!==document.body&&"function"==typeof ResizeObserver?(a=new ResizeObserver((function(){setTimeout(o)}))).observe(e):window.addEventListener("resize",o,!1),o(),{canvas:i,onDestroy:function(){a?a.disconnect():window.removeEventListener("resize",o),e.removeChild(i)}}}(n||document.body,0,l);if(!h)return null;a=h.canvas,p=h.onDestroy}void 0===u.premultipliedAlpha&&(u.premultipliedAlpha=!0),i=function(e,t){function r(r){try{return e.getContext(r,t)}catch(e){return null}}return r("webgl")||r("experimental-webgl")||r("webgl-experimental")}(a,u)}return i?{gl:i,canvas:a,container:n,extensions:s,optionalExtensions:c,pixelRatio:l,profile:d,onDone:m,onDestroy:p}:(p(),m("webgl not supported, try upgrading your browser or graphics drivers http://get.webgl.org"),null)}function R(e,t){for(var r=Array(e),n=0;n<e;++n)r[n]=t(n);return r}function M(e){var t,r;return t=(e>65535)<<4,t|=r=((e>>>=t)>255)<<3,t|=r=((e>>>=r)>15)<<2,(t|=r=((e>>>=r)>3)<<1)|(e>>>=r)>>1}function U(){var e=R(8,(function(){return[]}));function t(t){var r=function(e){for(var t=16;t<=1<<28;t*=16)if(e<=t)return t;return 0}(t),n=e[M(r)>>2];return n.length>0?n.pop():new ArrayBuffer(r)}function r(t){e[M(t.byteLength)>>2].push(t)}return{alloc:t,free:r,allocType:function(e,r){var n=null;switch(e){case 5120:n=new Int8Array(t(r),0,r);break;case 5121:n=new Uint8Array(t(r),0,r);break;case 5122:n=new Int16Array(t(2*r),0,r);break;case 5123:n=new Uint16Array(t(2*r),0,r);break;case 5124:n=new Int32Array(t(4*r),0,r);break;case 5125:n=new Uint32Array(t(4*r),0,r);break;case 5126:n=new Float32Array(t(4*r),0,r);break;default:return null}return n.length!==r?n.subarray(0,r):n},freeType:function(e){r(e.buffer)}}}var W=U();W.zero=U();var G=3553,H=6408,N=5126,q=36160;function Q(t){return!!t&&"object"==typeof t&&Array.isArray(t.shape)&&Array.isArray(t.stride)&&"number"==typeof t.offset&&t.shape.length===t.stride.length&&(Array.isArray(t.data)||e(t.data))}var Y=function(e){return Object.keys(e).map((function(t){return e[t]}))},X={shape:function(e){for(var t=[],r=e;r.length;r=r[0])t.push(r.length);return t},flatten:function(e,t,r,n){var a=1;if(t.length)for(var i=0;i<t.length;++i)a*=t[i];else a=0;var o=n||W.allocType(r,a);switch(t.length){case 0:break;case 1:!function(e,t,r){for(var n=0;n<t;++n)r[n]=e[n]}(e,t[0],o);break;case 2:!function(e,t,r,n){for(var a=0,i=0;i<t;++i)for(var o=e[i],f=0;f<r;++f)n[a++]=o[f]}(e,t[0],t[1],o);break;case 3:$(e,t[0],t[1],t[2],o,0);break;default:K(e,t,0,o,0)}return o}};function $(e,t,r,n,a,i){for(var o=i,f=0;f<t;++f)for(var u=e[f],s=0;s<r;++s)for(var c=u[s],l=0;l<n;++l)a[o++]=c[l]}function K(e,t,r,n,a){for(var i=1,o=r+1;o<t.length;++o)i*=t[o];var f=t[r];if(t.length-r==4){var u=t[r+1],s=t[r+2],c=t[r+3];for(o=0;o<f;++o)$(e[o],u,s,c,n,a),a+=i}else for(o=0;o<f;++o)K(e[o],t,r+1,n,a),a+=i}var J={"[object Int8Array]":5120,"[object Int16Array]":5122,"[object Int32Array]":5124,"[object Uint8Array]":5121,"[object Uint8ClampedArray]":5121,"[object Uint16Array]":5123,"[object Uint32Array]":5125,"[object Float32Array]":5126,"[object Float64Array]":5121,"[object ArrayBuffer]":5121},Z={int8:5120,int16:5122,int32:5124,uint8:5121,uint16:5123,uint32:5125,float:5126,float32:5126},ee={dynamic:35048,stream:35040,static:35044},te=X.flatten,re=X.shape,ne=35044,ae=35040,ie=5121,oe=5126,fe=[];function ue(e){return 0|J[Object.prototype.toString.call(e)]}function se(e,t){for(var r=0;r<t.length;++r)e[r]=t[r]}function ce(e,t,r,n,a,i,o){for(var f=0,u=0;u<r;++u)for(var s=0;s<n;++s)e[f++]=t[a*u+i*s+o]}fe[5120]=1,fe[5122]=2,fe[5124]=4,fe[5121]=1,fe[5123]=2,fe[5125]=4,fe[5126]=4;var le={points:0,point:0,lines:1,line:1,triangles:4,triangle:4,"line loop":2,"line strip":3,"triangle strip":5,"triangle fan":6},de=0,me=1,pe=4,he=5120,be=5121,ve=5122,ge=5123,ye=5124,xe=5125,we=34963,Ae=35040,_e=35044,ke=new Float32Array(1),Se=new Uint32Array(ke.buffer),Oe=5123;function Ee(e){for(var t=W.allocType(Oe,e.length),r=0;r<e.length;++r)if(isNaN(e[r]))t[r]=65535;else if(e[r]===1/0)t[r]=31744;else if(e[r]===-1/0)t[r]=64512;else{ke[0]=e[r];var n=Se[0],a=n>>>31<<15,i=(n<<1>>>24)-127,o=n>>13&1023;if(i<-24)t[r]=a;else if(i<-14){var f=-14-i;t[r]=a+(o+1024>>f)}else t[r]=i>15?a+31744:a+(i+15<<10)+o}return t}function Te(t){return Array.isArray(t)||e(t)}var De=function(e){return!(e&e-1||!e)},je=34467,Ce=3553,ze=34067,Fe=34069,Ve=6408,Be=6406,Ie=6407,Pe=6409,Le=6410,Re=32854,Me=32855,Ue=36194,We=32819,Ge=32820,He=33635,Ne=34042,qe=6402,Qe=34041,Ye=35904,Xe=35906,$e=36193,Ke=33776,Je=33777,Ze=33778,et=33779,tt=35986,rt=35987,nt=34798,at=35840,it=35841,ot=35842,ft=35843,ut=36196,st=5121,ct=5123,lt=5125,dt=5126,mt=10242,pt=10243,ht=10497,bt=33071,vt=33648,gt=10240,yt=10241,xt=9728,wt=9729,At=9984,_t=9985,kt=9986,St=9987,Ot=33170,Et=4352,Tt=4353,Dt=4354,jt=34046,Ct=3317,zt=37440,Ft=37441,Vt=37443,Bt=37444,It=33984,Pt=[At,kt,_t,St],Lt=[0,Pe,Le,Ie,Ve],Rt={};function Mt(e){return"[object "+e+"]"}Rt[Pe]=Rt[Be]=Rt[qe]=1,Rt[Qe]=Rt[Le]=2,Rt[Ie]=Rt[Ye]=3,Rt[Ve]=Rt[Xe]=4;var Ut=Mt("HTMLCanvasElement"),Wt=Mt("OffscreenCanvas"),Gt=Mt("CanvasRenderingContext2D"),Ht=Mt("ImageBitmap"),Nt=Mt("HTMLImageElement"),qt=Mt("HTMLVideoElement"),Qt=Object.keys(J).concat([Ut,Wt,Gt,Ht,Nt,qt]),Yt=[];Yt[st]=1,Yt[dt]=4,Yt[$e]=2,Yt[ct]=2,Yt[lt]=4;var Xt=[];function $t(e){return Array.isArray(e)&&(0===e.length||"number"==typeof e[0])}function Kt(e){return!!Array.isArray(e)&&!(0===e.length||!Te(e[0]))}function Jt(e){return Object.prototype.toString.call(e)}function Zt(e){return Jt(e)===Ut}function er(e){return Jt(e)===Wt}function tr(e){if(!e)return!1;var t=Jt(e);return Qt.indexOf(t)>=0||$t(e)||Kt(e)||Q(e)}function rr(e){return 0|J[Object.prototype.toString.call(e)]}function nr(e,t){return W.allocType(e.type===$e?dt:e.type,t)}function ar(e,t){e.type===$e?(e.data=Ee(t),W.freeType(t)):e.data=t}function ir(e,t,r,n,a,i){var o;if(o=void 0!==Xt[e]?Xt[e]:Rt[e]*Yt[t],i&&(o*=6),a){for(var f=0,u=r;u>=1;)f+=o*u*u,u/=2;return f}return o*r*n}function or(r,n,a,i,o,f,u){var s={"don't care":Et,"dont care":Et,nice:Dt,fast:Tt},c={repeat:ht,clamp:bt,mirror:vt},l={nearest:xt,linear:wt},d=t({mipmap:St,"nearest mipmap nearest":At,"linear mipmap nearest":_t,"nearest mipmap linear":kt,"linear mipmap linear":St},l),m={none:0,browser:Bt},p={uint8:st,rgba4:We,rgb565:He,"rgb5 a1":Ge},h={alpha:Be,luminance:Pe,"luminance alpha":Le,rgb:Ie,rgba:Ve,rgba4:Re,"rgb5 a1":Me,rgb565:Ue},b={};n.ext_srgb&&(h.srgb=Ye,h.srgba=Xe),n.oes_texture_float&&(p.float32=p.float=dt),n.oes_texture_half_float&&(p.float16=p["half float"]=$e),n.webgl_depth_texture&&(t(h,{depth:qe,"depth stencil":Qe}),t(p,{uint16:ct,uint32:lt,"depth stencil":Ne})),n.webgl_compressed_texture_s3tc&&t(b,{"rgb s3tc dxt1":Ke,"rgba s3tc dxt1":Je,"rgba s3tc dxt3":Ze,"rgba s3tc dxt5":et}),n.webgl_compressed_texture_atc&&t(b,{"rgb atc":tt,"rgba atc explicit alpha":rt,"rgba atc interpolated alpha":nt}),n.webgl_compressed_texture_pvrtc&&t(b,{"rgb pvrtc 4bppv1":at,"rgb pvrtc 2bppv1":it,"rgba pvrtc 4bppv1":ot,"rgba pvrtc 2bppv1":ft}),n.webgl_compressed_texture_etc1&&(b["rgb etc1"]=ut);var v=Array.prototype.slice.call(r.getParameter(je));Object.keys(b).forEach((function(e){var t=b[e];v.indexOf(t)>=0&&(h[e]=t)}));var g=Object.keys(h);a.textureFormats=g;var y=[];Object.keys(h).forEach((function(e){var t=h[e];y[t]=e}));var x=[];Object.keys(p).forEach((function(e){var t=p[e];x[t]=e}));var w=[];Object.keys(l).forEach((function(e){var t=l[e];w[t]=e}));var A=[];Object.keys(d).forEach((function(e){var t=d[e];A[t]=e}));var _=[];Object.keys(c).forEach((function(e){var t=c[e];_[t]=e}));var k=g.reduce((function(e,t){var r=h[t];return r===Pe||r===Be||r===Pe||r===Le||r===qe||r===Qe||n.ext_srgb&&(r===Ye||r===Xe)?e[r]=r:r===Me||t.indexOf("rgba")>=0?e[r]=Ve:e[r]=Ie,e}),{});function S(){this.internalformat=Ve,this.format=Ve,this.type=st,this.compressed=!1,this.premultiplyAlpha=!1,this.flipY=!1,this.unpackAlignment=1,this.colorSpace=Bt,this.width=0,this.height=0,this.channels=0}function O(e,t){e.internalformat=t.internalformat,e.format=t.format,e.type=t.type,e.compressed=t.compressed,e.premultiplyAlpha=t.premultiplyAlpha,e.flipY=t.flipY,e.unpackAlignment=t.unpackAlignment,e.colorSpace=t.colorSpace,e.width=t.width,e.height=t.height,e.channels=t.channels}function T(e,t){if("object"==typeof t&&t){if("premultiplyAlpha"in t&&(E.type(t.premultiplyAlpha,"boolean","invalid premultiplyAlpha"),e.premultiplyAlpha=t.premultiplyAlpha),"flipY"in t&&(E.type(t.flipY,"boolean","invalid texture flip"),e.flipY=t.flipY),"alignment"in t&&(E.oneOf(t.alignment,[1,2,4,8],"invalid texture unpack alignment"),e.unpackAlignment=t.alignment),"colorSpace"in t&&(E.parameter(t.colorSpace,m,"invalid colorSpace"),e.colorSpace=m[t.colorSpace]),"type"in t){var r=t.type;E(n.oes_texture_float||!("float"===r||"float32"===r),"you must enable the OES_texture_float extension in order to use floating point textures."),E(n.oes_texture_half_float||!("half float"===r||"float16"===r),"you must enable the OES_texture_half_float extension in order to use 16-bit floating point textures."),E(n.webgl_depth_texture||!("uint16"===r||"uint32"===r||"depth stencil"===r),"you must enable the WEBGL_depth_texture extension in order to use depth/stencil textures."),E.parameter(r,p,"invalid texture type"),e.type=p[r]}var i=e.width,o=e.height,f=e.channels,u=!1;"shape"in t?(E(Array.isArray(t.shape)&&t.shape.length>=2,"shape must be an array"),i=t.shape[0],o=t.shape[1],3===t.shape.length&&(f=t.shape[2],E(f>0&&f<=4,"invalid number of channels"),u=!0),E(i>=0&&i<=a.maxTextureSize,"invalid width"),E(o>=0&&o<=a.maxTextureSize,"invalid height")):("radius"in t&&(i=o=t.radius,E(i>=0&&i<=a.maxTextureSize,"invalid radius")),"width"in t&&(i=t.width,E(i>=0&&i<=a.maxTextureSize,"invalid width")),"height"in t&&(o=t.height,E(o>=0&&o<=a.maxTextureSize,"invalid height")),"channels"in t&&(f=t.channels,E(f>0&&f<=4,"invalid number of channels"),u=!0)),e.width=0|i,e.height=0|o,e.channels=0|f;var s=!1;if("format"in t){var c=t.format;E(n.webgl_depth_texture||!("depth"===c||"depth stencil"===c),"you must enable the WEBGL_depth_texture extension in order to use depth/stencil textures."),E.parameter(c,h,"invalid texture format");var l=e.internalformat=h[c];e.format=k[l],c in p&&("type"in t||(e.type=p[c])),c in b&&(e.compressed=!0),s=!0}!u&&s?e.channels=Rt[e.format]:u&&!s?e.channels!==Lt[e.format]&&(e.format=e.internalformat=Lt[e.channels]):s&&u&&E(e.channels===Rt[e.format],"number of channels inconsistent with specified format")}}function D(e){r.pixelStorei(zt,e.flipY),r.pixelStorei(Ft,e.premultiplyAlpha),r.pixelStorei(Vt,e.colorSpace),r.pixelStorei(Ct,e.unpackAlignment)}function j(){S.call(this),this.xOffset=0,this.yOffset=0,this.data=null,this.needsFree=!1,this.element=null,this.needsCopy=!1}function C(t,r){var n=null;if(tr(r)?n=r:r&&(E.type(r,"object","invalid pixel data type"),T(t,r),"x"in r&&(t.xOffset=0|r.x),"y"in r&&(t.yOffset=0|r.y),tr(r.data)&&(n=r.data)),E(!t.compressed||n instanceof Uint8Array,"compressed texture data must be stored in a uint8array"),r.copy){E(!n,"can not specify copy and data field for the same texture");var i=o.viewportWidth,f=o.viewportHeight;t.width=t.width||i-t.xOffset,t.height=t.height||f-t.yOffset,t.needsCopy=!0,E(t.xOffset>=0&&t.xOffset<i&&t.yOffset>=0&&t.yOffset<f&&t.width>0&&t.width<=i&&t.height>0&&t.height<=f,"copy texture read out of bounds")}else if(n){if(e(n))t.channels=t.channels||4,t.data=n,"type"in r||t.type!==st||(t.type=rr(n));else if($t(n))t.channels=t.channels||4,function(e,t){var r=t.length;switch(e.type){case st:case ct:case lt:case dt:var n=W.allocType(e.type,r);n.set(t),e.data=n;break;case $e:e.data=Ee(t);break;default:E.raise("unsupported texture type, must specify a typed array")}}(t,n),t.alignment=1,t.needsFree=!0;else if(Q(n)){var u=n.data;Array.isArray(u)||t.type!==st||(t.type=rr(u));var s,c,l,d,m,p,h=n.shape,b=n.stride;3===h.length?(l=h[2],p=b[2]):(E(2===h.length,"invalid ndarray pixel data, must be 2 or 3D"),l=1,p=1),s=h[0],c=h[1],d=b[0],m=b[1],t.alignment=1,t.width=s,t.height=c,t.channels=l,t.format=t.internalformat=Lt[l],t.needsFree=!0,function(e,t,r,n,a,i){for(var o=e.width,f=e.height,u=e.channels,s=nr(e,o*f*u),c=0,l=0;l<f;++l)for(var d=0;d<o;++d)for(var m=0;m<u;++m)s[c++]=t[r*d+n*l+a*m+i];ar(e,s)}(t,u,d,m,p,n.offset)}else if(Zt(n)||er(n)||Jt(n)===Gt)Zt(n)||er(n)?t.element=n:t.element=n.canvas,t.width=t.element.width,t.height=t.element.height,t.channels=4;else if(function(e){return Jt(e)===Ht}(n))t.element=n,t.width=n.width,t.height=n.height,t.channels=4;else if(function(e){return Jt(e)===Nt}(n))t.element=n,t.width=n.naturalWidth,t.height=n.naturalHeight,t.channels=4;else if(function(e){return Jt(e)===qt}(n))t.element=n,t.width=n.videoWidth,t.height=n.videoHeight,t.channels=4;else if(Kt(n)){var v=t.width||n[0].length,g=t.height||n.length,y=t.channels;y=Te(n[0][0])?y||n[0][0].length:y||1;for(var x=X.shape(n),w=1,A=0;A<x.length;++A)w*=x[A];var _=nr(t,w);X.flatten(n,x,"",_),ar(t,_),t.alignment=1,t.width=v,t.height=g,t.channels=y,t.format=t.internalformat=Lt[y],t.needsFree=!0}}else t.width=t.width||1,t.height=t.height||1,t.channels=t.channels||4;t.type===dt?E(a.extensions.indexOf("oes_texture_float")>=0,"oes_texture_float extension not enabled"):t.type===$e&&E(a.extensions.indexOf("oes_texture_half_float")>=0,"oes_texture_half_float extension not enabled")}function z(e,t,n){var a=e.element,o=e.data,f=e.internalformat,u=e.format,s=e.type,c=e.width,l=e.height;D(e),a?r.texImage2D(t,n,u,u,s,a):e.compressed?r.compressedTexImage2D(t,n,f,c,l,0,o):e.needsCopy?(i(),r.copyTexImage2D(t,n,u,e.xOffset,e.yOffset,c,l,0)):r.texImage2D(t,n,u,c,l,0,u,s,o||null)}function F(e,t,n,a,o){var f=e.element,u=e.data,s=e.internalformat,c=e.format,l=e.type,d=e.width,m=e.height;D(e),f?r.texSubImage2D(t,o,n,a,c,l,f):e.compressed?r.compressedTexSubImage2D(t,o,n,a,s,d,m,u):e.needsCopy?(i(),r.copyTexSubImage2D(t,o,n,a,e.xOffset,e.yOffset,d,m)):r.texSubImage2D(t,o,n,a,d,m,c,l,u)}var V=[];function B(){return V.pop()||new j}function I(e){e.needsFree&&W.freeType(e.data),j.call(e),V.push(e)}function P(){S.call(this),this.genMipmaps=!1,this.mipmapHint=Et,this.mipmask=0,this.images=Array(16)}function L(e,t,r){var n=e.images[0]=B();e.mipmask=1,n.width=e.width=t,n.height=e.height=r,n.channels=e.channels=4}function R(e,t){var r=null;if(tr(t))O(r=e.images[0]=B(),e),C(r,t),e.mipmask=1;else if(T(e,t),Array.isArray(t.mipmap))for(var n=t.mipmap,a=0;a<n.length;++a)O(r=e.images[a]=B(),e),r.width>>=a,r.height>>=a,C(r,n[a]),e.mipmask|=1<<a;else O(r=e.images[0]=B(),e),C(r,t),e.mipmask=1;O(e,e.images[0]),!e.compressed||e.internalformat!==Ke&&e.internalformat!==Je&&e.internalformat!==Ze&&e.internalformat!==et||E(e.width%4==0&&e.height%4==0,"for compressed texture formats, mipmap level 0 must have width and height that are a multiple of 4")}function M(e,t){for(var r=e.images,n=0;n<r.length;++n){if(!r[n])return;z(r[n],t,n)}}var U=[];function G(){var e=U.pop()||new P;S.call(e),e.mipmask=0;for(var t=0;t<16;++t)e.images[t]=null;return e}function H(e){for(var t=e.images,r=0;r<t.length;++r)t[r]&&I(t[r]),t[r]=null;U.push(e)}function N(){this.minFilter=xt,this.magFilter=xt,this.wrapS=bt,this.wrapT=bt,this.anisotropic=1,this.genMipmaps=!1,this.mipmapHint=Et}function q(e,t){if("min"in t){var r=t.min;E.parameter(r,d),e.minFilter=d[r],Pt.indexOf(e.minFilter)>=0&&!("faces"in t)&&(e.genMipmaps=!0)}if("mag"in t){var n=t.mag;E.parameter(n,l),e.magFilter=l[n]}var i=e.wrapS,o=e.wrapT;if("wrap"in t){var f=t.wrap;"string"==typeof f?(E.parameter(f,c),i=o=c[f]):Array.isArray(f)&&(E.parameter(f[0],c),E.parameter(f[1],c),i=c[f[0]],o=c[f[1]])}else{if("wrapS"in t){var u=t.wrapS;E.parameter(u,c),i=c[u]}if("wrapT"in t){var m=t.wrapT;E.parameter(m,c),o=c[m]}}if(e.wrapS=i,e.wrapT=o,"anisotropic"in t){var p=t.anisotropic;E("number"==typeof p&&p>=1&&p<=a.maxAnisotropic,"aniso samples must be between 1 and "),e.anisotropic=t.anisotropic}if("mipmap"in t){var h=!1;switch(typeof t.mipmap){case"string":E.parameter(t.mipmap,s,"invalid mipmap hint"),e.mipmapHint=s[t.mipmap],e.genMipmaps=!0,h=!0;break;case"boolean":h=e.genMipmaps=t.mipmap;break;case"object":E(Array.isArray(t.mipmap),"invalid mipmap type"),e.genMipmaps=!1,h=!0;break;default:E.raise("invalid mipmap type")}h&&!("min"in t)&&(e.minFilter=At)}}function $(e,t){r.texParameteri(t,yt,e.minFilter),r.texParameteri(t,gt,e.magFilter),r.texParameteri(t,mt,e.wrapS),r.texParameteri(t,pt,e.wrapT),n.ext_texture_filter_anisotropic&&r.texParameteri(t,jt,e.anisotropic),e.genMipmaps&&(r.hint(Ot,e.mipmapHint),r.generateMipmap(t))}var K=0,J={},Z=a.maxTextureUnits,ee=Array(Z).map((function(){return null}));function te(e){S.call(this),this.mipmask=0,this.internalformat=Ve,this.id=K++,this.refCount=1,this.target=e,this.texture=r.createTexture(),this.unit=-1,this.bindCount=0,this.texInfo=new N,u.profile&&(this.stats={size:0})}function re(e){r.activeTexture(It),r.bindTexture(e.target,e.texture)}function ne(){var e=ee[0];e?r.bindTexture(e.target,e.texture):r.bindTexture(Ce,null)}function ae(e){var t=e.texture;E(t,"must not double destroy texture");var n=e.unit,a=e.target;n>=0&&(r.activeTexture(It+n),r.bindTexture(a,null),ee[n]=null),r.deleteTexture(t),e.texture=null,e.params=null,e.pixels=null,e.refCount=0,delete J[e.id],f.textureCount--}return t(te.prototype,{bind:function(){var e=this;e.bindCount+=1;var t=e.unit;if(t<0){for(var n=0;n<Z;++n){var a=ee[n];if(a){if(a.bindCount>0)continue;a.unit=-1}ee[n]=e,t=n;break}t>=Z&&E.raise("insufficient number of texture units"),u.profile&&f.maxTextureUnits<t+1&&(f.maxTextureUnits=t+1),e.unit=t,r.activeTexture(It+t),r.bindTexture(e.target,e.texture)}return t},unbind:function(){this.bindCount-=1},decRef:function(){--this.refCount<=0&&ae(this)}}),u.profile&&(f.getTotalTextureSize=function(){var e=0;return Object.keys(J).forEach((function(t){e+=J[t].stats.size})),e}),{create2D:function(e,t){var n=new te(Ce);function i(e,t){var r=n.texInfo;N.call(r);var o=G();return"number"==typeof e?L(o,0|e,"number"==typeof t?0|t:0|e):e?(E.type(e,"object","invalid arguments to regl.texture"),q(r,e),R(o,e)):L(o,1,1),r.genMipmaps&&(o.mipmask=(o.width<<1)-1),n.mipmask=o.mipmask,O(n,o),E.texture2D(r,o,a),n.internalformat=o.internalformat,i.width=o.width,i.height=o.height,re(n),M(o,Ce),$(r,Ce),ne(),H(o),u.profile&&(n.stats.size=ir(n.internalformat,n.type,o.width,o.height,r.genMipmaps,!1)),i.format=y[n.internalformat],i.type=x[n.type],i.mag=w[r.magFilter],i.min=A[r.minFilter],i.wrapS=_[r.wrapS],i.wrapT=_[r.wrapT],i}return J[n.id]=n,f.textureCount++,i(e,t),i.subimage=function(e,t,r,a){E(!!e,"must specify image data");var o=0|t,f=0|r,u=0|a,s=B();return O(s,n),s.width=0,s.height=0,C(s,e),s.width=s.width||(n.width>>u)-o,s.height=s.height||(n.height>>u)-f,E(n.type===s.type&&n.format===s.format&&n.internalformat===s.internalformat,"incompatible format for texture.subimage"),E(o>=0&&f>=0&&o+s.width<=n.width&&f+s.height<=n.height,"texture.subimage write out of bounds"),E(n.mipmask&1<<u,"missing mipmap data"),E(s.data||s.element||s.needsCopy,"missing image data"),re(n),F(s,Ce,o,f,u),ne(),I(s),i},i.resize=function(e,t){var a=0|e,o=0|t||a;if(a===n.width&&o===n.height)return i;i.width=n.width=a,i.height=n.height=o,re(n);for(var f=0;n.mipmask>>f;++f){var s=a>>f,c=o>>f;if(!s||!c)break;r.texImage2D(Ce,f,n.format,s,c,0,n.format,n.type,null)}return ne(),u.profile&&(n.stats.size=ir(n.internalformat,n.type,a,o,!1,!1)),i},i._reglType="texture2d",i._texture=n,u.profile&&(i.stats=n.stats),i.destroy=function(){n.decRef()},i},createCube:function(e,t,n,i,o,s){var c=new te(ze);J[c.id]=c,f.cubeCount++;var l=new Array(6);function d(e,t,r,n,i,o){var f,s=c.texInfo;for(N.call(s),f=0;f<6;++f)l[f]=G();if("number"!=typeof e&&e)if("object"==typeof e)if(t)R(l[0],e),R(l[1],t),R(l[2],r),R(l[3],n),R(l[4],i),R(l[5],o);else if(q(s,e),T(c,e),"faces"in e){var m=e.faces;for(E(Array.isArray(m)&&6===m.length,"cube faces must be a length 6 array"),f=0;f<6;++f)E("object"==typeof m[f]&&!!m[f],"invalid input for cube map face"),O(l[f],c),R(l[f],m[f])}else for(f=0;f<6;++f)R(l[f],e);else E.raise("invalid arguments to cube map");else{var p=0|e||1;for(f=0;f<6;++f)L(l[f],p,p)}for(O(c,l[0]),E.optional((function(){a.npotTextureCube||E(De(c.width)&&De(c.height),"your browser does not support non power or two texture dimensions")})),s.genMipmaps?c.mipmask=(l[0].width<<1)-1:c.mipmask=l[0].mipmask,E.textureCube(c,s,l,a),c.internalformat=l[0].internalformat,d.width=l[0].width,d.height=l[0].height,re(c),f=0;f<6;++f)M(l[f],Fe+f);for($(s,ze),ne(),u.profile&&(c.stats.size=ir(c.internalformat,c.type,d.width,d.height,s.genMipmaps,!0)),d.format=y[c.internalformat],d.type=x[c.type],d.mag=w[s.magFilter],d.min=A[s.minFilter],d.wrapS=_[s.wrapS],d.wrapT=_[s.wrapT],f=0;f<6;++f)H(l[f]);return d}return d(e,t,n,i,o,s),d.subimage=function(e,t,r,n,a){E(!!t,"must specify image data"),E("number"==typeof e&&e===(0|e)&&e>=0&&e<6,"invalid face");var i=0|r,o=0|n,f=0|a,u=B();return O(u,c),u.width=0,u.height=0,C(u,t),u.width=u.width||(c.width>>f)-i,u.height=u.height||(c.height>>f)-o,E(c.type===u.type&&c.format===u.format&&c.internalformat===u.internalformat,"incompatible format for texture.subimage"),E(i>=0&&o>=0&&i+u.width<=c.width&&o+u.height<=c.height,"texture.subimage write out of bounds"),E(c.mipmask&1<<f,"missing mipmap data"),E(u.data||u.element||u.needsCopy,"missing image data"),re(c),F(u,Fe+e,i,o,f),ne(),I(u),d},d.resize=function(e){var t=0|e;if(t!==c.width){d.width=c.width=t,d.height=c.height=t,re(c);for(var n=0;n<6;++n)for(var a=0;c.mipmask>>a;++a)r.texImage2D(Fe+n,a,c.format,t>>a,t>>a,0,c.format,c.type,null);return ne(),u.profile&&(c.stats.size=ir(c.internalformat,c.type,d.width,d.height,!1,!0)),d}},d._reglType="textureCube",d._texture=c,u.profile&&(d.stats=c.stats),d.destroy=function(){c.decRef()},d},clear:function(){for(var e=0;e<Z;++e)r.activeTexture(It+e),r.bindTexture(Ce,null),ee[e]=null;Y(J).forEach(ae),f.cubeCount=0,f.textureCount=0},getTexture:function(e){return null},restore:function(){for(var e=0;e<Z;++e){var t=ee[e];t&&(t.bindCount=0,t.unit=-1,ee[e]=null)}Y(J).forEach((function(e){e.texture=r.createTexture(),r.bindTexture(e.target,e.texture);for(var t=0;t<32;++t)if(e.mipmask&1<<t)if(e.target===Ce)r.texImage2D(Ce,t,e.internalformat,e.width>>t,e.height>>t,0,e.internalformat,e.type,null);else for(var n=0;n<6;++n)r.texImage2D(Fe+n,t,e.internalformat,e.width>>t,e.height>>t,0,e.internalformat,e.type,null);$(e.texInfo,e.target)}))},refresh:function(){for(var e=0;e<Z;++e){var t=ee[e];t&&(t.bindCount=0,t.unit=-1,ee[e]=null),r.activeTexture(It+e),r.bindTexture(Ce,null),r.bindTexture(ze,null)}}}}Xt[Re]=2,Xt[Me]=2,Xt[Ue]=2,Xt[Qe]=4,Xt[Ke]=.5,Xt[Je]=.5,Xt[Ze]=1,Xt[et]=1,Xt[tt]=.5,Xt[rt]=1,Xt[nt]=1,Xt[at]=.5,Xt[it]=.25,Xt[ot]=.5,Xt[ft]=.25,Xt[ut]=.5;var fr=36161,ur=32854,sr=[];function cr(e,t,r){return sr[e]*t*r}sr[32854]=2,sr[32855]=2,sr[36194]=2,sr[33189]=2,sr[36168]=1,sr[34041]=4,sr[35907]=4,sr[34836]=16,sr[34842]=8,sr[34843]=6;var lr=36160,dr=36161,mr=3553,pr=34069,hr=36064,br=36096,vr=36128,gr=33306,yr=36053,xr=6402,wr=[6407,6408],Ar=[];Ar[6408]=4,Ar[6407]=3;var _r=[];_r[5121]=1,_r[5126]=4,_r[36193]=2;var kr=33189,Sr=36168,Or=34041,Er=[32854,32855,36194,35907,34842,34843,34836],Tr={};Tr[yr]="complete",Tr[36054]="incomplete attachment",Tr[36057]="incomplete dimensions",Tr[36055]="incomplete, missing attachment",Tr[36061]="unsupported";var Dr=5126,jr=34962,Cr=34963,zr=["attributes","elements","offset","count","primitive","instances"];function Fr(){this.state=0,this.x=0,this.y=0,this.z=0,this.w=0,this.buffer=null,this.size=0,this.normalized=!1,this.type=Dr,this.offset=0,this.stride=0,this.divisor=0}var Vr=35632,Br=35633,Ir=35718,Pr=35721,Lr=6408,Rr=5121,Mr=3333,Ur=5126;function Wr(t,r,n,a,i,o,f){function u(u){var s;null===r.next?(E(i.preserveDrawingBuffer,'you must create a webgl context with "preserveDrawingBuffer":true in order to read pixels from the drawing buffer'),s=Rr):(E(null!==r.next.colorAttachments[0].texture,"You cannot read from a renderbuffer"),s=r.next.colorAttachments[0].texture._texture.type,E.optional((function(){o.oes_texture_float?(E(s===Rr||s===Ur,"Reading from a framebuffer is only allowed for the types 'uint8' and 'float'"),s===Ur&&E(f.readFloat,"Reading 'float' values is not permitted in your browser. For a fallback, please see: https://www.npmjs.com/package/glsl-read-float")):E(s===Rr,"Reading from a framebuffer is only allowed for the type 'uint8'")})));var c=0,l=0,d=a.framebufferWidth,m=a.framebufferHeight,p=null;e(u)?p=u:u&&(E.type(u,"object","invalid arguments to regl.read()"),c=0|u.x,l=0|u.y,E(c>=0&&c<a.framebufferWidth,"invalid x offset for regl.read"),E(l>=0&&l<a.framebufferHeight,"invalid y offset for regl.read"),d=0|(u.width||a.framebufferWidth-c),m=0|(u.height||a.framebufferHeight-l),p=u.data||null),p&&(s===Rr?E(p instanceof Uint8Array,"buffer must be 'Uint8Array' when reading from a framebuffer of type 'uint8'"):s===Ur&&E(p instanceof Float32Array,"buffer must be 'Float32Array' when reading from a framebuffer of type 'float'")),E(d>0&&d+c<=a.framebufferWidth,"invalid width for read pixels"),E(m>0&&m+l<=a.framebufferHeight,"invalid height for read pixels"),n();var h=d*m*4;return p||(s===Rr?p=new Uint8Array(h):s===Ur&&(p=p||new Float32Array(h))),E.isTypedArray(p,"data buffer for regl.read() must be a typedarray"),E(p.byteLength>=h,"data buffer for regl.read() too small"),t.pixelStorei(Mr,4),t.readPixels(c,l,d,m,Lr,s,p),p}return function(e){return e&&"framebuffer"in e?function(e){var t;return r.setFBO({framebuffer:e.framebuffer},(function(){t=u(e)})),t}(e):u(e)}}function Gr(e){return Array.prototype.slice.call(e)}function Hr(e){return Gr(e).join("")}var Nr="xyzw".split(""),qr=5121,Qr=1,Yr=2,Xr=0,$r=1,Kr=2,Jr=3,Zr=4,en=5,tn=6,rn="dither",nn="blend.enable",an="blend.color",on="blend.equation",fn="blend.func",un="depth.enable",sn="depth.func",cn="depth.range",ln="depth.mask",dn="colorMask",mn="cull.enable",pn="cull.face",hn="frontFace",bn="lineWidth",vn="polygonOffset.enable",gn="polygonOffset.offset",yn="sample.alpha",xn="sample.enable",wn="sample.coverage",An="stencil.enable",_n="stencil.mask",kn="stencil.func",Sn="stencil.opFront",On="stencil.opBack",En="scissor.enable",Tn="scissor.box",Dn="viewport",jn="profile",Cn="framebuffer",zn="vert",Fn="frag",Vn="elements",Bn="primitive",In="count",Pn="offset",Ln="instances",Rn="vao",Mn="Width",Un="Height",Wn=Cn+Mn,Gn=Cn+Un,Hn=Dn+Mn,Nn=Dn+Un,qn="drawingBuffer",Qn=qn+Mn,Yn=qn+Un,Xn=[fn,on,kn,Sn,On,wn,Dn,Tn,gn],$n=34962,Kn=34963,Jn=3553,Zn=34067,ea=2884,ta=3042,ra=3024,na=2960,aa=2929,ia=3089,oa=32823,fa=32926,ua=32928,sa=5126,ca=35664,la=35665,da=35666,ma=5124,pa=35667,ha=35668,ba=35669,va=35670,ga=35671,ya=35672,xa=35673,wa=35674,Aa=35675,_a=35676,ka=35678,Sa=35680,Oa=4,Ea=1028,Ta=1029,Da=2304,ja=2305,Ca=32775,za=32776,Fa=519,Va=7680,Ba=0,Ia=1,Pa=32774,La=513,Ra=36160,Ma=36064,Ua={0:0,1:1,zero:0,one:1,"src color":768,"one minus src color":769,"src alpha":770,"one minus src alpha":771,"dst color":774,"one minus dst color":775,"dst alpha":772,"one minus dst alpha":773,"constant color":32769,"one minus constant color":32770,"constant alpha":32771,"one minus constant alpha":32772,"src alpha saturate":776},Wa=["constant color, constant alpha","one minus constant color, constant alpha","constant color, one minus constant alpha","one minus constant color, one minus constant alpha","constant alpha, constant color","constant alpha, one minus constant color","one minus constant alpha, constant color","one minus constant alpha, one minus constant color"],Ga={never:512,less:513,"<":513,equal:514,"=":514,"==":514,"===":514,lequal:515,"<=":515,greater:516,">":516,notequal:517,"!=":517,"!==":517,gequal:518,">=":518,always:519},Ha={0:0,zero:0,keep:7680,replace:7681,increment:7682,decrement:7683,"increment wrap":34055,"decrement wrap":34056,invert:5386},Na={frag:35632,vert:35633},qa={cw:Da,ccw:ja};function Qa(t){return Array.isArray(t)||e(t)||Q(t)}function Ya(e){return e.sort((function(e,t){return e===Dn?-1:t===Dn?1:e<t?-1:1}))}function Xa(e,t,r,n){this.thisDep=e,this.contextDep=t,this.propDep=r,this.append=n}function $a(e){return e&&!(e.thisDep||e.contextDep||e.propDep)}function Ka(e){return new Xa(!1,!1,!1,e)}function Ja(e,t){var r=e.type;if(r===Xr){var n=e.data.length;return new Xa(!0,n>=1,n>=2,t)}if(r===Zr){var a=e.data;return new Xa(a.thisDep,a.contextDep,a.propDep,t)}if(r===en)return new Xa(!1,!1,!1,t);if(r===tn){for(var i=!1,o=!1,f=!1,u=0;u<e.data.length;++u){var s=e.data[u];if(s.type===$r)f=!0;else if(s.type===Kr)o=!0;else if(s.type===Jr)i=!0;else if(s.type===Xr){i=!0;var c=s.data;c>=1&&(o=!0),c>=2&&(f=!0)}else s.type===Zr&&(i=i||s.data.thisDep,o=o||s.data.contextDep,f=f||s.data.propDep)}return new Xa(i,o,f,t)}return new Xa(r===Jr,r===Kr,r===$r,t)}var Za=new Xa(!1,!1,!1,(function(){}));function ei(e,r,n,a,i,o,f,u,s,c,l,d,m,p,h){var b=c.Record,v={add:32774,subtract:32778,"reverse subtract":32779};n.ext_blend_minmax&&(v.min=Ca,v.max=za);var g=n.angle_instanced_arrays,y=n.webgl_draw_buffers,x=n.oes_vertex_array_object,w={dirty:!0,profile:h.profile},A={},_=[],k={},S={};function O(e){return e.replace(".","_")}function T(e,t,r){var n=O(e);_.push(e),A[n]=w[n]=!!r,k[n]=t}function D(e,t,r){var n=O(e);_.push(e),Array.isArray(r)?(w[n]=r.slice(),A[n]=r.slice()):w[n]=A[n]=r,S[n]=t}T(rn,ra),T(nn,ta),D(an,"blendColor",[0,0,0,0]),D(on,"blendEquationSeparate",[Pa,Pa]),D(fn,"blendFuncSeparate",[Ia,Ba,Ia,Ba]),T(un,aa,!0),D(sn,"depthFunc",La),D(cn,"depthRange",[0,1]),D(ln,"depthMask",!0),D(dn,dn,[!0,!0,!0,!0]),T(mn,ea),D(pn,"cullFace",Ta),D(hn,hn,ja),D(bn,bn,1),T(vn,oa),D(gn,"polygonOffset",[0,0]),T(yn,fa),T(xn,ua),D(wn,"sampleCoverage",[1,!1]),T(An,na),D(_n,"stencilMask",-1),D(kn,"stencilFunc",[Fa,0,-1]),D(Sn,"stencilOpSeparate",[Ea,Va,Va,Va]),D(On,"stencilOpSeparate",[Ta,Va,Va,Va]),T(En,ia),D(Tn,"scissor",[0,0,e.drawingBufferWidth,e.drawingBufferHeight]),D(Dn,Dn,[0,0,e.drawingBufferWidth,e.drawingBufferHeight]);var j={gl:e,context:m,strings:r,next:A,current:w,draw:d,elements:o,buffer:i,shader:l,attributes:c.state,vao:c,uniforms:s,framebuffer:u,extensions:n,timer:p,isBufferArgs:Qa},C={primTypes:le,compareFuncs:Ga,blendFuncs:Ua,blendEquations:v,stencilOps:Ha,glTypes:Z,orientationType:qa};E.optional((function(){j.isArrayLike=Te})),y&&(C.backBuffer=[Ta],C.drawBuffer=R(a.maxDrawbuffers,(function(e){return 0===e?[0]:R(e,(function(e){return Ma+e}))})));var z=0;function V(){var e=function(){var e=0,r=[],n=[];function a(){var r=[],n=[];return t((function(){r.push.apply(r,Gr(arguments))}),{def:function(){var t="v"+e++;return n.push(t),arguments.length>0&&(r.push(t,"="),r.push.apply(r,Gr(arguments)),r.push(";")),t},toString:function(){return Hr([n.length>0?"var "+n.join(",")+";":"",Hr(r)])}})}function i(){var e=a(),r=a(),n=e.toString,i=r.toString;function o(t,n){r(t,n,"=",e.def(t,n),";")}return t((function(){e.apply(e,Gr(arguments))}),{def:e.def,entry:e,exit:r,save:o,set:function(t,r,n){o(t,r),e(t,r,"=",n,";")},toString:function(){return n()+i()}})}var o=a(),f={};return{global:o,link:function(t){for(var a=0;a<n.length;++a)if(n[a]===t)return r[a];var i="g"+e++;return r.push(i),n.push(t),i},block:a,proc:function(e,r){var n=[];function a(){var e="a"+n.length;return n.push(e),e}r=r||0;for(var o=0;o<r;++o)a();var u=i(),s=u.toString;return f[e]=t(u,{arg:a,toString:function(){return Hr(["function(",n.join(),"){",s(),"}"])}})},scope:i,cond:function(){var e=Hr(arguments),r=i(),n=i(),a=r.toString,o=n.toString;return t(r,{then:function(){return r.apply(r,Gr(arguments)),this},else:function(){return n.apply(n,Gr(arguments)),this},toString:function(){var t=o();return t&&(t="else{"+t+"}"),Hr(["if(",e,"){",a(),"}",t])}})},compile:function(){var e=['"use strict";',o,"return {"];Object.keys(f).forEach((function(t){e.push('"',t,'":',f[t].toString(),",")})),e.push("}");var t=Hr(e).replace(/;/g,";\n").replace(/}/g,"}\n").replace(/{/g,"{\n");return Function.apply(null,r.concat(t)).apply(null,n)}}}(),n=e.link,a=e.global;e.id=z++,e.batchId="0";var i=n(j),o=e.shared={props:"a0"};Object.keys(j).forEach((function(e){o[e]=a.def(i,".",e)})),E.optional((function(){e.CHECK=n(E),e.commandStr=E.guessCommand(),e.command=n(e.commandStr),e.assert=function(e,t,r){e("if(!(",t,"))",this.CHECK,".commandRaise(",n(r),",",this.command,");")},C.invalidBlendCombinations=Wa}));var f=e.next={},u=e.current={};Object.keys(S).forEach((function(e){Array.isArray(w[e])&&(f[e]=a.def(o.next,".",e),u[e]=a.def(o.current,".",e))}));var s=e.constants={};Object.keys(C).forEach((function(e){s[e]=a.def(JSON.stringify(C[e]))})),e.invoke=function(t,r){switch(r.type){case Xr:var a=["this",o.context,o.props,e.batchId];return t.def(n(r.data),".call(",a.slice(0,Math.max(r.data.length+1,4)),")");case $r:return t.def(o.props,r.data);case Kr:return t.def(o.context,r.data);case Jr:return t.def("this",r.data);case Zr:return r.data.append(e,t),r.data.ref;case en:return r.data.toString();case tn:return r.data.map((function(r){return e.invoke(t,r)}))}},e.attribCache={};var l={};return e.scopeAttrib=function(e){var t=r.id(e);if(t in l)return l[t];var a=c.scope[t];return a||(a=c.scope[t]=new b),l[t]=n(a)},e}function B(e,t,f,s,d){var m=e.static,p=e.dynamic;E.optional((function(){var e=[Cn,zn,Fn,Vn,Bn,Pn,In,Ln,jn,Rn].concat(_);function t(t){Object.keys(t).forEach((function(t){E.command(e.indexOf(t)>=0,'unknown parameter "'+t+'"',d.commandStr)}))}t(m),t(p)}));var h=function(e,t){var r=e.static;if("string"==typeof r[Fn]&&"string"==typeof r[zn]){if(Object.keys(t.dynamic).length>0)return null;var n=t.static,a=Object.keys(n);if(a.length>0&&"number"==typeof n[a[0]]){for(var i=[],o=0;o<a.length;++o)E("number"==typeof n[a[o]],"must specify all vertex attribute locations when using vaos"),i.push([0|n[a[o]],a[o]]);return i}}return null}(e,t),y=function(e,t){var r=e.static,n=e.dynamic;if(Cn in r){var a=r[Cn];return a?(a=u.getFramebuffer(a),E.command(a,"invalid framebuffer object"),Ka((function(e,t){var r=e.link(a),n=e.shared;t.set(n.framebuffer,".next",r);var i=n.context;return t.set(i,"."+Wn,r+".width"),t.set(i,"."+Gn,r+".height"),r}))):Ka((function(e,t){var r=e.shared;t.set(r.framebuffer,".next","null");var n=r.context;return t.set(n,"."+Wn,n+"."+Qn),t.set(n,"."+Gn,n+"."+Yn),"null"}))}if(Cn in n){var i=n[Cn];return Ja(i,(function(e,t){var r=e.invoke(t,i),n=e.shared,a=n.framebuffer,o=t.def(a,".getFramebuffer(",r,")");E.optional((function(){e.assert(t,"!"+r+"||"+o,"invalid framebuffer object")})),t.set(a,".next",o);var f=n.context;return t.set(f,"."+Wn,o+"?"+o+".width:"+f+"."+Qn),t.set(f,"."+Gn,o+"?"+o+".height:"+f+"."+Yn),o}))}return null}(e),x=function(e,t,r){var n=e.static,a=e.dynamic;function i(e){if(e in n){var i=n[e];E.commandType(i,"object","invalid "+e,r.commandStr);var o,f,u=!0,s=0|i.x,c=0|i.y;return"width"in i?(o=0|i.width,E.command(o>=0,"invalid "+e,r.commandStr)):u=!1,"height"in i?(f=0|i.height,E.command(f>=0,"invalid "+e,r.commandStr)):u=!1,new Xa(!u&&t&&t.thisDep,!u&&t&&t.contextDep,!u&&t&&t.propDep,(function(e,t){var r=e.shared.context,n=o;"width"in i||(n=t.def(r,".",Wn,"-",s));var a=f;return"height"in i||(a=t.def(r,".",Gn,"-",c)),[s,c,n,a]}))}if(e in a){var l=a[e],d=Ja(l,(function(t,r){var n=t.invoke(r,l);E.optional((function(){t.assert(r,n+"&&typeof "+n+'==="object"',"invalid "+e)}));var a=t.shared.context,i=r.def(n,".x|0"),o=r.def(n,".y|0"),f=r.def('"width" in ',n,"?",n,".width|0:","(",a,".",Wn,"-",i,")"),u=r.def('"height" in ',n,"?",n,".height|0:","(",a,".",Gn,"-",o,")");return E.optional((function(){t.assert(r,f+">=0&&"+u+">=0","invalid "+e)})),[i,o,f,u]}));return t&&(d.thisDep=d.thisDep||t.thisDep,d.contextDep=d.contextDep||t.contextDep,d.propDep=d.propDep||t.propDep),d}return t?new Xa(t.thisDep,t.contextDep,t.propDep,(function(e,t){var r=e.shared.context;return[0,0,t.def(r,".",Wn),t.def(r,".",Gn)]})):null}var o=i(Dn);if(o){var f=o;o=new Xa(o.thisDep,o.contextDep,o.propDep,(function(e,t){var r=f.append(e,t),n=e.shared.context;return t.set(n,"."+Hn,r[2]),t.set(n,"."+Nn,r[3]),r}))}return{viewport:o,scissor_box:i(Tn)}}(e,y,d),w=function(e,t){var r=e.static,n=e.dynamic,a={},i=!1,f=function(){if(Rn in r){var e=r[Rn];return null!==e&&null===c.getVAO(e)&&(e=c.createVAO(e)),i=!0,a.vao=e,Ka((function(t){var r=c.getVAO(e);return r?t.link(r):"null"}))}if(Rn in n){i=!0;var t=n[Rn];return Ja(t,(function(e,r){var n=e.invoke(r,t);return r.def(e.shared.vao+".getVAO("+n+")")}))}return null}(),u=!1,s=function(){if(Vn in r){var e=r[Vn];if(a.elements=e,Qa(e)){var s=a.elements=o.create(e,!0);e=o.getElements(s),u=!0}else e&&(e=o.getElements(e),u=!0,E.command(e,"invalid elements",t.commandStr));var c=Ka((function(t,r){if(e){var n=t.link(e);return t.ELEMENTS=n,n}return t.ELEMENTS=null,null}));return c.value=e,c}if(Vn in n){u=!0;var l=n[Vn];return Ja(l,(function(e,t){var r=e.shared,n=r.isBufferArgs,a=r.elements,i=e.invoke(t,l),o=t.def("null"),f=t.def(n,"(",i,")"),u=e.cond(f).then(o,"=",a,".createStream(",i,");").else(o,"=",a,".getElements(",i,");");return E.optional((function(){e.assert(u.else,"!"+i+"||"+o,"invalid elements")})),t.entry(u),t.exit(e.cond(f).then(a,".destroyStream(",o,");")),e.ELEMENTS=o,o}))}return i?new Xa(f.thisDep,f.contextDep,f.propDep,(function(e,t){return t.def(e.shared.vao+".currentVAO?"+e.shared.elements+".getElements("+e.shared.vao+".currentVAO.elements):null")})):null}();function l(e,o){if(e in r){var s=0|r[e];return o?a.offset=s:a.instances=s,E.command(!o||s>=0,"invalid "+e,t.commandStr),Ka((function(e,t){return o&&(e.OFFSET=s),s}))}if(e in n){var c=n[e];return Ja(c,(function(t,r){var n=t.invoke(r,c);return o&&(t.OFFSET=n,E.optional((function(){t.assert(r,n+">=0","invalid "+e)}))),n}))}if(o){if(u)return Ka((function(e,t){return e.OFFSET=0,0}));if(i)return new Xa(f.thisDep,f.contextDep,f.propDep,(function(e,t){return t.def(e.shared.vao+".currentVAO?"+e.shared.vao+".currentVAO.offset:0")}))}else if(i)return new Xa(f.thisDep,f.contextDep,f.propDep,(function(e,t){return t.def(e.shared.vao+".currentVAO?"+e.shared.vao+".currentVAO.instances:-1")}));return null}var d=l(Pn,!0),m=function(){if(Bn in r){var e=r[Bn];return a.primitive=e,E.commandParameter(e,le,"invalid primitve",t.commandStr),Ka((function(t,r){return le[e]}))}if(Bn in n){var o=n[Bn];return Ja(o,(function(e,t){var r=e.constants.primTypes,n=e.invoke(t,o);return E.optional((function(){e.assert(t,n+" in "+r,"invalid primitive, must be one of "+Object.keys(le))})),t.def(r,"[",n,"]")}))}return u?$a(s)?s.value?Ka((function(e,t){return t.def(e.ELEMENTS,".primType")})):Ka((function(){return Oa})):new Xa(s.thisDep,s.contextDep,s.propDep,(function(e,t){var r=e.ELEMENTS;return t.def(r,"?",r,".primType:",Oa)})):i?new Xa(f.thisDep,f.contextDep,f.propDep,(function(e,t){return t.def(e.shared.vao+".currentVAO?"+e.shared.vao+".currentVAO.primitive:"+Oa)})):null}(),p=function(){if(In in r){var e=0|r[In];return a.count=e,E.command("number"==typeof e&&e>=0,"invalid vertex count",t.commandStr),Ka((function(){return e}))}if(In in n){var o=n[In];return Ja(o,(function(e,t){var r=e.invoke(t,o);return E.optional((function(){e.assert(t,"typeof "+r+'==="number"&&'+r+">=0&&"+r+"===("+r+"|0)","invalid vertex count")})),r}))}if(u){if($a(s)){if(s)return d?new Xa(d.thisDep,d.contextDep,d.propDep,(function(e,t){var r=t.def(e.ELEMENTS,".vertCount-",e.OFFSET);return E.optional((function(){e.assert(t,r+">=0","invalid vertex offset/element buffer too small")})),r})):Ka((function(e,t){return t.def(e.ELEMENTS,".vertCount")}));var c=Ka((function(){return-1}));return E.optional((function(){c.MISSING=!0})),c}var l=new Xa(s.thisDep||d.thisDep,s.contextDep||d.contextDep,s.propDep||d.propDep,(function(e,t){var r=e.ELEMENTS;return e.OFFSET?t.def(r,"?",r,".vertCount-",e.OFFSET,":-1"):t.def(r,"?",r,".vertCount:-1")}));return E.optional((function(){l.DYNAMIC=!0})),l}if(i){var m=new Xa(f.thisDep,f.contextDep,f.propDep,(function(e,t){return t.def(e.shared.vao,".currentVAO?",e.shared.vao,".currentVAO.count:-1")}));return m}return null}(),h=l(Ln,!1);return{elements:s,primitive:m,count:p,instances:h,offset:d,vao:f,vaoActive:i,elementsActive:u,static:a}}(e,d),A=function(e,t){var r=e.static,n=e.dynamic,i={};return _.forEach((function(e){var o=O(e);function f(t,a){if(e in r){var f=t(r[e]);i[o]=Ka((function(){return f}))}else if(e in n){var u=n[e];i[o]=Ja(u,(function(e,t){return a(e,t,e.invoke(t,u))}))}}switch(e){case mn:case nn:case rn:case An:case un:case En:case vn:case yn:case xn:case ln:return f((function(r){return E.commandType(r,"boolean",e,t.commandStr),r}),(function(t,r,n){return E.optional((function(){t.assert(r,"typeof "+n+'==="boolean"',"invalid flag "+e,t.commandStr)})),n}));case sn:return f((function(r){return E.commandParameter(r,Ga,"invalid "+e,t.commandStr),Ga[r]}),(function(t,r,n){var a=t.constants.compareFuncs;return E.optional((function(){t.assert(r,n+" in "+a,"invalid "+e+", must be one of "+Object.keys(Ga))})),r.def(a,"[",n,"]")}));case cn:return f((function(e){return E.command(Te(e)&&2===e.length&&"number"==typeof e[0]&&"number"==typeof e[1]&&e[0]<=e[1],"depth range is 2d array",t.commandStr),e}),(function(e,t,r){return E.optional((function(){e.assert(t,e.shared.isArrayLike+"("+r+")&&"+r+".length===2&&typeof "+r+'[0]==="number"&&typeof '+r+'[1]==="number"&&'+r+"[0]<="+r+"[1]","depth range must be a 2d array")})),[t.def("+",r,"[0]"),t.def("+",r,"[1]")]}));case fn:return f((function(e){E.commandType(e,"object","blend.func",t.commandStr);var r="srcRGB"in e?e.srcRGB:e.src,n="srcAlpha"in e?e.srcAlpha:e.src,a="dstRGB"in e?e.dstRGB:e.dst,i="dstAlpha"in e?e.dstAlpha:e.dst;return E.commandParameter(r,Ua,o+".srcRGB",t.commandStr),E.commandParameter(n,Ua,o+".srcAlpha",t.commandStr),E.commandParameter(a,Ua,o+".dstRGB",t.commandStr),E.commandParameter(i,Ua,o+".dstAlpha",t.commandStr),E.command(-1===Wa.indexOf(r+", "+a),"unallowed blending combination (srcRGB, dstRGB) = ("+r+", "+a+")",t.commandStr),[Ua[r],Ua[a],Ua[n],Ua[i]]}),(function(t,r,n){var a=t.constants.blendFuncs;function i(i,o){var f=r.def('"',i,o,'" in ',n,"?",n,".",i,o,":",n,".",i);return E.optional((function(){t.assert(r,f+" in "+a,"invalid "+e+"."+i+o+", must be one of "+Object.keys(Ua))})),f}E.optional((function(){t.assert(r,n+"&&typeof "+n+'==="object"',"invalid blend func, must be an object")}));var o=i("src","RGB"),f=i("dst","RGB");E.optional((function(){var e=t.constants.invalidBlendCombinations;t.assert(r,e+".indexOf("+o+'+", "+'+f+") === -1 ","unallowed blending combination for (srcRGB, dstRGB)")}));var u=r.def(a,"[",o,"]"),s=r.def(a,"[",i("src","Alpha"),"]");return[u,r.def(a,"[",f,"]"),s,r.def(a,"[",i("dst","Alpha"),"]")]}));case on:return f((function(r){return"string"==typeof r?(E.commandParameter(r,v,"invalid "+e,t.commandStr),[v[r],v[r]]):"object"==typeof r?(E.commandParameter(r.rgb,v,e+".rgb",t.commandStr),E.commandParameter(r.alpha,v,e+".alpha",t.commandStr),[v[r.rgb],v[r.alpha]]):void E.commandRaise("invalid blend.equation",t.commandStr)}),(function(t,r,n){var a=t.constants.blendEquations,i=r.def(),o=r.def(),f=t.cond("typeof ",n,'==="string"');return E.optional((function(){function r(e,r,n){t.assert(e,n+" in "+a,"invalid "+r+", must be one of "+Object.keys(v))}r(f.then,e,n),t.assert(f.else,n+"&&typeof "+n+'==="object"',"invalid "+e),r(f.else,e+".rgb",n+".rgb"),r(f.else,e+".alpha",n+".alpha")})),f.then(i,"=",o,"=",a,"[",n,"];"),f.else(i,"=",a,"[",n,".rgb];",o,"=",a,"[",n,".alpha];"),r(f),[i,o]}));case an:return f((function(e){return E.command(Te(e)&&4===e.length,"blend.color must be a 4d array",t.commandStr),R(4,(function(t){return+e[t]}))}),(function(e,t,r){return E.optional((function(){e.assert(t,e.shared.isArrayLike+"("+r+")&&"+r+".length===4","blend.color must be a 4d array")})),R(4,(function(e){return t.def("+",r,"[",e,"]")}))}));case _n:return f((function(e){return E.commandType(e,"number",o,t.commandStr),0|e}),(function(e,t,r){return E.optional((function(){e.assert(t,"typeof "+r+'==="number"',"invalid stencil.mask")})),t.def(r,"|0")}));case kn:return f((function(r){E.commandType(r,"object",o,t.commandStr);var n=r.cmp||"keep",a=r.ref||0,i="mask"in r?r.mask:-1;return E.commandParameter(n,Ga,e+".cmp",t.commandStr),E.commandType(a,"number",e+".ref",t.commandStr),E.commandType(i,"number",e+".mask",t.commandStr),[Ga[n],a,i]}),(function(e,t,r){var n=e.constants.compareFuncs;return E.optional((function(){function a(){e.assert(t,Array.prototype.join.call(arguments,""),"invalid stencil.func")}a(r+"&&typeof ",r,'==="object"'),a('!("cmp" in ',r,")||(",r,".cmp in ",n,")")})),[t.def('"cmp" in ',r,"?",n,"[",r,".cmp]",":",Va),t.def(r,".ref|0"),t.def('"mask" in ',r,"?",r,".mask|0:-1")]}));case Sn:case On:return f((function(r){E.commandType(r,"object",o,t.commandStr);var n=r.fail||"keep",a=r.zfail||"keep",i=r.zpass||"keep";return E.commandParameter(n,Ha,e+".fail",t.commandStr),E.commandParameter(a,Ha,e+".zfail",t.commandStr),E.commandParameter(i,Ha,e+".zpass",t.commandStr),[e===On?Ta:Ea,Ha[n],Ha[a],Ha[i]]}),(function(t,r,n){var a=t.constants.stencilOps;function i(i){return E.optional((function(){t.assert(r,'!("'+i+'" in '+n+")||("+n+"."+i+" in "+a+")","invalid "+e+"."+i+", must be one of "+Object.keys(Ha))})),r.def('"',i,'" in ',n,"?",a,"[",n,".",i,"]:",Va)}return E.optional((function(){t.assert(r,n+"&&typeof "+n+'==="object"',"invalid "+e)})),[e===On?Ta:Ea,i("fail"),i("zfail"),i("zpass")]}));case gn:return f((function(e){E.commandType(e,"object",o,t.commandStr);var r=0|e.factor,n=0|e.units;return E.commandType(r,"number",o+".factor",t.commandStr),E.commandType(n,"number",o+".units",t.commandStr),[r,n]}),(function(t,r,n){return E.optional((function(){t.assert(r,n+"&&typeof "+n+'==="object"',"invalid "+e)})),[r.def(n,".factor|0"),r.def(n,".units|0")]}));case pn:return f((function(e){var r=0;return"front"===e?r=Ea:"back"===e&&(r=Ta),E.command(!!r,o,t.commandStr),r}),(function(e,t,r){return E.optional((function(){e.assert(t,r+'==="front"||'+r+'==="back"',"invalid cull.face")})),t.def(r,'==="front"?',Ea,":",Ta)}));case bn:return f((function(e){return E.command("number"==typeof e&&e>=a.lineWidthDims[0]&&e<=a.lineWidthDims[1],"invalid line width, must be a positive number between "+a.lineWidthDims[0]+" and "+a.lineWidthDims[1],t.commandStr),e}),(function(e,t,r){return E.optional((function(){e.assert(t,"typeof "+r+'==="number"&&'+r+">="+a.lineWidthDims[0]+"&&"+r+"<="+a.lineWidthDims[1],"invalid line width")})),r}));case hn:return f((function(e){return E.commandParameter(e,qa,o,t.commandStr),qa[e]}),(function(e,t,r){return E.optional((function(){e.assert(t,r+'==="cw"||'+r+'==="ccw"',"invalid frontFace, must be one of cw,ccw")})),t.def(r+'==="cw"?'+Da+":"+ja)}));case dn:return f((function(e){return E.command(Te(e)&&4===e.length,"color.mask must be length 4 array",t.commandStr),e.map((function(e){return!!e}))}),(function(e,t,r){return E.optional((function(){e.assert(t,e.shared.isArrayLike+"("+r+")&&"+r+".length===4","invalid color.mask")})),R(4,(function(e){return"!!"+r+"["+e+"]"}))}));case wn:return f((function(e){E.command("object"==typeof e&&e,o,t.commandStr);var r="value"in e?e.value:1,n=!!e.invert;return E.command("number"==typeof r&&r>=0&&r<=1,"sample.coverage.value must be a number between 0 and 1",t.commandStr),[r,n]}),(function(e,t,r){return E.optional((function(){e.assert(t,r+"&&typeof "+r+'==="object"',"invalid sample.coverage")})),[t.def('"value" in ',r,"?+",r,".value:1"),t.def("!!",r,".invert")]}))}})),i}(e,d),k=function(e,t,n){var a=e.static,i=e.dynamic;function o(e){if(e in a){var t=r.id(a[e]);E.optional((function(){l.shader(Na[e],t,E.guessCommand())}));var n=Ka((function(){return t}));return n.id=t,n}if(e in i){var o=i[e];return Ja(o,(function(t,r){var n=t.invoke(r,o),a=r.def(t.shared.strings,".id(",n,")");return E.optional((function(){r(t.shared.shader,".shader(",Na[e],",",a,",",t.command,");")})),a}))}return null}var f,u=o(Fn),s=o(zn),c=null;return $a(u)&&$a(s)?(c=l.program(s.id,u.id,null,n),f=Ka((function(e,t){return e.link(c)}))):f=new Xa(u&&u.thisDep||s&&s.thisDep,u&&u.contextDep||s&&s.contextDep,u&&u.propDep||s&&s.propDep,(function(e,t){var r,n=e.shared.shader;r=u?u.append(e,t):t.def(n,".",Fn);var a=n+".program("+(s?s.append(e,t):t.def(n,".",zn))+","+r;return E.optional((function(){a+=","+e.command})),t.def(a+")")})),{frag:u,vert:s,progVar:f,program:c}}(e,0,h);function S(e){var t=x[e];t&&(A[e]=t)}S(Dn),S(O(Tn));var T=Object.keys(A).length>0,D={framebuffer:y,draw:w,shader:k,state:A,dirty:T,scopeVAO:null,drawVAO:null,useVAO:!1,attributes:{}};if(D.profile=function(e){var t,r=e.static,n=e.dynamic;if(jn in r){var a=!!r[jn];(t=Ka((function(e,t){return a}))).enable=a}else if(jn in n){var i=n[jn];t=Ja(i,(function(e,t){return e.invoke(t,i)}))}return t}(e),D.uniforms=function(e,t){var r=e.static,n=e.dynamic,a={};return Object.keys(r).forEach((function(e){var n,i=r[e];if("number"==typeof i||"boolean"==typeof i)n=Ka((function(){return i}));else if("function"==typeof i){var o=i._reglType;"texture2d"===o||"textureCube"===o?n=Ka((function(e){return e.link(i)})):"framebuffer"===o||"framebufferCube"===o?(E.command(i.color.length>0,'missing color attachment for framebuffer sent to uniform "'+e+'"',t.commandStr),n=Ka((function(e){return e.link(i.color[0])}))):E.commandRaise('invalid data for uniform "'+e+'"',t.commandStr)}else Te(i)?n=Ka((function(t){return t.global.def("[",R(i.length,(function(r){return E.command("number"==typeof i[r]||"boolean"==typeof i[r],"invalid uniform "+e,t.commandStr),i[r]})),"]")})):E.commandRaise('invalid or missing data for uniform "'+e+'"',t.commandStr);n.value=i,a[e]=n})),Object.keys(n).forEach((function(e){var t=n[e];a[e]=Ja(t,(function(e,r){return e.invoke(r,t)}))})),a}(f,d),D.drawVAO=D.scopeVAO=w.vao,!D.drawVAO&&k.program&&!h&&n.angle_instanced_arrays&&w.static.elements){var j=!0,C=k.program.attributes.map((function(e){var r=t.static[e];return j=j&&!!r,r}));if(j&&C.length>0){var z=c.getVAO(c.createVAO({attributes:C,elements:w.static.elements}));D.drawVAO=new Xa(null,null,null,(function(e,t){return e.link(z)})),D.useVAO=!0}}return h?D.useVAO=!0:D.attributes=function(e,t){var n=e.static,a=e.dynamic,o={};return Object.keys(n).forEach((function(e){var a=n[e],f=r.id(e),u=new b;if(Qa(a))u.state=Qr,u.buffer=i.getBuffer(i.create(a,$n,!1,!0)),u.type=0;else{var s=i.getBuffer(a);if(s)u.state=Qr,u.buffer=s,u.type=0;else if(E.command("object"==typeof a&&a,"invalid data for attribute "+e,t.commandStr),"constant"in a){var c=a.constant;u.buffer="null",u.state=Yr,"number"==typeof c?u.x=c:(E.command(Te(c)&&c.length>0&&c.length<=4,"invalid constant for attribute "+e,t.commandStr),Nr.forEach((function(e,t){t<c.length&&(u[e]=c[t])})))}else{s=Qa(a.buffer)?i.getBuffer(i.create(a.buffer,$n,!1,!0)):i.getBuffer(a.buffer),E.command(!!s,'missing buffer for attribute "'+e+'"',t.commandStr);var l=0|a.offset;E.command(l>=0,'invalid offset for attribute "'+e+'"',t.commandStr);var d=0|a.stride;E.command(d>=0&&d<256,'invalid stride for attribute "'+e+'", must be integer betweeen [0, 255]',t.commandStr);var m=0|a.size;E.command(!("size"in a)||m>0&&m<=4,'invalid size for attribute "'+e+'", must be 1,2,3,4',t.commandStr);var p=!!a.normalized,h=0;"type"in a&&(E.commandParameter(a.type,Z,"invalid type for attribute "+e,t.commandStr),h=Z[a.type]);var v=0|a.divisor;E.optional((function(){"divisor"in a&&(E.command(0===v||g,'cannot specify divisor for attribute "'+e+'", instancing not supported',t.commandStr),E.command(v>=0,'invalid divisor for attribute "'+e+'"',t.commandStr));var r=t.commandStr,n=["buffer","offset","divisor","normalized","type","size","stride"];Object.keys(a).forEach((function(t){E.command(n.indexOf(t)>=0,'unknown parameter "'+t+'" for attribute pointer "'+e+'" (valid parameters are '+n+")",r)}))})),u.buffer=s,u.state=Qr,u.size=m,u.normalized=p,u.type=h||s.dtype,u.offset=l,u.stride=d,u.divisor=v}}o[e]=Ka((function(e,t){var r=e.attribCache;if(f in r)return r[f];var n={isStream:!1};return Object.keys(u).forEach((function(e){n[e]=u[e]})),u.buffer&&(n.buffer=e.link(u.buffer),n.type=n.type||n.buffer+".dtype"),r[f]=n,n}))})),Object.keys(a).forEach((function(e){var t=a[e];o[e]=Ja(t,(function(r,n){var a=r.invoke(n,t),i=r.shared,o=r.constants,f=i.isBufferArgs,u=i.buffer;E.optional((function(){r.assert(n,a+"&&(typeof "+a+'==="object"||typeof '+a+'==="function")&&('+f+"("+a+")||"+u+".getBuffer("+a+")||"+u+".getBuffer("+a+".buffer)||"+f+"("+a+'.buffer)||("constant" in '+a+"&&(typeof "+a+'.constant==="number"||'+i.isArrayLike+"("+a+".constant))))",'invalid dynamic attribute "'+e+'"')}));var s={isStream:n.def(!1)},c=new b;c.state=Qr,Object.keys(c).forEach((function(e){s[e]=n.def(""+c[e])}));var l=s.buffer,d=s.type;function m(e){n(s[e],"=",a,".",e,"|0;")}return n("if(",f,"(",a,")){",s.isStream,"=true;",l,"=",u,".createStream(",$n,",",a,");",d,"=",l,".dtype;","}else{",l,"=",u,".getBuffer(",a,");","if(",l,"){",d,"=",l,".dtype;",'}else if("constant" in ',a,"){",s.state,"=",Yr,";","if(typeof "+a+'.constant === "number"){',s[Nr[0]],"=",a,".constant;",Nr.slice(1).map((function(e){return s[e]})).join("="),"=0;","}else{",Nr.map((function(e,t){return s[e]+"="+a+".constant.length>"+t+"?"+a+".constant["+t+"]:0;"})).join(""),"}}else{","if(",f,"(",a,".buffer)){",l,"=",u,".createStream(",$n,",",a,".buffer);","}else{",l,"=",u,".getBuffer(",a,".buffer);","}",d,'="type" in ',a,"?",o.glTypes,"[",a,".type]:",l,".dtype;",s.normalized,"=!!",a,".normalized;"),m("size"),m("offset"),m("stride"),m("divisor"),n("}}"),n.exit("if(",s.isStream,"){",u,".destroyStream(",l,");","}"),s}))})),o}(t,d),D.context=function(e){var t=e.static,r=e.dynamic,n={};return Object.keys(t).forEach((function(e){var r=t[e];n[e]=Ka((function(e,t){return"number"==typeof r||"boolean"==typeof r?""+r:e.link(r)}))})),Object.keys(r).forEach((function(e){var t=r[e];n[e]=Ja(t,(function(e,r){return e.invoke(r,t)}))})),n}(s),D}function I(e,t,r){var n=e.shared.context,a=e.scope();Object.keys(r).forEach((function(i){t.save(n,"."+i);var o=r[i].append(e,t);Array.isArray(o)?a(n,".",i,"=[",o.join(),"];"):a(n,".",i,"=",o,";")})),t(a)}function P(e,t,r,n){var a,i=e.shared,o=i.gl,f=i.framebuffer;y&&(a=t.def(i.extensions,".webgl_draw_buffers"));var u,s=e.constants,c=s.drawBuffer,l=s.backBuffer;u=r?r.append(e,t):t.def(f,".next"),n||t("if(",u,"!==",f,".cur){"),t("if(",u,"){",o,".bindFramebuffer(",Ra,",",u,".framebuffer);"),y&&t(a,".drawBuffersWEBGL(",c,"[",u,".colorAttachments.length]);"),t("}else{",o,".bindFramebuffer(",Ra,",null);"),y&&t(a,".drawBuffersWEBGL(",l,");"),t("}",f,".cur=",u,";"),n||t("}")}function L(e,t,r){var n=e.shared,a=n.gl,i=e.current,o=e.next,f=n.current,u=n.next,s=e.cond(f,".dirty");_.forEach((function(t){var n,c,l=O(t);if(!(l in r.state))if(l in o){n=o[l],c=i[l];var d=R(w[l].length,(function(e){return s.def(n,"[",e,"]")}));s(e.cond(d.map((function(e,t){return e+"!=="+c+"["+t+"]"})).join("||")).then(a,".",S[l],"(",d,");",d.map((function(e,t){return c+"["+t+"]="+e})).join(";"),";"))}else{n=s.def(u,".",l);var m=e.cond(n,"!==",f,".",l);s(m),l in k?m(e.cond(n).then(a,".enable(",k[l],");").else(a,".disable(",k[l],");"),f,".",l,"=",n,";"):m(a,".",S[l],"(",n,");",f,".",l,"=",n,";")}})),0===Object.keys(r.state).length&&s(f,".dirty=false;"),t(s)}function M(e,t,r,n){var a=e.shared,i=e.current,o=a.current,f=a.gl;Ya(Object.keys(r)).forEach((function(a){var u=r[a];if(!n||n(u)){var s=u.append(e,t);if(k[a]){var c=k[a];$a(u)?t(f,s?".enable(":".disable(",c,");"):t(e.cond(s).then(f,".enable(",c,");").else(f,".disable(",c,");")),t(o,".",a,"=",s,";")}else if(Te(s)){var l=i[a];t(f,".",S[a],"(",s,");",s.map((function(e,t){return l+"["+t+"]="+e})).join(";"),";")}else t(f,".",S[a],"(",s,");",o,".",a,"=",s,";")}}))}function U(e,t){g&&(e.instancing=t.def(e.shared.extensions,".angle_instanced_arrays"))}function W(e,t,r,n,a){var i,o,f,u=e.shared,s=e.stats,c=u.current,l=u.timer,d=r.profile;function m(){return"undefined"==typeof performance?"Date.now()":"performance.now()"}function h(e){e(i=t.def(),"=",m(),";"),"string"==typeof a?e(s,".count+=",a,";"):e(s,".count++;"),p&&(n?e(o=t.def(),"=",l,".getNumPendingQueries();"):e(l,".beginQuery(",s,");"))}function b(e){e(s,".cpuTime+=",m(),"-",i,";"),p&&(n?e(l,".pushScopeStats(",o,",",l,".getNumPendingQueries(),",s,");"):e(l,".endQuery();"))}function v(e){var r=t.def(c,".profile");t(c,".profile=",e,";"),t.exit(c,".profile=",r,";")}if(d){if($a(d))return void(d.enable?(h(t),b(t.exit),v("true")):v("false"));v(f=d.append(e,t))}else f=t.def(c,".profile");var g=e.block();h(g),t("if(",f,"){",g,"}");var y=e.block();b(y),t.exit("if(",f,"){",y,"}")}function G(e,t,r,n,a){var i=e.shared;n.forEach((function(n){var o,f=n.name,u=r.attributes[f];if(u){if(!a(u))return;o=u.append(e,t)}else{if(!a(Za))return;var s=e.scopeAttrib(f);E.optional((function(){e.assert(t,s+".state","missing attribute "+f)})),o={},Object.keys(new b).forEach((function(e){o[e]=t.def(s,".",e)}))}!function(r,n,a){var o=i.gl,f=t.def(r,".location"),u=t.def(i.attributes,"[",f,"]"),s=a.state,c=a.buffer,l=[a.x,a.y,a.z,a.w],d=["buffer","normalized","offset","stride"];function m(){t("if(!",u,".buffer){",o,".enableVertexAttribArray(",f,");}");var r,i=a.type;if(r=a.size?t.def(a.size,"||",n):n,t("if(",u,".type!==",i,"||",u,".size!==",r,"||",d.map((function(e){return u+"."+e+"!=="+a[e]})).join("||"),"){",o,".bindBuffer(",$n,",",c,".buffer);",o,".vertexAttribPointer(",[f,r,i,a.normalized,a.stride,a.offset],");",u,".type=",i,";",u,".size=",r,";",d.map((function(e){return u+"."+e+"="+a[e]+";"})).join(""),"}"),g){var s=a.divisor;t("if(",u,".divisor!==",s,"){",e.instancing,".vertexAttribDivisorANGLE(",[f,s],");",u,".divisor=",s,";}")}}function p(){t("if(",u,".buffer){",o,".disableVertexAttribArray(",f,");",u,".buffer=null;","}if(",Nr.map((function(e,t){return u+"."+e+"!=="+l[t]})).join("||"),"){",o,".vertexAttrib4f(",f,",",l,");",Nr.map((function(e,t){return u+"."+e+"="+l[t]+";"})).join(""),"}")}s===Qr?m():s===Yr?p():(t("if(",s,"===",Qr,"){"),m(),t("}else{"),p(),t("}"))}(e.link(n),function(e){switch(e){case ca:case pa:case ga:return 2;case la:case ha:case ya:return 3;case da:case ba:case xa:return 4;default:return 1}}(n.info.type),o)}))}function H(e,t,n,a,i,o){for(var f,u=e.shared,s=u.gl,c={},l=0;l<a.length;++l){var d=a[l],m=d.name,p=d.info.type,h=d.info.size,b=n.uniforms[m];if(h>1){if(!b)continue;var v=m.replace("[0]","");if(c[v])continue;c[v]=1}var g,y=e.link(d)+".location";if(b){if(!i(b))continue;if($a(b)){var x=b.value;if(E.command(null!=x,'missing uniform "'+m+'"',e.commandStr),p===ka||p===Sa){E.command("function"==typeof x&&(p===ka&&("texture2d"===x._reglType||"framebuffer"===x._reglType)||p===Sa&&("textureCube"===x._reglType||"framebufferCube"===x._reglType)),"invalid texture for uniform "+m,e.commandStr);var w=e.link(x._texture||x.color[0]._texture);t(s,".uniform1i(",y,",",w+".bind());"),t.exit(w,".unbind();")}else if(p===wa||p===Aa||p===_a){E.optional((function(){E.command(Te(x),"invalid matrix for uniform "+m,e.commandStr),E.command(p===wa&&4===x.length||p===Aa&&9===x.length||p===_a&&16===x.length,"invalid length for matrix uniform "+m,e.commandStr)}));var A=e.global.def("new Float32Array(["+Array.prototype.slice.call(x)+"])"),_=2;p===Aa?_=3:p===_a&&(_=4),t(s,".uniformMatrix",_,"fv(",y,",false,",A,");")}else{switch(p){case sa:1===h?E.commandType(x,"number","uniform "+m,e.commandStr):E.command(Te(x)&&x.length===h,"uniform "+m,e.commandStr),f="1f";break;case ca:E.command(Te(x)&&x.length&&x.length%2==0&&x.length<=2*h,"uniform "+m,e.commandStr),f="2f";break;case la:E.command(Te(x)&&x.length&&x.length%3==0&&x.length<=3*h,"uniform "+m,e.commandStr),f="3f";break;case da:E.command(Te(x)&&x.length&&x.length%4==0&&x.length<=4*h,"uniform "+m,e.commandStr),f="4f";break;case va:1===h?E.commandType(x,"boolean","uniform "+m,e.commandStr):E.command(Te(x)&&x.length===h,"uniform "+m,e.commandStr),f="1i";break;case ma:1===h?E.commandType(x,"number","uniform "+m,e.commandStr):E.command(Te(x)&&x.length===h,"uniform "+m,e.commandStr),f="1i";break;case ga:case pa:E.command(Te(x)&&x.length&&x.length%2==0&&x.length<=2*h,"uniform "+m,e.commandStr),f="2i";break;case ya:case ha:E.command(Te(x)&&x.length&&x.length%3==0&&x.length<=3*h,"uniform "+m,e.commandStr),f="3i";break;case xa:case ba:E.command(Te(x)&&x.length&&x.length%4==0&&x.length<=4*h,"uniform "+m,e.commandStr),f="4i"}h>1?(f+="v",x=e.global.def("["+Array.prototype.slice.call(x)+"]")):x=Te(x)?Array.prototype.slice.call(x):x,t(s,".uniform",f,"(",y,",",x,");")}continue}g=b.append(e,t)}else{if(!i(Za))continue;g=t.def(u.uniforms,"[",r.id(m),"]")}p===ka?(E(!Array.isArray(g),"must specify a scalar prop for textures"),t("if(",g,"&&",g,'._reglType==="framebuffer"){',g,"=",g,".color[0];","}")):p===Sa&&(E(!Array.isArray(g),"must specify a scalar prop for cube maps"),t("if(",g,"&&",g,'._reglType==="framebufferCube"){',g,"=",g,".color[0];","}")),E.optional((function(){function r(r,n){e.assert(t,r,'bad data or missing for uniform "'+m+'".  '+n)}function n(e,t){1===t&&E(!Array.isArray(g),"must not specify an array type for uniform"),r("Array.isArray("+g+") && typeof "+g+'[0]===" '+e+'" || typeof '+g+'==="'+e+'"',"invalid type, expected "+e)}function a(t,n,a){Array.isArray(g)?E(g.length&&g.length%t==0&&g.length<=t*a,"must have length of "+(1===a?"":"n * ")+t):r(u.isArrayLike+"("+g+")&&"+g+".length && "+g+".length % "+t+" === 0 && "+g+".length<="+t*a,"invalid vector, should have length of "+(1===a?"":"n * ")+t,e.commandStr)}function i(t){E(!Array.isArray(g),"must not specify a value type"),r("typeof "+g+'==="function"&&'+g+'._reglType==="texture'+(t===Jn?"2d":"Cube")+'"',"invalid texture type",e.commandStr)}switch(p){case ma:n("number",h);break;case pa:a(2,0,h);break;case ha:a(3,0,h);break;case ba:a(4,0,h);break;case sa:n("number",h);break;case ca:a(2,0,h);break;case la:a(3,0,h);break;case da:a(4,0,h);break;case va:n("boolean",h);break;case ga:a(2,0,h);break;case ya:a(3,0,h);break;case xa:case wa:a(4,0,h);break;case Aa:a(9,0,h);break;case _a:a(16,0,h);break;case ka:i(Jn);break;case Sa:i(Zn)}}));var k=1;switch(p){case ka:case Sa:var S=t.def(g,"._texture");t(s,".uniform1i(",y,",",S,".bind());"),t.exit(S,".unbind();");continue;case ma:case va:f="1i";break;case pa:case ga:f="2i",k=2;break;case ha:case ya:f="3i",k=3;break;case ba:case xa:f="4i",k=4;break;case sa:f="1f";break;case ca:f="2f",k=2;break;case la:f="3f",k=3;break;case da:f="4f",k=4;break;case wa:f="Matrix2fv";break;case Aa:f="Matrix3fv";break;case _a:f="Matrix4fv"}if(-1===f.indexOf("Matrix")&&h>1&&(f+="v",k=1),"M"===f.charAt(0)){t(s,".uniform",f,"(",y,",");var O=Math.pow(p-wa+2,2),T=e.global.def("new Float32Array(",O,")");Array.isArray(g)?t("false,(",R(O,(function(e){return T+"["+e+"]="+g[e]})),",",T,")"):t("false,(Array.isArray(",g,")||",g," instanceof Float32Array)?",g,":(",R(O,(function(e){return T+"["+e+"]="+g+"["+e+"]"})),",",T,")"),t(");")}else if(k>1){for(var D=[],j=[],C=0;C<k;++C)Array.isArray(g)?j.push(g[C]):j.push(t.def(g+"["+C+"]")),o&&D.push(t.def());o&&t("if(!",e.batchId,"||",D.map((function(e,t){return e+"!=="+j[t]})).join("||"),"){",D.map((function(e,t){return e+"="+j[t]+";"})).join("")),t(s,".uniform",f,"(",y,",",j.join(","),");"),o&&t("}")}else{if(E(!Array.isArray(g),"uniform value must not be an array"),o){var z=t.def();t("if(!",e.batchId,"||",z,"!==",g,"){",z,"=",g,";")}t(s,".uniform",f,"(",y,",",g,");"),o&&t("}")}}}function N(e,t,r,n){var a=e.shared,i=a.gl,o=a.draw,f=n.draw,u=function(){var u,s=f.elements,c=t;return s?((s.contextDep&&n.contextDynamic||s.propDep)&&(c=r),u=s.append(e,c),f.elementsActive&&c("if("+u+")"+i+".bindBuffer("+Kn+","+u+".buffer.buffer);")):(u=c.def(),c(u,"=",o,".",Vn,";","if(",u,"){",i,".bindBuffer(",Kn,",",u,".buffer.buffer);}","else if(",a.vao,".currentVAO){",u,"=",e.shared.elements+".getElements("+a.vao,".currentVAO.elements);",x?"":"if("+u+")"+i+".bindBuffer("+Kn+","+u+".buffer.buffer);","}")),u}();function s(a){var i=f[a];return i?i.contextDep&&n.contextDynamic||i.propDep?i.append(e,r):i.append(e,t):t.def(o,".",a)}var c,l,d=s(Bn),m=s(Pn),p=function(){var a,i=f.count,u=t;return i?((i.contextDep&&n.contextDynamic||i.propDep)&&(u=r),a=i.append(e,u),E.optional((function(){i.MISSING&&e.assert(t,"false","missing vertex count"),i.DYNAMIC&&e.assert(u,a+">=0","missing vertex count")}))):(a=u.def(o,".",In),E.optional((function(){e.assert(u,a+">=0","missing vertex count")}))),a}();if("number"==typeof p){if(0===p)return}else r("if(",p,"){"),r.exit("}");g&&(c=s(Ln),l=e.instancing);var h=u+".type",b=f.elements&&$a(f.elements)&&!f.vaoActive;function v(){function e(){r(l,".drawElementsInstancedANGLE(",[d,p,h,m+"<<(("+h+"-"+qr+")>>1)",c],");")}function t(){r(l,".drawArraysInstancedANGLE(",[d,m,p,c],");")}u&&"null"!==u?b?e():(r("if(",u,"){"),e(),r("}else{"),t(),r("}")):t()}function y(){function e(){r(i+".drawElements("+[d,p,h,m+"<<(("+h+"-"+qr+")>>1)"]+");")}function t(){r(i+".drawArrays("+[d,m,p]+");")}u&&"null"!==u?b?e():(r("if(",u,"){"),e(),r("}else{"),t(),r("}")):t()}g&&("number"!=typeof c||c>=0)?"string"==typeof c?(r("if(",c,">0){"),v(),r("}else if(",c,"<0){"),y(),r("}")):v():y()}function q(e,t,r,n,a){var i=V(),o=i.proc("body",a);return E.optional((function(){i.commandStr=t.commandStr,i.command=i.link(t.commandStr)})),g&&(i.instancing=o.def(i.shared.extensions,".angle_instanced_arrays")),e(i,o,r,n),i.compile().body}function Q(e,t,r,n){U(e,t),r.useVAO?r.drawVAO?t(e.shared.vao,".setVAO(",r.drawVAO.append(e,t),");"):t(e.shared.vao,".setVAO(",e.shared.vao,".targetVAO);"):(t(e.shared.vao,".setVAO(null);"),G(e,t,r,n.attributes,(function(){return!0}))),H(e,t,r,n.uniforms,(function(){return!0}),!1),N(e,t,t,r)}function Y(e,t,r,n){function a(){return!0}e.batchId="a1",U(e,t),G(e,t,r,n.attributes,a),H(e,t,r,n.uniforms,a,!1),N(e,t,t,r)}function X(e,t,r,n){U(e,t);var a=r.contextDep,i=t.def(),o=t.def();e.shared.props=o,e.batchId=i;var f=e.scope(),u=e.scope();function s(e){return e.contextDep&&a||e.propDep}function c(e){return!s(e)}if(t(f.entry,"for(",i,"=0;",i,"<","a1",";++",i,"){",o,"=","a0","[",i,"];",u,"}",f.exit),r.needsContext&&I(e,u,r.context),r.needsFramebuffer&&P(e,u,r.framebuffer),M(e,u,r.state,s),r.profile&&s(r.profile)&&W(e,u,r,!1,!0),n)r.useVAO?r.drawVAO?s(r.drawVAO)?u(e.shared.vao,".setVAO(",r.drawVAO.append(e,u),");"):f(e.shared.vao,".setVAO(",r.drawVAO.append(e,f),");"):f(e.shared.vao,".setVAO(",e.shared.vao,".targetVAO);"):(f(e.shared.vao,".setVAO(null);"),G(e,f,r,n.attributes,c),G(e,u,r,n.attributes,s)),H(e,f,r,n.uniforms,c,!1),H(e,u,r,n.uniforms,s,!0),N(e,f,u,r);else{var l=e.global.def("{}"),d=r.shader.progVar.append(e,u),m=u.def(d,".id"),p=u.def(l,"[",m,"]");u(e.shared.gl,".useProgram(",d,".program);","if(!",p,"){",p,"=",l,"[",m,"]=",e.link((function(t){return q(Y,e,r,t,2)})),"(",d,");}",p,".call(this,a0[",i,"],",i,");")}}function $(e,t,r){var n=t.static[r];if(n&&function(e){if("object"==typeof e&&!Te(e)){for(var t=Object.keys(e),r=0;r<t.length;++r)if(F.isDynamic(e[t[r]]))return!0;return!1}}(n)){var a=e.global,i=Object.keys(n),o=!1,f=!1,u=!1,s=e.global.def("{}");i.forEach((function(t){var r=n[t];if(F.isDynamic(r)){"function"==typeof r&&(r=n[t]=F.unbox(r));var i=Ja(r,null);o=o||i.thisDep,u=u||i.propDep,f=f||i.contextDep}else{switch(a(s,".",t,"="),typeof r){case"number":a(r);break;case"string":a('"',r,'"');break;case"object":Array.isArray(r)&&a("[",r.join(),"]");break;default:a(e.link(r))}a(";")}})),t.dynamic[r]=new F.DynamicVariable(Zr,{thisDep:o,contextDep:f,propDep:u,ref:s,append:function(e,t){i.forEach((function(r){var a=n[r];if(F.isDynamic(a)){var i=e.invoke(t,a);t(s,".",r,"=",i,";")}}))}}),delete t.static[r]}}return{next:A,current:w,procs:function(){var e=V(),t=e.proc("poll"),r=e.proc("refresh"),i=e.block();t(i),r(i);var o,f=e.shared,u=f.gl,s=f.next,c=f.current;i(c,".dirty=false;"),P(e,t),P(e,r,null,!0),g&&(o=e.link(g)),n.oes_vertex_array_object&&r(e.link(n.oes_vertex_array_object),".bindVertexArrayOES(null);");for(var l=0;l<a.maxAttributes;++l){var d=r.def(f.attributes,"[",l,"]"),m=e.cond(d,".buffer");m.then(u,".enableVertexAttribArray(",l,");",u,".bindBuffer(",$n,",",d,".buffer.buffer);",u,".vertexAttribPointer(",l,",",d,".size,",d,".type,",d,".normalized,",d,".stride,",d,".offset);").else(u,".disableVertexAttribArray(",l,");",u,".vertexAttrib4f(",l,",",d,".x,",d,".y,",d,".z,",d,".w);",d,".buffer=null;"),r(m),g&&r(o,".vertexAttribDivisorANGLE(",l,",",d,".divisor);")}return r(e.shared.vao,".currentVAO=null;",e.shared.vao,".setVAO(",e.shared.vao,".targetVAO);"),Object.keys(k).forEach((function(n){var a=k[n],o=i.def(s,".",n),f=e.block();f("if(",o,"){",u,".enable(",a,")}else{",u,".disable(",a,")}",c,".",n,"=",o,";"),r(f),t("if(",o,"!==",c,".",n,"){",f,"}")})),Object.keys(S).forEach((function(n){var a,o,f=S[n],l=w[n],d=e.block();if(d(u,".",f,"("),Te(l)){var m=l.length;a=e.global.def(s,".",n),o=e.global.def(c,".",n),d(R(m,(function(e){return a+"["+e+"]"})),");",R(m,(function(e){return o+"["+e+"]="+a+"["+e+"];"})).join("")),t("if(",R(m,(function(e){return a+"["+e+"]!=="+o+"["+e+"]"})).join("||"),"){",d,"}")}else a=i.def(s,".",n),o=i.def(c,".",n),d(a,");",c,".",n,"=",a,";"),t("if(",a,"!==",o,"){",d,"}");r(d)})),e.compile()}(),compile:function(e,n,a,i,o){var f=V();f.stats=f.link(o),Object.keys(n.static).forEach((function(e){$(f,n,e)})),Xn.forEach((function(t){$(f,e,t)}));var u=B(e,n,a,i,f);return function(e,t){var r=e.proc("draw",1);U(e,r),I(e,r,t.context),P(e,r,t.framebuffer),L(e,r,t),M(e,r,t.state),W(e,r,t,!1,!0);var n=t.shader.progVar.append(e,r);if(r(e.shared.gl,".useProgram(",n,".program);"),t.shader.program)Q(e,r,t,t.shader.program);else{r(e.shared.vao,".setVAO(null);");var a=e.global.def("{}"),i=r.def(n,".id"),o=r.def(a,"[",i,"]");r(e.cond(o).then(o,".call(this,a0);").else(o,"=",a,"[",i,"]=",e.link((function(r){return q(Q,e,t,r,1)})),"(",n,");",o,".call(this,a0);"))}Object.keys(t.state).length>0&&r(e.shared.current,".dirty=true;"),e.shared.vao&&r(e.shared.vao,".setVAO(null);")}(f,u),function(e,t){var n=e.proc("scope",3);e.batchId="a2";var a=e.shared,i=a.current;function o(r){var i=t.shader[r];i&&n.set(a.shader,"."+r,i.append(e,n))}I(e,n,t.context),t.framebuffer&&t.framebuffer.append(e,n),Ya(Object.keys(t.state)).forEach((function(r){var i=t.state[r].append(e,n);Te(i)?i.forEach((function(t,a){n.set(e.next[r],"["+a+"]",t)})):n.set(a.next,"."+r,i)})),W(e,n,t,!0,!0),[Vn,Pn,In,Ln,Bn].forEach((function(r){var i=t.draw[r];i&&n.set(a.draw,"."+r,""+i.append(e,n))})),Object.keys(t.uniforms).forEach((function(i){var o=t.uniforms[i].append(e,n);Array.isArray(o)&&(o="["+o.join()+"]"),n.set(a.uniforms,"["+r.id(i)+"]",o)})),Object.keys(t.attributes).forEach((function(r){var a=t.attributes[r].append(e,n),i=e.scopeAttrib(r);Object.keys(new b).forEach((function(e){n.set(i,"."+e,a[e])}))})),t.scopeVAO&&n.set(a.vao,".targetVAO",t.scopeVAO.append(e,n)),o(zn),o(Fn),Object.keys(t.state).length>0&&(n(i,".dirty=true;"),n.exit(i,".dirty=true;")),n("a1(",e.shared.context,",a0,",e.batchId,");")}(f,u),function(e,t){var r=e.proc("batch",2);e.batchId="0",U(e,r);var n=!1,a=!0;Object.keys(t.context).forEach((function(e){n=n||t.context[e].propDep})),n||(I(e,r,t.context),a=!1);var i=t.framebuffer,o=!1;function f(e){return e.contextDep&&n||e.propDep}i?(i.propDep?n=o=!0:i.contextDep&&n&&(o=!0),o||P(e,r,i)):P(e,r,null),t.state.viewport&&t.state.viewport.propDep&&(n=!0),L(e,r,t),M(e,r,t.state,(function(e){return!f(e)})),t.profile&&f(t.profile)||W(e,r,t,!1,"a1"),t.contextDep=n,t.needsContext=a,t.needsFramebuffer=o;var u=t.shader.progVar;if(u.contextDep&&n||u.propDep)X(e,r,t,null);else{var s=u.append(e,r);if(r(e.shared.gl,".useProgram(",s,".program);"),t.shader.program)X(e,r,t,t.shader.program);else{r(e.shared.vao,".setVAO(null);");var c=e.global.def("{}"),l=r.def(s,".id"),d=r.def(c,"[",l,"]");r(e.cond(d).then(d,".call(this,a0,a1);").else(d,"=",c,"[",l,"]=",e.link((function(r){return q(X,e,t,r,2)})),"(",s,");",d,".call(this,a0,a1);"))}}Object.keys(t.state).length>0&&r(e.shared.current,".dirty=true;"),e.shared.vao&&r(e.shared.vao,".setVAO(null);")}(f,u),t(f.compile(),{destroy:function(){u.shader.program.destroy()}})}}}var ti=function(e,t){if(!t.ext_disjoint_timer_query)return null;var r=[];function n(e){r.push(e)}var a=[];function i(){this.startQueryIndex=-1,this.endQueryIndex=-1,this.sum=0,this.stats=null}var o=[];function f(e){o.push(e)}var u=[];function s(e,t,r){var n=o.pop()||new i;n.startQueryIndex=e,n.endQueryIndex=t,n.sum=0,n.stats=r,u.push(n)}var c=[],l=[];return{beginQuery:function(e){var n=r.pop()||t.ext_disjoint_timer_query.createQueryEXT();t.ext_disjoint_timer_query.beginQueryEXT(35007,n),a.push(n),s(a.length-1,a.length,e)},endQuery:function(){t.ext_disjoint_timer_query.endQueryEXT(35007)},pushScopeStats:s,update:function(){var e,r,i=a.length;if(0!==i){l.length=Math.max(l.length,i+1),c.length=Math.max(c.length,i+1),c[0]=0,l[0]=0;var o=0;for(e=0,r=0;r<a.length;++r){var s=a[r];t.ext_disjoint_timer_query.getQueryObjectEXT(s,34919)?(o+=t.ext_disjoint_timer_query.getQueryObjectEXT(s,34918),n(s)):a[e++]=s,c[r+1]=o,l[r+1]=e}for(a.length=e,e=0,r=0;r<u.length;++r){var d=u[r],m=d.startQueryIndex,p=d.endQueryIndex;d.sum+=c[p]-c[m];var h=l[m],b=l[p];b===h?(d.stats.gpuTime+=d.sum/1e6,f(d)):(d.startQueryIndex=h,d.endQueryIndex=b,u[e++]=d)}u.length=e}},getNumPendingQueries:function(){return a.length},clear:function(){r.push.apply(r,a);for(var e=0;e<r.length;e++)t.ext_disjoint_timer_query.deleteQueryEXT(r[e]);a.length=0,r.length=0},restore:function(){a.length=0,r.length=0}}},ri="webglcontextlost",ni="webglcontextrestored";function ai(e,t){for(var r=0;r<e.length;++r)if(e[r]===t)return r;return-1}return function(r){var n=L(r);if(!n)return null;var a=n.gl,i=a.getContextAttributes(),o=a.isContextLost(),f=function(e,t){var r={};function n(t){E.type(t,"string","extension name must be string");var n,a=t.toLowerCase();try{n=r[a]=e.getExtension(a)}catch(e){}return!!n}for(var a=0;a<t.extensions.length;++a){var i=t.extensions[a];if(!n(i))return t.onDestroy(),t.onDone('"'+i+'" extension is not supported by the current WebGL context, try upgrading your system or a different browser'),null}return t.optionalExtensions.forEach(n),{extensions:r,restore:function(){Object.keys(r).forEach((function(e){if(r[e]&&!n(e))throw new Error("(regl): error restoring extension "+e)}))}}}(a,n);if(!f)return null;var u,s,c=(u={"":0},s=[""],{id:function(e){var t=u[e];return t||(t=u[e]=s.length,s.push(e),t)},str:function(e){return s[e]}}),l={vaoCount:0,bufferCount:0,elementsCount:0,framebufferCount:0,shaderCount:0,textureCount:0,cubeCount:0,renderbufferCount:0,maxTextureUnits:0},d=f.extensions,m=ti(0,d),p=B(),h=a.drawingBufferWidth,b=a.drawingBufferHeight,v={tick:0,time:0,viewportWidth:h,viewportHeight:b,framebufferWidth:h,framebufferHeight:b,drawingBufferWidth:h,drawingBufferHeight:b,pixelRatio:n.pixelRatio},g={elements:null,primitive:4,count:-1,offset:0,instances:-1},y=function(e,t){var r=1;t.ext_texture_filter_anisotropic&&(r=e.getParameter(34047));var n=1,a=1;t.webgl_draw_buffers&&(n=e.getParameter(34852),a=e.getParameter(36063));var i=!!t.oes_texture_float;if(i){var o=e.createTexture();e.bindTexture(G,o),e.texImage2D(G,0,H,1,1,0,H,N,null);var f=e.createFramebuffer();if(e.bindFramebuffer(q,f),e.framebufferTexture2D(q,36064,G,o,0),e.bindTexture(G,null),36053!==e.checkFramebufferStatus(q))i=!1;else{e.viewport(0,0,1,1),e.clearColor(1,0,0,1),e.clear(16384);var u=W.allocType(N,4);e.readPixels(0,0,1,1,H,N,u),e.getError()?i=!1:(e.deleteFramebuffer(f),e.deleteTexture(o),i=1===u[0]),W.freeType(u)}}var s=!0;if("undefined"==typeof navigator||!(/MSIE/.test(navigator.userAgent)||/Trident\//.test(navigator.appVersion)||/Edge/.test(navigator.userAgent))){var c=e.createTexture(),l=W.allocType(5121,36);e.activeTexture(33984),e.bindTexture(34067,c),e.texImage2D(34069,0,H,3,3,0,H,5121,l),W.freeType(l),e.bindTexture(34067,null),e.deleteTexture(c),s=!e.getError()}return{colorBits:[e.getParameter(3410),e.getParameter(3411),e.getParameter(3412),e.getParameter(3413)],depthBits:e.getParameter(3414),stencilBits:e.getParameter(3415),subpixelBits:e.getParameter(3408),extensions:Object.keys(t).filter((function(e){return!!t[e]})),maxAnisotropic:r,maxDrawbuffers:n,maxColorAttachments:a,pointSizeDims:e.getParameter(33901),lineWidthDims:e.getParameter(33902),maxViewportDims:e.getParameter(3386),maxCombinedTextureUnits:e.getParameter(35661),maxCubeMapSize:e.getParameter(34076),maxRenderbufferSize:e.getParameter(34024),maxTextureUnits:e.getParameter(34930),maxTextureSize:e.getParameter(3379),maxAttributes:e.getParameter(34921),maxVertexUniforms:e.getParameter(36347),maxVertexTextureUnits:e.getParameter(35660),maxVaryingVectors:e.getParameter(36348),maxFragmentUniforms:e.getParameter(36349),glsl:e.getParameter(35724),renderer:e.getParameter(7937),vendor:e.getParameter(7936),version:e.getParameter(7938),readFloat:i,npotTextureCube:s}}(a,d),x=function(t,r,n,a){var i=0,o={};function f(e){this.id=i++,this.buffer=t.createBuffer(),this.type=e,this.usage=ne,this.byteLength=0,this.dimension=1,this.dtype=ie,this.persistentData=null,n.profile&&(this.stats={size:0})}f.prototype.bind=function(){t.bindBuffer(this.type,this.buffer)},f.prototype.destroy=function(){l(this)};var u=[];function s(e,r,n){e.byteLength=r.byteLength,t.bufferData(e.type,r,n)}function c(t,r,n,a,i,o){var f,u;if(t.usage=n,Array.isArray(r)){if(t.dtype=a||oe,r.length>0)if(Array.isArray(r[0])){f=re(r);for(var c=1,l=1;l<f.length;++l)c*=f[l];t.dimension=c,s(t,u=te(r,f,t.dtype),n),o?t.persistentData=u:W.freeType(u)}else if("number"==typeof r[0]){t.dimension=i;var d=W.allocType(t.dtype,r.length);se(d,r),s(t,d,n),o?t.persistentData=d:W.freeType(d)}else e(r[0])?(t.dimension=r[0].length,t.dtype=a||ue(r[0])||oe,s(t,u=te(r,[r.length,r[0].length],t.dtype),n),o?t.persistentData=u:W.freeType(u)):E.raise("invalid buffer data")}else if(e(r))t.dtype=a||ue(r),t.dimension=i,s(t,r,n),o&&(t.persistentData=new Uint8Array(new Uint8Array(r.buffer)));else if(Q(r)){f=r.shape;var m=r.stride,p=r.offset,h=0,b=0,v=0,g=0;1===f.length?(h=f[0],b=1,v=m[0],g=0):2===f.length?(h=f[0],b=f[1],v=m[0],g=m[1]):E.raise("invalid shape"),t.dtype=a||ue(r.data)||oe,t.dimension=b;var y=W.allocType(t.dtype,h*b);ce(y,r.data,h,b,v,g,p),s(t,y,n),o?t.persistentData=y:W.freeType(y)}else r instanceof ArrayBuffer?(t.dtype=ie,t.dimension=i,s(t,r,n),o&&(t.persistentData=new Uint8Array(new Uint8Array(r)))):E.raise("invalid buffer data")}function l(e){r.bufferCount--,a(e);var n=e.buffer;E(n,"buffer must not be deleted already"),t.deleteBuffer(n),e.buffer=null,delete o[e.id]}return n.profile&&(r.getTotalBufferSize=function(){var e=0;return Object.keys(o).forEach((function(t){e+=o[t].stats.size})),e}),{create:function(a,i,u,s){r.bufferCount++;var d=new f(i);function m(r){var a=ne,i=null,o=0,f=0,u=1;return Array.isArray(r)||e(r)||Q(r)||r instanceof ArrayBuffer?i=r:"number"==typeof r?o=0|r:r&&(E.type(r,"object","buffer arguments must be an object, a number or an array"),"data"in r&&(E(null===i||Array.isArray(i)||e(i)||Q(i),"invalid data for buffer"),i=r.data),"usage"in r&&(E.parameter(r.usage,ee,"invalid buffer usage"),a=ee[r.usage]),"type"in r&&(E.parameter(r.type,Z,"invalid buffer type"),f=Z[r.type]),"dimension"in r&&(E.type(r.dimension,"number","invalid dimension"),u=0|r.dimension),"length"in r&&(E.nni(o,"buffer length must be a nonnegative integer"),o=0|r.length)),d.bind(),i?c(d,i,a,f,u,s):(o&&t.bufferData(d.type,o,a),d.dtype=f||ie,d.usage=a,d.dimension=u,d.byteLength=o),n.profile&&(d.stats.size=d.byteLength*fe[d.dtype]),m}function p(e,r){E(r+e.byteLength<=d.byteLength,"invalid buffer subdata call, buffer is too small.  Can't write data of size "+e.byteLength+" starting from offset "+r+" to a buffer of size "+d.byteLength),t.bufferSubData(d.type,r,e)}return o[d.id]=d,u||m(a),m._reglType="buffer",m._buffer=d,m.subdata=function(t,r){var n,a=0|(r||0);if(d.bind(),e(t)||t instanceof ArrayBuffer)p(t,a);else if(Array.isArray(t)){if(t.length>0)if("number"==typeof t[0]){var i=W.allocType(d.dtype,t.length);se(i,t),p(i,a),W.freeType(i)}else if(Array.isArray(t[0])||e(t[0])){n=re(t);var o=te(t,n,d.dtype);p(o,a),W.freeType(o)}else E.raise("invalid buffer data")}else if(Q(t)){n=t.shape;var f=t.stride,u=0,s=0,c=0,l=0;1===n.length?(u=n[0],s=1,c=f[0],l=0):2===n.length?(u=n[0],s=n[1],c=f[0],l=f[1]):E.raise("invalid shape");var h=Array.isArray(t.data)?d.dtype:ue(t.data),b=W.allocType(h,u*s);ce(b,t.data,u,s,c,l,t.offset),p(b,a),W.freeType(b)}else E.raise("invalid data for buffer subdata");return m},n.profile&&(m.stats=d.stats),m.destroy=function(){l(d)},m},createStream:function(e,t){var r=u.pop();return r||(r=new f(e)),r.bind(),c(r,t,ae,0,1,!1),r},destroyStream:function(e){u.push(e)},clear:function(){Y(o).forEach(l),u.forEach(l)},getBuffer:function(e){return e&&e._buffer instanceof f?e._buffer:null},restore:function(){Y(o).forEach((function(e){e.buffer=t.createBuffer(),t.bindBuffer(e.type,e.buffer),t.bufferData(e.type,e.persistentData||e.byteLength,e.usage)}))},_initBuffer:c}}(a,l,n,(function(e){return A.destroyBuffer(e)})),w=function(t,r,n,a){var i={},o=0,f={uint8:be,uint16:ge};function u(e){this.id=o++,i[this.id]=this,this.buffer=e,this.primType=pe,this.vertCount=0,this.type=0}r.oes_element_index_uint&&(f.uint32=xe),u.prototype.bind=function(){this.buffer.bind()};var s=[];function c(a,i,o,f,u,s,c){var l;if(a.buffer.bind(),i){var d=c;c||e(i)&&(!Q(i)||e(i.data))||(d=r.oes_element_index_uint?xe:ge),n._initBuffer(a.buffer,i,o,d,3)}else t.bufferData(we,s,o),a.buffer.dtype=l||be,a.buffer.usage=o,a.buffer.dimension=3,a.buffer.byteLength=s;if(l=c,!c){switch(a.buffer.dtype){case be:case he:l=be;break;case ge:case ve:l=ge;break;case xe:case ye:l=xe;break;default:E.raise("unsupported type for element array")}a.buffer.dtype=l}a.type=l,E(l!==xe||!!r.oes_element_index_uint,"32 bit element buffers not supported, enable oes_element_index_uint first");var m=u;m<0&&(m=a.buffer.byteLength,l===ge?m>>=1:l===xe&&(m>>=2)),a.vertCount=m;var p=f;if(f<0){p=pe;var h=a.buffer.dimension;1===h&&(p=de),2===h&&(p=me),3===h&&(p=pe)}a.primType=p}function l(e){a.elementsCount--,E(null!==e.buffer,"must not double destroy elements"),delete i[e.id],e.buffer.destroy(),e.buffer=null}return{create:function(t,r){var i=n.create(null,we,!0),o=new u(i._buffer);function s(t){if(t)if("number"==typeof t)i(t),o.primType=pe,o.vertCount=0|t,o.type=be;else{var r=null,n=_e,a=-1,u=-1,l=0,d=0;Array.isArray(t)||e(t)||Q(t)?r=t:(E.type(t,"object","invalid arguments for elements"),"data"in t&&(r=t.data,E(Array.isArray(r)||e(r)||Q(r),"invalid data for element buffer")),"usage"in t&&(E.parameter(t.usage,ee,"invalid element buffer usage"),n=ee[t.usage]),"primitive"in t&&(E.parameter(t.primitive,le,"invalid element buffer primitive"),a=le[t.primitive]),"count"in t&&(E("number"==typeof t.count&&t.count>=0,"invalid vertex count for elements"),u=0|t.count),"type"in t&&(E.parameter(t.type,f,"invalid buffer type"),d=f[t.type]),"length"in t?l=0|t.length:(l=u,d===ge||d===ve?l*=2:d!==xe&&d!==ye||(l*=4))),c(o,r,n,a,u,l,d)}else i(),o.primType=pe,o.vertCount=0,o.type=be;return s}return a.elementsCount++,s(t),s._reglType="elements",s._elements=o,s.subdata=function(e,t){return i.subdata(e,t),s},s.destroy=function(){l(o)},s},createStream:function(e){var t=s.pop();return t||(t=new u(n.create(null,we,!0,!1)._buffer)),c(t,e,Ae,-1,-1,0,0),t},destroyStream:function(e){s.push(e)},getElements:function(e){return"function"==typeof e&&e._elements instanceof u?e._elements:null},clear:function(){Y(i).forEach(l)}}}(a,d,x,l),A=function(t,r,n,a,i,o,f){for(var u=n.maxAttributes,s=new Array(u),c=0;c<u;++c)s[c]=new Fr;var l=0,d={},m={Record:Fr,scope:{},state:s,currentVAO:null,targetVAO:null,restore:p()?function(){p()&&Y(d).forEach((function(e){e.refresh()}))}:function(){},createVAO:function(t){var n=new b;function f(t){var a;if(Array.isArray(t))a=t,n.elements&&n.ownsElements&&n.elements.destroy(),n.elements=null,n.ownsElements=!1,n.offset=0,n.count=0,n.instances=-1,n.primitive=4;else{if(E("object"==typeof t,"invalid arguments for create vao"),E("attributes"in t,"must specify attributes for vao"),t.elements){var s=t.elements;n.ownsElements?"function"==typeof s&&"elements"===s._reglType?(n.elements.destroy(),n.ownsElements=!1):(n.elements(s),n.ownsElements=!1):o.getElements(t.elements)?(n.elements=t.elements,n.ownsElements=!1):(n.elements=o.create(t.elements),n.ownsElements=!0)}else n.elements=null,n.ownsElements=!1;a=t.attributes,n.offset=0,n.count=-1,n.instances=-1,n.primitive=4,n.elements&&(n.count=n.elements._elements.vertCount,n.primitive=n.elements._elements.primType),"offset"in t&&(n.offset=0|t.offset),"count"in t&&(n.count=0|t.count),"instances"in t&&(n.instances=0|t.instances),"primitive"in t&&(E(t.primitive in le,"bad primitive type: "+t.primitive),n.primitive=le[t.primitive]),E.optional((()=>{for(var e=Object.keys(t),r=0;r<e.length;++r)E(zr.indexOf(e[r])>=0,'invalid option for vao: "'+e[r]+'" valid options are '+zr)})),E(Array.isArray(a),"attributes must be an array")}E(a.length<u,"too many attributes"),E(a.length>0,"must specify at least one attribute");var c={},l=n.attributes;l.length=a.length;for(var d=0;d<a.length;++d){var m,p=a[d],h=l[d]=new Fr,b=p.data||p;Array.isArray(b)||e(b)||Q(b)?(n.buffers[d]&&(m=n.buffers[d],e(b)&&m._buffer.byteLength>=b.byteLength?m.subdata(b):(m.destroy(),n.buffers[d]=null)),n.buffers[d]||(m=n.buffers[d]=i.create(p,jr,!1,!0)),h.buffer=i.getBuffer(m),h.size=0|h.buffer.dimension,h.normalized=!1,h.type=h.buffer.dtype,h.offset=0,h.stride=0,h.divisor=0,h.state=1,c[d]=1):i.getBuffer(p)?(h.buffer=i.getBuffer(p),h.size=0|h.buffer.dimension,h.normalized=!1,h.type=h.buffer.dtype,h.offset=0,h.stride=0,h.divisor=0,h.state=1):i.getBuffer(p.buffer)?(h.buffer=i.getBuffer(p.buffer),h.size=0|(+p.size||h.buffer.dimension),h.normalized=!!p.normalized||!1,"type"in p?(E.parameter(p.type,Z,"invalid buffer type"),h.type=Z[p.type]):h.type=h.buffer.dtype,h.offset=0|(p.offset||0),h.stride=0|(p.stride||0),h.divisor=0|(p.divisor||0),h.state=1,E(h.size>=1&&h.size<=4,"size must be between 1 and 4"),E(h.offset>=0,"invalid offset"),E(h.stride>=0&&h.stride<=255,"stride must be between 0 and 255"),E(h.divisor>=0,"divisor must be positive"),E(!h.divisor||!!r.angle_instanced_arrays,"ANGLE_instanced_arrays must be enabled to use divisor")):"x"in p?(E(d>0,"first attribute must not be a constant"),h.x=+p.x||0,h.y=+p.y||0,h.z=+p.z||0,h.w=+p.w||0,h.state=2):E(!1,"invalid attribute spec for location "+d)}for(var v=0;v<n.buffers.length;++v)!c[v]&&n.buffers[v]&&(n.buffers[v].destroy(),n.buffers[v]=null);return n.refresh(),f}return a.vaoCount+=1,f.destroy=function(){for(var e=0;e<n.buffers.length;++e)n.buffers[e]&&n.buffers[e].destroy();n.buffers.length=0,n.ownsElements&&(n.elements.destroy(),n.elements=null,n.ownsElements=!1),n.destroy()},f._vao=n,f._reglType="vao",f(t)},getVAO:function(e){return"function"==typeof e&&e._vao?e._vao:null},destroyBuffer:function(e){for(var r=0;r<s.length;++r){var n=s[r];n.buffer===e&&(t.disableVertexAttribArray(r),n.buffer=null)}},setVAO:p()?function(e){if(e!==m.currentVAO){var t=p();e?t.bindVertexArrayOES(e.vao):t.bindVertexArrayOES(null),m.currentVAO=e}}:function(e){if(e!==m.currentVAO){if(e)e.bindAttrs();else{for(var r=h(),n=0;n<s.length;++n){var a=s[n];a.buffer?(t.enableVertexAttribArray(n),a.buffer.bind(),t.vertexAttribPointer(n,a.size,a.type,a.normalized,a.stride,a.offfset),r&&a.divisor&&r.vertexAttribDivisorANGLE(n,a.divisor)):(t.disableVertexAttribArray(n),t.vertexAttrib4f(n,a.x,a.y,a.z,a.w))}f.elements?t.bindBuffer(Cr,f.elements.buffer.buffer):t.bindBuffer(Cr,null)}m.currentVAO=e}},clear:p()?function(){Y(d).forEach((function(e){e.destroy()}))}:function(){}};function p(){return r.oes_vertex_array_object}function h(){return r.angle_instanced_arrays}function b(){this.id=++l,this.attributes=[],this.elements=null,this.ownsElements=!1,this.count=0,this.offset=0,this.instances=-1,this.primitive=4;var e=p();this.vao=e?e.createVertexArrayOES():null,d[this.id]=this,this.buffers=[]}return b.prototype.bindAttrs=function(){for(var e=h(),r=this.attributes,n=0;n<r.length;++n){var a=r[n];a.buffer?(t.enableVertexAttribArray(n),t.bindBuffer(jr,a.buffer.buffer),t.vertexAttribPointer(n,a.size,a.type,a.normalized,a.stride,a.offset),e&&a.divisor&&e.vertexAttribDivisorANGLE(n,a.divisor)):(t.disableVertexAttribArray(n),t.vertexAttrib4f(n,a.x,a.y,a.z,a.w))}for(var i=r.length;i<u;++i)t.disableVertexAttribArray(i);var f=o.getElements(this.elements);f?t.bindBuffer(Cr,f.buffer.buffer):t.bindBuffer(Cr,null)},b.prototype.refresh=function(){var e=p();e&&(e.bindVertexArrayOES(this.vao),this.bindAttrs(),m.currentVAO=null,e.bindVertexArrayOES(null))},b.prototype.destroy=function(){if(this.vao){var e=p();this===m.currentVAO&&(m.currentVAO=null,e.bindVertexArrayOES(null)),e.deleteVertexArrayOES(this.vao),this.vao=null}this.ownsElements&&(this.elements.destroy(),this.elements=null,this.ownsElements=!1),d[this.id]&&(delete d[this.id],a.vaoCount-=1)},m}(a,d,y,l,x,w,g),_=function(e,r,n,a){var i={},o={};function f(e,t,r,n){this.name=e,this.id=t,this.location=r,this.info=n}function u(e,t){for(var r=0;r<e.length;++r)if(e[r].id===t.id)return void(e[r].location=t.location);e.push(t)}function s(t,n,a){var f=t===Vr?i:o,u=f[n];if(!u){var s=r.str(n);u=e.createShader(t),e.shaderSource(u,s),e.compileShader(u),E.shaderError(e,u,s,t,a),f[n]=u}return u}var c={},l=[],d=0;function m(e,t){this.id=d++,this.fragId=e,this.vertId=t,this.program=null,this.uniforms=[],this.attributes=[],this.refCount=1,a.profile&&(this.stats={uniformsCount:0,attributesCount:0})}function p(t,n,i){var o,c,l=s(Vr,t.fragId),d=s(Br,t.vertId),m=t.program=e.createProgram();if(e.attachShader(m,l),e.attachShader(m,d),i)for(o=0;o<i.length;++o){var p=i[o];e.bindAttribLocation(m,p[0],p[1])}e.linkProgram(m),E.linkError(e,m,r.str(t.fragId),r.str(t.vertId),n);var h=e.getProgramParameter(m,Ir);a.profile&&(t.stats.uniformsCount=h);var b=t.uniforms;for(o=0;o<h;++o)if(c=e.getActiveUniform(m,o)){if(c.size>1)for(var v=0;v<c.size;++v){var g=c.name.replace("[0]","["+v+"]");u(b,new f(g,r.id(g),e.getUniformLocation(m,g),c))}var y=c.name;c.size>1&&(y=y.replace("[0]","")),u(b,new f(y,r.id(y),e.getUniformLocation(m,y),c))}var x=e.getProgramParameter(m,Pr);a.profile&&(t.stats.attributesCount=x);var w=t.attributes;for(o=0;o<x;++o)(c=e.getActiveAttrib(m,o))&&u(w,new f(c.name,r.id(c.name),e.getAttribLocation(m,c.name),c))}return a.profile&&(n.getMaxUniformsCount=function(){var e=0;return l.forEach((function(t){t.stats.uniformsCount>e&&(e=t.stats.uniformsCount)})),e},n.getMaxAttributesCount=function(){var e=0;return l.forEach((function(t){t.stats.attributesCount>e&&(e=t.stats.attributesCount)})),e}),{clear:function(){var t=e.deleteShader.bind(e);Y(i).forEach(t),i={},Y(o).forEach(t),o={},l.forEach((function(t){e.deleteProgram(t.program)})),l.length=0,c={},n.shaderCount=0},program:function(r,a,f,u){E.command(r>=0,"missing vertex shader",f),E.command(a>=0,"missing fragment shader",f);var s=c[a];s||(s=c[a]={});var d=s[r];if(d&&(d.refCount++,!u))return d;var h=new m(a,r);return n.shaderCount++,p(h,f,u),d||(s[r]=h),l.push(h),t(h,{destroy:function(){if(h.refCount--,h.refCount<=0){e.deleteProgram(h.program);var t=l.indexOf(h);l.splice(t,1),n.shaderCount--}s[h.vertId].refCount<=0&&(e.deleteShader(o[h.vertId]),delete o[h.vertId],delete c[h.fragId][h.vertId]),Object.keys(c[h.fragId]).length||(e.deleteShader(i[h.fragId]),delete i[h.fragId],delete c[h.fragId])}})},restore:function(){i={},o={};for(var e=0;e<l.length;++e)p(l[e],null,l[e].attributes.map((function(e){return[e.location,e.name]})))},shader:s,frag:-1,vert:-1}}(a,c,l,n),k=or(a,d,y,(function(){T.procs.poll()}),v,l,n),S=function(e,t,r,n,a){var i={rgba4:ur,rgb565:36194,"rgb5 a1":32855,depth:33189,stencil:36168,"depth stencil":34041};t.ext_srgb&&(i.srgba=35907),t.ext_color_buffer_half_float&&(i.rgba16f=34842,i.rgb16f=34843),t.webgl_color_buffer_float&&(i.rgba32f=34836);var o=[];Object.keys(i).forEach((function(e){var t=i[e];o[t]=e}));var f=0,u={};function s(e){this.id=f++,this.refCount=1,this.renderbuffer=e,this.format=ur,this.width=0,this.height=0,a.profile&&(this.stats={size:0})}function c(t){var r=t.renderbuffer;E(r,"must not double destroy renderbuffer"),e.bindRenderbuffer(fr,null),e.deleteRenderbuffer(r),t.renderbuffer=null,t.refCount=0,delete u[t.id],n.renderbufferCount--}return s.prototype.decRef=function(){--this.refCount<=0&&c(this)},a.profile&&(n.getTotalRenderbufferSize=function(){var e=0;return Object.keys(u).forEach((function(t){e+=u[t].stats.size})),e}),{create:function(t,f){var c=new s(e.createRenderbuffer());function l(t,n){var f=0,u=0,s=ur;if("object"==typeof t&&t){var d=t;if("shape"in d){var m=d.shape;E(Array.isArray(m)&&m.length>=2,"invalid renderbuffer shape"),f=0|m[0],u=0|m[1]}else"radius"in d&&(f=u=0|d.radius),"width"in d&&(f=0|d.width),"height"in d&&(u=0|d.height);"format"in d&&(E.parameter(d.format,i,"invalid renderbuffer format"),s=i[d.format])}else"number"==typeof t?(f=0|t,u="number"==typeof n?0|n:f):t?E.raise("invalid arguments to renderbuffer constructor"):f=u=1;if(E(f>0&&u>0&&f<=r.maxRenderbufferSize&&u<=r.maxRenderbufferSize,"invalid renderbuffer size"),f!==c.width||u!==c.height||s!==c.format)return l.width=c.width=f,l.height=c.height=u,c.format=s,e.bindRenderbuffer(fr,c.renderbuffer),e.renderbufferStorage(fr,s,f,u),E(0===e.getError(),"invalid render buffer format"),a.profile&&(c.stats.size=cr(c.format,c.width,c.height)),l.format=o[c.format],l}return u[c.id]=c,n.renderbufferCount++,l(t,f),l.resize=function(t,n){var i=0|t,o=0|n||i;return i===c.width&&o===c.height||(E(i>0&&o>0&&i<=r.maxRenderbufferSize&&o<=r.maxRenderbufferSize,"invalid renderbuffer size"),l.width=c.width=i,l.height=c.height=o,e.bindRenderbuffer(fr,c.renderbuffer),e.renderbufferStorage(fr,c.format,i,o),E(0===e.getError(),"invalid render buffer format"),a.profile&&(c.stats.size=cr(c.format,c.width,c.height))),l},l._reglType="renderbuffer",l._renderbuffer=c,a.profile&&(l.stats=c.stats),l.destroy=function(){c.decRef()},l},clear:function(){Y(u).forEach(c)},restore:function(){Y(u).forEach((function(t){t.renderbuffer=e.createRenderbuffer(),e.bindRenderbuffer(fr,t.renderbuffer),e.renderbufferStorage(fr,t.format,t.width,t.height)})),e.bindRenderbuffer(fr,null)}}}(a,d,y,l,n),O=function(e,r,n,a,i,o){var f={cur:null,next:null,dirty:!1,setFBO:null},u=["rgba"],s=["rgba4","rgb565","rgb5 a1"];r.ext_srgb&&s.push("srgba"),r.ext_color_buffer_half_float&&s.push("rgba16f","rgb16f"),r.webgl_color_buffer_float&&s.push("rgba32f");var c=["uint8"];function l(e,t,r){this.target=e,this.texture=t,this.renderbuffer=r;var n=0,a=0;t?(n=t.width,a=t.height):r&&(n=r.width,a=r.height),this.width=n,this.height=a}function d(e){e&&(e.texture&&e.texture._texture.decRef(),e.renderbuffer&&e.renderbuffer._renderbuffer.decRef())}function m(e,t,r){if(e)if(e.texture){var n=e.texture._texture,a=Math.max(1,n.width),i=Math.max(1,n.height);E(a===t&&i===r,"inconsistent width/height for supplied texture"),n.refCount+=1}else{var o=e.renderbuffer._renderbuffer;E(o.width===t&&o.height===r,"inconsistent width/height for renderbuffer"),o.refCount+=1}}function p(t,r){r&&(r.texture?e.framebufferTexture2D(lr,t,r.target,r.texture._texture.texture,0):e.framebufferRenderbuffer(lr,t,dr,r.renderbuffer._renderbuffer.renderbuffer))}function h(e){var t=mr,r=null,n=null,a=e;"object"==typeof e&&(a=e.data,"target"in e&&(t=0|e.target)),E.type(a,"function","invalid attachment data");var i=a._reglType;return"texture2d"===i?(r=a,E(t===mr)):"textureCube"===i?(r=a,E(t>=pr&&t<pr+6,"invalid cube map target")):"renderbuffer"===i?(n=a,t=dr):E.raise("invalid regl object for attachment"),new l(t,r,n)}function b(e,t,r,n,o){if(r){var f=a.create2D({width:e,height:t,format:n,type:o});return f._texture.refCount=0,new l(mr,f,null)}var u=i.create({width:e,height:t,format:n});return u._renderbuffer.refCount=0,new l(dr,null,u)}function v(e){return e&&(e.texture||e.renderbuffer)}function g(e,t,r){e&&(e.texture?e.texture.resize(t,r):e.renderbuffer&&e.renderbuffer.resize(t,r),e.width=t,e.height=r)}r.oes_texture_half_float&&c.push("half float","float16"),r.oes_texture_float&&c.push("float","float32");var y=0,x={};function w(){this.id=y++,x[this.id]=this,this.framebuffer=e.createFramebuffer(),this.width=0,this.height=0,this.colorAttachments=[],this.depthAttachment=null,this.stencilAttachment=null,this.depthStencilAttachment=null}function A(e){e.colorAttachments.forEach(d),d(e.depthAttachment),d(e.stencilAttachment),d(e.depthStencilAttachment)}function _(t){var r=t.framebuffer;E(r,"must not double destroy framebuffer"),e.deleteFramebuffer(r),t.framebuffer=null,o.framebufferCount--,delete x[t.id]}function k(t){var r;e.bindFramebuffer(lr,t.framebuffer);var a=t.colorAttachments;for(r=0;r<a.length;++r)p(hr+r,a[r]);for(r=a.length;r<n.maxColorAttachments;++r)e.framebufferTexture2D(lr,hr+r,mr,null,0);e.framebufferTexture2D(lr,gr,mr,null,0),e.framebufferTexture2D(lr,br,mr,null,0),e.framebufferTexture2D(lr,vr,mr,null,0),p(br,t.depthAttachment),p(vr,t.stencilAttachment),p(gr,t.depthStencilAttachment);var i=e.checkFramebufferStatus(lr);e.isContextLost()||i===yr||E.raise("framebuffer configuration not supported, status = "+Tr[i]),e.bindFramebuffer(lr,f.next?f.next.framebuffer:null),f.cur=f.next,e.getError()}function S(e,a){var i=new w;function l(e,t){var a;E(f.next!==i,"can not update framebuffer which is currently in use");var o=0,d=0,p=!0,g=!0,y=null,x=!0,w="rgba",_="uint8",S=1,O=null,T=null,D=null,j=!1;if("number"==typeof e)o=0|e,d=0|t||o;else if(e){E.type(e,"object","invalid arguments for framebuffer");var C=e;if("shape"in C){var z=C.shape;E(Array.isArray(z)&&z.length>=2,"invalid shape for framebuffer"),o=z[0],d=z[1]}else"radius"in C&&(o=d=C.radius),"width"in C&&(o=C.width),"height"in C&&(d=C.height);("color"in C||"colors"in C)&&(y=C.color||C.colors,Array.isArray(y)&&E(1===y.length||r.webgl_draw_buffers,"multiple render targets not supported")),y||("colorCount"in C&&(S=0|C.colorCount,E(S>0,"invalid color buffer count")),"colorTexture"in C&&(x=!!C.colorTexture,w="rgba4"),"colorType"in C&&(_=C.colorType,x?(E(r.oes_texture_float||!("float"===_||"float32"===_),"you must enable OES_texture_float in order to use floating point framebuffer objects"),E(r.oes_texture_half_float||!("half float"===_||"float16"===_),"you must enable OES_texture_half_float in order to use 16-bit floating point framebuffer objects")):"half float"===_||"float16"===_?(E(r.ext_color_buffer_half_float,"you must enable EXT_color_buffer_half_float to use 16-bit render buffers"),w="rgba16f"):"float"!==_&&"float32"!==_||(E(r.webgl_color_buffer_float,"you must enable WEBGL_color_buffer_float in order to use 32-bit floating point renderbuffers"),w="rgba32f"),E.oneOf(_,c,"invalid color type")),"colorFormat"in C&&(w=C.colorFormat,u.indexOf(w)>=0?x=!0:s.indexOf(w)>=0?x=!1:E.optional((function(){x?E.oneOf(C.colorFormat,u,"invalid color format for texture"):E.oneOf(C.colorFormat,s,"invalid color format for renderbuffer")})))),("depthTexture"in C||"depthStencilTexture"in C)&&(j=!(!C.depthTexture&&!C.depthStencilTexture),E(!j||r.webgl_depth_texture,"webgl_depth_texture extension not supported")),"depth"in C&&("boolean"==typeof C.depth?p=C.depth:(O=C.depth,g=!1)),"stencil"in C&&("boolean"==typeof C.stencil?g=C.stencil:(T=C.stencil,p=!1)),"depthStencil"in C&&("boolean"==typeof C.depthStencil?p=g=C.depthStencil:(D=C.depthStencil,p=!1,g=!1))}else o=d=1;var F=null,V=null,B=null,I=null;if(Array.isArray(y))F=y.map(h);else if(y)F=[h(y)];else for(F=new Array(S),a=0;a<S;++a)F[a]=b(o,d,x,w,_);E(r.webgl_draw_buffers||F.length<=1,"you must enable the WEBGL_draw_buffers extension in order to use multiple color buffers."),E(F.length<=n.maxColorAttachments,"too many color attachments, not supported"),o=o||F[0].width,d=d||F[0].height,O?V=h(O):p&&!g&&(V=b(o,d,j,"depth","uint32")),T?B=h(T):g&&!p&&(B=b(o,d,!1,"stencil","uint8")),D?I=h(D):!O&&!T&&g&&p&&(I=b(o,d,j,"depth stencil","depth stencil")),E(!!O+!!T+!!D<=1,"invalid framebuffer configuration, can specify exactly one depth/stencil attachment");var P=null;for(a=0;a<F.length;++a)if(m(F[a],o,d),E(!F[a]||F[a].texture&&wr.indexOf(F[a].texture._texture.format)>=0||F[a].renderbuffer&&Er.indexOf(F[a].renderbuffer._renderbuffer.format)>=0,"framebuffer color attachment "+a+" is invalid"),F[a]&&F[a].texture){var L=Ar[F[a].texture._texture.format]*_r[F[a].texture._texture.type];null===P?P=L:E(P===L,"all color attachments much have the same number of bits per pixel.")}return m(V,o,d),E(!V||V.texture&&V.texture._texture.format===xr||V.renderbuffer&&V.renderbuffer._renderbuffer.format===kr,"invalid depth attachment for framebuffer object"),m(B,o,d),E(!B||B.renderbuffer&&B.renderbuffer._renderbuffer.format===Sr,"invalid stencil attachment for framebuffer object"),m(I,o,d),E(!I||I.texture&&I.texture._texture.format===Or||I.renderbuffer&&I.renderbuffer._renderbuffer.format===Or,"invalid depth-stencil attachment for framebuffer object"),A(i),i.width=o,i.height=d,i.colorAttachments=F,i.depthAttachment=V,i.stencilAttachment=B,i.depthStencilAttachment=I,l.color=F.map(v),l.depth=v(V),l.stencil=v(B),l.depthStencil=v(I),l.width=i.width,l.height=i.height,k(i),l}return o.framebufferCount++,l(e,a),t(l,{resize:function(e,t){E(f.next!==i,"can not resize a framebuffer which is currently in use");var r=Math.max(0|e,1),n=Math.max(0|t||r,1);if(r===i.width&&n===i.height)return l;for(var a=i.colorAttachments,o=0;o<a.length;++o)g(a[o],r,n);return g(i.depthAttachment,r,n),g(i.stencilAttachment,r,n),g(i.depthStencilAttachment,r,n),i.width=l.width=r,i.height=l.height=n,k(i),l},_reglType:"framebuffer",_framebuffer:i,destroy:function(){_(i),A(i)},use:function(e){f.setFBO({framebuffer:l},e)}})}return t(f,{getFramebuffer:function(e){if("function"==typeof e&&"framebuffer"===e._reglType){var t=e._framebuffer;if(t instanceof w)return t}return null},create:S,createCube:function(e){var i=Array(6);function o(e){var n;E(i.indexOf(f.next)<0,"can not update framebuffer which is currently in use");var s,l={color:null},d=0,m=null,p="rgba",h="uint8",b=1;if("number"==typeof e)d=0|e;else if(e){E.type(e,"object","invalid arguments for framebuffer");var v=e;if("shape"in v){var g=v.shape;E(Array.isArray(g)&&g.length>=2,"invalid shape for framebuffer"),E(g[0]===g[1],"cube framebuffer must be square"),d=g[0]}else"radius"in v&&(d=0|v.radius),"width"in v?(d=0|v.width,"height"in v&&E(v.height===d,"must be square")):"height"in v&&(d=0|v.height);("color"in v||"colors"in v)&&(m=v.color||v.colors,Array.isArray(m)&&E(1===m.length||r.webgl_draw_buffers,"multiple render targets not supported")),m||("colorCount"in v&&(b=0|v.colorCount,E(b>0,"invalid color buffer count")),"colorType"in v&&(E.oneOf(v.colorType,c,"invalid color type"),h=v.colorType),"colorFormat"in v&&(p=v.colorFormat,E.oneOf(v.colorFormat,u,"invalid color format for texture"))),"depth"in v&&(l.depth=v.depth),"stencil"in v&&(l.stencil=v.stencil),"depthStencil"in v&&(l.depthStencil=v.depthStencil)}else d=1;if(m)if(Array.isArray(m))for(s=[],n=0;n<m.length;++n)s[n]=m[n];else s=[m];else{s=Array(b);var y={radius:d,format:p,type:h};for(n=0;n<b;++n)s[n]=a.createCube(y)}for(l.color=Array(s.length),n=0;n<s.length;++n){var x=s[n];E("function"==typeof x&&"textureCube"===x._reglType,"invalid cube map"),d=d||x.width,E(x.width===d&&x.height===d,"invalid cube map shape"),l.color[n]={target:pr,data:s[n]}}for(n=0;n<6;++n){for(var w=0;w<s.length;++w)l.color[w].target=pr+n;n>0&&(l.depth=i[0].depth,l.stencil=i[0].stencil,l.depthStencil=i[0].depthStencil),i[n]?i[n](l):i[n]=S(l)}return t(o,{width:d,height:d,color:s})}return o(e),t(o,{faces:i,resize:function(e){var t,r=0|e;if(E(r>0&&r<=n.maxCubeMapSize,"invalid radius for cube fbo"),r===o.width)return o;var a=o.color;for(t=0;t<a.length;++t)a[t].resize(r);for(t=0;t<6;++t)i[t].resize(r);return o.width=o.height=r,o},_reglType:"framebufferCube",destroy:function(){i.forEach((function(e){e.destroy()}))}})},clear:function(){Y(x).forEach(_)},restore:function(){f.cur=null,f.next=null,f.dirty=!0,Y(x).forEach((function(t){t.framebuffer=e.createFramebuffer(),k(t)}))}})}(a,d,y,k,S,l),T=ei(a,c,d,y,x,w,0,O,{},A,_,g,v,m,n),D=Wr(a,O,T.procs.poll,v,i,d,y),j=T.next,C=a.canvas,z=[],I=[],P=[],R=[n.onDestroy],M=null;function U(){if(0===z.length)return m&&m.update(),void(M=null);M=V.next(U),De();for(var e=z.length-1;e>=0;--e){var t=z[e];t&&t(v,null,0)}a.flush(),m&&m.update()}function X(){!M&&z.length>0&&(M=V.next(U))}function $(){M&&(V.cancel(U),M=null)}function K(e){e.preventDefault(),o=!0,$(),I.forEach((function(e){e()}))}function J(e){a.getError(),o=!1,f.restore(),_.restore(),x.restore(),k.restore(),S.restore(),O.restore(),A.restore(),m&&m.restore(),T.procs.refresh(),X(),P.forEach((function(e){e()}))}function ke(e){function r(e,t){var r={},n={};return Object.keys(e).forEach((function(a){var i=e[a];if(F.isDynamic(i))n[a]=F.unbox(i,a);else{if(t&&Array.isArray(i))for(var o=0;o<i.length;++o)if(F.isDynamic(i[o]))return void(n[a]=F.unbox(i,a));r[a]=i}})),{dynamic:n,static:r}}E(!!e,"invalid args to regl({...})"),E.type(e,"object","invalid args to regl({...})");var n=r(e.context||{},!0),a=r(e.uniforms||{},!0),i=r(e.attributes||{},!1),f=r(function(e){var r=t({},e);function n(e){if(e in r){var t=r[e];delete r[e],Object.keys(t).forEach((function(n){r[e+"."+n]=t[n]}))}}return delete r.uniforms,delete r.attributes,delete r.context,delete r.vao,"stencil"in r&&r.stencil.op&&(r.stencil.opBack=r.stencil.opFront=r.stencil.op,delete r.stencil.op),n("blend"),n("depth"),n("cull"),n("stencil"),n("polygonOffset"),n("scissor"),n("sample"),"vao"in e&&(r.vao=e.vao),r}(e),!1),u={gpuTime:0,cpuTime:0,count:0},s=T.compile(f,i,a,n,u),c=s.draw,l=s.batch,d=s.scope,m=[];return t((function(e,t){var r;if(o&&E.raise("context lost"),"function"==typeof e)return d.call(this,null,e,0);if("function"==typeof t)if("number"==typeof e)for(r=0;r<e;++r)d.call(this,null,t,r);else{if(!Array.isArray(e))return d.call(this,e,t,0);for(r=0;r<e.length;++r)d.call(this,e[r],t,r)}else if("number"==typeof e){if(e>0)return l.call(this,function(e){for(;m.length<e;)m.push(null);return m}(0|e),0|e)}else{if(!Array.isArray(e))return c.call(this,e);if(e.length)return l.call(this,e,e.length)}}),{stats:u,destroy:function(){s.destroy()}})}C&&(C.addEventListener(ri,K,!1),C.addEventListener(ni,J,!1));var Se=O.setFBO=ke({framebuffer:F.define.call(null,1,"framebuffer")});function Oe(e,t){var r=0;T.procs.poll();var n=t.color;n&&(a.clearColor(+n[0]||0,+n[1]||0,+n[2]||0,+n[3]||0),r|=16384),"depth"in t&&(a.clearDepth(+t.depth),r|=256),"stencil"in t&&(a.clearStencil(0|t.stencil),r|=1024),E(!!r,"called regl.clear with no buffer specified"),a.clear(r)}function Ee(e){return E.type(e,"function","regl.frame() callback must be a function"),z.push(e),X(),{cancel:function(){var t=ai(z,e);E(t>=0,"cannot cancel a frame twice"),z[t]=function e(){var t=ai(z,e);z[t]=z[z.length-1],z.length-=1,z.length<=0&&$()}}}}function Te(){var e=j.viewport,t=j.scissor_box;e[0]=e[1]=t[0]=t[1]=0,v.viewportWidth=v.framebufferWidth=v.drawingBufferWidth=e[2]=t[2]=a.drawingBufferWidth,v.viewportHeight=v.framebufferHeight=v.drawingBufferHeight=e[3]=t[3]=a.drawingBufferHeight}function De(){v.tick+=1,v.time=Ce(),Te(),T.procs.poll()}function je(){k.refresh(),Te(),T.procs.refresh(),m&&m.update()}function Ce(){return(B()-p)/1e3}je();var ze=t(ke,{clear:function(e){if(E("object"==typeof e&&e,"regl.clear() takes an object as input"),"framebuffer"in e)if(e.framebuffer&&"framebufferCube"===e.framebuffer_reglType)for(var r=0;r<6;++r)Se(t({framebuffer:e.framebuffer.faces[r]},e),Oe);else Se(e,Oe);else Oe(0,e)},prop:F.define.bind(null,1),context:F.define.bind(null,2),this:F.define.bind(null,3),draw:ke({}),buffer:function(e){return x.create(e,34962,!1,!1)},elements:function(e){return w.create(e,!1)},texture:k.create2D,cube:k.createCube,renderbuffer:S.create,framebuffer:O.create,framebufferCube:O.createCube,vao:A.createVAO,attributes:i,frame:Ee,on:function(e,t){var r;switch(E.type(t,"function","listener callback must be a function"),e){case"frame":return Ee(t);case"lost":r=I;break;case"restore":r=P;break;case"destroy":r=R;break;default:E.raise("invalid event, must be one of frame,lost,restore,destroy")}return r.push(t),{cancel:function(){for(var e=0;e<r.length;++e)if(r[e]===t)return r[e]=r[r.length-1],void r.pop()}}},limits:y,hasExtension:function(e){return y.extensions.indexOf(e.toLowerCase())>=0},read:D,destroy:function(){z.length=0,$(),C&&(C.removeEventListener(ri,K),C.removeEventListener(ni,J)),_.clear(),O.clear(),S.clear(),A.clear(),k.clear(),w.clear(),x.clear(),m&&m.clear(),R.forEach((function(e){e()}))},_gl:a,_refresh:je,poll:function(){De(),m&&m.update()},now:Ce,stats:l});return n.onDone(null,ze),ze}},"object"==typeof r&&void 0!==t?t.exports=o():"function"==typeof define&&define.amd?define(o):i.createREGL=o()},
550: function _(t,e,a,s,r){s();const n=t(551),_=t(10),o=t(13);class c{constructor(t){this._regl=t,this._map=new Map}_create_texture(t){const e=t.length;let a=0;const s=[];let r=0,_=0;for(let n=0;n<e;n++)a+=t[n],s.push(t[n]+t[(n+1)%e]),n%2==0?_=Math.max(_,t[n]):r=Math.min(r,-t[n]);r*=.5,_*=.5;const o=(0,n.gcd)(s),c=[0];for(let a=0;a<e;a++)c.push(c[a]+t[a]);const h=2*a/o,i=(0,n.is_pow_2)(h),l=i?h:128,g=.5*o*h/l;let p;if(i){if(p=.5*t[0],g<p){p-=Math.floor(p/g)*g}}else p=0;const u=p-.5*g,m=new Uint8Array(l);let f=0;for(let e=0;e<l;e++){const a=p+e*g;a>c[f+1]&&f++;const s=t[f],n=c[f]+.5*s;let o=.5*s-Math.abs(a-n);f%2==1&&(o=-o),m[e]=Math.round(255*(o-r)/(_-r))}return[[a,u,r,_],this._regl.texture({shape:[l,1,1],data:m,wrapS:"repeat",format:"alpha",type:"uint8",mag:"linear",min:"linear"})]}_get_key(t){return t.join(",")}_get_or_create(t){const e=this._get_key(t);let a=this._map.get(e);if(null==a){const s=(0,n.gcd)(t);if(s>1){t=(0,o.map)(t,(t=>t/s)),a=this._get_or_create(t);const[r,n,_]=a;a=[r,n,s],this._map.set(e,a)}else{const[r,n]=this._create_texture(t);a=[r,n,s],this._map.set(e,a)}}return a}get(t){return t.length%2==1&&(t=(0,_.concat)([t,t])),this._get_or_create(t)}}a.DashCache=c,c.__name__="DashCache"},
551: function _(n,t,e,r,o){function u(n,t){let e,r;n>t?(e=n,r=t):(e=t,r=n);let o=e%r;for(;0!=o;)e=r,r=o,o=e%r;return r}r(),e.gcd=function(n){let t=n[0];for(let e=1;e<n.length;e++)t=u(t,n[e]);return t},e.is_pow_2=function(n){return!(n&n-1)&&0!=n}},
552: function _(n,i,o,t,_){t();o.default="\nprecision mediump float;\n\nattribute vec2 a_position;\nvarying vec2 v_tex_coords;\n\nvoid main()\n{\n  gl_Position = vec4(a_position.x, a_position.y, 0.0, 1.0);\n  v_tex_coords = 0.5*(1.0 + a_position);\n}\n"},
553: function _(n,e,r,o,f){o();r.default="\nprecision mediump float;\n\nuniform sampler2D u_framebuffer_tex;\nvarying vec2 v_tex_coords;\n\nvoid main()\n{\n  gl_FragColor = texture2D(u_framebuffer_tex, v_tex_coords);\n}\n"},
554: function _(n,o,i,s,a){s();i.default="\nprecision mediump float;\n\nattribute vec2 a_position;\nattribute vec4 a_bounds;\n\nuniform vec2 u_canvas_size;\n\nvarying vec2 v_tex_coords;\n\nvoid main()\n{\n  v_tex_coords = vec2(a_position.x < 0.0 ? 0.0 : 1.0, a_position.y < 0.0 ? 0.0 : 1.0);\n\n  float x = a_position.x < 0.0 ? a_bounds[0] : a_bounds[2];\n  float y = a_position.y < 0.0 ? a_bounds[1] : a_bounds[3];\n  vec2 xy = vec2(x, y);\n\n  vec2 pos = xy + 0.5;  // Bokeh's offset.\n  pos /= u_canvas_size;  // in 0..1\n  gl_Position = vec4(2.0*pos.x - 1.0, 1.0 - 2.0*pos.y, 0.0, 1.0);\n}\n"},
555: function _(a,l,o,n,e){n();o.default="\nprecision mediump float;\n\nuniform sampler2D u_tex;\nuniform float u_global_alpha;\n\nvarying vec2 v_tex_coords;\n\nvoid main()\n{\n  vec4 color = texture2D(u_tex, v_tex_coords);\n  float alpha = color.a*u_global_alpha;\n  gl_FragColor = vec4(color.rgb*alpha, alpha);  // Premultiplied alpha.\n}\n"},
556: function _(n,t,_,e,i){e();_.default="\nprecision mediump float;\n\nconst int butt_cap   = 0;\nconst int round_cap  = 1;\nconst int square_cap = 2;\n\nconst int miter_join = 0;\nconst int round_join = 1;\nconst int bevel_join = 2;\n\nattribute vec2 a_position;\nattribute vec2 a_point_prev;\nattribute vec2 a_point_start;\nattribute vec2 a_point_end;\nattribute vec2 a_point_next;\nattribute float a_show_prev;\nattribute float a_show_curr;\nattribute float a_show_next;\nattribute float a_linewidth;\nattribute vec4 a_line_color;\nattribute float a_line_cap;\nattribute float a_line_join;\n#ifdef DASHED\nattribute float a_length_so_far;\nattribute vec4 a_dash_tex_info;\nattribute float a_dash_scale;\nattribute float a_dash_offset;\n#endif\n\nuniform vec2 u_canvas_size;\nuniform float u_antialias;\nuniform float u_miter_limit;\n\nvarying float v_linewidth;\nvarying vec4 v_line_color;\nvarying float v_line_cap;\nvarying float v_line_join;\nvarying float v_segment_length;\nvarying vec2 v_coords;\nvarying float v_flags;  // Boolean flags\nvarying float v_cos_turn_angle_start;\nvarying float v_cos_turn_angle_end;\n#ifdef DASHED\nvarying float v_length_so_far;\nvarying vec4 v_dash_tex_info;\nvarying float v_dash_scale;\nvarying float v_dash_offset;\n#endif\n\n#define SMALL 1e-6\n\nfloat cross_z(in vec2 v0, in vec2 v1)\n{\n    return v0.x*v1.y - v0.y*v1.x;\n}\n\nvec2 right_vector(in vec2 v)\n{\n    return vec2(v.y, -v.x);\n}\n\n// Calculate cos/sin turn angle with adjacent segment, and unit normal vector to right\nfloat calc_turn_angle(in bool has_cap, in vec2 segment_right, in vec2 other_right, out vec2 point_right, out float sin_turn_angle)\n{\n    float cos_turn_angle;\n    vec2 diff = segment_right + other_right;\n    float len = length(diff);\n    if (has_cap || len < SMALL) {\n        point_right = segment_right;\n        cos_turn_angle = -1.0;  // Turns back on itself.\n        sin_turn_angle = 0.0;\n    }\n    else {\n        point_right = diff / len;\n        cos_turn_angle = dot(segment_right, other_right);   // cos zero at +/-pi/2, +ve angle is turn right\n        sin_turn_angle = cross_z(segment_right, other_right);\n    }\n    return cos_turn_angle;\n}\n\n// If miter too large use bevel join instead\nbool miter_too_large(in int join_type, in float cos_turn_angle)\n{\n    float cos_half_angle_sqr = 0.5*(1.0 + cos_turn_angle);  // Trig identity\n    return join_type == miter_join && cos_half_angle_sqr < 1.0 / (u_miter_limit*u_miter_limit);\n}\n\nvec2 normalize_check_len(in vec2 vec, in float len)\n{\n    if (abs(len) < SMALL)\n        return vec2(1.0, 0.0);\n    else\n        return vec / len;\n}\n\nvec2 normalize_check(in vec2 vec)\n{\n    return normalize_check_len(vec, length(vec));\n}\n\nvoid main()\n{\n    if (a_show_curr < 0.5) {\n        // Line segment has non-finite value at one or both ends, do not render.\n        gl_Position = vec4(-2.0, -2.0, 0.0, 1.0);\n        return;\n    }\n\n    int join_type = int(a_line_join + 0.5);\n    int cap_type = int(a_line_cap + 0.5);\n\n    v_linewidth = a_linewidth;\n    v_line_color = a_line_color;\n    if (v_linewidth < 1.0) {\n        // Linewidth less than 1 is implemented as 1 but with reduced alpha.\n        v_line_color.a *= v_linewidth;\n        v_linewidth = 1.0;\n    }\n\n    float halfwidth = 0.5*(v_linewidth + u_antialias);\n\n    vec2 segment_along = a_point_end - a_point_start;\n    v_segment_length = length(a_point_end - a_point_start);\n    segment_along = normalize_check_len(segment_along, v_segment_length); // unit vector.\n    vec2 segment_right = right_vector(segment_along);  // unit vector.\n    vec2 xy;\n\n    // in screen coords\n    vec2 prev_along = normalize_check(a_point_start - a_point_prev);\n    vec2 prev_right = right_vector(prev_along);\n    vec2 next_right = right_vector(normalize_check(a_point_next - a_point_end));\n\n    v_coords.y = a_position.y*halfwidth;  // Overwritten later for join points.\n\n    // Start and end cap properties\n    bool has_start_cap = a_show_prev < 0.5;\n    bool has_end_cap = a_show_next < 0.5;\n\n    // Start and end join properties\n    vec2 point_right_start, point_right_end;\n    float sin_turn_angle_start, sin_turn_angle_end;\n    v_cos_turn_angle_start = calc_turn_angle(has_start_cap, segment_right, prev_right, point_right_start, sin_turn_angle_start);\n    v_cos_turn_angle_end = calc_turn_angle(has_end_cap, segment_right, next_right, point_right_end, sin_turn_angle_end);\n    float sign_turn_right_start = sin_turn_angle_start >= 0.0 ? 1.0 : -1.0;\n\n    bool miter_too_large_start = !has_start_cap && miter_too_large(join_type, v_cos_turn_angle_start);\n    bool miter_too_large_end = !has_end_cap && miter_too_large(join_type, v_cos_turn_angle_end);\n\n    float sign_at_start = -sign(a_position.x);  // +ve at segment start, -ve end.\n    vec2 point = sign_at_start > 0.0 ? a_point_start : a_point_end;\n\n    if ( (has_start_cap && sign_at_start > 0.0) ||\n         (has_end_cap && sign_at_start < 0.0) ) {\n        // Cap.\n        xy = point - segment_right*(halfwidth*a_position.y);\n        if (cap_type == butt_cap)\n            xy -= sign_at_start*0.5*u_antialias*segment_along;\n        else\n            xy -= sign_at_start*halfwidth*segment_along;\n    }\n    else if (sign_at_start > 0.0) {\n        vec2 inside_point = a_point_start + segment_right*(sign_turn_right_start*halfwidth);\n        vec2 prev_outside_point = a_point_start - prev_right*(sign_turn_right_start*halfwidth);\n\n        // join at start.\n        if (join_type == round_join || join_type == bevel_join || miter_too_large_start) {\n            if (v_cos_turn_angle_start <= 0.0) {  // |turn_angle| > 90 degrees\n                xy = a_point_start - segment_right*(halfwidth*a_position.y) - halfwidth*segment_along;\n            }\n            else {\n                if (a_position.x < -1.5) {\n                    xy = prev_outside_point;\n                    v_coords.y = -dot(xy - a_point_start, segment_right);\n                }\n                else if (a_position.y*sign_turn_right_start > 0.0) {  // outside corner of turn\n                    float d = halfwidth*abs(sin_turn_angle_start);\n                    xy = a_point_start - segment_right*(halfwidth*a_position.y) - d*segment_along;\n                }\n                else {  // inside corner of turn\n                    xy = inside_point;\n                }\n            }\n        }\n        else {  // miter join\n            if (a_position.x < -1.5) {\n                xy = prev_outside_point;\n                v_coords.y = -dot(xy - a_point_start, segment_right);\n            }\n            else if (a_position.y*sign_turn_right_start > 0.0) {  // outside corner of turn\n                float tan_half_turn_angle = (1.0-v_cos_turn_angle_start) / sin_turn_angle_start;  // Trig identity\n                float d = sign_turn_right_start*halfwidth*tan_half_turn_angle;\n                xy = a_point_start - segment_right*(halfwidth*a_position.y) - d*segment_along;\n            }\n            else {  // inside corner if turn\n                xy = inside_point;\n            }\n        }\n    }\n    else {\n        xy = point - segment_right*(halfwidth*a_position.y);\n    }\n\n    vec2 pos = xy + 0.5;  // Bokeh's offset.\n    pos /= u_canvas_size;  // in 0..1\n    gl_Position = vec4(2.0*pos.x - 1.0, 1.0 - 2.0*pos.y, 0.0, 1.0);\n\n    bool turn_right_start = sin_turn_angle_start >= 0.0;\n    bool turn_right_end = sin_turn_angle_end >= 0.0;\n\n    v_coords.x = dot(xy - a_point_start, segment_along);\n    v_flags = float(int(has_start_cap) +\n                    2*int(has_end_cap) +\n                    4*int(miter_too_large_start) +\n                    8*int(miter_too_large_end) +\n                    16*int(turn_right_start) +\n                    32*int(turn_right_end));\n\n    v_line_cap = a_line_cap;\n    v_line_join = a_line_join;\n\n#ifdef DASHED\n    v_length_so_far = a_length_so_far;\n    v_dash_tex_info = a_dash_tex_info;\n    v_dash_scale = a_dash_scale;\n    v_dash_offset = a_dash_offset;\n#endif\n}\n"},
557: function _(n,t,a,i,e){i();a.default="\nprecision mediump float;\n\nconst int butt_cap   = 0;\nconst int round_cap  = 1;\nconst int square_cap = 2;\n\nconst int miter_join = 0;\nconst int round_join = 1;\nconst int bevel_join = 2;\n\nuniform float u_antialias;\n#ifdef DASHED\nuniform sampler2D u_dash_tex;\n#endif\n\nvarying float v_linewidth;\nvarying vec4 v_line_color;\nvarying float v_line_cap;\nvarying float v_line_join;\nvarying float v_segment_length;\nvarying vec2 v_coords;\nvarying float v_flags;\nvarying float v_cos_turn_angle_start;\nvarying float v_cos_turn_angle_end;\n#ifdef DASHED\nvarying float v_length_so_far;\nvarying vec4 v_dash_tex_info;\nvarying float v_dash_scale;\nvarying float v_dash_offset;\n#endif\n\n#define ONE_MINUS_SMALL (1.0 - 1e-6)\n\nfloat cross_z(in vec2 v0, in vec2 v1)\n{\n    return v0.x*v1.y - v0.y*v1.x;\n}\n\nvec2 right_vector(in vec2 v)\n{\n    return vec2(v.y, -v.x);\n}\n\nfloat bevel_join_distance(in vec2 coords, in vec2 other_right, in float sign_turn_right)\n{\n    // other_right is unit vector facing right of the other (previous or next) segment, in coord reference frame\n    float hw = 0.5*v_linewidth;  // Not including antialiasing\n    if (other_right.y >= ONE_MINUS_SMALL) {  // other_right.y is -cos(turn_angle)\n        // 180 degree turn.\n        return abs(hw - v_coords.x);\n    }\n    else {\n        const vec2 segment_right = vec2(0.0, -1.0);\n        // corner_right is unit vector bisecting corner facing right, in coord reference frame\n        vec2 corner_right = normalize(other_right + segment_right);\n        vec2 outside_point = (-hw*sign_turn_right)*segment_right;\n        return hw + sign_turn_right*dot(outside_point - coords, corner_right);\n    }\n}\n\nfloat cap(in int cap_type, in float x, in float y)\n{\n    // x is distance along segment in direction away from end of segment,\n    // y is distance across segment.\n    if (cap_type == butt_cap)\n        return max(0.5*v_linewidth - x, abs(y));\n    else if (cap_type == square_cap)\n        return max(-x, abs(y));\n    else  // cap_type == round_cap\n        return distance(vec2(min(x, 0.0), y), vec2(0.0, 0.0));\n}\n\nfloat distance_to_alpha(in float dist)\n{\n    return 1.0 - smoothstep(0.5*(v_linewidth - u_antialias),\n                            0.5*(v_linewidth + u_antialias), dist);\n}\n\nvec2 turn_angle_to_right_vector(in float cos_turn_angle, in float sign_turn_right)\n{\n    float sin_turn_angle = sign_turn_right*sqrt(1.0 - cos_turn_angle*cos_turn_angle);\n    return vec2(sin_turn_angle, -cos_turn_angle);\n}\n\n#ifdef DASHED\nfloat dash_distance(in float x)\n{\n    // x is in direction of v_coords.x, i.e. along segment.\n    float tex_length = v_dash_tex_info.x;\n    float tex_offset = v_dash_tex_info.y;\n    float tex_dist_min = v_dash_tex_info.z;\n    float tex_dist_max = v_dash_tex_info.w;\n\n    // Apply offset.\n    x += v_length_so_far - v_dash_scale*tex_offset + v_dash_offset;\n\n    // Interpolate within texture to obtain distance to dash.\n    float dist = texture2D(u_dash_tex,\n                           vec2(x / (tex_length*v_dash_scale), 0.0)).a;\n\n    // Scale distance within min and max limits.\n    dist = tex_dist_min + dist*(tex_dist_max - tex_dist_min);\n\n    return v_dash_scale*dist;\n}\n\nmat2 rotation_matrix(in vec2 other_right)\n{\n    float sin_angle = other_right.x;\n    float cos_angle = -other_right.y;\n    return mat2(cos_angle, -sin_angle, sin_angle, cos_angle);\n}\n#endif\n\nvoid main()\n{\n    int join_type = int(v_line_join + 0.5);\n    int cap_type = int(v_line_cap + 0.5);\n    float halfwidth = 0.5*(v_linewidth + u_antialias);\n    float half_antialias = 0.5*u_antialias;\n\n    // Extract flags.\n    int flags = int(v_flags + 0.5);\n    bool turn_right_end = (flags / 32 > 0);\n    float sign_turn_right_end = turn_right_end ? 1.0 : -1.0;\n    flags -= 32*int(turn_right_end);\n    bool turn_right_start = (flags / 16 > 0);\n    float sign_turn_right_start = turn_right_start ? 1.0 : -1.0;\n    flags -= 16*int(turn_right_start);\n    bool miter_too_large_end = (flags / 8 > 0);\n    flags -= 8*int(miter_too_large_end);\n    bool miter_too_large_start = (flags / 4 > 0);\n    flags -= 4*int(miter_too_large_start);\n    bool has_end_cap = (flags / 2 > 0);\n    flags -= 2*int(has_end_cap);\n    bool has_start_cap = flags > 0;\n\n    // Unit vectors to right of previous and next segments in coord reference frame\n    vec2 prev_right = turn_angle_to_right_vector(v_cos_turn_angle_start, sign_turn_right_start);\n    vec2 next_right = turn_angle_to_right_vector(v_cos_turn_angle_end, sign_turn_right_end);\n\n    float dist = v_coords.y;  // For straight segment, and miter join.\n\n    // Along-segment coords with respect to end of segment, facing inwards\n    vec2 end_coords = vec2(v_segment_length, 0.0) - v_coords;\n\n    if (v_coords.x <= half_antialias) {\n        // At start of segment, either cap or join.\n        if (has_start_cap)\n            dist = cap(cap_type, v_coords.x, v_coords.y);\n        else if (join_type == round_join) {\n            if (v_coords.x <= 0.0)\n                dist = distance(v_coords, vec2(0.0, 0.0));\n        }\n        else {  // bevel or miter join\n            if (join_type == bevel_join || miter_too_large_start)\n                dist = max(abs(dist), bevel_join_distance(v_coords, prev_right, sign_turn_right_start));\n            float prev_sideways_dist = -sign_turn_right_start*dot(v_coords, prev_right);\n            dist = max(abs(dist), prev_sideways_dist);\n        }\n    }\n\n    if (end_coords.x <= half_antialias) {\n        if (has_end_cap) {\n            dist = max(abs(dist), cap(cap_type, end_coords.x, v_coords.y));\n        }\n        else if (join_type == bevel_join || miter_too_large_end) {\n            // Bevel join at end impacts half antialias distance\n            dist = max(abs(dist), bevel_join_distance(end_coords, next_right, sign_turn_right_end));\n        }\n    }\n\n    float alpha = distance_to_alpha(abs(dist));\n\n#ifdef DASHED\n    if (v_dash_tex_info.x >= 0.0) {\n        // Dashes in straight segments (outside of joins) are easily calculated.\n        dist = dash_distance(v_coords.x);\n\n        vec2 prev_coords = rotation_matrix(prev_right)*v_coords;\n        float start_dash_distance = dash_distance(0.0);\n\n        if (!has_start_cap && cap_type == butt_cap) {\n            // Outer of start join rendered solid color or not at all depending on whether corner\n            // point is in dash or gap, with antialiased ends.\n            bool outer_solid = start_dash_distance >= 0.0 && v_coords.x < half_antialias && prev_coords.x > -half_antialias;\n            if (outer_solid) {\n                // Within solid outer region, antialiased at ends\n                float half_aa_dist = dash_distance(half_antialias);\n                if (half_aa_dist > 0.0)  // Next dash near, do not want antialiased gap\n                    dist = half_aa_dist - v_coords.x + half_antialias;\n                else\n                    dist = start_dash_distance - v_coords.x;\n\n                half_aa_dist = dash_distance(-half_antialias);\n                if (half_aa_dist > 0.0)  // Prev dash nearm do not want antialiased gap\n                    dist = min(dist, half_aa_dist + prev_coords.x + half_antialias);\n                else\n                    dist = min(dist, start_dash_distance + prev_coords.x);\n            }\n            else {\n                // Outer not rendered, antialias ends.\n                if (v_coords.x < half_antialias)\n                    dist = min(0.0, dash_distance(half_antialias) - half_antialias) + v_coords.x;\n\n                if (prev_coords.x > -half_antialias && prev_coords.x <= half_antialias) {\n                    // Antialias from end of previous segment into join\n                    float prev_dist = min(0.0, dash_distance(-half_antialias) - half_antialias) - prev_coords.x;\n                    // Consider width of previous segment\n                    prev_dist = min(prev_dist, 0.5*v_linewidth - abs(prev_coords.y));\n                    dist = max(dist, prev_dist);\n                }\n            }\n        }\n\n        if (!has_end_cap && cap_type == butt_cap && end_coords.x < half_antialias) {\n            float end_dash_distance = dash_distance(v_segment_length);\n            bool increasing = end_dash_distance >= 0.0 && sign_turn_right_end*v_coords.y < 0.0;\n            if (!increasing) {\n                float half_aa_dist = dash_distance(v_segment_length - half_antialias);\n                dist = min(0.0, half_aa_dist - half_antialias) + end_coords.x;\n            }\n        }\n\n        dist = cap(cap_type, dist, v_coords.y);\n\n        float dash_alpha = distance_to_alpha(dist);\n        alpha = min(alpha, dash_alpha);\n    }\n#endif\n\n    alpha = v_line_color.a*alpha;\n    gl_FragColor = vec4(v_line_color.rgb*alpha, alpha);  // Premultiplied alpha.\n}\n"},
558: function _(n,e,i,_,a){_();i.default="\nprecision mediump float;\n\nattribute vec2 a_position;\nattribute vec2 a_center;\nattribute float a_width;   // or radius or outer_radius\nattribute float a_height;  // or inner_radius\nattribute float a_angle;   // or start_angle\nattribute float a_aux;     // or end_angle\nattribute float a_linewidth;\nattribute vec4 a_line_color;\nattribute vec4 a_fill_color;\nattribute float a_line_cap;\nattribute float a_line_join;\nattribute float a_show;\n\n#ifdef HATCH\nattribute float a_hatch_pattern;\nattribute float a_hatch_scale;\nattribute float a_hatch_weight;\nattribute vec4 a_hatch_color;\n#endif\n\nuniform vec2 u_canvas_size;\nuniform float u_antialias;\n\n#ifdef MULTI_MARKER\nuniform float u_size_hint;\n#endif\n\n#ifdef USE_ROUND_RECT\nuniform vec4 u_border_radius;\nvarying vec4 v_border_radius;\n#endif\n\n#ifdef USE_ANNULAR_WEDGE\nvarying float v_outer_radius;\nvarying float v_inner_radius;\nvarying float v_start_angle;\nvarying float v_end_angle;\n#endif\n\n#ifdef USE_ANNULUS\nvarying float v_outer_radius;\nvarying float v_inner_radius;\n#endif\n\n#ifdef USE_WEDGE\nvarying float v_radius;\nvarying float v_start_angle;\nvarying float v_end_angle;\n#endif\n\n#if defined(USE_CIRCLE) || defined(USE_NGON)\nvarying float v_radius;\n#endif\n\n#ifdef USE_NGON\nvarying float v_n;\n#endif\n\nvarying float v_linewidth;\nvarying vec2 v_size; // 2D size for rects compared to 1D for markers.\nvarying vec4 v_line_color;\nvarying vec4 v_fill_color;\nvarying float v_line_cap;\nvarying float v_line_join;\nvarying vec2 v_coords;\n\n#ifdef HATCH\nvarying float v_hatch_pattern;\nvarying float v_hatch_scale;\nvarying float v_hatch_weight;\nvarying vec4 v_hatch_color;\nvarying vec2 v_hatch_coords;\n#endif\n\n#ifdef MULTI_MARKER\n\n#define M_DASH         1\n#define M_DOT          2\n#define M_DIAMOND      3\n#define M_HEX          4\n#define M_SQUARE_PIN   5\n#define M_TRIANGLE     6\n#define M_TRIANGLE_PIN 7\n#define M_STAR         8\n\nvec2 enclosing_size() {\n  // Need extra size of (v_linewidth+u_antialias) if edge of marker parallel to\n  // edge of bounding box.  If symmetric spike towards edge then multiply by\n  // 1/cos(theta) where theta is angle between spike and bbox edges.\n  int size_hint = int(u_size_hint + 0.5);\n  if (size_hint == M_DASH)\n    return vec2(v_size.x + v_linewidth + u_antialias,\n                           v_linewidth + u_antialias);\n  else if (size_hint == M_DOT)\n    return 0.25*v_size + u_antialias;\n  else if (size_hint == M_DIAMOND)\n    return vec2(v_size.x*(2.0/3.0) + (v_linewidth + u_antialias)*1.20185,\n                v_size.y + (v_linewidth + u_antialias)*1.80278);\n  else if (size_hint == M_HEX)\n    return v_size + (v_linewidth + u_antialias)*vec2(2.0/sqrt(3.0), 1.0);\n  else if (size_hint == M_SQUARE_PIN)  // Square pin\n    return v_size + (v_linewidth + u_antialias)*3.1;\n  else if (size_hint == M_TRIANGLE)\n    return vec2(v_size.x + (v_linewidth + u_antialias)*sqrt(3.0),\n                v_size.y*(2.0/sqrt(3.0)) + (v_linewidth + u_antialias)*2.0);\n  else if (size_hint == M_TRIANGLE_PIN)\n    return v_size + (v_linewidth + u_antialias)*vec2(4.8, 6.0);\n  else if (size_hint == M_STAR)\n    return vec2(v_size.x*0.95106 + (v_linewidth + u_antialias)*3.0,\n                v_size.y + (v_linewidth + u_antialias)*3.2);\n  else\n    return v_size + v_linewidth + u_antialias;\n}\n#else\nvec2 enclosing_size() {\n  return v_size + v_linewidth + u_antialias;\n}\n#endif\n\nvoid main()\n{\n#if defined(USE_RECT) || defined(USE_ROUND_RECT) || defined(USE_HEX_TILE)\n  v_size = vec2(a_width, a_height);\n#elif defined(USE_ANNULUS) || defined(USE_ANNULAR_WEDGE) || defined(USE_WEDGE)\n  v_size = vec2(2.0*a_width, 2.0*a_width);\n#else\n  v_size = vec2(a_width, a_width);\n#endif\n\n#ifdef USE_NGON\n  v_n = a_aux;\n#endif\n\n  if (a_show < 0.5 || v_size.x < 0.0 || v_size.y < 0.0 || (v_size.x == 0.0 && v_size.y == 0.0)\n#ifdef USE_NGON\n      || v_n < 3.0\n#endif\n  ) {\n    // Do not show this marker.\n    gl_Position = vec4(-2.0, -2.0, 0.0, 1.0);\n    return;\n  }\n\n#ifdef USE_ANNULAR_WEDGE\n  v_outer_radius = a_width;\n  v_inner_radius = a_height;\n  v_start_angle = a_angle;\n  v_end_angle = a_aux;\n#endif\n\n#ifdef USE_ANNULUS\n  v_outer_radius = a_width;\n  v_inner_radius = a_height;\n#endif\n\n#ifdef USE_WEDGE\n  v_radius = a_width;\n  v_start_angle = a_angle;\n  v_end_angle = a_aux;\n#endif\n\n#if defined(USE_CIRCLE) || defined(USE_NGON)\n  v_radius = 0.5*a_width;\n#endif\n\n#ifdef USE_ROUND_RECT\n  // Scale corner radii if they are too large, the same as canvas\n  // https://html.spec.whatwg.org/multipage/canvas.html#dom-context-2d-roundrect\n  // Order of border_radius is top_left, top_right, bottom_right, bottom_left\n  const vec2 unit2 = vec2(1.0, 1.0);\n  float scale = min(v_size.x / max(dot(u_border_radius.xy, unit2), dot(u_border_radius.zw, unit2)),\n                    v_size.y / max(dot(u_border_radius.yz, unit2), dot(u_border_radius.wx, unit2)));\n  v_border_radius = u_border_radius*min(scale, 1.0);\n#endif\n\n  v_linewidth = a_linewidth;\n  v_line_color = a_line_color;\n  v_fill_color = a_fill_color;\n  v_line_cap = a_line_cap;\n  v_line_join = a_line_join;\n\n  if (v_linewidth < 1.0) {\n    // Linewidth less than 1 is implemented as 1 but with reduced alpha.\n    v_line_color.a *= v_linewidth;\n    v_linewidth = 1.0;\n  }\n\n#ifdef HATCH\n  v_hatch_pattern = a_hatch_pattern;\n  v_hatch_scale = a_hatch_scale;\n  v_hatch_weight = a_hatch_weight;\n  v_hatch_color = a_hatch_color;\n#endif\n\n  // Coordinates in rotated frame with respect to center of marker, used for\n  // distance functions in fragment shader.\n  v_coords = a_position*enclosing_size();\n\n#if defined(USE_CIRCLE) || defined(USE_ANNULUS) || defined(USE_ANNULAR_WEDGE) || defined(USE_WEDGE)\n  vec2 pos = a_center + v_coords;\n#else\n  float c = cos(-a_angle);\n  float s = sin(-a_angle);\n  mat2 rotation = mat2(c, -s, s, c);\n\n  vec2 pos = a_center + rotation*v_coords;\n#endif\n\n#ifdef HATCH\n  // Coordinates for hatching in unrotated frame of reference.\n  v_hatch_coords = pos - 0.5;\n#endif\n\n  pos += 0.5; // Make up for Bokeh's offset.\n  pos /= u_canvas_size; // 0 to 1.\n  gl_Position = vec4(2.0*pos.x - 1.0, 1.0 - 2.0*pos.y, 0.0, 1.0);\n}\n"},
559: function _(n,i,e,t,a){t();e.default="\nprecision mediump float;\n\nconst float SQRT2 = sqrt(2.0);\nconst float SQRT3 = sqrt(3.0);\nconst float PI = 3.14159265358979323846;\n\nconst int butt_cap = 0;\nconst int round_cap = 1;\nconst int square_cap = 2;\n\nconst int miter_join = 0;\nconst int round_join = 1;\nconst int bevel_join = 2;\n\n#ifdef HATCH\nconst int hatch_dot = 1;\nconst int hatch_ring = 2;\nconst int hatch_horizontal_line = 3;\nconst int hatch_vertical_line = 4;\nconst int hatch_cross = 5;\nconst int hatch_horizontal_dash = 6;\nconst int hatch_vertical_dash = 7;\nconst int hatch_spiral = 8;\nconst int hatch_right_diagonal_line = 9;\nconst int hatch_left_diagonal_line = 10;\nconst int hatch_diagonal_cross = 11;\nconst int hatch_right_diagonal_dash = 12;\nconst int hatch_left_diagonal_dash = 13;\nconst int hatch_horizontal_wave = 14;\nconst int hatch_vertical_wave = 15;\nconst int hatch_criss_cross = 16;\n#endif\n\nuniform float u_antialias;\n\nvarying vec2 v_coords;\nvarying vec2 v_size;\n\n#ifdef USE_ANNULAR_WEDGE\nvarying float v_outer_radius;\nvarying float v_inner_radius;\nvarying float v_start_angle;\nvarying float v_end_angle;\n#endif\n\n#ifdef USE_ANNULUS\nvarying float v_outer_radius;\nvarying float v_inner_radius;\n#endif\n\n#ifdef USE_WEDGE\nvarying float v_radius;\nvarying float v_start_angle;\nvarying float v_end_angle;\n#endif\n\n#if defined(USE_CIRCLE) || defined(USE_NGON)\nvarying float v_radius;\n#endif\n\n#ifdef USE_NGON\nvarying float v_n;\n#endif\n\n#ifdef USE_ROUND_RECT\nvarying vec4 v_border_radius;\n#endif\n\nvarying float v_linewidth;\nvarying vec4 v_line_color;\nvarying vec4 v_fill_color;\nvarying float v_line_cap;\nvarying float v_line_join;\n\n#ifdef HATCH\nvarying float v_hatch_pattern;\nvarying float v_hatch_scale;\nvarying float v_hatch_weight;\nvarying vec4 v_hatch_color;\nvarying vec2 v_hatch_coords;\n#endif\n\n// Lines within the marker (dot, cross, x and y) are added at the end as they are\n// on top of the fill rather than astride it.\n#if defined(USE_CIRCLE_DOT) || defined(USE_DIAMOND_DOT) || defined(USE_DOT) ||     defined(USE_HEX_DOT) || defined(USE_SQUARE_DOT) || defined(USE_STAR_DOT) ||     defined(USE_TRIANGLE_DOT)\n  #define APPEND_DOT\n#endif\n\n#if defined(USE_CIRCLE_CROSS) || defined(USE_SQUARE_CROSS)\n  #define APPEND_CROSS\n#endif\n\n#ifdef USE_DIAMOND_CROSS\n  #define APPEND_CROSS_2\n#endif\n\n#ifdef USE_CIRCLE_X\n  #define APPEND_X\n  #define APPEND_X_LEN (0.5*v_size.x)\n#endif\n\n#ifdef USE_SQUARE_X\n  #define APPEND_X\n  #define APPEND_X_LEN (v_size.x/SQRT2)\n#endif\n\n#ifdef USE_CIRCLE_Y\n  #define APPEND_Y\n#endif\n\n#if defined(USE_ASTERISK) || defined(USE_CROSS) || defined(USE_DASH) ||     defined(USE_DOT) || defined(USE_X) || defined(USE_Y)\n  // No fill.\n  #define LINE_ONLY\n#endif\n\n#if defined(LINE_ONLY) || defined(APPEND_CROSS) || defined(APPEND_CROSS_2) ||     defined(APPEND_X) || defined(APPEND_Y)\nfloat end_cap_distance(in vec2 p, in vec2 end_point, in vec2 unit_direction, in int line_cap)\n{\n  vec2 offset = p - end_point;\n  if (line_cap == butt_cap)\n    return dot(offset, unit_direction) + 0.5*v_linewidth;\n  else if (line_cap == square_cap)\n    return dot(offset, unit_direction);\n  else if (line_cap == round_cap && dot(offset, unit_direction) > 0.0)\n    return length(offset);\n  else\n    // Default is outside of line and should be -0.5*(v_linewidth+u_antialias) or less,\n    // so here avoid the multiplication.\n    return -v_linewidth-u_antialias;\n}\n#endif\n\n#if !(defined(LINE_ONLY) || defined(USE_SQUARE_PIN) || defined(USE_TRIANGLE_PIN))\n// For line join at a vec2 corner where 2 line segments meet, consider bevel points which are the 2\n// points obtained by moving half a linewidth away from the corner point in the directions normal to\n// the line segments.  The line through these points is the bevel line, characterised by a vec2\n// unit_normal and offset distance from the corner point.  Edge of bevel join straddles this line,\n// round join occurs outside of this line centred on the corner point.  In general\n//   offset = (linewidth/2)*sin(alpha/2)\n// where alpha is the angle between the 2 line segments at the corner.\nfloat line_join_distance_no_miter(\n  in vec2 p, in vec2 corner, in vec2 unit_normal, in float offset, in int line_join)\n{\n  // Simplified version of line_join_distance ignoring miter which most markers do implicitly\n  // as they are composed of straight line segments.\n  float dist_outside = dot((p - corner), unit_normal) - offset;\n\n  if (line_join == bevel_join && dist_outside > -0.5*u_antialias)\n    return dist_outside + 0.5*v_linewidth;\n  else if (dist_outside > 0.0)  // round_join\n    return distance(p, corner);\n  else\n    // Default is outside of line and should be -0.5*(v_linewidth+u_antialias) or less,\n    // so here avoid the multiplication.\n    return -v_linewidth-u_antialias;\n}\n#endif\n\n#if defined(USE_SQUARE_PIN) || defined(USE_TRIANGLE_PIN)\n// Line join distance including miter but only one-sided check as assuming use of symmetry in\n// calling function.\nfloat line_join_distance_incl_miter(\n  in vec2 p, in vec2 corner, in vec2 unit_normal, in float offset, in int line_join,\n  vec2 miter_unit_normal)\n{\n  float dist_outside = dot((p - corner), unit_normal) - offset;\n\n  if (line_join == miter_join && dist_outside > 0.0)\n    return dot((p - corner), miter_unit_normal);\n  else if (line_join == bevel_join && dist_outside > -0.5*u_antialias)\n    return dist_outside + 0.5*v_linewidth;\n  else if (dist_outside > 0.0)  // round_join\n    return distance(p, corner);\n  else\n    return -v_linewidth-u_antialias;\n}\n#endif\n\n#if defined(APPEND_CROSS) || defined(APPEND_X) || defined(USE_ASTERISK) ||     defined(USE_CROSS) || defined(USE_X)\nfloat one_cross(in vec2 p, in int line_cap, in float len)\n{\n  p = abs(p);\n  p = (p.y > p.x) ? p.yx : p.xy;\n  float dist = p.y;\n  float end_dist = end_cap_distance(p, vec2(len, 0.0), vec2(1.0, 0.0), line_cap);\n  return max(dist, end_dist);\n}\n#endif\n\n#ifdef APPEND_CROSS_2\nfloat one_cross_2(in vec2 p, in int line_cap, in vec2 lengths)\n{\n  // Cross with different length in x and y directions.\n  p = abs(p);\n  bool switch_xy = (p.y > p.x);\n  p = switch_xy ? p.yx : p.xy;\n  float len = switch_xy ? lengths.y : lengths.x;\n  float dist = p.y;\n  float end_dist = end_cap_distance(p, vec2(len, 0.0), vec2(1.0, 0.0), line_cap);\n  return max(dist, end_dist);\n}\n#endif\n\n#if defined(APPEND_Y) || defined(USE_Y)\nfloat one_y(in vec2 p, in int line_cap, in float len)\n{\n  p = vec2(abs(p.x), -p.y);\n\n  // End point of line to right is (1/2, 1/3)*len*SQRT3.\n  // Unit vector along line is (1/2, 1/3)*k where k = 6/SQRT13.\n  const float k = 6.0/sqrt(13.0);\n  vec2 unit_along = vec2(0.5*k, k/3.0);\n  vec2 end_point = vec2(0.5*len*SQRT3, len*SQRT3/3.0);\n  float dist = max(abs(dot(p, vec2(-unit_along.y, unit_along.x))),\n                   end_cap_distance(p, end_point, unit_along, line_cap));\n\n  if (p.y < 0.0) {\n    // Vertical line.\n    float vert_dist = max(p.x,\n                          end_cap_distance(p, vec2(0.0, -len), vec2(0.0, -1.0), line_cap));\n    dist = min(dist, vert_dist);\n  }\n  return dist;\n}\n#endif\n\n// One marker_distance function per marker type.\n// Distance is zero on edge of marker, +ve outside and -ve inside.\n\n#ifdef USE_ASTERISK\nfloat marker_distance(in vec2 p, in int line_cap, in int line_join)\n{\n  // Assuming v_size.x == v.size_y\n  vec2 p_diag = vec2((p.x + p.y)/SQRT2, (p.x - p.y)/SQRT2);\n  float len = 0.5*v_size.x;\n  return min(one_cross(p, line_cap, len),  // cross\n             one_cross(p_diag, line_cap, len));  // x\n}\n#endif\n\n#if defined(USE_ANNULUS) || defined(USE_WEDGE) || defined(USE_ANNULAR_WEDGE)\nfloat merge(in float d1, in float d2)\n{\n  return min(d1, d2);\n}\n\nfloat intersect(in float d1, in float d2)\n{\n  return max(d1, d2);\n}\n\nfloat subtract(in float d1, in float d2)\n{\n  return max(d1, -d2);\n}\n\nfloat circle(in vec2 p, in float radius)\n{\n  return length(p) - radius;\n}\n\nfloat segment_square(in vec2 p, in vec2 q) {\n  vec2 v = p - q*clamp(dot(p, q)/dot(q, q), 0.0, 1.0);\n  return dot(v, v);\n}\n\nvec2 xy(in float angle)\n{\n  return vec2(cos(angle), sin(angle));\n}\n\nfloat cross_z(in vec2 v0, in vec2 v1)\n{\n    return v0.x*v1.y - v0.y*v1.x;\n}\n\n// From https://www.shadertoy.com/view/wldXWB (MIT licensed)\nfloat wedge(in vec2 p, in float r, in float start_angle, in float end_angle)\n{\n    vec2 a = r*xy(start_angle);\n    vec2 b = r*xy(end_angle);\n\n    // distance\n    float d = sqrt(merge(segment_square(p, a), segment_square(p, b)));\n\n    // sign\n    float s;\n    if (cross_z(a, b) < 0.0) {\n        s =  sign(max(cross_z(a, p), cross_z(p, b)));\n    } else {\n        s = -sign(max(cross_z(p, a), cross_z(b, p)));\n    }\n\n    return s*d;\n}\n\nfloat annulus(in vec2 p, in float outer_radius, in float inner_radius)\n{\n  float outer = circle(p, outer_radius);\n  float inner = circle(p, inner_radius);\n\n  return subtract(outer, inner);\n}\n#endif\n\n#if defined(USE_ANNULUS)\nfloat marker_distance(in vec2 p, in int line_cap, in int line_join)\n{\n  return annulus(p, v_outer_radius, v_inner_radius);\n}\n#endif\n\n#if defined(USE_WEDGE)\nfloat marker_distance(in vec2 p, in int line_cap, in int line_join)\n{\n  return intersect(\n    circle(p, v_radius),\n    wedge(p, v_radius, v_start_angle, v_end_angle));\n}\n#endif\n\n#if defined(USE_ANNULAR_WEDGE)\nfloat marker_distance(in vec2 p, in int line_cap, in int line_join)\n{\n  return intersect(\n    annulus(p, v_outer_radius, v_inner_radius),\n    wedge(p, v_outer_radius, v_start_angle, v_end_angle));\n}\n#endif\n\n#if defined(USE_CIRCLE) || defined(USE_CIRCLE_CROSS) || defined(USE_CIRCLE_DOT) ||     defined(USE_CIRCLE_X) || defined(USE_CIRCLE_Y)\nfloat marker_distance(in vec2 p, in int line_cap, in int line_join)\n{\n  // Assuming v_size.x == v.size_y\n  return length(p) - 0.5*v_size.x;\n}\n#endif\n\n#ifdef USE_CROSS\nfloat marker_distance(in vec2 p, in int line_cap, in int line_join)\n{\n  // Assuming v_size.x == v.size_y\n  return one_cross(p, line_cap, 0.5*v_size.x);\n}\n#endif\n\n#ifdef USE_DASH\nfloat marker_distance(in vec2 p, in int line_cap, in int line_join)\n{\n  p = abs(p);\n  float dist = p.y;\n  float end_dist = end_cap_distance(p, vec2(0.5*v_size.x, 0.0), vec2(1.0, 0.0), line_cap);\n  return max(dist, end_dist);\n}\n#endif\n\n#if defined(USE_DIAMOND) || defined(USE_DIAMOND_CROSS) || defined(USE_DIAMOND_DOT)\nfloat marker_distance(in vec2 p, in int line_cap, in int line_join)\n{\n  // Assuming v_size.x == v.size_y\n  // Only need to consider +ve quadrant, the 2 end points are (2r/3, 0) and (0, r)\n  // where r = radius = v_size.x/2.\n  // Line has outward-facing unit normal vec2(1, 2/3)/k where k = SQRT13/3\n  // hence vec2(3, 2)/SQRT13, and distance from origin of 2r/(3k) = 2r/SQRT13.\n  p = abs(p);\n  float r = 0.5*v_size.x;\n  const float SQRT13 = sqrt(13.0);\n  float dist = dot(p, vec2(3.0, 2.0))/SQRT13 - 2.0*r/SQRT13;\n\n  if (line_join != miter_join) {\n    dist = max(dist, line_join_distance_no_miter(\n      p, vec2(0.0, r), vec2(0.0, 1.0), v_linewidth/SQRT13, line_join));\n\n    dist = max(dist, line_join_distance_no_miter(\n      p, vec2(r*2.0/3.0, 0.0), vec2(1.0, 0.0), v_linewidth*(1.5/SQRT13), line_join));\n  }\n\n  return dist;\n}\n#endif\n\n#ifdef USE_DOT\nfloat marker_distance(in vec2 p, in int line_cap, in int line_join)\n{\n  // Dot is always appended.\n  return v_linewidth+u_antialias;\n}\n#endif\n\n#if defined(USE_HEX_TILE) || defined(USE_HEX) || defined(USE_HEX_DOT)\nfloat marker_distance(in vec2 p, in int line_cap, in int line_join)\n{\n  // A regular hexagon has v_size.x == v.size_y = r where r is the length of\n  // each of the 3 sides of the 6 equilateral triangles that comprise the hex.\n  // Only consider +ve quadrant, the 3 corners are at (0, h), (rx/2, h), (rx, 0)\n  // where rx = 0.5*v_size.x, ry = 0.5*v_size.y and h = ry*SQRT3/2.\n  // Sloping line has outward normal vec2(h, rx/2).  Length of this is\n  // len = sqrt(h**2 + rx**2/4) to give unit normal (h, rx/2)/len and distance\n  // from origin of this line is rx*h/len.\n  p = abs(p);\n  float rx = v_size.x/2.0;\n  float h = v_size.y*(SQRT3/4.0);\n  float len_normal = sqrt(h*h + 0.25*rx*rx);\n  vec2 unit_normal = vec2(h, 0.5*rx) / len_normal;\n  float dist = max(dot(p, unit_normal) - rx*h/len_normal,  // Distance from sloping line.\n                   p.y - h);  // Distance from horizontal line.\n\n  if (line_join != miter_join) {\n    dist = max(dist, line_join_distance_no_miter(\n      p, vec2(rx, 0.0), vec2(1.0, 0.0), 0.5*v_linewidth*unit_normal.x, line_join));\n\n    unit_normal = normalize(unit_normal + vec2(0.0, 1.0));  // At (rx/2, h) corner.\n    dist = max(dist, line_join_distance_no_miter(\n      p, vec2(0.5*rx, h), unit_normal, 0.5*v_linewidth*unit_normal.y, line_join));\n  }\n  return dist;\n}\n#endif\n\n#ifdef USE_NGON\nfloat marker_distance(in vec2 p, in int line_cap, in int line_join)\n{\n  float side_angle = 2.0*PI / v_n;  // Angle subtended by 1 side of ngon at center.\n\n  // Use symmetry to transform p around center into first half of first side of ngon.\n  p.y = -p.y;\n  float angle = mod(atan(p.x, p.y), side_angle);\n  angle = min(angle, side_angle - angle);\n  p = length(p)*vec2(sin(angle), cos(angle));\n\n  float half_angle = 0.5*side_angle;\n  float cos_half_angle = cos(half_angle);\n  vec2 unit_normal = vec2(sin(half_angle), cos_half_angle);\n  vec2 corner = vec2(0.0, v_size.y/2.0);\n  float dist = dot(p - corner, unit_normal);\n\n  if (line_join != miter_join) {\n    dist = max(dist, line_join_distance_no_miter(\n      p, corner, vec2(0.0, 1.0), 0.5*v_linewidth*cos_half_angle, line_join));\n  }\n  return dist;\n}\n#endif\n\n#ifdef USE_PLUS\nfloat marker_distance(in vec2 p, in int line_cap, in int line_join)\n{\n  // Assuming v_size.x == v.size_y\n  // Only need to consider one octant, the +ve quadrant with x >= y.\n  p = abs(p);\n  p = (p.y > p.x) ? p.yx : p.xy;\n\n  // 3 corners are (r, 0), (r, 3r/8) and (3r/8, 3r/8).\n  float r = 0.5*v_size.x;\n  p = p - vec2(r, 0.375*r);  // Distance with respect to outside corner\n  float dist = max(p.x, p.y);\n\n  if (line_join != miter_join) {\n    // Outside corner\n    dist = max(dist, line_join_distance_no_miter(\n      p, vec2(0.0, 0.0), vec2(1.0/SQRT2, 1.0/SQRT2), v_linewidth/(2.0*SQRT2), line_join));\n\n    // Inside corner\n    dist = min(dist, -line_join_distance_no_miter(\n      p, vec2(-5.0*r/8.0, 0.0), vec2(-1.0/SQRT2, -1.0/SQRT2), v_linewidth/(2.0*SQRT2), line_join));\n  }\n\n  return dist;\n}\n#endif\n\n#if defined(USE_ROUND_RECT)\nfloat marker_distance(in vec2 p, in int line_cap, in int line_join)\n{\n  vec2 halfsize = v_size/2.0;\n  vec2 p2 = abs(p) - halfsize;  // Offset from corner\n  float dist = max(p2.x, p2.y);\n\n  if (line_join != miter_join) {\n    dist = max(dist, line_join_distance_no_miter(\n      p2, vec2(0.0, 0.0), vec2(1.0/SQRT2, 1.0/SQRT2), v_linewidth/(2.0*SQRT2), line_join));\n  }\n\n  // Need to consider distance to all 4 corners\n  // Order of border_radius is top_left, top_right, bottom_right, bottom_left\n  vec4 border_radius = v_border_radius;\n  vec4 xsign = vec4(-1.0, 1.0, 1.0, -1.0);\n  vec4 ysign = vec4(-1.0, -1.0, 1.0, 1.0);\n  for (int i = 0; i < 4; i++) {\n    float radius = border_radius.x;\n    p2 = p*vec2(xsign.x, ysign.x);  // In +ve quadrant\n    vec2 offset = p2 - halfsize + radius;\n    if (min(radius, min(offset.x, offset.y)) > 0.0) {\n      dist = max(dist, length(offset) - radius);\n    }\n    // Swizzle\n    border_radius.xyzw = border_radius.yzwx;\n    xsign.xyzw = xsign.yzwx;\n    ysign.xyzw = ysign.yzwx;\n  }\n\n  return dist;\n}\n#endif\n\n#if defined(USE_RECT) || defined(USE_SQUARE) || defined(USE_SQUARE_CROSS) || defined(USE_SQUARE_DOT) || defined(USE_SQUARE_X)\nfloat marker_distance(in vec2 p, in int line_cap, in int line_join)\n{\n  vec2 p2 = abs(p) - v_size/2.0;  // Offset from corner\n  float dist = max(p2.x, p2.y);\n\n  if (line_join != miter_join) {\n    dist = max(dist, line_join_distance_no_miter(\n      p2, vec2(0.0, 0.0), vec2(1.0/SQRT2, 1.0/SQRT2), v_linewidth/(2.0*SQRT2), line_join));\n  }\n\n  return dist;\n}\n#endif\n\n#ifdef USE_SQUARE_PIN\nfloat marker_distance(in vec2 p, in int line_cap, in int line_join)\n{\n  // Assuming v_size.x == v.size_y\n  p = abs(p);\n  p = (p.y > p.x) ? p.yx : p.xy;\n  // p is in octant between y=0 and y=x.\n  // Quadratic bezier curve passes through (r, r), (11r/16, 0) and (r, -r).\n  // Circular arc that passes through the same points has center at\n  // x = r + 231r/160 = 2.44275r and y = 0 and hence radius is\n  // x - 11r/16 = 1.75626 precisely.\n  float r = 0.5*v_size.x;\n  float center_x = r*2.44375;\n  float radius = r*1.75626;\n  float dist = radius - distance(p, vec2(center_x, 0.0));\n\n  // Magic number is 0.5*sin(atan(8/5) - pi/4)\n  dist = max(dist, line_join_distance_incl_miter(\n    p, vec2(r, r), vec2(1.0/SQRT2, 1.0/SQRT2), v_linewidth*0.1124297533493792, line_join,\n    vec2(8.0/sqrt(89.0), -5.0/sqrt(89.0))));\n\n  return dist;\n}\n#endif\n\n#if defined(USE_STAR) || defined(USE_STAR_DOT)\nfloat marker_distance(in vec2 p, in int line_cap, in int line_join)\n{\n  // Assuming v_size.x == v.size_y\n  const float SQRT5 = sqrt(5.0);\n  const float COS72 = 0.25*(SQRT5 - 1.0);\n  const float SIN72 = sqrt((5.0+SQRT5) / 8.0);\n\n  float angle = atan(p.x, p.y);  // In range -pi to +pi clockwise from +y direction.\n  angle = mod(angle, 0.4*PI) - 0.2*PI;  // In range -pi/5 to +pi/5 clockwise from +y direction.\n  p = length(p)*vec2(cos(angle), abs(sin(angle)));  // (x,y) in pi/10 (36 degree) sector.\n\n  // 2 corners are at (r, 0) and (r-a*SIN72, a*COS72) where a = r sqrt(5-2*sqrt(5)).\n  // Line has outward-facing unit normal vec2(COS72, SIN72) and distance from\n  // origin of dot(vec2(r, 0), vec2(COS72, SIN72)) = r*COS72\n  float r = 0.5*v_size.x;\n  float a = r*sqrt(5.0 - 2.0*SQRT5);\n  float dist = dot(p, vec2(COS72, SIN72)) - r*COS72;\n\n  if (line_join != miter_join) {\n    // Outside corner\n    dist = max(dist, line_join_distance_no_miter(\n      p, vec2(r, 0.0), vec2(1.0, 0.0), v_linewidth*(0.5*COS72), line_join));\n\n    // Inside corner\n    const float COS36 = sqrt(0.5 + COS72/2.0);\n    const float SIN36 = sqrt(0.5 - COS72/2.0);\n    dist = min(dist, -line_join_distance_no_miter(\n      p, vec2(r-a*SIN72, a*COS72), vec2(-COS36, -SIN36), v_linewidth*(0.5*COS36), line_join));\n  }\n\n  return dist;\n}\n#endif\n\n#if defined(USE_TRIANGLE) || defined(USE_TRIANGLE_DOT) || defined(USE_INVERTED_TRIANGLE)\nfloat marker_distance(in vec2 p, in int line_cap, in int line_join)\n{\n  // Assuming v_size.x == v.size_y\n  // For normal triangle 3 corners are at (-r, a), (r, a), (0, a-h)=(0, -2h/3)\n  // given r = radius = v_size.x/2, h = SQRT3*r, a = h/3.\n  // Sloping line has outward-facing unit normal vec2(h, -r)/2r = vec2(SQRT3, -1)/2\n  // and distance from origin of a.  Horizontal line has outward-facing unit normal\n  // vec2(0, 1) and distance from origin of a.\n  float r = 0.5*v_size.x;\n  float a = r*SQRT3/3.0;\n\n  // Only need to consider +ve x.\n#ifdef USE_INVERTED_TRIANGLE\n  p = vec2(abs(p.x), -p.y);\n#else\n  p = vec2(abs(p.x), p.y);\n#endif\n\n  float dist = max(0.5*dot(p, vec2(SQRT3, -1.0)) - a,  // Distance from sloping line.\n                   p.y - a);  // Distance from horizontal line.\n\n  if (line_join != miter_join) {\n    dist = max(dist, line_join_distance_no_miter(\n      p, vec2(0.0, -(2.0/SQRT3)*r), vec2(0.0, -1.0), v_linewidth*0.25, line_join));\n\n    dist = max(dist, line_join_distance_no_miter(\n      p, vec2(r, a), vec2(SQRT3/2.0, 0.5), v_linewidth*0.25, line_join));\n  }\n\n  return dist;\n}\n#endif\n\n#ifdef USE_TRIANGLE_PIN\nfloat marker_distance(in vec2 p, in int line_cap, in int line_join)\n{\n  // Assuming v_size.x == v.size_y\n  float angle = atan(p.x, -p.y);  // In range -pi to +pi.\n  angle = mod(angle, PI*2.0/3.0) - PI/3.0;  // In range -pi/3 to pi/3.\n  p = length(p)*vec2(cos(angle), abs(sin(angle)));  // (x,y) in range 0 to pi/3.\n  // Quadratic bezier curve passes through (a, r), ((a+b)/2, 0) and (a, -r) where\n  // a = r/SQRT3, b = 3a/8 = r SQRT3/8.  Circular arc that passes through the same points has\n  // center at (a+x, 0) and radius x+c where c = (a-b)/2 and x = (r**2 - c**2) / (2c).\n  // Ignore r factor until the end so can use const.\n  const float a = 1.0/SQRT3;\n  const float b = SQRT3/8.0;\n  const float c = (a-b)/2.0;\n  const float x = (1.0 - c*c) / (2.0*c);\n  const float center_x = x + a;\n  const float radius = x + c;\n  float r = 0.5*v_size.x;\n  float dist = r*radius - distance(p, vec2(r*center_x, 0.0));\n\n  // Magic number is 0.5*sin(atan(8*sqrt(3)/5) - pi/3)\n  dist = max(dist, line_join_distance_incl_miter(\n    p, vec2(a*r, r), vec2(0.5, 0.5*SQRT3), v_linewidth*0.0881844526878324, line_join,\n    vec2(8.0*SQRT3, -5.0)/sqrt(217.0)));\n\n  return dist;\n}\n#endif\n\n#ifdef USE_X\nfloat marker_distance(in vec2 p, in int line_cap, in int line_join)\n{\n  // Assuming v_size.x == v.size_y\n  p = vec2((p.x + p.y)/SQRT2, (p.x - p.y)/SQRT2);\n  return one_cross(p, line_cap, 0.5*v_size.x);\n}\n#endif\n\n#ifdef USE_Y\nfloat marker_distance(in vec2 p, in int line_cap, in int line_join)\n{\n  // Assuming v_size.x == v.size_y\n  return one_y(p, line_cap, 0.5*v_size.x);\n}\n#endif\n\n// Convert distance from edge of marker to fraction in range 0 to 1, depending\n// on antialiasing width.\nfloat distance_to_fraction(in float dist)\n{\n  return 1.0 - smoothstep(-0.5*u_antialias, 0.5*u_antialias, dist);\n}\n\n// Return fraction from 0 (no fill color) to 1 (full fill color).\nfloat fill_fraction(in float dist)\n{\n  return distance_to_fraction(dist);\n}\n\n// Return fraction in range 0 (no line color) to 1 (full line color).\nfloat line_fraction(in float dist)\n{\n  return distance_to_fraction(abs(dist) - 0.5*v_linewidth);\n}\n\n// Return fraction (in range 0 to 1) of a color, with premultiplied alpha.\nvec4 fractional_color(in vec4 color, in float fraction)\n{\n  color.a *= fraction;\n  color.rgb *= color.a;\n  return color;\n}\n\n// Blend colors that have premultiplied alpha.\nvec4 blend_colors(in vec4 src, in vec4 dest)\n{\n  return (1.0 - src.a)*dest + src;\n}\n\n#ifdef APPEND_DOT\nfloat dot_fraction(in vec2 p)\n{\n  // Assuming v_size.x == v_size.y\n  float radius = 0.125*v_size.x;\n  float dot_distance = max(length(p) - radius, -0.5*u_antialias);\n  return fill_fraction(dot_distance);\n}\n#endif\n\n#ifdef HATCH\n// Wrap coordinate(s) by removing integer part to give distance from center of\n// repeat, in the range -0.5 to +0.5.\nfloat wrap(in float x)\n{\n  return fract(x) - 0.5;\n}\n\nvec2 wrap(in vec2 xy)\n{\n  return fract(xy) - 0.5;\n}\n\n// Return fraction from 0 (no hatch color) to 1 (full hatch color).\nfloat hatch_fraction(in vec2 coords, in int hatch_pattern)\n{\n  float scale = v_hatch_scale; // Hatch repeat distance.\n\n  // Coordinates and linewidth/halfwidth are scaled to hatch repeat distance.\n  coords = coords / scale;\n  float halfwidth = 0.5*v_hatch_weight / scale; // Half the hatch linewidth.\n\n  // Default is to return fraction of zero, i.e. no pattern.\n  float dist = u_antialias;\n\n  if (hatch_pattern == hatch_dot) {\n    const float dot_radius = 0.25;\n    dist = length(wrap(coords)) - dot_radius;\n  }\n  else if (hatch_pattern == hatch_ring) {\n    const float ring_radius = 0.25;\n    dist = abs(length(wrap(coords)) - ring_radius) - halfwidth;\n  }\n  else if (hatch_pattern == hatch_horizontal_line) {\n    dist = abs(wrap(coords.y)) - halfwidth;\n  }\n  else if (hatch_pattern == hatch_vertical_line) {\n    dist = abs(wrap(coords.x)) - halfwidth;\n  }\n  else if (hatch_pattern == hatch_cross) {\n    dist = min(abs(wrap(coords.x)), abs(wrap(coords.y))) - halfwidth;\n  }\n  else if (hatch_pattern == hatch_horizontal_dash) {\n    // Dashes have square caps.\n    const float halflength = 0.25;\n    dist = max(abs(wrap(coords.y)),\n               abs(wrap(coords.x) + 0.25) - halflength) - halfwidth;\n  }\n  else if (hatch_pattern == hatch_vertical_dash) {\n    const float halflength = 0.25;\n    dist = max(abs(wrap(coords.x)),\n               abs(wrap(coords.y) + 0.25) - halflength) - halfwidth;\n  }\n  else if (hatch_pattern == hatch_spiral) {\n    vec2 wrap2 = wrap(coords);\n    float angle = wrap(atan(wrap2.y, wrap2.x) / (2.0*PI));\n    // Canvas spiral radius increases by scale*pi/15 each rotation.\n    const float dr = PI/15.0;\n    float radius = length(wrap2);\n    // At any angle, spiral lines are equally spaced dr apart.\n    // Find distance to nearest of these lines.\n    float frac = fract((radius - dr*angle) / dr); // 0 to 1.\n    dist = dr*(abs(frac - 0.5));\n    dist = min(dist, radius) - halfwidth; // Consider center point also.\n  }\n  else if (hatch_pattern == hatch_right_diagonal_line) {\n    dist = abs(wrap(2.0*coords.x + coords.y))/sqrt(5.0) - halfwidth;\n  }\n  else if (hatch_pattern == hatch_left_diagonal_line) {\n    dist = abs(wrap(2.0*coords.x - coords.y))/sqrt(5.0) - halfwidth;\n  }\n  else if (hatch_pattern == hatch_diagonal_cross) {\n    coords = vec2(coords.x + coords.y + 0.5, coords.x - coords.y + 0.5);\n    dist = min(abs(wrap(coords.x)), abs(wrap(coords.y))) / SQRT2 - halfwidth;\n  }\n  else if (hatch_pattern == hatch_right_diagonal_dash) {\n    float across = coords.x + coords.y + 0.5;\n    dist = abs(wrap(across)) / SQRT2; // Distance to nearest solid line.\n\n    across = floor(across); // Offset for dash.\n    float along = wrap(0.5*(coords.x - coords.y + across));\n    const float halflength = 0.25;\n    along = abs(along) - halflength; // Distance along line.\n\n    dist = max(dist, along) - halfwidth;\n  }\n  else if (hatch_pattern == hatch_left_diagonal_dash) {\n    float across = coords.x - coords.y + 0.5;\n    dist = abs(wrap(across)) / SQRT2; // Distance to nearest solid line.\n\n    across = floor(across); // Offset for dash.\n    float along = wrap(0.5*(coords.x + coords.y + across));\n    const float halflength = 0.25;\n    along = abs(along) - halflength; // Distance along line.\n\n    dist = max(dist, along) - halfwidth;\n  }\n  else if (hatch_pattern == hatch_horizontal_wave) {\n    float wrapx = wrap(coords.x);\n    float wrapy = wrap(coords.y - 0.25 + abs(wrapx));\n    dist = abs(wrapy) / SQRT2 - halfwidth;\n  }\n  else if (hatch_pattern == hatch_vertical_wave) {\n    float wrapy = wrap(coords.y);\n    float wrapx = wrap(coords.x - 0.25 + abs(wrapy));\n    dist = abs(wrapx) / SQRT2 - halfwidth;\n  }\n  else if (hatch_pattern == hatch_criss_cross) {\n    float plus = min(abs(wrap(coords.x)), abs(wrap(coords.y)));\n\n    coords = vec2(coords.x + coords.y + 0.5, coords.x - coords.y + 0.5);\n    float X = min(abs(wrap(coords.x)), abs(wrap(coords.y))) / SQRT2;\n\n    dist = min(plus, X) - halfwidth;\n  }\n\n  return distance_to_fraction(dist*scale);\n}\n#endif\n\nvoid main()\n{\n  int line_cap = int(v_line_cap + 0.5);\n  int line_join = int(v_line_join + 0.5);\n#ifdef HATCH\n  int hatch_pattern = int(v_hatch_pattern + 0.5);\n#endif\n\n  float dist = marker_distance(v_coords, line_cap, line_join);\n\n#ifdef LINE_ONLY\n  vec4 color = vec4(0.0, 0.0, 0.0, 0.0);\n#else\n  float fill_frac = fill_fraction(dist);\n  vec4 color = fractional_color(v_fill_color, fill_frac);\n#endif\n\n#if defined(HATCH) && !defined(LINE_ONLY)\n  if (hatch_pattern > 0 && fill_frac > 0.0) {\n    float hatch_frac = hatch_fraction(v_hatch_coords, hatch_pattern);\n    vec4 hatch_color = fractional_color(v_hatch_color, hatch_frac*fill_frac);\n    color = blend_colors(hatch_color, color);\n  }\n#endif\n\n  float line_frac = line_fraction(dist);\n\n#ifdef APPEND_DOT\n  line_frac = max(line_frac, dot_fraction(v_coords));\n#endif\n#ifdef APPEND_CROSS\n  line_frac = max(line_frac, line_fraction(one_cross(v_coords, line_cap, 0.5*v_size.x)));\n#endif\n#ifdef APPEND_CROSS_2\n  vec2 lengths = vec2(v_size.x/3.0, v_size.x/2.0);\n  line_frac = max(line_frac, line_fraction(one_cross_2(v_coords, line_cap, lengths)));\n#endif\n#ifdef APPEND_X\n  vec2 p = vec2((v_coords.x + v_coords.y)/SQRT2, (v_coords.x - v_coords.y)/SQRT2);\n  line_frac = max(line_frac, line_fraction(one_cross(p, line_cap, APPEND_X_LEN)));\n#endif\n#ifdef APPEND_Y\n  line_frac = max(line_frac, line_fraction(one_y(v_coords, line_cap, 0.5*v_size.x)));\n#endif\n\n  if (line_frac > 0.0) {\n    vec4 line_color = fractional_color(v_line_color, line_frac);\n    color = blend_colors(line_color, color);\n  }\n\n  gl_FragColor = color;\n}\n"},
560: function _(t,e,r,s,_){s();const n=t(561);class a extends n.SXSYGlyphGL{constructor(t,e){super(t,e),this.glyph=e}get marker_type(){return"annular_wedge"}get outer_radius(){return this._widths}get inner_radius(){return this._heights}get start_angle(){return this._angles}get end_angle(){return this._auxs}_set_data(){super._set_data(),this.outer_radius.set_from_array(this.glyph.souter_radius),this.inner_radius.set_from_array(this.glyph.sinner_radius),"anticlock"==this.glyph.model.direction?(this.start_angle.set_from_prop(this.glyph.start_angle),this.end_angle.set_from_prop(this.glyph.end_angle)):(this.start_angle.set_from_prop(this.glyph.end_angle),this.end_angle.set_from_prop(this.glyph.start_angle))}}r.AnnularWedgeGL=a,a.__name__="AnnularWedgeGL"},
561: function _(s,e,t,i,n){i();const r=s(562),_=s(566);class h extends r.SingleMarkerGL{constructor(s,e){super(s,e),this.glyph=e}_set_data(){const s=this.nvertices,e=this._centers.get_sized_array(2*s);(0,_.interleave)(this.glyph.sx,this.glyph.sy,s,r.SingleMarkerGL.missing_point,e),this._centers.update()}}t.SXSYGlyphGL=h,h.__name__="SXSYGlyphGL"},
562: function _(s,a,t,e,_){e();const h=s(563);class l extends h.BaseMarkerGL{constructor(s,a){super(s,a),this.glyph=a}_get_visuals(){return this.glyph.visuals}draw(s,a,t){this._draw_impl(s,t,a.glglyph)}_draw_impl(s,a,t){(t.data_changed||t.data_mapped)&&(t.set_data(),t.data_changed=!1,t.data_mapped=!1),this.visuals_changed&&(this._set_visuals(),this.visuals_changed=!1);const e=t.nvertices,_=this._show.length,h=this._show.get_sized_array(e);if(s.length<e){this._show_all=!1,h.fill(0);for(let a=0;a<s.length;a++)h[s[a]]=255}else this._show_all&&_==e||(this._show_all=!0,h.fill(255));this._show.update(),this._draw_one_marker_type(t.marker_type,a,t)}}t.SingleMarkerGL=l,l.__name__="SingleMarkerGL"},
563: function _(t,e,_,i,s){i();const r=t(564),h=t(565),a=t(566);class l extends r.BaseGLGlyph{constructor(){super(...arguments),this._antialias=1.5,this._centers=new h.Float32Buffer(this.regl_wrapper),this._widths=new h.Float32Buffer(this.regl_wrapper),this._heights=new h.Float32Buffer(this.regl_wrapper),this._angles=new h.Float32Buffer(this.regl_wrapper),this._auxs=new h.Float32Buffer(this.regl_wrapper),this._border_radius=[0,0,0,0],this._border_radius_nonzero=!1,this._show=new h.Uint8Buffer(this.regl_wrapper),this._show_all=!1,this._linewidths=new h.Float32Buffer(this.regl_wrapper),this._line_caps=new h.Uint8Buffer(this.regl_wrapper),this._line_joins=new h.Uint8Buffer(this.regl_wrapper),this._line_rgba=new h.NormalizedUint8Buffer(this.regl_wrapper,4),this._fill_rgba=new h.NormalizedUint8Buffer(this.regl_wrapper,4),this._have_hatch=!1,this._hatch_patterns=new h.Uint8Buffer(this.regl_wrapper),this._hatch_scales=new h.Float32Buffer(this.regl_wrapper),this._hatch_weights=new h.Float32Buffer(this.regl_wrapper),this._hatch_rgba=new h.NormalizedUint8Buffer(this.regl_wrapper,4),this._did_set_once=!1}marker_props(t){return{width:t._widths,height:t._heights,angle:t._angles,aux:t._auxs,border_radius:t._border_radius}}get line_props(){return{linewidth:this._linewidths,line_color:this._line_rgba,line_cap:this._line_caps,line_join:this._line_joins}}get fill_props(){return{fill_color:this._fill_rgba}}get hatch_props(){return{hatch_pattern:this._hatch_patterns,hatch_scale:this._hatch_scales,hatch_weight:this._hatch_weights,hatch_color:this._hatch_rgba}}_draw_one_marker_type(t,e,_){const i={scissor:this.regl_wrapper.scissor,viewport:this.regl_wrapper.viewport,canvas_size:[e.width,e.height],size_hint:(0,a.marker_type_to_size_hint)(t),nmarkers:_.nvertices,antialias:this._antialias/e.pixel_ratio,show:this._show,center:_._centers,...this.marker_props(_),...this.line_props,...this.fill_props};if(this._have_hatch){const e={...i,...this.hatch_props};this.regl_wrapper.marker_hatch(t)(e)}else{this.regl_wrapper.marker_no_hatch(t)(i)}}set_data(){this._did_set_once||(this._did_set_once=!0,this._set_once()),this._set_data()}_set_once(){}_set_visuals(){const{line:t,fill:e,hatch:_}=this._get_visuals();this._linewidths.set_from_prop(t.line_width),this._line_caps.set_from_line_cap(t.line_cap),this._line_joins.set_from_line_join(t.line_join),this._line_rgba.set_from_color(t.line_color,t.line_alpha),this._fill_rgba.set_from_color(e.fill_color,e.fill_alpha),this._have_hatch=_.doit,this._have_hatch&&(this._hatch_patterns.set_from_hatch_pattern(_.hatch_pattern),this._hatch_scales.set_from_prop(_.hatch_scale),this._hatch_weights.set_from_prop(_.hatch_weight),this._hatch_rgba.set_from_color(_.hatch_color,_.hatch_alpha))}}_.BaseMarkerGL=l,l.__name__="BaseMarkerGL",l.missing_point=-1e4},
564: function _(e,t,a,s,i){s();class h{constructor(e,t){this.nvertices=0,this.size_changed=!1,this.data_changed=!1,this.data_mapped=!1,this.visuals_changed=!1,this.regl_wrapper=e,this.glyph=t}set_data_changed(){const{data_size:e}=this.glyph;e!=this.nvertices&&(this.nvertices=e,this.size_changed=!0),this.data_changed=!0}set_data_mapped(){this.data_mapped=!0}set_visuals_changed(){this.visuals_changed=!0}render(e,t,a){if(0==t.length)return;const{width:s,height:i}=this.glyph.renderer.plot_view.canvas_view.webgl.canvas,{pixel_ratio:h}=this.glyph.renderer.plot_view.canvas_view,_={pixel_ratio:h,width:s/h,height:i/h};this.draw(t,a,_)}}a.BaseGLGlyph=h,h.__name__="BaseGLGlyph"},
565: function _(t,e,r,a,s){a();const i=t(566),_=t(12),n=t(22);class l{constructor(t,e=1){this.regl_wrapper=t,this.is_scalar=!0,this.elements_per_primitive=e}get_array(){return(0,_.assert)(null!=this.array,"WrappedBuffer not yet initialised"),this.array}get_sized_array(t){return null!=this.array&&this.array.length==t||(this.array=this.new_array(t)),this.array}is_normalized(){return!1}get length(){return null!=this.array?this.array.length:0}set_from_array(t){const e=t.length,r=this.get_sized_array(e);for(let a=0;a<e;a++)r[a]=t[a];this.update()}set_from_prop(t){const e=t.is_Scalar()?1:t.length,r=this.get_sized_array(e);for(let a=0;a<e;a++)r[a]=t.get(a);this.update(t.is_Scalar())}set_from_scalar(t){this.get_sized_array(1).fill(t),this.update(!0)}to_attribute_config(t=0,e=1){return{buffer:this.buffer,divisor:this.is_scalar?e:1,normalized:this.is_normalized(),offset:t*this.bytes_per_element()}}to_attribute_config_nested(t=0,e=1){return{buffer:this.buffer,divisor:e*this.elements_per_primitive,normalized:this.is_normalized(),offset:this.is_scalar?0:t*this.bytes_per_element()*this.elements_per_primitive}}update(t=!1){null==this.buffer?this.buffer=this.regl_wrapper.buffer({usage:"dynamic",data:this.array}):this.buffer({data:this.array}),this.is_scalar=t}}l.__name__="WrappedBuffer";class o extends l{bytes_per_element(){return Float32Array.BYTES_PER_ELEMENT}new_array(t){return new Float32Array(t)}}r.Float32Buffer=o,o.__name__="Float32Buffer";class h extends l{bytes_per_element(){return Uint8Array.BYTES_PER_ELEMENT}new_array(t){return new Uint8Array(t)}set_from_color(t,e){const r=t.is_Scalar()&&e.is_Scalar(),a=r?1:t.length,s=this.get_sized_array(4*a);for(let r=0;r<a;r++){const[a,i,_,l]=(0,n.color2rgba)(t.get(r),e.get(r));s[4*r]=a,s[4*r+1]=i,s[4*r+2]=_,s[4*r+3]=l}this.update(r)}set_from_hatch_pattern(t){const e=t.is_Scalar()?1:t.length,r=this.get_sized_array(e);for(let a=0;a<e;a++)r[a]=(0,i.hatch_pattern_to_index)(t.get(a));this.update(t.is_Scalar())}set_from_line_cap(t){const e=t.is_Scalar()?1:t.length,r=this.get_sized_array(e);for(let a=0;a<e;a++)r[a]=i.cap_lookup[t.get(a)];this.update(t.is_Scalar())}set_from_line_join(t){const e=t.is_Scalar()?1:t.length,r=this.get_sized_array(e);for(let a=0;a<e;a++)r[a]=i.join_lookup[t.get(a)];this.update(t.is_Scalar())}}r.Uint8Buffer=h,h.__name__="Uint8Buffer";class f extends h{is_normalized(){return!0}}r.NormalizedUint8Buffer=f,f.__name__="NormalizedUint8Buffer"},
566: function _(e,a,t,n,r){n(),t.interleave=function(e,a,t,n,r){for(let i=0;i<t;i++){const t=e[i],s=a[i];isFinite(t+s)?(r[2*i]=t,r[2*i+1]=s):(r[2*i]=n,r[2*i+1]=n)}},t.hatch_pattern_to_index=function(e){return s[i.hatch_aliases[e]??e]??0},t.marker_type_to_size_hint=function(e){switch(e){case"dash":return 1;case"dot":return 2;case"diamond":case"diamond_cross":case"diamond_dot":return 3;case"hex":case"hex_tile":return 4;case"square_pin":return 5;case"inverted_triangle":case"ngon":case"triangle":case"triangle_dot":return 6;case"triangle_pin":return 7;case"star":case"star_dot":return 8;default:return 0}};const i=e(93);t.cap_lookup={butt:0,round:1,square:2},t.join_lookup={miter:0,round:1,bevel:2};const s={blank:0,dot:1,ring:2,horizontal_line:3,vertical_line:4,cross:5,horizontal_dash:6,vertical_dash:7,spiral:8,right_diagonal_line:9,left_diagonal_line:10,diagonal_cross:11,right_diagonal_dash:12,left_diagonal_dash:13,horizontal_wave:14,vertical_wave:15,criss_cross:16}},
567: function _(s,t,r,e,_){e();const a=s(561);class n extends a.SXSYGlyphGL{constructor(s,t){super(s,t),this.glyph=t}get marker_type(){return"annulus"}get outer_radius(){return this._widths}get inner_radius(){return this._heights}_set_data(){super._set_data(),this.outer_radius.set_from_array(this.glyph.souter_radius),this.inner_radius.set_from_array(this.glyph.sinner_radius)}_set_once(){super._set_once(),this._angles.set_from_scalar(0),this._auxs.set_from_scalar(0)}}r.AnnulusGL=n,n.__name__="AnnulusGL"},
568: function _(s,e,i,t,_){t();const h=s(564),l=s(565),n=s(88);class a extends h.BaseGLGlyph{constructor(s,e){super(s,e),this._antialias=1.5,this._miter_limit=10,this._linewidth=new l.Float32Buffer(this.regl_wrapper),this._line_color=new l.NormalizedUint8Buffer(this.regl_wrapper,4),this._line_cap=new l.Uint8Buffer(this.regl_wrapper),this._line_join=new l.Uint8Buffer(this.regl_wrapper),this._is_dashed=!1,this._dash_tex=[],this.glyph=e}_draw_single(s,e,i,t,_,h,l=null){const n={scissor:this.regl_wrapper.scissor,viewport:this.regl_wrapper.viewport,canvas_size:[e.width,e.height],antialias:this._antialias/e.pixel_ratio,miter_limit:this._miter_limit,points:s._points,show:l??s._show,nsegments:_,linewidth:this._linewidth,line_color:this._line_color,line_cap:this._line_cap,line_join:this._line_join,framebuffer:h,point_offset:t,line_offset:i};if(this._is_dashed&&null!=this._dash_tex[i]){const e={...n,length_so_far:s._length_so_far,dash_tex:this._dash_tex[i],dash_tex_info:this._dash_tex_info,dash_scale:this._dash_scale,dash_offset:this._dash_offset};this.regl_wrapper.dashed_line()(e)}else this.regl_wrapper.solid_line()(n)}_set_length_single(s,e,i){const t=s.length;let _=0;for(let h=0;h<t;h++)s[h]=_,1==i[h+1]?_+=Math.sqrt((e[2*h+4]-e[2*h+2])**2+(e[2*h+5]-e[2*h+3])**2):_=0}_set_points_single(s,e,i){const t=s.length/2-2,_=t>2&&e[0]==e[t-1]&&i[0]==i[t-1]&&isFinite(e[0]+i[0]);for(let _=1;_<t+1;_++)s[2*_]=e[_-1],s[2*_+1]=i[_-1];_?(s[0]=s[2*t-2],s[1]=s[2*t-1],s[2*t+2]=s[4],s[2*t+3]=s[5]):(s[0]=0,s[1]=0,s[2*t+2]=0,s[2*t+3]=0)}_set_show_single(s,e){const i=e.length/2-2;let t=isFinite(e[2]+e[3]);for(let _=1;_<i;_++){const i=isFinite(e[2*_+2]+e[2*_+3]);s[_]=t&&i?1:0,t=i}i>2&&e[0]==e[2*i-2]&&e[1]==e[2*i-1]?(s[0]=s[i-1],s[i]=s[1]):(s[0]=0,s[i]=0)}_set_visuals(){const s=this._get_visuals();this._line_color.set_from_color(s.line_color,s.line_alpha),this._linewidth.set_from_prop(s.line_width),this._line_cap.set_from_line_cap(s.line_cap),this._line_join.set_from_line_join(s.line_join);const{line_dash:e}=s;if(this._is_dashed=!(e.is_Scalar()&&0==e.get(0).length),this._is_dashed){null==this._dash_offset&&(this._dash_offset=new l.Float32Buffer(this.regl_wrapper)),this._dash_offset.set_from_prop(s.line_dash_offset);const i=e.length;null==this._dash_tex_info&&(this._dash_tex_info=new l.Float32Buffer(this.regl_wrapper,4));const t=this._dash_tex_info.get_sized_array(4*i);null==this._dash_scale&&(this._dash_scale=new l.Float32Buffer(this.regl_wrapper));const _=this._dash_scale.get_sized_array(i);for(let s=0;s<i;s++){const i=(0,n.resolve_line_dash)(e.get(s));if(i.length>0){const[e,h,l]=this.regl_wrapper.get_dash(i);this._dash_tex.push(h);for(let i=0;i<4;i++)t[4*s+i]=e[i];_[s]=l}else this._dash_tex.push(null),t.fill(0,4*s,4*(s+1)),_[s]=0}this._dash_tex_info.update(),this._dash_scale.update()}}}i.BaseLineGL=a,a.__name__="BaseLineGL"},
569: function _(e,s,r,t,_){t();const c=e(570);class n extends c.RadialGL{constructor(e,s){super(e,s),this.glyph=s}get marker_type(){return"circle"}_set_once(){super._set_once(),this._angles.set_from_scalar(0)}}r.CircleGL=n,n.__name__="CircleGL"},
570: function _(s,t,e,_,a){_();const i=s(561),r=s(13);class h extends i.SXSYGlyphGL{constructor(s,t){super(s,t),this.glyph=t}get size(){return this._widths}_set_data(){super._set_data(),this.size.set_from_array((0,r.mul)(this.glyph.sradius,2))}_set_once(){super._set_once(),this._heights.set_from_scalar(0)}}e.RadialGL=h,h.__name__="RadialGL"},
571: function _(s,t,_,e,h){e();const a=s(561);class r extends a.SXSYGlyphGL{constructor(s,t){super(s,t),this.glyph=t}get marker_type(){return"hex_tile"}_set_data(){super._set_data(),"pointytop"==this.glyph.model.orientation?(this._angles.set_from_scalar(.5*Math.PI),this._widths.set_from_scalar(2*this.glyph.svy[0]),this._heights.set_from_scalar(4*this.glyph.svx[4]/Math.sqrt(3))):(this._angles.set_from_scalar(0),this._widths.set_from_scalar(2*this.glyph.svx[0]),this._heights.set_from_scalar(4*this.glyph.svy[4]/Math.sqrt(3)))}_set_once(){super._set_once(),this._auxs.set_from_scalar(0)}}_.HexTileGL=r,r.__name__="HexTileGL"},
572: function _(t,e,s,a,i){a();const n=t(564),_=t(565),h=t(12);class l extends n.BaseGLGlyph{constructor(t,e){super(t,e),this._tex=[],this._bounds=[],this._image_changed=!1,this.glyph=e}draw(t,e,s){const a=e.glglyph;(a.data_changed||a.data_mapped)&&a._set_data(),(a._image_changed||a.data_changed)&&a._set_image(),a.data_changed=!1,a.data_mapped=!1,a._image_changed=!1;const{global_alpha:i}=this.glyph.visuals.image;for(const e of t){if(null==a._tex[e]||null==a._bounds[e])continue;const t={scissor:this.regl_wrapper.scissor,viewport:this.regl_wrapper.viewport,canvas_size:[s.width,s.height],bounds:a._bounds[e],tex:a._tex[e],global_alpha:i.get(e)};this.regl_wrapper.image()(t)}}set_image_changed(){this._image_changed=!0}_set_data(){const{image:t}=this.glyph,e=t.length;this._bounds.length!=e&&(this._bounds=Array(e).fill(null));for(let t=0;t<e;t++){const{sx:e,sy:s,sdw:a,sdh:i,xy_anchor:n,xy_scale:h,xy_sign:l}=this.glyph,g=e[t],o=s[t],d=a[t],r=i[t];if(!isFinite(g+o+d+r)){this._bounds[t]=null;continue}null==this._bounds[t]&&(this._bounds[t]=new _.Float32Buffer(this.regl_wrapper));const u=this._bounds[t].get_sized_array(4);u[0]=e[t]+a[t]*(.5*(1-h.x)-n.x)*l.x,u[1]=s[t]+i[t]*(.5*(1-h.y)-n.y)*l.y,u[2]=u[0]+a[t]*h.x*l.x,u[3]=u[1]+i[t]*h.y*l.y,this._bounds[t].update()}}_set_image(){const{image:t,image_data:e}=this.glyph,s=t.length;(0,h.assert)(null!=e),this._tex.length!=s&&(this._tex=Array(s).fill(null));for(let t=0;t<s;t++){const s=e[t];if(null==s){this._tex[t]=null;continue}const a={width:s.width,height:s.height,data:s,format:"rgba",type:"uint8"};null==this._tex[t]?this._tex[t]=this.regl_wrapper.texture(a):this._tex[t](a)}}}s.ImageGL=l,l.__name__="ImageGL"},
573: function _(t,s,e,i,n){i();const h=t(565),_=t(574);class l extends _.SingleLineGL{constructor(t,s){super(t,s),this.glyph=s}draw(t,s,e){this._draw_impl(t,e,s.glglyph)}_get_show_buffer(t,s){const e=s._show;let i=e;if(t.length!=e.length-1){const s=this.glyph.parent.nonselection_glyph==this.glyph,n=e.length,_=e.get_sized_array(n);null==this._show&&(this._show=new h.Uint8Buffer(this.regl_wrapper));const l=this._show.get_sized_array(n);l.fill(0);let r=t[0];s&&r>0&&(l[r]=_[r]);for(let e=1;e<t.length;e++){const i=t[e];i==r+1?l[i]=_[i]:s&&(l[r+1]=_[r+1],l[i]=_[i]),r=i}s&&r!=n-2&&(l[r+1]=_[r+1]),this._show.update(),i=this._show}return i}_get_visuals(){return this.glyph.visuals.line}_set_data_points(){const t=this.glyph.sx,s=this.glyph.sy,e=t.length;null==this._points&&(this._points=new h.Float32Buffer(this.regl_wrapper));const i=this._points.get_sized_array(2*(e+2));return this._set_points_single(i,t,s),this._points.update(),i}}e.LineGL=l,l.__name__="LineGL"},
574: function _(s,t,_,e,a){e();const h=s(568),n=s(565);class i extends h.BaseLineGL{constructor(s,t){super(s,t),this.glyph=t}_draw_impl(s,t,_){this.visuals_changed&&(this._set_visuals(),this.visuals_changed=!1);const e=_.data_changed||_.data_mapped;e&&_._set_data(_.data_changed),(e&&_._is_dashed||this._is_dashed)&&_._set_length(),e&&(_.data_changed=!1,_.data_mapped=!1);const a=this._get_show_buffer(s,_),h=_._points.length/2-2-1;this._draw_single(_,t,0,0,h,null,a)}_set_data(s){const t=this._set_data_points();if(s){const s=t.length/2-2;null==this._show&&(this._show=new n.Uint8Buffer(this.regl_wrapper));const _=this._show.get_sized_array(s+1);this._set_show_single(_,t),this._show.update()}}_set_length(){const s=this._points.get_array(),t=this._show.get_array(),_=s.length/2-2;null==this._length_so_far&&(this._length_so_far=new n.Float32Buffer(this.regl_wrapper));const e=this._length_so_far.get_sized_array(_-1);this._set_length_single(e,s,t),this._length_so_far.update()}}_.SingleLineGL=i,i.__name__="SingleLineGL"},
575: function _(t,s,r,e,_){e();const i=t(562),{abs:o}=Math;class a extends i.SingleMarkerGL{constructor(t,s){super(t,s),this.glyph=s}get marker_type(){return this._border_radius_nonzero?"round_rect":"rect"}_set_data(){const t=this.nvertices,s=this._centers.get_sized_array(2*t),r=this._widths.get_sized_array(t),e=this._heights.get_sized_array(t),{sleft:_,sright:a,stop:h,sbottom:n}=this.glyph,{missing_point:d}=i.SingleMarkerGL;for(let i=0;i<t;i++){const t=_[i],u=a[i],c=h[i],g=n[i];isFinite(t+u+c+g)?(s[2*i]=(t+u)/2,s[2*i+1]=(c+g)/2,r[i]=o(u-t),e[i]=o(c-g)):(s[2*i]=d,s[2*i+1]=d,r[i]=d,e[i]=d)}if(this._centers.update(),this._widths.update(),this._heights.update(),this._angles.set_from_scalar(0),null!=this.glyph.border_radius){const{top_left:t,top_right:s,bottom_right:r,bottom_left:e}=this.glyph.border_radius;this._border_radius=[t,s,r,e],this._border_radius_nonzero=Math.max(...this._border_radius)>0}else this._border_radius=[0,0,0,0],this._border_radius_nonzero=!1}_set_once(){super._set_once(),this._auxs.set_from_scalar(0)}}r.LRTBGL=a,a.__name__="LRTBGL"},
576: function _(s,t,e,a,_){a();const h=s(568),i=s(565);class r extends h.BaseLineGL{constructor(s,t){super(s,t),this.glyph=t}draw(s,t,e){this.visuals_changed&&(this._set_visuals(),this.visuals_changed=!1);const a=t.glglyph,_=a.data_changed||a.data_mapped;_&&a._set_data(a.data_changed),(_&&a._is_dashed||this._is_dashed)&&a._set_length(),_&&(a.data_changed=!1,a.data_mapped=!1);const{data_size:h}=this.glyph;let i=null,r=null;h>1&&([i,r]=this.regl_wrapper.framebuffer_and_texture);let l=0,n=-1;for(const _ of s){for(let s=n+1;s<_;s++){l+=2*(t.sxs.get(s).length+2)}const s=t.sxs.get(_).length,h=s-1;if(null!=i&&this.regl_wrapper.clear_framebuffer(i),this._draw_single(a,e,_,l,h,i),null!=i){const s={scissor:this.regl_wrapper.scissor,viewport:this.regl_wrapper.viewport,framebuffer_tex:r};this.regl_wrapper.accumulate()(s)}l+=2*(s+2),n=_}}_get_visuals(){return this.glyph.visuals.line}_set_data(s){const t=this.glyph.data_size,e=this.glyph.sxs.data.length;null==this._points&&(this._points=new i.Float32Buffer(this.regl_wrapper));const a=this._points.get_sized_array(2*(e+2*t));let _=0;for(let s=0;s<t;s++){const t=this.glyph.sxs.get(s),e=this.glyph.sys.get(s),h=t.length,i=a.subarray(_,_+2*(h+2));this._set_points_single(i,t,e),_+=2*(h+2)}if(this._points.update(),s){null==this._show&&(this._show=new i.Uint8Buffer(this.regl_wrapper));const s=this._show.get_sized_array(e+t);let _=0,h=0;for(let e=0;e<t;e++){const t=this.glyph.sxs.get(e).length,i=a.subarray(_,_+2*(t+2)),r=s.subarray(h,h+t+1);this._set_show_single(r,i),_+=2*(t+2),h+=t+1}this._show.update()}}_set_length(){const s=this.glyph.data_size,t=this.glyph.sxs.data.length,e=this._points.get_array(),a=this._show.get_array();null==this._length_so_far&&(this._length_so_far=new i.Float32Buffer(this.regl_wrapper));const _=this._length_so_far.get_sized_array(t-s);let h=0,r=0,l=0;for(let t=0;t<s;t++){const s=this.glyph.sxs.get(t).length,i=s-1,n=e.subarray(h,h+2*(s+2)),g=a.subarray(r,r+s+1),o=_.subarray(l,l+i);this._set_length_single(o,n,g),h+=2*(s+2),r+=s+1,l+=i}this._length_so_far.update()}}e.MultiLineGL=r,r.__name__="MultiLineGL"},
577: function _(s,t,e,_,a){_();const i=s(563),h=s(566);class r extends i.BaseMarkerGL{constructor(s,t){super(s,t),this.glyph=t}draw(s,t,e){const _=t.glglyph;(_.data_changed||_.data_mapped)&&(_.set_data(),_.data_changed=!1,_.data_mapped=!1),this.visuals_changed&&(this._set_visuals(),this.visuals_changed=!1);const a=_.nvertices,i=_._unique_marker_types.length;for(const t of _._unique_marker_types){if(null==t)continue;let h=a;const r=this._show.length,l=this._show.get_sized_array(a);if(i>1||s.length<a){this._show_all=!1,l.fill(0),h=0;for(const e of s)1!=i&&_._marker_types.get(e)!=t||(l[e]=255,h++)}else this._show_all&&r==a||(this._show_all=!0,l.fill(255));this._show.update(),0!=h&&this._draw_one_marker_type(t,e,_)}}_get_visuals(){return this.glyph.visuals}_set_data(){const s=this.nvertices,t=this._centers.get_sized_array(2*s);(0,h.interleave)(this.glyph.sx,this.glyph.sy,s,i.BaseMarkerGL.missing_point,t),this._centers.update(),this._widths.set_from_prop(this.glyph.size),this._angles.set_from_prop(this.glyph.angle),this._marker_types=this.glyph.marker,this._unique_marker_types=[...new Set(this._marker_types)]}_set_once(){super._set_once(),this._heights.set_from_scalar(0),this._auxs.set_from_scalar(0)}}e.MultiMarkerGL=r,r.__name__="MultiMarkerGL"},
578: function _(t,s,_,e,n){e();const a=t(570);class r extends a.RadialGL{constructor(t,s){super(t,s),this.glyph=s}get marker_type(){return"ngon"}_set_data(){super._set_data(),this._angles.set_from_prop(this.glyph.angle),this._auxs.set_from_prop(this.glyph.n)}}_.NgonGL=r,r.__name__="NgonGL"},
579: function _(t,s,_,r,e){r();const h=t(561);class o extends h.SXSYGlyphGL{constructor(t,s){super(t,s),this.glyph=s}get marker_type(){return this._border_radius_nonzero?"round_rect":"rect"}_set_data(){super._set_data(),this._widths.set_from_array(this.glyph.swidth),this._heights.set_from_array(this.glyph.sheight),this._angles.set_from_prop(this.glyph.angle);const{top_left:t,top_right:s,bottom_right:_,bottom_left:r}=this.glyph.border_radius;this._border_radius=[t,s,_,r],this._border_radius_nonzero=Math.max(...this._border_radius)>0}_set_once(){super._set_once(),this._auxs.set_from_scalar(0)}}_.RectGL=o,o.__name__="RectGL"},
580: function _(e,t,s,i,a){i();const n=e(565),r=e(574),l=e(12);class _ extends r.SingleLineGL{constructor(e,t){super(e,t),this.glyph=t}draw(e,t,s){this._draw_impl(e,s,t.glglyph)}_get_show_buffer(e,t){return t._show}_get_visuals(){return this.glyph.visuals.line}_set_data_points(){const e=this.glyph.sx,t=this.glyph.sy,s=this.glyph.model.mode;let i=e.length;const a=i>2&&e[0]==e[i-1]&&t[0]==t[i-1]&&isFinite(e[0])&&isFinite(t[0]),r="center"==s?2*i:2*i-1;null==this._points&&(this._points=new n.Float32Buffer(this.regl_wrapper));const _=this._points.get_sized_array(2*(r+2));let h=isFinite(e[0]+t[0]),o=2;"center"==s&&(_[o++]=h?e[0]:NaN,_[o++]=t[0]);for(let a=0;a<i-1;a++){const n=isFinite(e[a+1]+t[a+1]);switch(s){case"before":_[o++]=h?e[a]:NaN,_[o++]=t[a],a<i-1&&(_[o++]=h&&n?e[a]:NaN,_[o++]=t[a+1]);break;case"after":_[o++]=h?e[a]:NaN,_[o++]=t[a],a<i-1&&(_[o++]=h&&n?e[a+1]:NaN,_[o++]=t[a]);break;case"center":if(h&&n){const s=(e[a]+e[a+1])/2;_[o++]=s,_[o++]=t[a],_[o++]=s,_[o++]=t[a+1]}else _[o++]=h?e[a]:NaN,_[o++]=t[a],_[o++]=n?e[a+1]:NaN,_[o++]=t[a+1];break;default:(0,l.unreachable)()}h=n}return _[o++]=h?e[i-1]:NaN,_[o++]=h?t[i-1]:NaN,(0,l.assert)(o==2*r+2),i=r,a?(_[0]=_[2*i-2],_[1]=_[2*i-1],_[2*i+2]=_[4],_[2*i+3]=_[5]):(_[0]=0,_[1]=0,_[2*i+2]=0,_[2*i+3]=0),this._points.update(),_}}s.StepGL=_,_.__name__="StepGL"},
581: function _(t,e,s,_,r){_();const a=t(561);class n extends a.SXSYGlyphGL{constructor(t,e){super(t,e),this.glyph=e}get marker_type(){return"wedge"}get radius(){return this._widths}get start_angle(){return this._angles}get end_angle(){return this._auxs}_set_data(){super._set_data(),this.radius.set_from_array(this.glyph.sradius),"anticlock"==this.glyph.model.direction?(this.start_angle.set_from_prop(this.glyph.start_angle),this.end_angle.set_from_prop(this.glyph.end_angle)):(this.start_angle.set_from_prop(this.glyph.end_angle),this.end_angle.set_from_prop(this.glyph.start_angle))}_set_once(){super._set_once(),this._heights.set_from_scalar(0)}}s.WedgeGL=n,n.__name__="WedgeGL"},
}, 546, {"models/glyphs/webgl/main":546,"models/glyphs/webgl/index":547,"models/glyphs/webgl/regl_wrap":548,"models/glyphs/webgl/dash_cache":550,"models/glyphs/webgl/utils/math":551,"models/glyphs/webgl/accumulate.vert":552,"models/glyphs/webgl/accumulate.frag":553,"models/glyphs/webgl/image.vert":554,"models/glyphs/webgl/image.frag":555,"models/glyphs/webgl/regl_line.vert":556,"models/glyphs/webgl/regl_line.frag":557,"models/glyphs/webgl/marker.vert":558,"models/glyphs/webgl/marker.frag":559,"models/glyphs/webgl/annular_wedge":560,"models/glyphs/webgl/sxsy":561,"models/glyphs/webgl/single_marker":562,"models/glyphs/webgl/base_marker":563,"models/glyphs/webgl/base":564,"models/glyphs/webgl/buffer":565,"models/glyphs/webgl/webgl_utils":566,"models/glyphs/webgl/annulus":567,"models/glyphs/webgl/base_line":568,"models/glyphs/webgl/circle":569,"models/glyphs/webgl/radial":570,"models/glyphs/webgl/hex_tile":571,"models/glyphs/webgl/image":572,"models/glyphs/webgl/line_gl":573,"models/glyphs/webgl/single_line":574,"models/glyphs/webgl/lrtb":575,"models/glyphs/webgl/multi_line":576,"models/glyphs/webgl/multi_marker":577,"models/glyphs/webgl/ngon":578,"models/glyphs/webgl/rect":579,"models/glyphs/webgl/step":580,"models/glyphs/webgl/wedge":581}, {});});
