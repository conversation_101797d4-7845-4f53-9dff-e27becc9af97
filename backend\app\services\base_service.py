from pathlib import Path
import logging
import hashlib
import aiofiles
from typing import Dict, Any, Optional, Union, BinaryIO
from fastapi import UploadFile

logger = logging.getLogger(__name__)

class BaseService:
    """服务基类，提供通用功能"""
    
    def __init__(self, base_dir: Path = None):
        """初始化基础服务"""
        self.base_dir = base_dir or Path.cwd()
    
    async def ensure_directory(self, directory: Path) -> Path:
        """确保目录存在"""
        directory.mkdir(parents=True, exist_ok=True)
        return directory
    
    async def calculate_file_hash(self, file_path: Path) -> str:
        """计算文件哈希值"""
        try:
            sha256_hash = hashlib.sha256()
            
            async with aiofiles.open(file_path, 'rb') as f:
                while chunk := await f.read(8192):
                    sha256_hash.update(chunk)
                    
            return sha256_hash.hexdigest()
        except Exception as e:
            logger.error(f"计算文件哈希值失败: {str(e)}")
            # 生成基于文件名和大小的临时哈希
            import time
            temp_hash = f"{file_path.name}_{file_path.stat().st_size}_{time.time()}"
            return hashlib.sha256(temp_hash.encode()).hexdigest()
    
    async def save_file_chunks(self, source: Union[BinaryIO, UploadFile], destination: Path) -> Dict[str, Any]:
        """以块的方式保存文件"""
        try:
            total_bytes = 0
            async with aiofiles.open(destination, 'wb') as f:
                while chunk := await source.read(8192):
                    await f.write(chunk)
                    total_bytes += len(chunk)
            
            # 确保文件已写入
            if not destination.exists():
                raise FileNotFoundError(f"保存后找不到文件: {destination}")
                
            file_size = destination.stat().st_size
            if file_size == 0:
                logger.warning(f"保存的文件大小为0: {destination}")
                
            # 计算文件哈希值
            file_hash = await self.calculate_file_hash(destination)
            
            return {
                "success": True,
                "file_size": file_size,
                "file_hash": file_hash,
                "total_bytes": total_bytes,
                "message": f"成功写入 {total_bytes} 字节"
            }
        except Exception as e:
            logger.error(f"保存文件失败: {str(e)}")
            # 如果文件已创建但保存失败，尝试删除
            if destination.exists():
                try:
                    destination.unlink()
                    logger.info(f"已删除失败的文件: {destination}")
                except Exception as clean_error:
                    logger.error(f"无法删除失败的文件: {str(clean_error)}")
                    
            return {
                "success": False,
                "message": f"保存文件失败: {str(e)}"
            } 