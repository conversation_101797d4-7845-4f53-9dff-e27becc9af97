import asyncio
import logging
from sqlalchemy.orm import Session
from pathlib import Path
from datetime import datetime
from ..database import SessionLocal
from ..models.enums import ProcessStatus
from ..models import RawFile
from .timeseries_service import TimeSeriesService

logger = logging.getLogger(__name__)

class TaskProcessor:
    def __init__(self, processed_dir: Path):
        self.processed_dir = processed_dir
        self.timeseries_service = TimeSeriesService(processed_dir)
        self.is_running = False
        self.process_interval = 60  # 扫描间隔（秒）

    async def start(self):
        """启动任务处理器"""
        self.is_running = True
        logger.info("任务处理器已启动")
        
        while self.is_running:
            try:
                # 处理未转换的文件
                await self.process_pending_files()
                
                # 等待下一次处理
                await asyncio.sleep(self.process_interval)
                
            except Exception as e:
                logger.error(f"处理任务时出错: {str(e)}")
                await asyncio.sleep(10)  # 出错后等待一段时间再继续

    async def stop(self):
        """停止任务处理器"""
        self.is_running = False
        logger.info("任务处理器已停止")

    async def process_pending_files(self):
        """处理所有待转换的文件"""
        db = SessionLocal()
        try:
            # 获取所有未处理的文件
            pending_files = db.query(RawFile).filter(
                RawFile.status == ProcessStatus.PENDING
            ).all()

            if not pending_files:
                return

            logger.info(f"发现 {len(pending_files)} 个待处理文件")

            for file in pending_files:
                try:
                    logger.info(f"开始处理文件: {file.filename}")
                    
                    # 检查文件是否存在
                    if not Path(file.file_path).exists():
                        logger.error(f"文件不存在: {file.file_path}")
                        file.status = ProcessStatus.ERROR
                        file.error_message = "文件不存在"
                        continue

                    # 更新状态为处理中
                    file.status = ProcessStatus.PROCESSING
                    db.commit()

                    # 处理时间序列数据
                    await self.timeseries_service.process_timeseries_data(file, db)
                    
                    # 更新文件状态
                    file.status = ProcessStatus.COMPLETED
                    logger.info(f"文件处理完成: {file.filename}")

                except Exception as e:
                    logger.error(f"处理文件 {file.filename} 时出错: {str(e)}")
                    file.status = ProcessStatus.ERROR
                    file.error_message = str(e)

                finally:
                    db.commit()

        except Exception as e:
            logger.error(f"处理待转换文件时出错: {str(e)}")
            db.rollback()
        
        finally:
            db.close()

    @staticmethod
    async def manual_process_file(file_id: int) -> bool:
        """手动处理指定文件"""
        db = SessionLocal()
        try:
            file = db.query(RawFile).get(file_id)
            if not file:
                logger.error(f"文件不存在: {file_id}")
                return False

            # 更新状态为处理中
            file.status = ProcessStatus.PROCESSING
            db.commit()

            processor = TaskProcessor(Path("data/processed"))
            await processor.timeseries_service.process_timeseries_data(file, db)
            
            file.status = ProcessStatus.COMPLETED
            db.commit()
            
            logger.info(f"文件 {file.filename} 处理完成")
            return True

        except Exception as e:
            logger.error(f"处理文件 {file_id} 时出错: {str(e)}")
            if 'file' in locals() and file:
                file.status = ProcessStatus.ERROR
                file.error_message = str(e)
            db.commit()
            return False

        finally:
            db.close() 