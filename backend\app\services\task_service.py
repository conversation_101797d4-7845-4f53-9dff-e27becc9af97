from pathlib import Path
import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq
import uuid
import logging
import asyncio
from datetime import datetime
from typing import Dict, Any, List, Optional
from sqlalchemy.orm import Session

from app.models import RawFile, raw_parquet_association, ParquetFile
# from app.models.parquet_file import ParquetFile
from app.models.enums import ProcessStatus
from app.utils.file_utils import FileUtils

logger = logging.getLogger(__name__)

class TaskService:
    """任务处理服务，处理文件转换和处理任务"""
    
    def __init__(self, raw_dir: Path, processed_dir: Path):
        self.raw_dir = raw_dir
        self.processed_dir = processed_dir
        
        # 确保目录存在
        self.raw_dir.mkdir(parents=True, exist_ok=True)
        self.processed_dir.mkdir(parents=True, exist_ok=True)
        
        # 任务状态跟踪
        self.tasks = {}
    
    async def process_file(self, file_id: int, db: Session) -> Dict[str, Any]:
        """处理单个文件"""
        # 获取文件记录
        file = db.query(RawFile).filter(RawFile.id == file_id).first()
        if not file:
            raise ValueError(f"未找到ID为{file_id}的文件记录")
        
        # 生成任务ID
        task_id = str(uuid.uuid4())
        
        # 将任务加入队列
        self.tasks[task_id] = {
            "file_id": file_id,
            "status": "pending",
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "result": None,
            "error": None
        }
        
        # 异步处理任务
        asyncio.create_task(self._process_file_task(task_id, file, db))
        
        return {"task_id": task_id, "status": "pending"}
    
    async def _process_file_task(self, task_id: str, file: RawFile, db: Session):
        """异步处理文件任务"""
        self.tasks[task_id]["status"] = "processing"
        self.tasks[task_id]["updated_at"] = datetime.now()
        
        try:
            # 更新文件状态为处理中
            file.status = ProcessStatus.PROCESSING
            db.commit()
            
            # 检查文件路径
            file_path = Path(file.file_path)
            if not file_path.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            # 确定输出文件名
            output_filename = f"{file_path.stem}.parquet"
            output_path = self.processed_dir / output_filename
            
            # 加载CSV文件
            encoding = file.encoding or 'utf-8'
            delimiter = file.delimiter or ','
            
            try:
                # 读取CSV文件
                df = pd.read_csv(
                    file_path, 
                    encoding=encoding, 
                    sep=delimiter,
                    low_memory=False
                )
                
                # 转换为Parquet格式
                table = pa.Table.from_pandas(df)
                
                # 写入Parquet文件
                pq.write_table(table, str(output_path))
                
                # 计算压缩率
                original_size = file_path.stat().st_size
                compressed_size = output_path.stat().st_size
                compression_ratio = original_size / compressed_size if compressed_size > 0 else 0
                
                # 创建Parquet文件记录
                parquet_file = ParquetFile(
                    filename=output_filename,
                    file_path=str(output_path),
                    file_size=float(compressed_size),
                    file_hash=await FileUtils.calculate_file_hash(output_path),
                    status=ProcessStatus.COMPLETED,
                    record_count=len(df),
                    compression_ratio=float(compression_ratio),
                    # 如果有时间列，可以设置时间范围
                    # time_range_start = ...,
                    # time_range_end = ...,
                    schema=FileUtils.serialize_to_json(
                        {col: str(dtype) for col, dtype in df.dtypes.items()}
                    )
                )
                
                db.add(parquet_file)
                db.flush()  # 确保parquet_file有id
                
                # 创建关联关系
                association = raw_parquet_association.insert().values(
                    raw_file_id=file.id,
                    parquet_file_id=parquet_file.id
                )
                db.execute(association)
                
                # 更新原始文件状态
                file.status = ProcessStatus.COMPLETED
                file.total_records = len(df)
                
                db.commit()
                
                # 更新任务状态
                self.tasks[task_id]["status"] = "completed"
                self.tasks[task_id]["result"] = {
                    "output_path": str(output_path),
                    "record_count": len(df),
                    "parquet_file_id": parquet_file.id
                }
                
            except Exception as e:
                logger.error(f"处理文件失败: {str(e)}")
                file.status = ProcessStatus.ERROR
                file.error_message = str(e)
                db.commit()
                
                # 更新任务状态
                self.tasks[task_id]["status"] = "error"
                self.tasks[task_id]["error"] = str(e)
                
        except Exception as e:
            logger.error(f"任务处理失败: {str(e)}")
            
            # 更新文件状态
            try:
                file.status = ProcessStatus.ERROR
                file.error_message = str(e)
                db.commit()
            except:
                logger.error("更新文件状态失败")
            
            # 更新任务状态
            self.tasks[task_id]["status"] = "error"
            self.tasks[task_id]["error"] = str(e)
        
        finally:
            self.tasks[task_id]["updated_at"] = datetime.now()
    
    async def get_task_status(self, task_id: str, db: Session) -> Dict[str, Any]:
        """获取任务状态"""
        if task_id not in self.tasks:
            raise ValueError(f"未找到任务ID: {task_id}")
            
        task = self.tasks[task_id]
        
        # 如果任务已完成且处于内存中，可以考虑从内存中清除
        if task["status"] in ["completed", "error"] and (datetime.now() - task["updated_at"]).total_seconds() > 3600:
            # 任务完成超过1小时，可以从内存中清除
            pass
            
        return task
    
    async def batch_process(self, db: Session) -> Dict[str, Any]:
        """批量处理所有待处理的文件"""
        # 获取所有待处理的文件
        pending_files = db.query(RawFile).filter(RawFile.status == ProcessStatus.PENDING).all()
        
        tasks = []
        for file in pending_files:
            result = await self.process_file(file.id, db)
            tasks.append({
                "file_id": file.id,
                "task_id": result["task_id"],
                "filename": file.filename
            })
        
        return {
            "count": len(tasks),
            "tasks": tasks
        } 