"""
管道标准数据管理模块

此模块提供了管道标准数据的管理功能，包括：
- 加载和解析标准数据文件
- 管理标准数据
- 提供数据查询接口
"""

import os
from pathlib import Path
from typing import Dict, List, Optional, Any
import yaml
from dataclasses import dataclass, field
from datetime import datetime


@dataclass
class PipeField:
    """管道字段定义"""
    name: str
    title: str
    description: str
    data_type: str
    required: bool = False


@dataclass
class PipeSize:
    """管道尺寸数据"""
    nom: int
    od: float
    description: str
    ptn: str


@dataclass
class SizeTable:
    """管道尺寸表"""
    name: str
    version: str
    description: str
    created_time: str
    modified_time: str
    unit: str
    fields: List[PipeField]
    sizes: List[PipeSize]


@dataclass
class PipeStandard:
    """管道标准"""
    name: str
    version: str
    description: str
    created_time: str
    modified_time: str
    guid: str
    size_tables: List[SizeTable]


class PipeStandardManager:
    """管道标准管理器"""

    def __init__(self, data_dir: str = None):
        """
        初始化管道标准管理器
        
        Args:
            data_dir: 数据目录路径，默认为当前目录下的dataset/pipe_standards
        """
        if data_dir is None:
            data_dir = os.path.join(os.path.dirname(__file__),
                                    'dataset', 'pipe_standards')
        self.data_dir = Path(data_dir)
        self._standards: Dict[str, PipeStandard] = {}
        self._load_all_standards()

    def _load_all_standards(self) -> None:
        """加载所有标准数据"""
        if not self.data_dir.exists():
            raise FileNotFoundError(f"数据目录不存在: {self.data_dir}")

        for file_path in self.data_dir.glob("*.yaml"):
            self._load_standard(file_path)

    def _load_standard(self, file_path: Path) -> None:
        """
        加载单个标准数据文件
        
        Args:
            file_path: 标准数据文件路径
        """
        with open(file_path, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)

        # 解析目录信息
        catalog = data['catalog']
        size_tables = []

        # 解析尺寸表
        for table_data in data['size_tables']:
            # 解析字段定义
            fields = [
                PipeField(
                    name=field['name'],
                    title=field['title'],
                    description=field['description'],
                    data_type=field['data_type'],
                    required=field.get('required', False)
                ) for field in table_data['fields']
            ]

            # 解析尺寸数据
            sizes = [
                PipeSize(
                    nom=size['nom'],
                    od=size['od'],
                    description=size['description'],
                    ptn=size['ptn']
                ) for size in table_data['sizes']
            ]

            # 创建尺寸表
            size_table = SizeTable(
                name=table_data['name'],
                version=table_data['version'],
                description=table_data['description'],
                created_time=table_data['created_time'],
                modified_time=table_data['modified_time'],
                unit=table_data['unit'],
                fields=fields,
                sizes=sizes
            )
            size_tables.append(size_table)

        # 创建标准对象
        standard = PipeStandard(
            name=catalog['name'],
            version=catalog['version'],
            description=catalog['description'],
            created_time=catalog['created_time'],
            modified_time=catalog['modified_time'],
            guid=catalog['guid'],
            size_tables=size_tables
        )

        self._standards[standard.name] = standard

    def get_standard(self, name: str) -> Optional[PipeStandard]:
        """
        获取指定标准
        
        Args:
            name: 标准名称
            
        Returns:
            PipeStandard: 标准对象，如果不存在则返回None
        """
        return self._standards.get(name)

    def get_size_table(self, standard_name: str,
                       table_name: str) -> Optional[SizeTable]:
        """
        获取指定标准的尺寸表
        
        Args:
            standard_name: 标准名称
            table_name: 尺寸表名称
            
        Returns:
            SizeTable: 尺寸表对象，如果不存在则返回None
        """
        standard = self.get_standard(standard_name)
        if not standard:
            return None

        for table in standard.size_tables:
            if table.name == table_name:
                return table
        return None

    def get_pipe_size(self, standard_name: str, table_name: str,
                      nom: int) -> Optional[PipeSize]:
        """
        获取指定标准、尺寸表和公称直径的管道尺寸信息
        
        Args:
            standard_name: 标准名称
            table_name: 尺寸表名称
            nom: 公称直径
            
        Returns:
            PipeSize: 管道尺寸信息，如果不存在则返回None
        """
        table = self.get_size_table(standard_name, table_name)
        if not table:
            return None

        for size in table.sizes:
            if size.nom == nom:
                return size
        return None

    def get_all_standards(self) -> Dict[str, PipeStandard]:
        """
        获取所有标准
        
        Returns:
            Dict[str, PipeStandard]: 所有标准
        """
        return self._standards.copy()

    def get_available_standards(self) -> List[str]:
        """
        获取所有可用的标准名称
        
        Returns:
            List[str]: 所有可用的标准名称
        """
        return list(self._standards.keys())


# 创建全局管理器实例
standard_manager = PipeStandardManager()

if __name__ == '__main__':
    # 示例：获取GB-B标准的Size-b表中DN100的尺寸信息
    pipe_size = standard_manager.get_pipe_size("GB-B", "Size-b", 100)
    if pipe_size:
        print(f"DN100管道信息：")
        print(f"  公称直径：{pipe_size.nom}")
        print(f"  外径：{pipe_size.od}")
        print(f"  描述：{pipe_size.description}")
        print(f"  零件号：{pipe_size.ptn}")
    else:
        print("未找到指定的管道尺寸信息")
