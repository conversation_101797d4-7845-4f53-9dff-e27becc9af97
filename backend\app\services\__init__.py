"""
服务层包

此包包含所有业务逻辑服务，包括：
- 基础服务（BaseService）
- 文件服务（FileService, FileAnalyzer）
- 数据处理服务（ParquetService, TimeSeriesService）
- 任务服务（TaskService, TaskProcessor）
"""

from .file_analyzer import FileAnalyzer
from .file_service import FileService
from .parquet_service import ParquetService
from .task_service import TaskService
from .task_processor import TaskProcessor
from .timeseries_service import TimeSeriesService
from .base_service import BaseService

# 确保所有服务都在这里导入，便于统一管理
__all__ = [
    # 基础服务
    'BaseService',

    # 文件服务
    'FileService',
    'FileAnalyzer',

    # 数据处理服务
    'ParquetService',
    'TimeSeriesService',

    # 任务服务
    'TaskService',
    'TaskProcessor'
]

# 版本信息
__version__ = '1.0.0'
