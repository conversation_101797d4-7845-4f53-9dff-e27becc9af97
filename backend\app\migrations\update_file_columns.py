"""
迁移脚本：更新文件表结构
添加软删除字段和重命名时间范围字段
"""
from backend.app.core.config import settings
import sqlite3
import logging
import sys
import os
from pathlib import Path

# 将项目根目录添加到Python路径
project_root = Path(__file__).resolve().parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入配置

# 配置日志
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def migrate(db_path):
    """
    执行数据库迁移
    :param db_path: 数据库文件路径
    """
    try:
        logger.info(f"开始执行数据库迁移，更新文件表结构: {db_path}")

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 1. 为原始文件表添加软删除相关字段
        logger.info("为raw_files表添加软删除字段")
        raw_files_columns_to_add = [
            ("is_deleted", "BOOLEAN DEFAULT 0"),
            ("deleted_at", "TIMESTAMP"),
            ("deleted_by", "VARCHAR(100)"),
            ("deletion_reason", "TEXT"),
            ("parquet_count", "INTEGER")
        ]

        for column, type_def in raw_files_columns_to_add:
            try:
                cursor.execute(
                    f"ALTER TABLE raw_files ADD COLUMN {column} {type_def}")
                logger.info(f"成功添加字段 raw_files.{column}")
            except sqlite3.OperationalError as e:
                if "duplicate column name" in str(e).lower():
                    logger.warning(f"字段 raw_files.{column} 已存在，跳过")
                else:
                    logger.error(f"添加字段 raw_files.{column} 时出错: {str(e)}")

        # 2. 为parquet_files表添加软删除相关字段
        logger.info("为parquet_files表添加软删除字段")
        parquet_files_columns_to_add = [
            ("is_deleted", "BOOLEAN DEFAULT 0"),
            ("deleted_at", "TIMESTAMP"),
            ("deleted_by", "VARCHAR(100)"),
            ("deletion_reason", "TEXT")
        ]

        for column, type_def in parquet_files_columns_to_add:
            try:
                cursor.execute(
                    f"ALTER TABLE parquet_files ADD COLUMN {column} {type_def}")
                logger.info(f"成功添加字段 parquet_files.{column}")
            except sqlite3.OperationalError as e:
                if "duplicate column name" in str(e).lower():
                    logger.warning(f"字段 parquet_files.{column} 已存在，跳过")
                else:
                    logger.error(f"添加字段 parquet_files.{column} 时出错: {str(e)}")

        # 3. 为原始文件表添加新的时间范围字段
        logger.info("为raw_files表添加新的时间范围字段")
        try:
            cursor.execute(
                "ALTER TABLE raw_files ADD COLUMN time_range_start TIMESTAMP")
            cursor.execute(
                "ALTER TABLE raw_files ADD COLUMN time_range_end TIMESTAMP")
            logger.info("成功添加字段 time_range_start 和 time_range_end")
        except sqlite3.OperationalError as e:
            if "duplicate column name" in str(e).lower():
                logger.warning("时间范围字段已存在，跳过")
            else:
                logger.error(f"添加时间范围字段时出错: {str(e)}")

        # 4. 复制数据从旧字段到新字段
        logger.info("尝试从旧字段复制数据到新字段")
        try:
            cursor.execute("""
                UPDATE raw_files 
                SET time_range_start = data_start_time,
                    time_range_end = data_end_time
                WHERE data_start_time IS NOT NULL OR data_end_time IS NOT NULL
            """)
            affected_rows = cursor.rowcount
            logger.info(f"成功更新 {affected_rows} 条记录的时间范围数据")
        except sqlite3.OperationalError as e:
            logger.warning(f"复制时间范围数据时出错（可能是旧字段不存在）: {str(e)}")

        # 5. 为parquet_files表重命名时间范围字段
        logger.info("为parquet_files表添加新的时间范围字段")
        try:
            cursor.execute(
                "ALTER TABLE parquet_files ADD COLUMN time_range_start TIMESTAMP")
            cursor.execute(
                "ALTER TABLE parquet_files ADD COLUMN time_range_end TIMESTAMP")
            logger.info("成功添加字段 time_range_start 和 time_range_end")

            # 复制数据
            cursor.execute("""
                UPDATE parquet_files 
                SET time_range_start = start_time,
                    time_range_end = end_time
                WHERE start_time IS NOT NULL OR end_time IS NOT NULL
            """)
            affected_rows = cursor.rowcount
            logger.info(f"成功更新 {affected_rows} 条记录的时间范围数据")
        except sqlite3.OperationalError as e:
            logger.warning(f"parquet_files表更新时间范围字段时出错: {str(e)}")

        # 提交更改
        conn.commit()
        logger.info("数据库迁移成功完成")

    except Exception as e:
        logger.error(f"数据库迁移过程中出错: {str(e)}")
        conn.rollback()
        raise
    finally:
        if 'conn' in locals():
            conn.close()


if __name__ == "__main__":
    # 从settings获取数据库文件路径
    db_path = settings.DATABASE_URL.replace('sqlite:///', '')

    if not os.path.exists(db_path):
        logger.error(f"数据库文件不存在: {db_path}")
        # 尝试创建数据库目录
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        logger.info(f"已创建数据库目录: {os.path.dirname(db_path)}")
        # 创建空数据库文件
        open(db_path, 'a').close()
        logger.info(f"已创建空数据库文件: {db_path}")

    migrate(db_path)
