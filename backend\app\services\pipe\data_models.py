from dataclasses import dataclass
from typing import Optional, List
from datetime import datetime
import math


@dataclass
class PipeData:
    """管道基础数据类"""
    pipe_id: str                    # 管段号
    outer_diameter: float           # 管道外径(mm)
    medium_temperature: float       # 介质温度(℃)
    length: float                   # 管道长度(m)
    material: str = "S304"          # 管道材质，默认S304
    thickness: float = 0.0          # 管道壁厚(mm)
    inner_diameter: float = 0.0     # 管道内径(mm)
    section_modulus: float = 0.0    # 抗弯截面模数(m³)
    moment_of_inertia: float = 0.0  # 惯性矩(m⁴)
    surface_temperature: Optional[float] = None  # 表面温度(℃)
    ambient_temperature: Optional[float] = None  # 环境温度(℃)
    surface_coefficient: Optional[float] = None  # 表面传热系数(W/m²·℃)

    def __post_init__(self):
        """计算管道内径"""
        if self.thickness > 0 and self.inner_diameter == 0:
            self.inner_diameter = self.outer_diameter - 2 * self.thickness


@dataclass
class InsulationLayer_material:
    """保温/保冷层数据类"""
    material_name: str              # 材料名称
    sub_material_name: str          # 子名称
    density: float                  # 密度(kg/m³)
    max_temperature: float          # 最高使用温度(℃)
    recommended_temperature: float  # 推荐使用温度(℃)
    thermal_conductivity_70: float  # 常用导热系数(70℃)(W/m·K)
    thermal_conductivity_eq: str    # 导热系数参考方程
    compressive_strength: Optional[float] = None  # 抗压强度(MPa)

    @property
    def info(self) -> str:
        """返回材料的详细信息"""
        info_lines = [
            f"材料名称: {self.material_name}",
            f"子材料名称: {self.sub_material_name}",
            f"密度: {self.density} kg/m³",
            f"最高使用温度: {self.max_temperature} ℃",
            f"推荐使用温度: {self.recommended_temperature} ℃",
            f"70℃导热系数: {self.thermal_conductivity_70} W/m·K",
            "导热系数方程:"
        ]
        # 添加导热系数方程
        for eq in self.thermal_conductivity_eq.split('\n'):
            info_lines.append(f"  {eq}")

        # 添加抗压强度（如果有）
        if self.compressive_strength is not None:
            info_lines.append(f"抗压强度: {self.compressive_strength} MPa")

        return "\n".join(info_lines)


@dataclass
class ProtectionLayer:
    """保护层数据类"""
    material_name: str              # 材料名称
    thickness: float                # 厚度(mm)
    density: float                  # 密度(kg/m³)


@dataclass
class MoistureBarrierLayer:
    """防潮层数据类"""
    material_name: str              # 材料名称
    thickness: float                # 厚度(mm)
    density: float                  # 密度(kg/m³)


@dataclass
class InsulationData:
    """保温/保冷结构数据类"""
    # 基础计算结果
    raw_thickness: float            # 原始计算厚度(mm)
    standard_thickness: float       # 圆整后的标准厚度(mm)
    thermal_conductivity: float     # 导热系数(W/m·K)
    heat_loss: float               # 热损失(kW)
    calculation_status: str         # 计算状态

    # 保温/保冷层
    insulation_layer: InsulationLayer_material  # 保温/保冷层

    # 保护层
    protection_layer: ProtectionLayer  # 保护层

    # 防潮层（保冷结构需要）
    moisture_barrier_layer: Optional[MoistureBarrierLayer] = None  # 防潮层

    # 计算结果
    volume: float = 0.0            # 总体积(m³)
    volume_per_meter: float = 0.0  # 单位长度体积(m³/m)
    weight: float = 0.0            # 总重量(kg)
    weight_per_meter: float = 0.0  # 单位长度重量(kg/m)

    @property
    def is_cold_insulation(self) -> bool:
        """判断是否为保冷结构"""
        return self.moisture_barrier_layer is not None

    @property
    def total_thickness(self) -> float:
        """计算总厚度(mm)"""
        thickness = self.standard_thickness + self.protection_layer.thickness
        if self.is_cold_insulation:
            thickness += self.moisture_barrier_layer.thickness
        return thickness

    @property
    def total_weight_per_meter(self) -> float:
        """计算单位长度总重量(kg/m)"""
        # 保温/保冷层重量
        weight = self.weight_per_meter

        # 保护层重量
        protection_volume = math.pi * (
            (self.standard_thickness/1000 + self.protection_layer.thickness/1000)**2 -
            (self.standard_thickness/1000)**2
        ) / 4
        weight += protection_volume * self.protection_layer.density

        # 防潮层重量（如果存在）
        if self.is_cold_insulation:
            moisture_volume = math.pi * (
                (self.standard_thickness/1000 + self.moisture_barrier_layer.thickness/1000)**2 -
                (self.standard_thickness/1000)**2
            ) / 4
            weight += moisture_volume * self.moisture_barrier_layer.density

        return weight


@dataclass
class PipeResult:
    """管道计算结果类"""
    pipe_data: PipeData            # 管道基础数据
    insulation_data: InsulationData  # 保温层数据
    calculation_time: datetime     # 计算时间

    @property
    def final_outer_diameter(self) -> float:
        """计算最终外径(mm)"""
        return self.pipe_data.outer_diameter + 2 * self.insulation_data.standard_thickness

    def to_dict(self) -> dict:
        """转换为字典格式，用于生成报告"""
        return {
            "管段号": self.pipe_data.pipe_id,
            "外径(mm)": self.pipe_data.outer_diameter,
            "介质温度(℃)": self.pipe_data.medium_temperature,
            "管道长度(m)": self.pipe_data.length,
            "表面温度(℃)": self.pipe_data.surface_temperature,
            "环境温度(℃)": self.pipe_data.ambient_temperature,
            "表面换热系数(W/m²·℃)": self.pipe_data.surface_coefficient,
            "导热系数(W/m·K)": f"{self.insulation_data.thermal_conductivity:.4f}",
            "保温层计算厚度(mm)": f"{self.insulation_data.raw_thickness:.1f}",
            "保温层实施厚度(mm)": f"{self.insulation_data.standard_thickness:.1f}",
            "总散热损失(kW)": f"{self.insulation_data.heat_loss:.2f}",
            "规范状态": self.insulation_data.calculation_status,
            "计算时间": self.calculation_time.strftime("%Y-%m-%d %H:%M:%S")
        }


class StandardThickness:
    """标准厚度管理类"""
    STANDARD_THICKNESS = [30, 40, 50, 60, 70,
                          80, 90, 100, 120, 140, 160, 180, 200]

    @classmethod
    def round_thickness(cls, thickness_mm: float) -> float:
        """工程厚度圆整规则，使用二分查找优化"""
        if thickness_mm <= 0:
            return 0

        # 使用二分查找找到第一个大于等于计算厚度的标准厚度
        left, right = 0, len(cls.STANDARD_THICKNESS) - 1
        while left < right:
            mid = (left + right) // 2
            if cls.STANDARD_THICKNESS[mid] < thickness_mm:
                left = mid + 1
            else:
                right = mid

        return cls.STANDARD_THICKNESS[left] if cls.STANDARD_THICKNESS[left] >= thickness_mm else cls.STANDARD_THICKNESS[-1]


class DefaultParameters:
    """默认参数管理类"""
    DEFAULT_SURFACE_TEMP = 50      # 默认表面温度(℃)
    DEFAULT_AMBIENT_TEMP = 25      # 默认环境温度(℃)
    DEFAULT_SURFACE_COEFF = 11.6   # 默认表面传热系数(W/m²·℃)
    DEFAULT_LENGTH = 1.0           # 默认管道长度(m)
    DEFAULT_REPORT_FILENAME = f"管道保温计算报告_{datetime.now().strftime('%Y%m%d')}.xlsx"
