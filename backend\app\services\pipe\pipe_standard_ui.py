"""
管道标准数据管理界面

此模块提供了一个简单的命令行界面来查看和管理管道标准数据。
"""

from typing import List, Optional
from .pipe_standard_manager import standard_manager, PipeSize, SizeTable, PipeStandard


def print_header(text: str) -> None:
    """打印标题"""
    print("\n" + "=" * 50)
    print(text.center(50))
    print("=" * 50)


def print_section(text: str) -> None:
    """打印分节标题"""
    print("\n" + "-" * 50)
    print(text)
    print("-" * 50)


def print_pipe_size(size: PipeSize) -> None:
    """打印管道尺寸信息"""
    print(f"  公称直径：DN{size.nom}")
    print(f"  外径：{size.od}mm")
    print(f"  描述：{size.description}")
    print(f"  零件号：{size.ptn}")


def print_size_table(table: SizeTable) -> None:
    """打印尺寸表信息"""
    print(f"尺寸表：{table.name}")
    print(f"版本：{table.version}")
    print(f"描述：{table.description}")
    print(f"单位：{table.unit}")
    print(f"创建时间：{table.created_time}")
    print(f"修改时间：{table.modified_time}")

    print_section("字段定义")
    for field in table.fields:
        print(f"  {field.name}({field.title}): {field.description}")
        print(f"    类型：{field.data_type}")
        print(f"    必填：{'是' if field.required else '否'}")

    print_section("尺寸数据")
    for size in table.sizes:
        print_pipe_size(size)
        print()


def print_standard(standard: PipeStandard) -> None:
    """打印标准信息"""
    print(f"标准名称：{standard.name}")
    print(f"版本：{standard.version}")
    print(f"描述：{standard.description}")
    print(f"创建时间：{standard.created_time}")
    print(f"修改时间：{standard.modified_time}")
    print(f"GUID：{standard.guid}")

    for table in standard.size_tables:
        print_section(f"尺寸表：{table.name}")
        print_size_table(table)


def list_standards() -> None:
    """列出所有可用的标准"""
    standards = standard_manager.get_available_standards()
    print_header("可用的管道标准")
    for name in standards:
        print(f"- {name}")


def view_standard(name: str) -> None:
    """查看指定标准的详细信息"""
    standard = standard_manager.get_standard(name)
    if not standard:
        print(f"错误：未找到标准 {name}")
        return

    print_header(f"标准详细信息：{name}")
    print_standard(standard)


def search_pipe_size(standard_name: str, table_name: str, nom: int) -> None:
    """搜索指定的管道尺寸信息"""
    size = standard_manager.get_pipe_size(standard_name, table_name, nom)
    if not size:
        print(f"错误：未找到 {standard_name}.{table_name} DN{nom} 的管道尺寸信息")
        return

    print_header(f"管道尺寸信息：{standard_name}.{table_name} DN{nom}")
    print_pipe_size(size)


def main() -> None:
    """主函数"""
    while True:
        print_header("管道标准数据管理系统")
        print("1. 列出所有标准")
        print("2. 查看标准详细信息")
        print("3. 搜索管道尺寸")
        print("0. 退出")

        choice = input("\n请选择操作 [0-3]: ")

        if choice == "0":
            break
        elif choice == "1":
            list_standards()
        elif choice == "2":
            name = input("请输入标准名称：")
            view_standard(name)
        elif choice == "3":
            standard_name = input("请输入标准名称：")
            table_name = input("请输入尺寸表名称：")
            try:
                nom = int(input("请输入公称直径(DN)："))
                search_pipe_size(standard_name, table_name, nom)
            except ValueError:
                print("错误：公称直径必须是整数")
        else:
            print("错误：无效的选择")

        input("\n按回车键继续...")


if __name__ == "__main__":
    main()
