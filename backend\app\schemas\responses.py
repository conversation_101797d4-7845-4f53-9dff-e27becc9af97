from typing import List, Dict, Any, Optional, Generic, TypeVar
from pydantic import BaseModel, Field
from datetime import datetime

T = TypeVar('T')

class ResponseBase(BaseModel):
    """API响应基类"""
    success: bool = True
    message: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.now)

class ErrorResponse(ResponseBase):
    """错误响应"""
    success: bool = False
    error_code: Optional[str] = None
    details: Optional[Any] = None
    
    class Config:
        schema_extra = {
            "example": {
                "success": False,
                "message": "操作失败",
                "error_code": "FILE_NOT_FOUND",
                "details": "找不到指定ID的文件",
                "timestamp": "2023-08-01T12:00:00"
            }
        }

class DataResponse(ResponseBase, Generic[T]):
    """带数据的响应"""
    data: Optional[T] = None
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "操作成功",
                "data": {
                    "id": 1,
                    "name": "example"
                },
                "timestamp": "2023-08-01T12:00:00"
            }
        }

class ListResponse(ResponseBase, Generic[T]):
    """列表响应"""
    items: List[T] = []
    total: int = 0
    page: Optional[int] = None
    page_size: Optional[int] = None
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "获取列表成功",
                "items": [
                    {"id": 1, "name": "example1"},
                    {"id": 2, "name": "example2"}
                ],
                "total": 2,
                "timestamp": "2023-08-01T12:00:00"
            }
        }

class FileUploadResponse(DataResponse):
    """文件上传响应"""
    data: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "文件上传成功",
                "data": {
                    "filename": "example.csv",
                    "size": 1024,
                    "file_id": 1,
                    "status": "PENDING"
                },
                "timestamp": "2023-08-01T12:00:00"
            }
        }

class FileResponse(BaseModel):
    """文件响应模型"""
    id: int
    filename: str
    file_path: str
    file_size: float
    file_hash: str
    mime_type: Optional[str] = None
    status: str
    error_message: Optional[str] = None
    upload_time: datetime
    
    class Config:
        orm_mode = True

class UploadResultItem(BaseModel):
    """单个文件上传结果"""
    filename: str
    size: float
    hash: str
    path: str
    status: str
    message: Optional[str] = None
    error: Optional[str] = None

class UploadResult(BaseModel):
    """文件上传响应"""
    status: str
    message: str
    files: List[UploadResultItem]

class TaskResponse(BaseModel):
    """任务响应模型"""
    task_id: str
    status: str
    file_id: int
    created_at: datetime
    updated_at: datetime
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

class BatchTaskResult(BaseModel):
    """批量任务响应"""
    status: str
    message: str
    count: int
    tasks: List[Dict[str, Any]]

class ErrorResponse(BaseModel):
    """错误响应"""
    status: str = "error"
    message: str 