import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq
from pathlib import Path
import logging
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from ..models import RawFile, raw_parquet_association, ParquetFile
# from ..models.parquet_file import ParquetFile
from typing import List, Tuple, Optional
import numpy as np

logger = logging.getLogger(__name__)

class TimeSeriesService:
    def __init__(self, processed_dir: Path):
        self.processed_dir = processed_dir
        # 设置每个Parquet文件的最大时间跨度（例如：1天）
        self.max_segment_duration = timedelta(days=1)
        
    async def process_timeseries_data(
        self, 
        file_record: RawFile, 
        db: Session
    ):
        """处理时间序列数据"""
        try:
            # 读取CSV文件
            df = pd.read_csv(
                file_record.file_path,
                parse_dates=['timestamp']  # 假设时间列名为'timestamp'
            )
            
            # 按时间排序
            df = df.sort_values('timestamp')
            
            # 检查数据重叠
            overlapping_segments = self._find_overlapping_segments(
                df['timestamp'].min(),
                df['timestamp'].max(),
                db
            )
            
            # 处理每个时间段
            segments = self._split_into_segments(df)
            
            for segment_df, start_time, end_time in segments:
                await self._process_segment(
                    segment_df,
                    start_time,
                    end_time,
                    file_record,
                    overlapping_segments,
                    db
                )
                
        except Exception as e:
            logger.error(f"时间序列处理失败: {str(e)}")
            raise
            
    def _find_overlapping_segments(
        self,
        start_time: datetime,
        end_time: datetime,
        db: Session
    ) -> List[raw_parquet_association]:
        """查找时间重叠的数据段"""
        return db.query(raw_parquet_association).filter(
            raw_parquet_association.c.segment_end >= start_time,
            raw_parquet_association.c.segment_start <= end_time
        ).all()
        
    def _split_into_segments(
        self,
        df: pd.DataFrame
    ) -> List[Tuple[pd.DataFrame, datetime, datetime]]:
        """将数据分割成适当大小的时间段"""
        segments = []
        current_date = df['timestamp'].min().date()
        end_date = df['timestamp'].max().date()
        
        while current_date <= end_date:
            next_date = current_date + timedelta(days=1)
            mask = (
                (df['timestamp'].dt.date >= current_date) &
                (df['timestamp'].dt.date < next_date)
            )
            segment_df = df[mask]
            
            if not segment_df.empty:
                segments.append((
                    segment_df,
                    segment_df['timestamp'].min(),
                    segment_df['timestamp'].max()
                ))
                
            current_date = next_date
            
        return segments
        
    async def _process_segment(
        self,
        df: pd.DataFrame,
        start_time: datetime,
        end_time: datetime,
        file_record: RawFile,
        overlapping_segments: List[raw_parquet_association],
        db: Session
    ):
        """处理单个时间段的数据"""
        try:
            # 查找或创建对应的Parquet文件
            parquet_file = self._get_or_create_parquet_file(
                start_time,
                end_time,
                db
            )
            
            # 如果有重叠的数据，合并处理
            if overlapping_segments:
                df = await self._merge_overlapping_data(
                    df,
                    overlapping_segments,
                    parquet_file,
                    db
                )
            
            # 保存数据到Parquet文件
            self._save_to_parquet(df, parquet_file.file_path)
            
            # 创建新的数据段记录
            segment = raw_parquet_association(
                raw_file_id=file_record.id,
                parquet_file_id=parquet_file.id,
                segment_start=start_time,
                segment_end=end_time,
                record_count=len(df),
                min_value=float(df['value'].min()),
                max_value=float(df['value'].max()),
                mean_value=float(df['value'].mean())
            )
            
            db.add(segment)
            db.commit()
            
        except Exception as e:
            logger.error(f"段处理失败: {str(e)}")
            db.rollback()
            raise
            
    def _get_or_create_parquet_file(
        self,
        start_time: datetime,
        end_time: datetime,
        db: Session
    ) -> ParquetFile:
        """获取或创建Parquet文件记录"""
        # 查找现有的Parquet文件
        parquet_file = db.query(ParquetFile).filter(
            ParquetFile.start_time <= start_time,
            ParquetFile.end_time >= end_time
        ).first()
        
        if not parquet_file:
            # 创建新的Parquet文件
            file_path = self.processed_dir / f"data_{start_time.date()}.parquet"
            parquet_file = ParquetFile(
                file_path=str(file_path),
                start_time=start_time.replace(
                    hour=0, minute=0, second=0, microsecond=0
                ),
                end_time=(start_time + timedelta(days=1)).replace(
                    hour=0, minute=0, second=0, microsecond=0
                )
            )
            db.add(parquet_file)
            db.commit()
            
        return parquet_file
        
    async def _merge_overlapping_data(
        self,
        new_df: pd.DataFrame,
        overlapping_segments: List[raw_parquet_association],
        parquet_file: ParquetFile,
        db: Session
    ) -> pd.DataFrame:
        """合并重叠的数据"""
        # 读取现有数据
        existing_df = pd.read_parquet(parquet_file.file_path)
        
        # 合并数据，保留最新的记录
        combined_df = pd.concat([existing_df, new_df])
        combined_df = combined_df.drop_duplicates(
            subset=['timestamp'],
            keep='last'
        )
        
        # 按时间排序
        combined_df = combined_df.sort_values('timestamp')
        
        return combined_df
        
    def _save_to_parquet(self, df: pd.DataFrame, file_path: str):
        """保存数据到Parquet文件"""
        table = pa.Table.from_pandas(df)
        pq.write_table(
            table,
            file_path,
            compression='snappy',
            use_dictionary=True,
            version='2.6'
        )

    async def delete_file_data(
        self,
        file_record: RawFile,
        db: Session
    ):
        """删除文件相关的数据"""
        try:
            # 获取文件关联的所有数据段
            segments = file_record.time_series_segments
            
            for segment in segments:
                parquet_file = segment.parquet_file
                
                # 从Parquet文件中删除该段数据
                await self._remove_segment_data(segment, parquet_file)
                
                # 删除数据段记录
                db.delete(segment)
            
            db.commit()
            
        except Exception as e:
            logger.error(f"删除数据失败: {str(e)}")
            db.rollback()
            raise
            
    async def _remove_segment_data(
        self,
        segment: raw_parquet_association,
        parquet_file: ParquetFile
    ):
        """从Parquet文件中删除指定段的数据"""
        try:
            # 读取Parquet文件
            df = pd.read_parquet(parquet_file.file_path)
            
            # 删除指定时间范围的数据
            mask = ~(
                (df['timestamp'] >= segment.segment_start) &
                (df['timestamp'] <= segment.segment_end)
            )
            df = df[mask]
            
            # 保存更新后的数据
            if len(df) > 0:
                self._save_to_parquet(df, parquet_file.file_path)
            else:
                # 如果文件为空，删除文件
                Path(parquet_file.file_path).unlink(missing_ok=True)
                
        except Exception as e:
            logger.error(f"删除段数据失败: {str(e)}")
            raise 

    def _create_segment(self, raw_file, parquet_file, start_time, end_time, record_count, db):
        """创建数据段关联"""
        stmt = raw_parquet_association.insert().values(
            raw_file_id=raw_file.id,
            parquet_file_id=parquet_file.id,
            segment_start=start_time,
            segment_end=end_time,
            record_count=record_count
        )
        db.execute(stmt)
        db.commit() 