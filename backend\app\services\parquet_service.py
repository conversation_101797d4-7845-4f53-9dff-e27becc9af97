# 标准库导入
import asyncio
import os
import time
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Optional, Any, Tuple

# 第三方库导入
import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq
from sqlalchemy.orm import Session

# 本地应用导入
from ..core import logger, settings
from ..models import RawFile, ParquetFile, ProcessStatus, raw_parquet_association
from .file_analyzer import FileAnalyzer

# logger = logging.getLogger(__name__)

class ParquetService:
    def __init__(self, processed_dir: Path):
        self.processed_dir = processed_dir
        self.executor = ThreadPoolExecutor(max_workers=2)  # 限制并发转换数量
        self.time_column = "timestamp"  # 默认时间列名称
        self.chunk_size = 100000  # 每个块的行数
        self.analyzer = FileAnalyzer()  # 创建FileAnalyzer实例
        
    async def convert_to_parquet(self, file_record: RawFile, db: Session):
        """将CSV文件转换为Parquet格式"""
        try:
            # 更新状态为转换中
            file_record.parquet_status = "converting"
            db.commit()
            
            # 在线程池中执行转换
            await asyncio.get_event_loop().run_in_executor(
                self.executor,
                self._convert_file,
                file_record,
                db
            )
            
        except Exception as e:
            logger.error(f"Parquet转换失败: {str(e)}")
            file_record.parquet_status = "error"
            file_record.conversion_error = str(e)
            db.commit()
    
    def _convert_file(self, file_record: RawFile, db: Session):
        """实际的文件转换过程（在线程池中执行）"""
        try:
            logger.info(f"开始处理文件: {file_record.filename}")

            # 先探测文件编码和分隔符
            file_path = Path(file_record.file_path)
            if not file_path.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")

            # 使用文件服务中的方法探测编码和分隔符
            encoding = file_record.encoding
            delimiter = file_record.delimiter

            # 如果数据库中没有编码和分隔符信息，重新探测
            if not encoding or not delimiter:
                logger.info(f"探测文件编码和分隔符: {file_path}")
                # 使用FileAnalyzer实例的方法探测
                encoding = asyncio.run(
                    self.analyzer.detect_encoding(file_path))
                delimiter = asyncio.run(
                    self.analyzer.detect_delimiter(file_path, encoding))

                # 更新文件记录
                file_record.encoding = encoding
                file_record.delimiter = delimiter
                db.commit()

            logger.info(f"文件编码: {encoding}, 分隔符: {delimiter}")

            # 高效处理大文件 - 分块读取以减少内存使用
            # 确定每个块的大小 - 10Hz的数据，每个块包含约10分钟数据
            chunk_size = 6000  # 10Hz × 60秒 × 10分钟 = 6000行

            # 读取文件的第一个块，检测时间列
            first_chunk = pd.read_csv(
                file_path,
                encoding=encoding,
                sep=delimiter,
                nrows=100  # 只读取前100行来检测列
            )

            # 检测时间列
            time_column = self._detect_time_column(first_chunk)
            if not time_column:
                logger.warning(f"未检测到时间列，使用整体处理")
                # 没有时间列，整体处理文件
                # 但仍然分块读取以避免内存溢出
                total_chunks = 0
                total_records = 0

                # 构建输出文件路径
                parquet_filename = f"{Path(file_record.filename).stem}.parquet"
                parquet_path = self.processed_dir / parquet_filename

                # 创建输出目录
                self.processed_dir.mkdir(parents=True, exist_ok=True)

                # 分块读取并转换
                for chunk in pd.read_csv(
                    file_path,
                    encoding=encoding,
                    sep=delimiter,
                    chunksize=chunk_size
                ):
                    if total_chunks == 0:
                        # 第一个块，创建文件
                        table = pa.Table.from_pandas(chunk)
                        pq.write_table(
                            table,
                            str(parquet_path),
                            compression='snappy',
                            use_dictionary=True,
                            version='2.6'
                        )
                    else:
                        # 后续块，追加到文件
                        table = pa.Table.from_pandas(chunk)
                        pq.write_table(
                            table,
                            str(parquet_path),
                            compression='snappy',
                            use_dictionary=True,
                            version='2.6',
                            append=True
                        )

                    total_chunks += 1
                    total_records += len(chunk)
                    logger.info(f"处理块 {total_chunks}, 记录 {len(chunk)}")

                # 创建Parquet文件记录
                parquet_file = ParquetFile(
                    raw_file_id=file_record.id,
                    file_path=str(parquet_path),
                    file_size=parquet_path.stat().st_size,
                    created_at=datetime.now(),
                    record_count=total_records
                )
                db.add(parquet_file)
                parquet_files = [parquet_file]

            else:
                # 有时间列，按日期分区处理
                logger.info(f"检测到时间列: {time_column}")

                # 准备分区处理
                parquet_files = []
                date_chunks = {}  # 按日期存储数据块
                total_records = 0

                # 分块读取并分类
                for chunk in pd.read_csv(
                    file_path,
                    encoding=encoding,
                    sep=delimiter,
                    chunksize=chunk_size
                ):
                    # 转换时间列为datetime
                    chunk[time_column] = pd.to_datetime(
                        chunk[time_column], errors='coerce')
                    # 添加日期列
                    chunk['date'] = chunk[time_column].dt.date

                    # 按日期分组数据
                    for date, group in chunk.groupby('date'):
                        if date not in date_chunks:
                            date_chunks[date] = []
                        date_chunks[date].append(group)

                    total_records += len(chunk)

                logger.info(
                    f"文件包含 {len(date_chunks)} 个日期, 总记录数: {total_records}")

                # 按日期处理并保存
                for date, chunks in date_chunks.items():
                    # 合并同一日期的所有块
                    df_date = pd.concat(chunks)
                    df_date.drop('date', axis=1, inplace=True)

                    # 按日期构建Parquet文件路径
                    date_str = str(date).replace('-', '')  # 如20230101
                    year_month = date_str[:6]  # 如202301

                    parquet_dir = self.processed_dir / year_month
                    parquet_dir.mkdir(parents=True, exist_ok=True)

                    parquet_filename = f"{Path(file_record.filename).stem}_{date_str}.parquet"
                    parquet_path = parquet_dir / parquet_filename

                    # 检查是否已有相同日期的数据
                    if parquet_path.exists():
                        # 读取现有数据
                        try:
                            existing_df = pd.read_parquet(str(parquet_path))
                            # 合并新旧数据（去除重复项）
                            merged_df = pd.concat([existing_df, df_date]).drop_duplicates(
                                subset=[time_column])
                            # 排序
                            merged_df = merged_df.sort_values(by=time_column)
                            table = pa.Table.from_pandas(merged_df)
                        except Exception as e:
                            logger.error(f"合并现有数据失败: {str(e)}, 使用新数据替换")
                            table = pa.Table.from_pandas(df_date)
                    else:
                        # 创建新文件
                        table = pa.Table.from_pandas(df_date)

                    # 写入Parquet文件
                    pq.write_table(
                        table,
                        str(parquet_path),
                        compression='snappy',
                        use_dictionary=True,
                        version='2.6'
                    )

                    # 创建或更新Parquet文件记录
                    existing_parquet = db.query(ParquetFile).filter(
                        ParquetFile.file_path == str(parquet_path)
                    ).first()

                    if existing_parquet:
                        # 更新现有记录
                        existing_parquet.raw_file_id = file_record.id
                        existing_parquet.file_size = parquet_path.stat().st_size
                        existing_parquet.updated_at = datetime.now()
                        existing_parquet.record_count = len(table)
                        parquet_files.append(existing_parquet)
                    else:
                        # 创建新记录
                        parquet_file = ParquetFile(
                            raw_file_id=file_record.id,
                            file_path=str(parquet_path),
                            file_size=parquet_path.stat().st_size,
                            created_at=datetime.now(),
                            record_count=len(table),
                            date=date,
                            start_time=df_date[time_column].min(),
                            end_time=df_date[time_column].max()
                        )
                        db.add(parquet_file)
                        parquet_files.append(parquet_file)

                    logger.info(f"已保存日期 {date} 的数据到: {parquet_path}")

            # 更新数据库记录
            file_record.parquet_status = "completed"
            file_record.conversion_time = datetime.now()
            file_record.total_records = total_records
            file_record.parquet_count = len(parquet_files)

            # 记录数据时间范围
            if time_column and parquet_files:
                start_times = [
                    pf.start_time for pf in parquet_files if pf.start_time is not None]
                end_times = [
                    pf.end_time for pf in parquet_files if pf.end_time is not None]

                if start_times:
                    file_record.data_start_time = min(start_times)
                if end_times:
                    file_record.data_end_time = max(end_times)

            # 更新原始文件的状态为完成
            file_record.status = ProcessStatus.COMPLETED
            file_record.processed_at = datetime.now()

            db.commit()
            
            logger.info(
                f"文件转换成功: {file_record.filename}, 生成了 {len(parquet_files)} 个Parquet文件")
            
        except Exception as e:
            logger.error(f"文件转换失败: {str(e)}")
            file_record.parquet_status = "error"
            file_record.conversion_error = str(e)
            file_record.status = ProcessStatus.ERROR  # 同时更新原始文件状态
            db.commit()
            raise

    def _detect_time_column(self, df):
        """检测时间列"""
        time_columns = []

        # 检查列名是否包含时间相关关键词
        time_keywords = ['time', 'date', 'timestamp', '时间', '日期', 'datetime']
        for col in df.columns:
            if any(keyword in col.lower() for keyword in time_keywords):
                time_columns.append(col)

        # 如果通过名称没有找到，尝试检测包含日期格式的列
        if not time_columns:
            for col in df.columns:
                try:
                    # 尝试将前几个值转换为日期时间
                    sample = df[col].head(5).astype(str)
                    success = pd.to_datetime(
                        sample, errors='coerce').notna().all()
                    if success:
                        time_columns.append(col)
                except:
                    pass

        # 返回第一个匹配的时间列
        return time_columns[0] if time_columns else None

    def _detect_instrument_columns(self, df):
        """检测仪表数据列"""
        if df.empty:
            return []

        # 排除时间列和ID列
        exclude_keywords = ['time', 'date', 'id', 'index', 'key', '索引', '编号']
        numeric_columns = []

        for col in df.columns:
            # 排除时间和ID列
            if any(keyword in col.lower() for keyword in exclude_keywords):
                continue

            # 检查是否为数值列
            if pd.api.types.is_numeric_dtype(df[col]):
                numeric_columns.append(col)

        return numeric_columns

    async def delete_rawfile_data(self, file_record: RawFile, db: Session):
        """
        软删除原始文件相关的数据，并处理关联的Parquet文件
        :param file_record: 原始文件记录
        :param db: 数据库会话
        :return: 影响的Parquet文件列表
        """
        try:
            # 获取该原始文件关联的所有Parquet文件
            parquet_files = []

            # 查询与该原始文件相关联的关系记录
            associations = db.query(raw_parquet_association).filter(
                raw_parquet_association.c.raw_file_id == file_record.id
            ).all()

            # 获取相关联的所有Parquet文件ID
            parquet_file_ids = [
                assoc.parquet_file_id for assoc in associations]

            # 处理每个关联的Parquet文件
            for parquet_file_id in parquet_file_ids:
                # 查询当前Parquet文件
                parquet_file = db.query(ParquetFile).filter(
                    ParquetFile.id == parquet_file_id
                ).first()

                if not parquet_file:
                    continue

                # 查询与该Parquet文件关联的所有原始文件
                related_raw_files = db.query(raw_parquet_association).filter(
                    raw_parquet_association.c.parquet_file_id == parquet_file_id
                ).all()

                # 获取除当前要删除的原始文件之外的其他关联文件
                other_raw_file_ids = [
                    rf.raw_file_id for rf in related_raw_files
                    if rf.raw_file_id != file_record.id
                ]

                # 情况1：如果该Parquet文件只与当前要删除的原始文件相关联
                if len(other_raw_file_ids) == 0:
                    # 将Parquet文件标记为软删除
                    logger.info(
                        f"Parquet文件 {parquet_file.filename} 仅与当前删除的文件关联，标记为软删除")
                    parquet_file.is_deleted = True
                    parquet_file.deleted_at = datetime.now()
                    parquet_file.deletion_reason = f"关联的原始文件 {file_record.filename} 被删除"
                    parquet_files.append(parquet_file)

                # 情况2：如果该Parquet文件还与其他原始文件相关联
                else:
                    logger.info(
                        f"Parquet文件 {parquet_file.filename} 还与其他{len(other_raw_file_ids)}个文件关联，需要重新生成")

                    # 移除当前文件与该Parquet文件的关联
                    db.query(raw_parquet_association).filter(
                        raw_parquet_association.c.raw_file_id == file_record.id,
                        raw_parquet_association.c.parquet_file_id == parquet_file_id
                    ).delete()

                    # 放入重新处理队列 - 这里可以根据需要实现异步重新生成逻辑
                    # TODO: 实现异步重新生成Parquet文件的逻辑
                    # 例如: await self.regenerate_parquet_file(parquet_file, other_raw_file_ids, db)

                    # 记录该Parquet文件需要重新生成
                    parquet_file.status = ProcessStatus.PENDING
                    parquet_file.updated_at = datetime.now()
                    parquet_files.append(parquet_file)

            # 更新原始文件状态为软删除
            file_record.is_deleted = True
            file_record.deleted_at = datetime.now()
            file_record.parquet_count = len(parquet_files)

            # 提交变更
            db.commit()

            # 记录删除信息
            logger.info(
                f"已软删除文件: {file_record.filename}, 影响 {len(parquet_files)} 个Parquet文件")

            return parquet_files

        except Exception as e:
            logger.error(f"软删除文件数据时出错: {str(e)}")
            db.rollback()
            raise

    async def regenerate_parquet_file(self, parquet_file: ParquetFile, raw_file_ids: List[int], db: Session):
        """
        重新生成不包含已删除文件数据的Parquet文件
        :param parquet_file: 需要重新生成的Parquet文件
        :param raw_file_ids: 相关联的原始文件ID列表
        :param db: 数据库会话
        """
        try:
            logger.info(f"开始重新生成Parquet文件: {parquet_file.filename}")

            # 1. 读取原有的Parquet文件
            original_df = pd.read_parquet(parquet_file.file_path)

            # 2. 过滤数据 - 排除已删除文件的数据
            # 这里假设Parquet文件中有source_file_id列标识数据来源
            # 如果实际情况不同，需要根据具体数据结构调整过滤逻辑
            if 'source_file_id' in original_df.columns:
                filtered_df = original_df[original_df['source_file_id'].isin(
                    raw_file_ids)]
            else:
                # 如果没有明确的标识列，可能需要其他方式重建数据
                # 这里仅作为示例，实际实现可能会有所不同
                logger.warning(f"无法在Parquet文件中找到source_file_id列，尝试从原始文件重建数据")
                # 从关联的raw_file重建数据的逻辑
                # ...
                filtered_df = original_df  # 暂时使用原数据，实际中应替换为重建的数据

            # 3. 如果过滤后没有数据，则标记该Parquet文件为已删除
            if filtered_df.empty:
                logger.warning(
                    f"过滤后的Parquet文件 {parquet_file.filename} 没有数据，标记为已删除")
                parquet_file.is_deleted = True
                parquet_file.deleted_at = datetime.now()
                parquet_file.deletion_reason = "过滤已删除文件数据后无剩余数据"
                db.commit()
                return

            # 4. 保存新的Parquet文件
            # 生成新的文件名，避免覆盖原文件
            new_file_path = parquet_file.file_path.replace(
                '.parquet', f'_regenerated_{int(time.time())}.parquet')
            filtered_df.to_parquet(new_file_path, index=False)

            # 5. 更新Parquet文件记录
            parquet_file.file_path = new_file_path
            parquet_file.file_size = os.path.getsize(new_file_path)
            parquet_file.record_count = len(filtered_df)
            parquet_file.status = ProcessStatus.COMPLETED
            parquet_file.updated_at = datetime.now()

            # 如果有时间列，更新时间范围
            time_column = self._find_time_column(filtered_df)
            if time_column:
                parquet_file.time_range_start = filtered_df[time_column].min()
                parquet_file.time_range_end = filtered_df[time_column].max()

            db.commit()
            logger.info(
                f"成功重新生成Parquet文件: {parquet_file.filename}, 记录数: {parquet_file.record_count}")

        except Exception as e:
            logger.error(f"重新生成Parquet文件时出错: {str(e)}")
            db.rollback()
            raise

    async def get_parquet_files_for_rawfile(self, raw_file_id: int, db: Session):
        """获取原始文件关联的所有Parquet文件"""
        return db.query(ParquetFile).filter(
            ParquetFile.raw_file_id == raw_file_id
        ).all()

    def _update_file_metadata(self, file_record: RawFile, parquet_files: List[ParquetFile], db: Session):
        """更新原始文件的元数据"""
        # 更新时间范围、记录数等信息
        if parquet_files:
            # 找出最早和最晚的时间范围
            start_times = [
                pf.time_range_start for pf in parquet_files if pf.time_range_start is not None]
            end_times = [
                pf.time_range_end for pf in parquet_files if pf.time_range_end is not None]

            if start_times:
                file_record.time_range_start = min(start_times)
            if end_times:
                file_record.time_range_end = max(end_times)

            # 更新总记录数
            file_record.total_records = sum(
                pf.record_count for pf in parquet_files if pf.record_count is not None)

            # 更新Parquet文件计数
            file_record.parquet_count = len(parquet_files)

            # 设置处理完成
            file_record.status = ProcessStatus.COMPLETED
            file_record.error_message = None
            file_record.last_processed_at = datetime.now()

            db.commit()
