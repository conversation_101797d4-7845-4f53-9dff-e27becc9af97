from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Request, Path, Query, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Optional
import logging
import os
from pathlib import Path as PathLib

from app.database import get_db
from app.core.dependencies import get_file_service
from app.services.file_service import FileService
from app.models import RawFile, ParquetFile, ProcessStatus

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/upload")
async def upload_files(
    files: List[UploadFile] = File(...),
    background_tasks: BackgroundTasks = None,
    request: Request = None,
    db: Session = Depends(get_db),
    file_service: FileService = Depends(get_file_service)
):
    """处理文件上传"""
    try:
        results = []
        for file in files:
            try:
                # 检查文件是否为空
                if not file or not file.filename:
                    continue
                
                # 保存文件并记录到数据库
                file_record, status = await file_service.save_upload_file(file, db)
                
                if not file_record:
                    continue
                
                # 构建响应信息
                result = {
                    "filename": file_record.filename,
                    "size": file_record.file_size,
                    "hash": file_record.file_hash,
                    "path": file_record.file_path,
                    "status": file_record.status
                }

                # 附加其他信息
                if status == "duplicate":
                    result["message"] = "文件已存在"
                elif status == "success":
                    result["message"] = "上传成功"
                    # 如果文件状态为排队中，将其转换任务添加到后台任务
                    if file_record.status == ProcessStatus.PENDING:
                        try:
                            logger.info(
                                f"将文件添加到后台处理队列: {file_record.filename}")

                            # 添加到后台任务
                            if background_tasks:
                                background_tasks.add_task(
                                    process_file_in_background,
                                    file_id=file_record.id,
                                    db=db,
                                    file_service=file_service
                                )
                                result["message"] = "上传成功，已加入处理队列"

                        except Exception as e:
                            logger.error(f"添加后台任务时出错: {str(e)}")
                            result["message"] = f"上传成功但处理安排失败: {str(e)}"
                elif status == "error":
                    result["message"] = "上传失败"
                    if hasattr(file_record, 'error_message') and file_record.error_message:
                        result["error"] = file_record.error_message
                
                results.append(result)
                
            except Exception as e:
                logger.error(f"处理文件 {file.filename if file else 'unknown'} 时出错: {str(e)}")
                results.append({
                    "filename": file.filename if file else "未知文件",
                    "status": "error",
                    "message": "处理失败",
                    "error": str(e)
                })
        
        return {
            "status": "success",
            "message": f"处理完成 {len(results)} 个文件",
            "files": results
        }
        
    except Exception as e:
        logger.error(f"上传失败: {str(e)}")
        return {
            "status": "error",
            "message": str(e)
        }


async def process_file_in_background(file_id: int, db: Session, file_service: FileService):
    """后台处理文件的函数"""
    try:
        # 获取文件记录
        file_record = await file_service.get_file_by_id(file_id, db)
        if not file_record:
            logger.error(f"找不到文件记录，ID: {file_id}")
            return

        logger.info(f"开始后台处理文件: {file_record.filename}")

        # 更新状态为处理中
        file_record.status = ProcessStatus.PROCESSING
        db.commit()

        # 转换为Parquet
        await file_service.parquet_service.convert_to_parquet(file_record, db)

        logger.info(f"文件处理完成: {file_record.filename}")

    except Exception as e:
        logger.error(f"后台处理文件时出错: {str(e)}")
        try:
            # 尝试更新文件状态为错误
            file_record = await file_service.get_file_by_id(file_id, db)
            if file_record:
                file_record.status = ProcessStatus.ERROR
                file_record.error_message = str(e)
                db.commit()
        except Exception as inner_e:
            logger.error(f"更新文件状态时出错: {str(inner_e)}")


@router.get("/status/{file_id}")
async def get_file_status(
    file_id: int = Path(..., title="文件ID"),
    db: Session = Depends(get_db),
    file_service: FileService = Depends(get_file_service)
):
    """获取文件处理状态"""
    try:
        # 获取文件记录
        file_record = await file_service.get_file_by_id(file_id, db)
        if not file_record:
            raise HTTPException(status_code=404, detail="文件不存在")

        # 获取关联的Parquet文件
        parquet_files = await file_service.parquet_service.get_parquet_files_for_rawfile(file_id, db)

        # 构建状态响应
        status_info = {
            "id": file_record.id,
            "filename": file_record.filename,
            "status": file_record.status,
            "parquet_status": getattr(file_record, "parquet_status", None),
            "upload_time": file_record.upload_time.isoformat() if hasattr(file_record, "upload_time") and file_record.upload_time else None,
            "processed_at": file_record.processed_at.isoformat() if hasattr(file_record, "processed_at") and file_record.processed_at else None,
            "conversion_time": file_record.conversion_time.isoformat() if hasattr(file_record, "conversion_time") and file_record.conversion_time else None,
            "total_records": getattr(file_record, "total_records", 0),
            "error_message": getattr(file_record, "error_message", None),
            "parquet_files": [
                {
                    "id": p.id,
                    "path": p.file_path,
                    "size": p.file_size,
                    "record_count": getattr(p, "record_count", None),
                    "date": str(p.date) if hasattr(p, "date") and p.date else None,
                    "created_at": p.created_at.isoformat() if hasattr(p, "created_at") and p.created_at else None
                }
                for p in parquet_files
            ]
        }

        return {
            "status": "success",
            "file_status": status_info
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文件状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取文件状态失败: {str(e)}")


@router.get("/processing")
async def get_processing_files(
    db: Session = Depends(get_db),
    file_service: FileService = Depends(get_file_service)
):
    """获取所有正在处理中的文件"""
    try:
        # 获取处理中的文件
        processing_files = await file_service.get_files_by_status(ProcessStatus.PROCESSING, db)

        return {
            "status": "success",
            "count": len(processing_files),
            "files": [
                {
                    "id": f.id,
                    "filename": f.filename,
                    "upload_time": f.upload_time.isoformat() if hasattr(f, "upload_time") and f.upload_time else None,
                    "parquet_status": getattr(f, "parquet_status", None)
                }
                for f in processing_files
            ]
        }

    except Exception as e:
        logger.error(f"获取处理中文件列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取处理中文件列表失败: {str(e)}")

@router.get("/files")
async def list_files(
    status: Optional[str] = None,
    limit: int = Query(100, ge=1, le=1000),
    offset: int = Query(0, ge=0),
    db: Session = Depends(get_db),
    file_service: FileService = Depends(get_file_service)
):
    """列出所有文件，支持状态过滤和分页"""
    try:
        raw_files, total = await file_service.list_files(db, status=status, limit=limit, offset=offset)
        return {
            "status": "success",
            "total": total,
            "count": len(raw_files),
            "offset": offset,
            "limit": limit,
            "files": [file.to_dict() for file in raw_files]
        }
    except Exception as e:
        logger.error(f"获取文件列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{file_id}")
async def get_file_details(
    file_id: int = Path(..., title="文件ID"),
    db: Session = Depends(get_db),
    file_service: FileService = Depends(get_file_service)
):
    """获取文件详情"""
    file = await file_service.get_file_by_id(file_id, db)
    if not file:
        raise HTTPException(status_code=404, detail="文件不存在")
    return {"status": "success", "file": file.to_dict()}

@router.delete("/{file_id}")
async def delete_file(
    file_id: int = Path(..., title="文件ID"),
    reason: Optional[str] = None,
    user: Optional[str] = None,
    db: Session = Depends(get_db),
    file_service: FileService = Depends(get_file_service)
):
    """软删除文件"""
    try:
        result = await file_service.soft_delete_file(file_id, reason=reason, deleted_by=user, db=db)
        if result:
            return {"status": "success", "message": "文件已软删除"}
        else:
            raise HTTPException(status_code=404, detail="文件不存在")
    except Exception as e:
        logger.error(f"软删除文件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"软删除文件失败: {str(e)}")


@router.get("/deleted")
async def list_deleted_files(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页条数"),
    db: Session = Depends(get_db),
    file_service: FileService = Depends(get_file_service)
):
    """获取已删除文件列表"""
    try:
        offset = (page - 1) * page_size

        # 使用file_service获取已删除文件列表
        files, total = await file_service.list_deleted_files(db, limit=page_size, offset=offset)

        return {
            "status": "success",
            "total": total,
            "page": page,
            "page_size": page_size,
            "data": [file.to_dict() for file in files]
        }
    except Exception as e:
        logger.error(f"获取已删除文件列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取已删除文件列表失败: {str(e)}")

@router.get("/download/{file_id}")
async def download_file(
    file_id: int = Path(..., title="文件ID"),
    db: Session = Depends(get_db),
    file_service: FileService = Depends(get_file_service)
):
    """下载文件"""
    try:
        file_path, filename = await file_service.get_file_download_info(file_id, db)
        if not file_path:
            raise HTTPException(status_code=404, detail="文件不存在")
            
        return {"status": "success", "download_url": f"/api/v1/files/stream/{file_id}", "filename": filename}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取下载信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取下载信息失败: {str(e)}")

@router.get("/stream/{file_id}")
async def stream_file(
    file_id: int = Path(..., title="文件ID"),
    db: Session = Depends(get_db),
    file_service: FileService = Depends(get_file_service)
):
    """流式传输文件内容"""
    from fastapi.responses import StreamingResponse
    import io
    
    try:
        file_path, filename = await file_service.get_file_download_info(file_id, db)
        if not file_path or not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="文件不存在")

        def file_iterator():
            with open(file_path, "rb") as file:
                while chunk := file.read(8192):
                    yield chunk

        # 创建响应
        response = StreamingResponse(
            file_iterator(),
            media_type="application/octet-stream"
        )
        
        # 设置响应头，指定文件名
        response.headers["Content-Disposition"] = f'attachment; filename="{filename}"'
        
        return response
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"流式传输文件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"流式传输文件失败: {str(e)}") 