{"name": "swagger-ui-webpack-getting-started", "version": "0.0.1", "description": "A simple setup of Swagger UI with Webpack", "scripts": {"build": "webpack", "start": "webpack-dev-server --open"}, "author": "<PERSON>", "license": "Apache-2.0", "devDependencies": {"clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^11.0.0", "html-webpack-plugin": "^5.5.0", "webpack": "^5.74.0", "webpack-cli": "^4.10.0", "webpack-dev-server": "^4.11.0"}, "dependencies": {"css-loader": "^6.7.1", "json-loader": "^0.5.7", "style-loader": "^3.3.1", "swagger-ui": "^4.14.0", "yaml-loader": "^0.8.0"}}