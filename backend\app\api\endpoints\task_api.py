from fastapi import APIRouter, Depends, HTTPException, Path, Query
from sqlalchemy.orm import Session
import logging
from typing import List, Optional

from app.database import get_db
from app.core.dependencies import get_task_service
from app.services.task_service import TaskService

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/process/{file_id}")
async def process_file(
    file_id: int = Path(..., title="文件ID"),
    db: Session = Depends(get_db),
    task_service: TaskService = Depends(get_task_service)
):
    """处理指定文件"""
    try:
        result = await task_service.process_file(file_id, db)
        return {
            "status": "success",
            "message": "文件处理已启动",
            "task_id": result.get("task_id")
        }
    except Exception as e:
        logger.error(f"处理文件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/status/{task_id}")
async def get_task_status(
    task_id: str = Path(..., title="任务ID"),
    db: Session = Depends(get_db),
    task_service: TaskService = Depends(get_task_service)
):
    """获取任务状态"""
    try:
        status = await task_service.get_task_status(task_id, db)
        return {
            "status": "success",
            "task_id": task_id,
            "task_status": status
        }
    except Exception as e:
        logger.error(f"获取任务状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/batch")
async def batch_process(
    status: Optional[str] = "pending",
    limit: int = Query(10, ge=1, le=100),
    db: Session = Depends(get_db),
    task_service: TaskService = Depends(get_task_service)
):
    """批量处理文件"""
    try:
        result = await task_service.batch_process(db, status=status, limit=limit)
        return {
            "status": "success",
            "message": f"已启动 {result['count']} 个处理任务",
            "tasks": result["tasks"]
        }
    except Exception as e:
        logger.error(f"批量处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e)) 