from fastapi import HTT<PERSON>Exception, status, FastAPI, Request
from typing import Any, Dict, Optional
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from sqlalchemy.exc import SQLAlchemyError
import logging

logger = logging.getLogger(__name__)

class AppException(Exception):
    """应用基础异常类"""
    def __init__(
        self,
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail: str = "An unexpected error occurred",
        headers: Optional[Dict[str, Any]] = None
    ):
        self.status_code = status_code
        self.detail = detail
        self.headers = headers
        super().__init__(detail)
    
    def to_http_exception(self) -> HTTPException:
        """转换为FastAPI HTTPException"""
        return HTTPException(
            status_code=self.status_code,
            detail=self.detail,
            headers=self.headers
        )

class FileNotFoundError(AppException):
    """文件未找到异常"""
    def __init__(self, file_id: Optional[int] = None, filename: Optional[str] = None):
        detail = "File not found"
        if file_id:
            detail = f"File with ID {file_id} not found"
        elif filename:
            detail = f"File '{filename}' not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class FileProcessingError(AppException):
    """文件处理异常"""
    def __init__(self, message: str, file_id: Optional[int] = None):
        detail = f"Error processing file: {message}"
        if file_id:
            detail = f"Error processing file with ID {file_id}: {message}"
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class FileUploadError(AppException):
    """文件上传异常"""
    def __init__(self, message: str, filename: Optional[str] = None):
        detail = f"Error uploading file: {message}"
        if filename:
            detail = f"Error uploading file '{filename}': {message}"
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class InvalidFormatError(AppException):
    """文件格式无效异常"""
    def __init__(self, message: str, filename: Optional[str] = None):
        detail = f"Invalid file format: {message}"
        if filename:
            detail = f"Invalid format for file '{filename}': {message}"
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class DatabaseError(AppException):
    """数据库异常"""
    def __init__(self, message: str):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Database error: {message}"
        )

class ServiceError(Exception):
    """服务级别错误基类"""
    def __init__(self, message: str, status_code: int = 500, details: dict = None):
        self.message = message
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)

class ResourceNotFoundError(ServiceError):
    """资源未找到错误"""
    def __init__(self, resource_type: str, resource_id: str):
        super().__init__(
            f"{resource_type} with id {resource_id} not found",
            status_code=404,
            details={"resource_type": resource_type, "resource_id": resource_id}
        )

def setup_exception_handlers(app: FastAPI):
    """设置全局异常处理器"""
    
    @app.exception_handler(ServiceError)
    async def service_exception_handler(request: Request, exc: ServiceError):
        """处理服务级别异常"""
        logger.error(f"服务错误: {exc.message}")
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "status": "error",
                "message": exc.message,
                "details": exc.details
            }
        )
    
    @app.exception_handler(RequestValidationError)
    async def validation_exception_handler(request: Request, exc: RequestValidationError):
        """处理请求验证错误"""
        logger.error(f"请求验证错误: {str(exc)}")
        return JSONResponse(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            content={
                "status": "error",
                "message": "请求参数验证失败",
                "details": exc.errors()
            }
        )
    
    @app.exception_handler(SQLAlchemyError)
    async def sqlalchemy_exception_handler(request: Request, exc: SQLAlchemyError):
        """处理数据库错误"""
        logger.error(f"数据库错误: {str(exc)}")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "status": "error",
                "message": "数据库操作失败",
                "details": str(exc)
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """处理未捕获的异常"""
        logger.error(f"未捕获的异常: {str(exc)}", exc_info=True)
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "status": "error",
                "message": "内部服务器错误",
                "details": str(exc) if app.debug else "请联系管理员"
            }
        ) 