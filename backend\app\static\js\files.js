class FileManager {
    constructor() {
        this.files = [];
        this.initEventListeners();
        this.loadFiles();
    }

    initEventListeners() {
        // 搜索和过滤
        document.getElementById('searchInput').addEventListener('input', () => this.filterFiles());
        document.getElementById('statusFilter').addEventListener('change', () => this.filterFiles());

        // 模态框关闭
        document.querySelector('.close').addEventListener('click', () => {
            document.getElementById('fileModal').style.display = 'none';
        });

        // 点击模态框外部关闭
        window.addEventListener('click', (e) => {
            const modal = document.getElementById('fileModal');
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });
    }

    async loadFiles() {
        try {
            const response = await fetch('/api/v1/files');
            const data = await response.json();
            
            if (data.status === 'success') {
                this.files = data.files;
                this.renderFiles(this.files);
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('加载文件列表失败:', error);
            alert('加载文件列表失败');
        }
    }

    renderFiles(files) {
        const tbody = document.getElementById('filesList');
        tbody.innerHTML = '';

        files.forEach(file => {
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td>${file.id}</td>
                <td>${file.filename}</td>
                <td>${this.formatFileSize(file.size)}</td>
                <td>${this.formatHash(file.hash)}</td>
                <td>${this.formatDate(file.upload_time)}</td>
                <td><span class="status-badge status-${file.status}">${file.status}</span></td>
                <td>
                    <button class="btn btn-primary" onclick="fileManager.showDetails(${file.id})">
                        详情
                    </button>
                </td>
            `;
            tbody.appendChild(tr);
        });
    }

    filterFiles() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const statusFilter = document.getElementById('statusFilter').value;

        const filteredFiles = this.files.filter(file => {
            const matchesSearch = file.filename.toLowerCase().includes(searchTerm);
            const matchesStatus = !statusFilter || file.status === statusFilter;
            return matchesSearch && matchesStatus;
        });

        this.renderFiles(filteredFiles);
    }

    showDetails(fileId) {
        const file = this.files.find(f => f.id === fileId);
        if (!file) return;

        const detailsHtml = `
            <div class="file-details">
                <div class="detail-item">
                    <span class="detail-label">文件名:</span>
                    <span>${file.filename}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">大小:</span>
                    <span>${this.formatFileSize(file.size)}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">哈希值:</span>
                    <span>${file.hash}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">上传时间:</span>
                    <span>${this.formatDate(file.upload_time)}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">状态:</span>
                    <span class="status-badge status-${file.status}">${file.status}</span>
                </div>
                ${file.error_message ? `
                    <div class="detail-item">
                        <span class="detail-label">错误信息:</span>
                        <span class="error-message">${file.error_message}</span>
                    </div>
                ` : ''}
            </div>
        `;

        document.getElementById('fileDetails').innerHTML = detailsHtml;
        document.getElementById('fileModal').style.display = 'block';
    }

    formatFileSize(bytes) {
        const units = ['B', 'KB', 'MB', 'GB'];
        let size = bytes;
        let unitIndex = 0;
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        return `${size.toFixed(2)} ${units[unitIndex]}`;
    }

    formatHash(hash) {
        return hash.substring(0, 8) + '...' + hash.substring(hash.length - 8);
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString();
    }
}

// 初始化文件管理器
const fileManager = new FileManager(); 