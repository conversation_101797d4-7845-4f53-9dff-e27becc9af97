from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
import logging
import time
import os
import psutil
from app.database import get_db

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "version": "0.1.0"
    }

@router.get("/db")
async def db_health(db: Session = Depends(get_db)):
    """数据库连接检查"""
    try:
        # 执行简单查询
        db.execute("SELECT 1").fetchone()
        return {
            "status": "healthy",
            "database": "connected"
        }
    except Exception as e:
        logger.error(f"数据库连接失败: {str(e)}")
        return {
            "status": "unhealthy",
            "database": "disconnected",
            "error": str(e)
        }

@router.get("/system")
async def system_health():
    """系统健康状态"""
    try:
        process = psutil.Process(os.getpid())
        return {
            "status": "healthy",
            "memory_usage": process.memory_info().rss / 1024 / 1024,  # MB
            "cpu_percent": process.cpu_percent(interval=1.0),
            "uptime": time.time() - process.create_time()
        }
    except Exception as e:
        logger.error(f"获取系统健康状态失败: {str(e)}")
        return {
            "status": "error",
            "error": str(e)
        } 