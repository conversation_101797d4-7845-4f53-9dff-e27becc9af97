from fastapi import APIRouter, HTTPException
from typing import List
from ...services.combustion import CombustionSystem, Fuel

router = APIRouter()
combustion_system = CombustionSystem()


@router.post("/calculate")
async def calculate_combustion(fuel_type: str, temperature: float, pressure: float):
    """
    执行燃烧计算
    """
    try:
        # 创建燃料对象
        fuel = Fuel(
            name=fuel_type,
            formula=fuel_type,
            LHV=None  # 可以根据需要添加热值
        )

        # 执行计算
        result = combustion_system.combustion_analysis(
            fuel, temperature, pressure)

        return result
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/compare")
async def compare_fuels():
    """
    执行燃料对比分析
    """
    try:
        # 创建测试燃料列表
        fuels = [
            Fuel(name="甲烷", formula="CH4"),
            Fuel(name="航空煤油", formula="C12H23"),
            Fuel(name="空气", formula="Air")
        ]

        # 执行对比分析
        results = compare_cooling(fuels)

        return results
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
