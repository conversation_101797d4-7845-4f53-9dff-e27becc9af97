import chardet
import pandas as pd
import aiofiles

from pathlib import Path
from typing import Dict, Any, List, Optional
import json
import os
from ..core import logger

class FileAnalyzer:
    """文件分析器，处理文件格式检测等任务"""
    
    @staticmethod
    async def detect_encoding(file_path: Path, chunk_size: int = 1024) -> str:
        """
        分块读取文件并检测编码
        :param file_path: 文件路径
        :param chunk_size: 每次读取的字节数
        :return: 检测到的编码
        """
        try:
            file_size = os.path.getsize(file_path)
            max_chunks = min(10, max(1, file_size // chunk_size))
            
            async with aiofiles.open(file_path, 'rb') as f:
                raw_data = await f.read(chunk_size)
                result = chardet.detect(raw_data)
                chunks_read = 1
                
                while result['confidence'] < 0.9 and chunks_read < max_chunks:
                    chunk = await f.read(chunk_size)
                    if not chunk:
                        break
                    raw_data += chunk
                    result = chardet.detect(raw_data)
                    chunks_read += 1
                
            logger.info(f"文件编码检测结果: {result}")
            
            if not result['encoding']:
                logger.warning(f"无法检测到文件编码，默认使用UTF-8")
                return 'utf-8'
                
            return result['encoding']
        except Exception as e:
            logger.error(f"检测文件编码时出错: {str(e)}")
            return 'utf-8'  # 默认返回UTF-8
    
    @staticmethod
    async def detect_delimiter(file_path: Path, encoding: str, 
                              possible_delimiters: List[str] = [',', ';', '\t', '|']) -> Optional[str]:
        """
        检测CSV文件的分隔符
        :param file_path: CSV文件路径
        :param encoding: 文件编码
        :param possible_delimiters: 可能的分隔符列表
        :return: 检测到的分隔符
        """
        try:
            for delimiter in possible_delimiters:
                try:
                    # 尝试读取文件前100行
                    # 注意：pandas 不支持直接使用 aiofiles，所以这里仍使用同步方式
                    # 但我们已经在其他文件读取操作中使用了异步
                    df = pd.read_csv(
                        file_path, 
                        encoding=encoding, 
                        sep=delimiter, 
                        nrows=100, 
                        engine='python', 
                        error_bad_lines=False,
                        warn_bad_lines=True
                    )
                    
                    # 检查读取的列数是否合理（大于1列）
                    if len(df.columns) > 1:
                        logger.info(f"检测到分隔符: '{delimiter}'")
                        return delimiter
                except Exception as e:
                    logger.debug(f"尝试分隔符 '{delimiter}' 失败: {str(e)}")
                    continue
            
            logger.warning(f"无法检测到有效的分隔符")
            return None
        except Exception as e:
            logger.error(f"检测分隔符时出错: {str(e)}")
            return None
    
    @classmethod
    async def analyze_file(cls, file_path: Path) -> Dict[str, Any]:
        """
        分析文件格式，返回文件的元数据信息
        :param file_path: 文件路径
        :return: 文件格式信息字典
        """
        try:
            result = {
                "file_path": str(file_path),
                "file_size": os.path.getsize(file_path),
                "is_binary": False,
                "encoding": None,
                "delimiter": None,
                "columns": None,
                "row_count": None,
                "preview_rows": None,
                "error": None
            }
            
            # 检查文件是否为二进制文件
            try:
                async with aiofiles.open(file_path, 'rb') as f:
                    sample = await f.read(min(1024, result["file_size"]))
                    # 检查是否有空字节或非ASCII字符比例很高
                    if b'\x00' in sample or sum(1 for b in sample if b > 127) / len(sample) > 0.3:
                        result["is_binary"] = True
                        logger.info(f"文件被检测为二进制格式")
                        return result
            except Exception as e:
                logger.error(f"检查二进制格式时出错: {str(e)}")
                
            # 检测编码
            result["encoding"] = await cls.detect_encoding(file_path)
            
            # 如果文件名以.csv结尾，尝试检测分隔符
            if str(file_path).lower().endswith('.csv'):
                result["delimiter"] = await cls.detect_delimiter(file_path, result["encoding"])
                
                # 如果成功检测到分隔符，读取文件信息
                if result["delimiter"]:
                    try:
                        df = pd.read_csv(
                            file_path, 
                            encoding=result["encoding"], 
                            sep=result["delimiter"], 
                            nrows=10
                        )
                        
                        # 获取列名
                        result["columns"] = df.columns.tolist()
                        
                        # 异步计算总行数
                        line_count = 0
                        async with aiofiles.open(file_path, 'r', encoding=result["encoding"]) as f:
                            async for _ in f:
                                line_count += 1
                        result["row_count"] = line_count - 1  # 减去表头行
                        
                        # 获取前几行数据作为预览
                        result["preview_rows"] = df.head(5).to_dict(orient='records')
                        
                    except Exception as e:
                        logger.error(f"读取CSV文件信息时出错: {str(e)}")
                        result["error"] = f"读取文件信息失败: {str(e)}"
            
            return result
            
        except Exception as e:
            logger.error(f"分析文件格式时出错: {str(e)}")
            return {
                "file_path": str(file_path),
                "error": f"分析失败: {str(e)}"
            } 