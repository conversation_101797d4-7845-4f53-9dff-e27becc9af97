import math
import numpy as np
from scipy.optimize import fsolve
import pandas as pd
from datetime import datetime
from .data_models import PipeData, InsulationData, InsulationLayer_material
# from .insulation_materials import SILICA_ALUMINA_1
import re


class InsulatedPipe_GB50264:
    """根据GB 50264-2013的保温管道计算类（实现公式5.3.11）"""

    # 默认参数
    DEFAULT_SURFACE_TEMP = 50  # 默认表面温度(℃)
    DEFAULT_AMBIENT_TEMP = 25  # 默认环境温度(℃)
    DEFAULT_SURFACE_COEFF = 11.6  # 默认表面传热系数(W/m²·℃)
    DEFAULT_LENGTH = 1.0  # 默认管道长度(m)
    # 默认报告文件名，包含当前日期
    DEFAULT_REPORT_FILENAME = f"管道保温计算报告_{datetime.now().strftime('%Y%m%d')}.xlsx"

    STANDARD_THICKNESS = [30, 40, 50, 60,
                          70, 80, 90, 100, 120, 140, 160, 180, 200]

    @staticmethod
    def parse_thermal_conductivity_eq(equation: str) -> tuple:
        """解析导热系数方程
        
        Args:
            equation: 导热系数方程字符串，格式如：
                "λ=0.044+0.0002(t-70) (70≤t≤400)\nλ=0.108+0.00036(t-400) (t>400)"
                
        Returns:
            tuple: (lambda_0, a, b, t1, t2, lambda_1, c, t3)
                lambda_0: 基准导热系数
                a: 第一段斜率
                b: 第一段截距
                t1: 第一段起始温度
                t2: 第一段结束温度
                lambda_1: 第二段基准导热系数
                c: 第二段斜率
                t3: 第二段起始温度
        """
        # 使用正则表达式提取数值
        pattern = r"λ=([\d.]+)\+([\d.]+)\(t-(\d+)\) \((\d+)≤t≤(\d+)\)\nλ=([\d.]+)\+([\d.]+)\(t-(\d+)\)"
        match = re.match(pattern, equation)

        if not match:
            raise ValueError("导热系数方程格式错误")

        return tuple(float(x) for x in match.groups())

    @staticmethod
    def calculate_thermal_conductivity(material: InsulationLayer_material, Tm: float) -> float:
        """根据材料属性计算导热系数
        
        Args:
            material: 保温材料对象
            Tm: 平均温度(℃)
            
        Returns:
            float: 导热系数(W/m·K)
            
        Raises:
            ValueError: 当温度小于等于50℃时，提示无需保温
            ValueError: 当温度超过推荐使用温度时，提示温度过高
        """
        if Tm <= 50:
            raise ValueError("介质温度小于等于50℃，无需保温")

        if Tm > material.recommended_temperature:
            raise ValueError(
                f"介质温度{Tm}℃超过推荐使用温度{material.recommended_temperature}℃，请选择其他保温材料")

        # 解析导热系数方程
        lambda_0, a, b, t1, t2, lambda_1, c, t3 = InsulatedPipe_GB50264.parse_thermal_conductivity_eq(
            material.thermal_conductivity_eq)

        if Tm < t1:
            return lambda_0
        elif Tm <= t2:
            return lambda_0 + a * (Tm - t1)
        else:
            return lambda_1 + c * (Tm - t3)

    def __init__(self, D0_mm, T0, Ts=None, Ta=None, alpha_s=None, length_m=None):
        """
        初始化保温管道参数 (lambda_k 在内部计算)
        
        :param D0_mm: 管道外径(mm)
        :param T0: 介质温度(℃)
        :param Ts: 绝热层表面温度(℃)，可选，默认为50℃
        :param Ta: 环境温度(℃)，可选，默认为25℃
        :param alpha_s: 表面传热系数(W/m²·℃)，可选，默认为11.6
        :param length_m: 管道长度(m)，可选，默认为1.0
        """
        # 使用默认值或提供的值
        Ts = Ts if Ts is not None else self.DEFAULT_SURFACE_TEMP
        Ta = Ta if Ta is not None else self.DEFAULT_AMBIENT_TEMP
        alpha_s = alpha_s if alpha_s is not None else self.DEFAULT_SURFACE_COEFF
        length_m = length_m if length_m is not None else self.DEFAULT_LENGTH

        self.validate_input(D0_mm, T0, Ts, Ta)

        # 预计算并存储常用值
        self.D0 = D0_mm / 1000.0  # 转换为米(m)
        self.length_m = length_m  # 存储长度
        self.T0_celsius = T0
        self.T0 = T0 + 273.15
        self.Ts = Ts + 273.15
        self.Ta = Ta + 273.15
        self.alpha_s = alpha_s

        # 内部计算导热系数
        self.lambda_k = self.calculate_thermal_conductivity(
            SILICA_ALUMINA_1, (self.T0 + self.Ts) / 2 - 273.15)

        # 计算结果属性
        self.raw_thickness = 0.0  # 原始计算厚度(mm)
        self.standard_thickness = 0.0  # 圆整后的标准厚度(mm)
        self.calculation_status = "未计算"  # 计算状态

        # 计算结果存储
        self.heat_loss = 0.0  # 热损失(kW)
        self.volume = 0.0  # 总体积(m³)
        self.volume_per_meter = 0.0  # 单位长度体积(m³/m)
        self.weight = 0.0  # 总重量(kg)
        self.weight_per_meter = 0.0  # 单位长度重量(kg/m)
        self.diameters = (0.0, 0.0)  # (内径, 外径)(m)

        # 保温材料
        self.insulation_material = SILICA_ALUMINA_1

    def _calculate_diameters(self):
        """计算保温层内径和外径"""
        D1 = self.D0
        D2 = self.D0 + 2 * (self.standard_thickness / 1000)
        self.diameters = (D1, D2)
        return self.diameters

    def _calculate_insulation_volume_per_meter(self) -> float:
        """计算保温层单位长度体积(m³/m)"""
        if self.calculation_status != "Success":
            return 0.0

        D1, D2 = self._calculate_diameters()
        # 计算单位长度体积 = π * (D2² - D1²) / 4
        self.volume_per_meter = math.pi * (D2**2 - D1**2) / 4
        return self.volume_per_meter

    def calculate_insulation_volume(self) -> float:
        """计算保温层总体积(m³)"""
        if self.calculation_status != "Success":
            return 0.0

        volume_per_meter = self._calculate_insulation_volume_per_meter()
        self.volume = volume_per_meter * self.length_m
        return self.volume

    def calculate_insulation_weight_per_meter(self) -> float:
        """计算保温层单位长度重量(kg/m)"""
        if self.calculation_status != "Success":
            return 0.0

        volume_per_meter = self._calculate_insulation_volume_per_meter()
        self.weight_per_meter = volume_per_meter * self.insulation_material.density
        return self.weight_per_meter

    def calculate_insulation_weight(self) -> float:
        """计算保温层总重量(kg)"""
        if self.calculation_status != "Success":
            return 0.0

        weight_per_meter = self.calculate_insulation_weight_per_meter()
        self.weight = weight_per_meter * self.length_m
        return self.weight

    def calculate(self):
        """执行保温层厚度计算"""
        try:
            D1 = self.solve_diameter()
            thickness_m = (D1 - self.D0) / 2
            raw_thickness_mm = thickness_m * 1000
            self.standard_thickness = self.round_thickness(raw_thickness_mm)
            self.raw_thickness = raw_thickness_mm
            self.calculation_status = "Success"

            # 重新计算所有结果
            self._calculate_diameters()
            self._calculate_insulation_volume_per_meter()
            self.calculate_insulation_volume()
            self.calculate_insulation_weight_per_meter()
            self.calculate_insulation_weight()
            self.calculate_total_heat_loss_kW()

            return self
        except Exception as e:
            self.standard_thickness = 0
            self.raw_thickness = 0
            self.calculation_status = str(e)
            # 重置所有计算结果
            self.heat_loss = 0.0
            self.volume = 0.0
            self.volume_per_meter = 0.0
            self.weight = 0.0
            self.weight_per_meter = 0.0
            self.diameters = (0.0, 0.0)
            return self

    @property
    def final_od(self):
        """计算最终外径(mm) = 管道外径 + 2 * 标准厚度"""
        return self.raw_thickness * 2 + (self.standard_thickness * 2)

    def __repr__(self):
        """返回友好的字符串表示，格式：<Pipe: 标准厚度mm (状态)>"""
        return f"<Pipe: {self.standard_thickness}mm ({self.calculation_status})>"

    def solve_diameter(self):
        """求解公式5.3.11的数值解"""
        # 定义方程 f(D1) = D1*ln(D1/D0) - RHS
        def equation(D1):
            D1_float = float(D1[0])  # 确保转换为单个浮点数
            RHS = (2 * self.lambda_k / self.alpha_s) * \
                (self.T0 - self.Ts) / (self.Ts - self.Ta)
            return D1_float * math.log(D1_float/self.D0) - RHS

        # 初始猜测值（管道外径+50mm）
        initial_guess = np.array([self.D0 + 0.05])  # 使用数组形式

        # 使用SciPy求解器
        D1 = fsolve(equation, initial_guess, xtol=1e-6)
        return float(D1[0])  # 确保返回单个浮点数

    @classmethod
    def round_thickness(cls, thickness_mm):
        """工程厚度圆整规则，使用二分查找优化"""
        if thickness_mm <= 0:
            return 0

        # 使用二分查找找到第一个大于等于计算厚度的标准厚度
        left, right = 0, len(cls.STANDARD_THICKNESS) - 1
        while left < right:
            mid = (left + right) // 2
            if cls.STANDARD_THICKNESS[mid] < thickness_mm:
                left = mid + 1
            else:
                right = mid

        return cls.STANDARD_THICKNESS[left] if cls.STANDARD_THICKNESS[left] >= thickness_mm else cls.STANDARD_THICKNESS[-1]

    @staticmethod
    def validate_input(D0_mm, T0, Ts, Ta):
        """参数有效性验证"""
        if Ts <= Ta:
            raise ValueError("表面温度必须大于环境温度")
        if D0_mm <= 0:
            raise ValueError("管道直径必须大于0")
        if not (0 < T0 < 1000):
            raise ValueError("介质温度超出合理范围")
        if not (0 < Ts < 100):
            raise ValueError("表面温度超出安全范围")

    def calculate_total_heat_loss_kW(self):
        """计算整个管道的总散热损失(kW)，使用缓存优化"""
        if self.calculation_status != "Success":
            return 0.0

        # 计算保温层外径
        D1 = self.D0 + 2 * (self.standard_thickness / 1000)

        # 计算热阻
        if abs(D1 - self.D0) < 1e-9:
            self.heat_loss = 0.0
            return 0.0

        thermal_resistance_insulation = D1 / \
            (2 * self.lambda_k) * math.log(D1/self.D0)
        thermal_resistance_surface = 1 / self.alpha_s
        total_resistance = thermal_resistance_insulation + thermal_resistance_surface

        # 计算热流密度
        q_per_m2 = (self.T0 - self.Ta) / total_resistance
        q_per_meter = q_per_m2 * math.pi * D1
        total_heat_loss_W = q_per_meter * self.length_m

        # 缓存结果
        self.heat_loss = total_heat_loss_W / 1000
        return self.heat_loss

    # def to_pipe_result(self) -> PipeResult:
    #     """转换为PipeResult对象"""
    #     pipe_data = PipeData(
    #         pipe_id="",  # 需要外部提供
    #         outer_diameter=self.D0 * 1000,
    #         medium_temperature=self.T0_celsius,
    #         length=self.length_m,
    #         surface_temperature=self.Ts - 273.15,
    #         ambient_temperature=self.Ta - 273.15,
    #         surface_coefficient=self.alpha_s
    #     )

    #     insulation_data = InsulationData(
    #         raw_thickness=self.raw_thickness,
    #         standard_thickness=self.standard_thickness,
    #         thermal_conductivity=self.lambda_k,
    #         heat_loss=self.calculate_total_heat_loss_kW(),
    #         calculation_status=self.calculation_status
    #     )

    #     return PipeResult(
    #         pipe_data=pipe_data,
    #         insulation_data=insulation_data,
    #         calculation_time=datetime.now()
        # )

    @staticmethod
    def _get_insulated_pipe_result_1():
        """返回"格式1"所需的列名和顺序"""
        return [
            '管段号', '外径(mm)', '介质温度(℃)', '管道长度(m)',
            '表面温度(℃)', '环境温度(℃)',
            '导热系数(W/m·K)',
            '保温层计算厚度(mm)', '保温层实施厚度(mm)',
            '保温层体积(m³)', '保温层单位长度重量(kg/m)', '保温层总重量(kg)',
            '总散热损失(kW)', '规范状态'
        ]

    @staticmethod
    def generate_report_dataframe(report_data, format_type='format1'):
        """根据计算数据列表生成指定格式的 Pandas DataFrame 报告
        
        Args:
            report_data (list): 包含每行计算结果的字典列表。
            format_type (str): 需要生成的报告格式 ('format1', etc.)。
                               默认为 'format1'。
        
        Returns:
            pd.DataFrame: 格式化后的报告 DataFrame。
        
        Raises:
            ValueError: 如果请求了不支持的 format_type。
        """
        df = pd.DataFrame(report_data)

        if format_type == 'format1':
            output_columns = InsulatedPipe_GB50264._get_insulated_pipe_result_1()
            # 确保所有列都存在，不存在的列填充为 'N/A'
            for col in output_columns:
                if col not in df.columns:
                    df[col] = 'N/A'
            return df[output_columns]  # 按指定顺序选择列

        # elif format_type == 'format2':
        #     # 未来可以在这里添加格式2的逻辑
        #     # output_columns = InsulatedPipe_GB50264._get_format2_columns()
        #     # ...
        #     # return df[output_columns]
        #     pass

        else:
            raise ValueError(f"不支持的报告格式: {format_type}")

    @staticmethod
    def export_report_excel(dataframe, filename=None):
        """将 DataFrame 导出到 Excel 文件"""
        if filename is None:
            filename = InsulatedPipe_GB50264.DEFAULT_REPORT_FILENAME
        try:
            dataframe.to_excel(filename, index=False, engine='openpyxl')
            print(f"\n报告已成功导出到 {filename}")
        except Exception as e:
            print(f"\n导出到 Excel 失败: {e}")
            print("请确保已安装 'openpyxl' 库 (pip install openpyxl)")

    @staticmethod
    def process_and_report_batch(input_list, report_format='format1', output_filename=None):
        """
        处理简化的批量输入，生成报告 DataFrame，并可选地导出到 Excel。

        Args:
            input_list (list): 元组列表，每个元组包含 (pipe_id, D0_mm, T0, length_m)。
            report_format (str): 报告格式 ('format1', etc.)。
            output_filename (str, optional): Excel 导出文件名。如果为 None，则使用默认文件名。

        Returns:
            pd.DataFrame: 生成的报告 DataFrame。
        """
        print(
            f"\n--- 开始批量处理 (使用默认 Ts={InsulatedPipe_GB50264.DEFAULT_SURFACE_TEMP}°C, "
            f"Ta={InsulatedPipe_GB50264.DEFAULT_AMBIENT_TEMP}°C, "
            f"αs={InsulatedPipe_GB50264.DEFAULT_SURFACE_COEFF} W/m²·C) ---")

        results_data = []
        for pipe_id, D0_mm, T0, length_m in input_list:
            params_for_report = {
                "管段号": pipe_id, "外径(mm)": D0_mm, "介质温度(℃)": T0,
                "管道长度(m)": length_m,
                "表面温度(℃)": InsulatedPipe_GB50264.DEFAULT_SURFACE_TEMP,
                "环境温度(℃)": InsulatedPipe_GB50264.DEFAULT_AMBIENT_TEMP,
                "表面换热系数(W/m²·℃)": InsulatedPipe_GB50264.DEFAULT_SURFACE_COEFF
            }
            row_data = params_for_report.copy()

            try:
                pipe = InsulatedPipe_GB50264(
                    D0_mm=D0_mm, T0=T0, length_m=length_m
                )
                pipe.calculate()
                total_heat_loss_kw = pipe.calculate_total_heat_loss_kW(
                ) if pipe.calculation_status == "Success" else 0

                calculated_lambda_k = pipe.lambda_k
                row_data["导热系数(W/m·K)"] = f"{calculated_lambda_k:.4f}"

                row_data.update({
                    "保温层计算厚度(mm)": f"{pipe.raw_thickness:.1f}" if pipe.calculation_status == "Success" else "N/A",
                    "保温层实施厚度(mm)": f"{pipe.standard_thickness:.1f}" if pipe.calculation_status == "Success" else "N/A",
                    "保温层体积(m³)": f"{pipe.volume:.3f}" if pipe.calculation_status == "Success" else "N/A",
                    "保温层单位长度重量(kg/m)": f"{pipe.weight_per_meter:.2f}" if pipe.calculation_status == "Success" else "N/A",
                    "保温层总重量(kg)": f"{pipe.weight:.2f}" if pipe.calculation_status == "Success" else "N/A",
                    "总散热损失(kW)": f"{total_heat_loss_kw:.2f}" if pipe.calculation_status == "Success" else "N/A",
                    "规范状态": pipe.calculation_status
                })

            except ValueError as ve:
                row_data.update(
                    {"规范状态": f"输入错误: {ve}", "导热系数(W/m·K)": "N/A", "总散热损失(kW)": "N/A"})
            except Exception as e:
                row_data.update(
                    {"规范状态": f"计算错误: {e}", "导热系数(W/m·K)": "N/A", "总散热损失(kW)": "N/A"})
            finally:
                for col in ["导热系数(W/m·K)", "保温层计算厚度(mm)", "保温层实施厚度(mm)",
                            "保温层体积(m³)", "保温层单位长度重量(kg/m)", "保温层总重量(kg)",
                            "总散热损失(kW)"]:
                    if col not in row_data:
                        row_data[col] = "N/A"
                results_data.append(row_data)

        # 生成 DataFrame
        try:
            report_df = InsulatedPipe_GB50264.generate_report_dataframe(
                results_data, format_type=report_format)
        except ValueError as e:
            print(f"生成报告时出错: {e}")
            return pd.DataFrame()  # 格式错误时返回空 DataFrame

        # 导出到Excel文件
        InsulatedPipe_GB50264.export_report_excel(
            report_df,
            filename=output_filename if output_filename is not None else InsulatedPipe_GB50264.DEFAULT_REPORT_FILENAME
        )

        return report_df  # 返回生成的 DataFrame


# 批量计算示例
if __name__ == "__main__":
    # 1. 定义简化的输入数据
    # 格式： (管段号, 外径(mm), 介质温度(℃)，长度(m)，保温材料（默认硅酸铝棉制品）)
    input_params = [
        ("101", 273, 80, 1.0),  # 1米长
        ("102", 323.9, 80, 1),
        ("103", 76.1, 80, 1),
        ("104", 168.3, 80, 1),
        ("105", 323.9, 80, 1),
        ("106", 219.1, 80, 1),
        ("107", 323.9, 550, 1),
        ("108", 406.4, 550, 1),
        ("203", 273, 550, 1),
        ("204", 76.1, 550, 1),
        ("205", 114.3, 550, 1),
        ("206", 114.3, 550, 1),
        ("207", 168.3, 550, 1)
    ]
    input_params_2 = [
        ("101", 610, 500, 11.0),   # 10米长
        ("102", 406, 500, 30.0),
        ("103", 219, 500, 3.0),
        ("104", 610, 300, 27.0),
        ("105", 219, 300, 4),
        ("106", 610, 600, 6),
        ("107", 610, 500, 24)

    ]

    # 2. 调用类方法处理批量数据并导出报告
    report_df = InsulatedPipe_GB50264.process_and_report_batch(
        input_list=input_params,
        report_format='format1',
        # output_filename="保温计算示例报告.xlsx"
    )

    # (可选) 打印返回的 DataFrame 到控制台，用于快速查看
    if not report_df.empty:
        print("\n--- 计算结果 DataFrame (内存中) ---")
        print(report_df)
