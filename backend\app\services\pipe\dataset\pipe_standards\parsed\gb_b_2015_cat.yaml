name: GB-B
version: '2015.01'
description: ''
created_time: '2012-11-15 03:21:25'
modified_time: '2018-01-12 03:12:40'
guid: 55004FAB-E842-4202-97D4-BD1FF4BE43CF
size_tables:
  Size-b: !!python/object:__main__.SizeTable
    __dict__:
      id: 2
      name: Size-b
      version: '2015.01'
      description: ''
      created_time: '2012-03-19 11:08:35'
      modified_time: '2017-12-29 03:13:20'
      created_by: xia<PERSON><PERSON>_<PERSON>hang
      edited_by: ''
      status: '0'
      base_id: -1
      unit: '0'
      default_part_number: '001'
      max_row_id: 130
      type: 0
      fields:
        - !!python/object:__main__.PipeField
          __dict__:
            index: 0
            order: 0
            type: 0
            name: NOM
            title: NOM
            description: Nominal Pipe Size
            data_type: '1'
            required: true
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 1
            order: 1
            type: 0
            name: OD
            title: OD
            description: Outside Diameter
            data_type: '1'
            required: true
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 2
            order: 2
            type: 0
            name: DESCRIPTION
            title: DESCRIPTION
            description: Description
            data_type: '2'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 3
            order: 3
            type: 0
            name: PTN
            title: PTN
            description: Part Number
            data_type: '2'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
  - sizes: [['80', '10.0', '14', DN10, '0003'], ['81', '15.0', '18', DN15, '0004'],
  - ['82', '20.0', '25', DN20, '0005'], ['83', '25.0', '32', DN25, '0006'], [
  - '84', '32.0', '38', DN32, '0007'], ['85', '40.0', '45', DN40, 0008], ['86',
  - '50.0', '57', DN50, 0009], ['87', '65.0', '76', DN65, '0010'], ['88', '80.0',
  - '89', DN80, '0011'], ['90', '100.0', '108', DN100, '0013'], ['91', '125.0',
  - '133', DN125, '0014'], ['92', '150.0', '159', DN150, '0015'], ['93', '200.0',
  - '219', DN200, '0016'], ['94', '250.0', '273', DN250, '0017'], ['95', '300.0',
  - '325', DN300, 0018], ['96', '350.0', '377', DN350, 0019], ['97', '400.0',
  - '426', DN400, '0020'], ['98', '450.0', '480', DN450, '0021'], ['99', '500.0',
  - '530', DN500, '0022'], ['101', '600.0', '630', DN600, '0024'], ['102', '700.0',
  - '720', DN700, '0025'], ['103', '800.0', '820', DN800, '0026'], ['104', '900.0',
  - '920', DN900, '0027'], ['105', '1000.0', '1020', DN1000, 0028], ['106',
  - '1200.0', '1220', DN1200, 0029], ['107', '1400.0', '1420', DN1400, '0030'],
  - ['108', '1600.0', '1620', DN1600, '0031'], ['109', '1800.0', '1820', DN1800,
  - '0032'], ['110', '2000.0', '2020', DN2000, '0033']]
    __pydantic_extra__: null
    __pydantic_fields_set__: !!set
      unit: null
      sizes: null
      base_id: null
      modified_time: null
      created_by: null
      name: null
      max_row_id: null
      description: null
      fields: null
      default_part_number: null
      version: null
      id: null
      type: null
      created_time: null
      edited_by: null
      status: null
    __pydantic_private__:
      _next_id: 1
  Size: !!python/object:__main__.SizeTable
    __dict__:
      id: 6
      name: Size
      version: '2015.01'
      description: ''
      created_time: '2012-02-29 21:19:06'
      modified_time: '2018-01-09 01:47:22'
      created_by: ''
      edited_by: ''
      status: '0'
      base_id: -1
      unit: '0'
      default_part_number: '003'
      max_row_id: 153
      type: 0
      fields:
        - !!python/object:__main__.PipeField
          __dict__:
            index: 0
            order: 0
            type: 0
            name: NOM
            title: NOM
            description: Nominal Pipe Size
            data_type: '1'
            required: true
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 1
            order: 1
            type: 0
            name: OD
            title: OD
            description: Outside Diameter
            data_type: '1'
            required: true
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 2
            order: 2
            type: 0
            name: DESCRIPTION
            title: DESCRIPTION
            description: Description
            data_type: '2'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 3
            order: 3
            type: 0
            name: PTN
            title: PTN
            description: Part Number
            data_type: '2'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
  - sizes: [['113', '6.0', '10.2', 6mm, '0001'], ['114', '8.0', '13.5', 8mm, '0002'],
  - ['115', '10.0', '17.2', 10mm, '0003'], ['116', '15.0', '21.3', 15mm, '0004'],
  - ['117', '20.0', '26.9', 20mm, '0005'], ['118', '25.0', '33.7', 25mm, '0006'],
  - ['119', '32.0', '42.2', 32mm, '0007'], ['120', '40.0', '48.3', 40mm, 0008],
  - ['121', '50.0', '60.3', 50mm, 0009], ['122', '65.0', '76.1', 65mm, '0010'],
  - ['123', '80.0', '88.9', 80mm, '0011'], ['124', '90.0', '101.6', 90mm, '0012'],
  - ['125', '100.0', '114.3', 100mm, '0013'], ['126', '125.0', '139.7', 125mm,
  - '0014'], ['127', '150.0', '168.3', 150mm, '0015'], ['128', '200.0', '219.1',
  - 200mm, '0016'], ['129', '250.0', '273', 250mm, '0017'], ['130', '300.0',
  - '323.9', 300mm, 0018], ['131', '350.0', '355.6', 350mm, 0019], ['132', '400.0',
  - '406.4', 400mm, '0020'], ['133', '450.0', '457', 450mm, '0021'], ['134',
  - '500.0', '508', 500mm, '0022'], ['135', '550.0', '558.8', 550mm, '0023'],
  - ['136', '600.0', '610', 600mm, '0024'], ['137', '700.0', '711', 700mm, '0025'],
  - ['138', '800.0', '813', 800mm, '0026'], ['139', '900.0', '914', 900mm, '0027'],
  - ['140', '1000.0', '1016', 1000mm, 0028], ['141', '1200.0', '1220', 1200mm,
  - 0029], ['142', '1400.0', '1420', 1400mm, '0030'], ['143', '1600.0', '1620',
  - 1600mm, '0031'], ['144', '1800.0', '1820', 1800mm, '0032'], ['145', '2000.0',
  - '2020', 2000mm, '0033'], ['146', '2200.0', '2220', 2200mm, '0034'], ['147',
  - '2400.0', '2420', 2400mm, '0035'], ['148', '2600.0', '2620', 2600mm, '0036'],
  - ['149', '2800.0', '2820', 2800mm, '0037'], ['150', '3000.0', '3020', 3000mm,
  - 0038], ['151', '3200.0', '3220', 3200mm, 0039], ['152', '3400.0', '3420',
  - 3420mm, '0040'], ['153', '3600.0', '3620', 3620mm, '0041']]
    __pydantic_extra__: null
    __pydantic_fields_set__: !!set
      unit: null
      sizes: null
      base_id: null
      modified_time: null
      created_by: null
      name: null
      max_row_id: null
      description: null
      fields: null
      default_part_number: null
      version: null
      id: null
      type: null
      created_time: null
      edited_by: null
      status: null
    __pydantic_private__:
      _next_id: 1
  Size_General_b: !!python/object:__main__.SizeTable
    __dict__:
      id: 9
      name: Size_General_b
      version: '2015.01'
      description: ''
      created_time: '2012-10-12 03:07:42'
      modified_time: '2018-01-09 01:53:37'
      created_by: ''
      edited_by: ''
      status: '0'
      base_id: -1
      unit: '0'
      default_part_number: '001'
      max_row_id: 389
      type: 0
      fields:
        - !!python/object:__main__.PipeField
          __dict__:
            index: 0
            order: 0
            type: 0
            name: NOM
            title: NOM
            description: Nominal Pipe Size
            data_type: '1'
            required: true
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 1
            order: 1
            type: 0
            name: OD
            title: OD
            description: Outside Diameter
            data_type: '1'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 2
            order: 2
            type: 0
            name: DESCRIPTION
            title: DESCRIPTION
            description: Description
            data_type: '2'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 3
            order: 3
            type: 0
            name: PTN
            title: PTN
            description: Part Number
            data_type: '2'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
  - sizes: [['380', '10.0', '14', DN10, '0003'], ['358', '15.0', '18', DN15, '0004'],
  - ['359', '20.0', '25', DN20, '0005'], ['360', '25.0', '32', DN25, '0006'],
  - ['361', '32.0', '38', DN32, '0007'], ['362', '40.0', '45', DN40, 0008], [
  - '363', '50.0', '57', DN50, 0009], ['364', '65.0', '76', DN65, '0010'], [
  - '365', '80.0', '89', DN80, '0011'], ['366', '100.0', '108', DN100, '0013'],
  - ['367', '125.0', '133', DN125, '0014'], ['368', '150.0', '159', DN150, '0015'],
  - ['369', '200.0', '219', DN200, '0016'], ['370', '250.0', '273', DN250, '0017'],
  - ['371', '300.0', '325', DN300, 0018], ['372', '350.0', '377', DN350, 0019],
  - ['373', '400.0', '426', DN400, '0020'], ['374', '450.0', '480', DN450, '0021'],
  - ['375', '500.0', '530', DN500, '0022'], ['377', '600.0', '630', DN600, '0024'],
  - ['381', '700.0', '720', DN700, '0025'], ['382', '800.0', '820', DN800, '0026'],
  - ['383', '900.0', '920', DN900, '0027'], ['384', '1000.0', '1020', DN1000,
  - 0028], ['385', '1200.0', '1220', DN1200, 0029], ['386', '1400.0', '1420',
  - DN1400, '0030'], ['387', '1600.0', '1620', DN1600, '0031'], ['388', '1800.0',
  - '1820', DN1800, '0032'], ['389', '2000.0', '2020', DN2000, '0033']]
    __pydantic_extra__: null
    __pydantic_fields_set__: !!set
      unit: null
      sizes: null
      base_id: null
      modified_time: null
      created_by: null
      name: null
      max_row_id: null
      description: null
      fields: null
      default_part_number: null
      version: null
      id: null
      type: null
      created_time: null
      edited_by: null
      status: null
    __pydantic_private__:
      _next_id: 1
  Size_General: !!python/object:__main__.SizeTable
    __dict__:
      id: 10
      name: Size_General
      version: '2015.01'
      description: ''
      created_time: '2012-10-12 03:07:42'
      modified_time: '2018-01-09 08:29:06'
      created_by: ''
      edited_by: ''
      status: '0'
      base_id: -1
      unit: '0'
      default_part_number: '001'
      max_row_id: 389
      type: 0
      fields:
        - !!python/object:__main__.PipeField
          __dict__:
            index: 0
            order: 0
            type: 0
            name: NOM
            title: NOM
            description: Nominal Pipe Size
            data_type: '1'
            required: true
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 1
            order: 1
            type: 0
            name: OD
            title: OD
            description: Outside Diameter
            data_type: '1'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 2
            order: 2
            type: 0
            name: DESCRIPTION
            title: DESCRIPTION
            description: Description
            data_type: '2'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 3
            order: 3
            type: 0
            name: PTN
            title: PTN
            description: Part Number
            data_type: '2'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
  - sizes: [['378', '6.0', '10', 6mm, '0001'], ['379', '8.0', '14', 8mm, '0002'],
  - ['380', '10.0', '17', 10mm, '0003'], ['358', '15.0', '20', 15mm, '0004'],
  - ['359', '20.0', '27', 20mm, '0005'], ['360', '25.0', '34', 25mm, '0006'],
  - ['361', '32.0', '42', 32mm, '0007'], ['362', '40.0', '48', 40mm, 0008], [
  - '363', '50.0', '60', 50mm, 0009], ['364', '65.0', '76', 65mm, '0010'], [
  - '365', '80.0', '89', 80mm, '0011'], ['366', '100.0', '114', 100mm, '0013'],
  - ['367', '125.0', '140', 125mm, '0014'], ['368', '150.0', '168', 150mm, '0015'],
  - ['369', '200.0', '219', 200mm, '0016'], ['370', '250.0', '273', 250mm, '0017'],
  - ['371', '300.0', '325', 300mm, 0018], ['372', '350.0', '355', 350mm, 0019],
  - ['373', '400.0', '406', 400mm, '0020'], ['374', '450.0', '457', 450mm, '0021'],
  - ['375', '500.0', '508', 500mm, '0022'], ['377', '600.0', '610', 600mm, '0024'],
  - ['381', '700.0', '711', 700mm, '0025'], ['382', '800.0', '813', 800mm, '0026'],
  - ['383', '900.0', '914', 900mm, '0027'], ['384', '1000.0', '1016', 1000mm,
  - 0028], ['385', '1200.0', '1220', 1200mm, 0029], ['386', '1400.0', '1420',
  - 1400mm, '0030'], ['387', '1600.0', '1620', 1600mm, '0031'], ['388', '1800.0',
  - '1820', 1800mm, '0032'], ['389', '2000.0', '2020', 2000mm, '0033']]
    __pydantic_extra__: null
    __pydantic_fields_set__: !!set
      unit: null
      sizes: null
      base_id: null
      modified_time: null
      created_by: null
      name: null
      max_row_id: null
      description: null
      fields: null
      default_part_number: null
      version: null
      id: null
      type: null
      created_time: null
      edited_by: null
      status: null
    __pydantic_private__:
      _next_id: 1
  Size_General_1: !!python/object:__main__.SizeTable
    __dict__:
      id: 11
      name: Size_General_1
      version: '2015.01'
      description: ''
      created_time: '2012-10-12 03:07:42'
      modified_time: '2018-01-12 03:12:40'
      created_by: ''
      edited_by: ''
      status: '0'
      base_id: -1
      unit: '0'
      default_part_number: '001'
      max_row_id: 389
      type: 0
      fields:
        - !!python/object:__main__.PipeField
          __dict__:
            index: 0
            order: 0
            type: 0
            name: NOM
            title: NOM
            description: Nominal Pipe Size
            data_type: '1'
            required: true
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 1
            order: 1
            type: 0
            name: OD
            title: OD
            description: Outside Diameter
            data_type: '1'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 2
            order: 2
            type: 0
            name: DESCRIPTION
            title: DESCRIPTION
            description: Description
            data_type: '2'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 3
            order: 3
            type: 0
            name: PTN
            title: PTN
            description: Part Number
            data_type: '2'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
  - sizes: [['378', '6.0', '10', 6mm, '0001'], ['379', '8.0', '14', 8mm, '0002'],
  - ['380', '10.0', '17', 10mm, '0003'], ['358', '15.0', '20', 15mm, '0004'],
  - ['359', '20.0', '27', 20mm, '0005'], ['360', '25.0', '34', 25mm, '0006'],
  - ['361', '32.0', '42', 32mm, '0007'], ['362', '40.0', '48', 40mm, 0008], [
  - '363', '50.0', '60', 50mm, 0009], ['364', '65.0', '76', 65mm, '0010'], [
  - '365', '80.0', '89', 80mm, '0011'], ['366', '100.0', '114', 100mm, '0013'],
  - ['367', '125.0', '140', 125mm, '0014'], ['368', '150.0', '168', 150mm, '0015'],
  - ['369', '200.0', '219', 200mm, '0016'], ['370', '250.0', '273', 250mm, '0017'],
  - ['371', '300.0', '325', 300mm, 0018], ['372', '350.0', '355', 350mm, 0019],
  - ['373', '400.0', '406', 400mm, '0020'], ['374', '450.0', '457', 450mm, '0021'],
  - ['375', '500.0', '508', 500mm, '0022'], ['376', '550.0', '559', 550mm, '0023'],
  - ['377', '600.0', '610', 600mm, '0024'], ['381', '700.0', '711', 700mm, '0025'],
  - ['382', '800.0', '813', 800mm, '0026'], ['383', '900.0', '914', 900mm, '0027'],
  - ['384', '1000.0', '1016', 1000mm, 0028], ['385', '1200.0', '1220', 1200mm,
  - 0029], ['386', '1400.0', '1420', 1400mm, '0030'], ['387', '1600.0', '1620',
  - 1600mm, '0031'], ['388', '1800.0', '1820', 1800mm, '0032'], ['389', '2000.0',
          '2020', 2000mm, '0033']]
    __pydantic_extra__: null
    __pydantic_fields_set__: !!set
      unit: null
      sizes: null
      base_id: null
      modified_time: null
      created_by: null
      name: null
      max_row_id: null
      description: null
      fields: null
      default_part_number: null
      version: null
      id: null
      type: null
      created_time: null
      edited_by: null
      status: null
    __pydantic_private__:
      _next_id: 1
material_tables:
  material: !!python/object:__main__.MaterialTable
    __dict__:
      id: 0
      name: material
      version: '2015.01'
      description: ''
      created_time: '2012-11-15 05:45:54'
      modified_time: '2016-07-20 07:58:42'
      created_by: xiaoyuan_zhang
      edited_by: ''
      status: '2'
      base_id: -1
      unit: ''
      default_part_number: ''
      max_row_id: 9
      type: 0
      fields:
        - !!python/object:__main__.PipeField
          __dict__:
            index: 0
            order: 0
            type: 0
            name: SPEC
            title: SPEC
            description: Material Specification
            data_type: '2'
            required: true
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 1
            order: 1
            type: 0
            name: GRADE
            title: GRADE
            description: Grade
            data_type: '2'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 2
            order: 2
            type: 0
            name: COMPOSITION
            title: COMPOSITION
            description: Composition
            data_type: '2'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 3
            order: 3
            type: 0
            name: COMPTYPES
            title: COMPTYPES
            description: Component types
            data_type: '2'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 4
            order: 4
            type: 0
            name: DENSITY
            title: DENSITY
            description: Density
            data_type: '1'
            required: true
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 5
            order: 5
            type: 0
            name: PTN
            title: PTN
            description: Part Number
            data_type: '2'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
      items:
        ? ''
        : !!python/object:__main__.MaterialItem
          __dict__:
            id: 9
            code: ''
            name: ''
            description: ''
            status: ''
            spec: '20'
            grade: ''
            type: ''
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            name: null
            description: null
            code: null
            id: null
            type: null
            grade: null
            spec: null
            status: null
          __pydantic_private__: null
    __pydantic_extra__: null
    __pydantic_fields_set__: !!set
      unit: null
      base_id: null
      modified_time: null
      created_by: null
      name: null
      max_row_id: null
      description: null
      fields: null
      default_part_number: null
      version: null
      items: null
      id: null
      type: null
      created_time: null
      edited_by: null
      status: null
    __pydantic_private__:
      _next_id: 1
rating_tables: {}
connection_tables: {}
end_prep_tables: {}
wall_thickness_tables: {}
type_tables: {}
schedule_tables:
  Schedule: !!python/object:__main__.ScheduleTable
    __dict__:
      id: 2
      name: Schedule
      version: '2015.01'
      description: ''
      created_time: '2012-11-27 06:36:53'
      modified_time: '2012-12-05 06:14:10'
      created_by: xiaoyuan_zhang
      edited_by: ''
      status: '0'
      base_id: -1
      unit: ''
      default_part_number: '0001'
      max_row_id: 17
      type: 0
      fields:
        - !!python/object:__main__.PipeField
          __dict__:
            index: 0
            order: 0
            type: 0
            name: SCH
            title: SCH
            description: Schedule
            data_type: '2'
            required: true
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 1
            order: 1
            type: 0
            name: SCHENCWT
            title: SCHENCWT
            description: Schedule Enhance Weight
            data_type: '2'
            required: true
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 2
            order: 2
            type: 0
            name: PTN
            title: PTN
            description: Part Number
            data_type: '2'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
      items:
        ? ''
        : !!python/object:__main__.ScheduleItem
          __dict__:
            id: 17
            code: ''
            name: ''
            description: ''
            status: active
            value: ''
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            value: null
            name: null
            description: null
            code: null
            id: null
            status: null
          __pydantic_private__: null
    __pydantic_extra__: null
    __pydantic_fields_set__: !!set
      unit: null
      base_id: null
      modified_time: null
      created_by: null
      name: null
      max_row_id: null
      description: null
      fields: null
      default_part_number: null
      version: null
      items: null
      id: null
      type: null
      created_time: null
      edited_by: null
      status: null
    __pydantic_private__:
      _next_id: 1
thickness_tables:
  Thickness: !!python/object:__main__.ThicknessTable
    __dict__:
      id: 2
      name: Thickness
      version: '2015.01'
      description: ''
      created_time: '2012-11-27 06:37:54'
      modified_time: '2016-08-03 05:41:25'
      created_by: ''
      edited_by: ''
      status: '0'
      base_id: -1
      unit: ''
      default_part_number: ''
      max_row_id: 129
      type: 0
      fields:
        - !!python/object:__main__.PipeField
          __dict__:
            index: 0
            order: 0
            type: 0
            name: NOM
            title: NOM
            description: Nominal Pipe Size
            data_type: '1'
            required: true
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 1
            order: 1
            type: 0
            name: SCH0
            title: 5S
            description: ''
            data_type: '1'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 2
            order: 2
            type: 0
            name: SCH1
            title: '5'
            description: ''
            data_type: '1'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 3
            order: 3
            type: 0
            name: SCH2
            title: 10S
            description: ''
            data_type: '1'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 4
            order: 4
            type: 0
            name: SCH3
            title: '10'
            description: ''
            data_type: '1'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 5
            order: 5
            type: 0
            name: SCH4
            title: '20'
            description: ''
            data_type: '1'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 6
            order: 6
            type: 0
            name: SCH5
            title: '30'
            description: ''
            data_type: '1'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 7
            order: 7
            type: 0
            name: SCH6
            title: 40S
            description: ''
            data_type: '1'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 8
            order: 8
            type: 0
            name: SCH7
            title: '40'
            description: ''
            data_type: '1'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 9
            order: 9
            type: 0
            name: SCH8
            title: '60'
            description: ''
            data_type: '1'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 10
            order: 10
            type: 0
            name: SCH9
            title: 80S
            description: ''
            data_type: '1'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 11
            order: 11
            type: 0
            name: SCH10
            title: '80'
            description: ''
            data_type: '1'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 12
            order: 12
            type: 0
            name: SCH11
            title: '100'
            description: ''
            data_type: '1'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 13
            order: 13
            type: 0
            name: SCH12
            title: '120'
            description: ''
            data_type: '1'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 14
            order: 14
            type: 0
            name: SCH13
            title: '140'
            description: ''
            data_type: '1'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 15
            order: 15
            type: 0
            name: SCH14
            title: '160'
            description: ''
            data_type: '1'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 16
            order: 16
            type: 0
            name: SCH15
            title: STD
            description: ''
            data_type: '1'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 17
            order: 17
            type: 0
            name: SCH16
            title: XS
            description: ''
            data_type: '1'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
        - !!python/object:__main__.PipeField
          __dict__:
            index: 18
            order: 18
            type: 0
            name: SCH17
            title: XXS
            description: ''
            data_type: '1'
            required: false
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            order: null
            data_type: null
            name: null
            title: null
            description: null
            type: null
            index: null
            required: null
          __pydantic_private__: null
      items:
        ? ''
        : !!python/object:__main__.ThicknessItem
          __dict__:
            id: 110
            code: ''
            name: ''
            description: ''
            status: active
            value: 0.0
            unit: mm
          __pydantic_extra__: null
          __pydantic_fields_set__: !!set
            unit: null
            value: null
            name: null
            description: null
            code: null
            id: null
            status: null
          __pydantic_private__: null
    __pydantic_extra__: null
    __pydantic_fields_set__: !!set
      unit: null
      base_id: null
      modified_time: null
      created_by: null
      name: null
      max_row_id: null
      description: null
      fields: null
      default_part_number: null
      version: null
      items: null
      id: null
      type: null
      created_time: null
      edited_by: null
      status: null
    __pydantic_private__:
      _next_id: 1
