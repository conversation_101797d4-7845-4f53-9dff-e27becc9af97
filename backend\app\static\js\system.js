// 格式化字节大小
function formatBytes(bytes) {
  if (bytes === 0) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

// 格式化时间
function formatUptime(seconds) {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);

  let uptime = "";
  if (days > 0) uptime += `${days}天 `;
  if (hours > 0) uptime += `${hours}小时 `;
  if (minutes > 0) uptime += `${minutes}分钟`;

  return uptime.trim();
}

// 更新系统状态
async function updateSystemStatus() {
  try {
    const response = await fetch("/api/system/status");
    const data = await response.json();

    // 更新文件统计
    document.getElementById("totalFiles").textContent =
      data.file_stats.total_files;
    document.getElementById("filesToday").textContent =
      data.file_stats.files_today;
    document.getElementById("rawFiles").textContent = data.file_stats.raw_files;
    document.getElementById("processedFiles").textContent =
      data.file_stats.processed_files;

    // 更新存储统计
    document.getElementById("totalSize").textContent = formatBytes(
      data.storage_stats.total_size
    );
    document.getElementById("rawSize").textContent = formatBytes(
      data.storage_stats.raw_size
    );
    document.getElementById("processedSize").textContent = formatBytes(
      data.storage_stats.processed_size
    );
    document.getElementById(
      "compressionRatio"
    ).textContent = `${data.storage_stats.compression_ratio}%`;

    // 更新处理状态
    document.getElementById("pendingCount").textContent =
      data.processing_stats.pending;
    document.getElementById("processingCount").textContent =
      data.processing_stats.processing;
    document.getElementById("completedCount").textContent =
      data.processing_stats.completed;
    document.getElementById("errorCount").textContent =
      data.processing_stats.errors;

    // 更新数据统计
    document.getElementById("totalRecords").textContent =
      data.data_stats.total_records;
    document.getElementById("totalSegments").textContent =
      data.data_stats.total_segments;
    document.getElementById("lastUpdate").textContent = new Date(
      data.data_stats.last_update
    ).toLocaleString();

    // 更新系统信息
    document.getElementById("systemVersion").textContent =
      data.system_info.version;
    document.getElementById("uptime").textContent = formatUptime(
      data.system_info.uptime
    );
    document.getElementById(
      "memoryUsage"
    ).textContent = `${data.system_info.memory_usage}%`;
    document.getElementById(
      "cpuUsage"
    ).textContent = `${data.system_info.cpu_usage}%`;
  } catch (error) {
    console.error("获取系统状态失败:", error);
    showMessage("获取系统状态失败，请稍后重试", "error");
  }
}

// 显示消息
function showMessage(message, type = "info") {
  const messagesContainer = document.getElementById("messages");
  const messageElement = document.createElement("div");
  messageElement.className = `message ${type}`;
  messageElement.textContent = message;

  messagesContainer.appendChild(messageElement);

  // 3秒后自动移除消息
  setTimeout(() => {
    messageElement.remove();
  }, 3000);
}

// 页面加载完成后开始更新系统状态
document.addEventListener("DOMContentLoaded", () => {
  // 立即更新一次
  updateSystemStatus();

  // 每30秒更新一次
  setInterval(updateSystemStatus, 30000);
});
