"""
数据模型包

此包包含所有数据模型定义，包括：
- 基础模型（Base, BaseFile）
- 文件模型（RawFile, ParquetFile）
- 枚举类型（ProcessStatus）
- 关联表（raw_parquet_association）
- 工具函数（get_file_segments）
"""

from .base import Base
from .base_model import BaseModel
from .enums import ProcessStatus

# 导入文件模型
from .file_models import (
    BaseFile,
    RawFile,
    ParquetFile,
    raw_parquet_association,
    get_file_segments
)

# 确保所有模型都在这里导入，便于数据库表创建
__all__ = [
    # 基础模型
    'Base',
    'BaseModel',

    # 枚举类型
    'ProcessStatus',

    # 文件模型
    'BaseFile',
    'RawFile',
    'ParquetFile',
    'raw_parquet_association',

    # 工具函数
    'get_file_segments'
]

# 版本信息
__version__ = '1.0.0'
