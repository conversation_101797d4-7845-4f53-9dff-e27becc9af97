class UploadManager {
  constructor() {
    this.fileList = [];
    this.uploadInProgress = false;
    this.abortController = null;
    this.existingHashes = new Set();
    this.initEventListeners();
    // this.fetchExistingHashes();
  }

  initEventListeners() {
    // 文件选择
    document.getElementById("fileInput").addEventListener("change", async (e) => {
      console.log(`选择了 ${e.target.files.length} 个文件`);
      if (e.target.files.length > 0) {
        await this.addFilesFromInput(e.target.files);
        // 重置input，允许重复选择同一文件
        e.target.value = "";
      }
    });

    // 拖放区域事件
    const dropZone = document.getElementById("dropZone");
    dropZone.addEventListener("dragover", (e) => {
      e.preventDefault();
      dropZone.classList.add("active");
    });

    dropZone.addEventListener("dragleave", () => {
      dropZone.classList.remove("active");
    });

    dropZone.addEventListener("drop", async (e) => {
      e.preventDefault();
      dropZone.classList.remove("active");
      console.log(`拖放了 ${e.dataTransfer.files.length} 个文件`);
      if (e.dataTransfer.files.length > 0) {
        await this.addFilesFromInput(e.dataTransfer.files);
      }
    });

    // 上传按钮
    document.getElementById("uploadBtn").addEventListener("click", () => {
      if (this.uploadInProgress) {
        this.stopUpload();
      } else {
        this.uploadFiles();
      }
    });

    // 点击整个拖放区域触发文件选择
    document.getElementById("dropZone").addEventListener("click", () => {
      document.getElementById("fileInput").click();
    });

    // 阻止点击按钮的传播，避免触发两次
    // document.querySelector(".browse-btn").addEventListener("click", (e) => {
    //   e.stopPropagation();
    //   document.getElementById("fileInput").click();
    // });
  }

  async fetchExistingHashes() {
    try {
      const response = await fetch("/api/v1/files/hashes");
      if (!response.ok) {
        console.warn("获取文件哈希失败，继续上传流程");
        return;
      }

      const data = await response.json();
      if (data && data.status === "success" && Array.isArray(data.hashes)) {
        this.existingHashes = new Set(data.hashes);
        console.log(`已加载 ${this.existingHashes.size} 个文件哈希`);
      } else {
        console.warn("获取文件哈希返回数据格式不正确，继续上传流程");
      }
    } catch (error) {
      console.warn("获取文件哈希出错，继续上传流程:", error);
    }
  }

  async addFilesFromInput(fileInputList) {
    // 打印文件数量和类型，以检查输入
    console.log(`添加文件，数量: ${fileInputList.length}`);
    console.log(
      `文件列表类型: ${Object.prototype.toString.call(fileInputList)}`
    );

    // FileList 对象是类数组对象，转换为真正的数组
    const filesArray = Array.from(fileInputList);
    console.log(`转换后数组长度: ${filesArray.length}`);

    // 过滤出CSV文件并排除重复文件名
    const existingFileNames = this.fileList.map((f) => f.name);

    const validFiles = filesArray.filter((file) => {
      // 检查是否为CSV文件
      const isCSV = file.name.toLowerCase().endsWith(".csv");
      if (!isCSV) {
        console.log(`跳过非CSV文件: ${file.name}`);
        this.showMessage("warning", `跳过非CSV文件: ${file.name}`);
        return false;
      }

      // 检查是否已存在同名文件
      const isDuplicate = existingFileNames.includes(file.name);
      if (isDuplicate) {
        console.log(`跳过重复文件: ${file.name}`);
        this.showMessage("info", `跳过重复文件: ${file.name}`);
        return false;
      }

      // 检查文件大小是否为0
      if (file.size === 0) {
        console.log(`跳过空文件: ${file.name}`);
        this.showMessage("warning", `跳过空文件: ${file.name}`);
        return false;
      }

      return true;
    });

    // 如果有有效文件，获取现有文件哈希
    if (validFiles.length > 0) {
      await this.fetchExistingHashes();
    }

    // 逐个添加文件到列表
    validFiles.forEach((file) => {
      this.fileList.push({
        file: file,
        name: file.name,
        size: file.size,
        status: "waiting",
        message: "",
      });
    });

    // 更新文件计数
    this.updateFileCount();

    // 更新界面
    this.renderFileList();
    this.updateUploadButton();
  }

  updateFileCount() {
    const fileCountElem = document.getElementById("fileCount");
    // console.log('更新文件计数:', {
    //     'fileCountElem存在': !!fileCountElem,
    //     'fileList长度': this.fileList.length,
    //     'fileList内容': JSON.stringify(this.fileList.map(f => f.name))
    // });

    if (fileCountElem) {
      const oldText = fileCountElem.textContent;
      fileCountElem.textContent = `${this.fileList.length} 个文件`;
      // console.log(`文件计数从 "${oldText}" 更新为 "${fileCountElem.textContent}"`);
    } else {
      console.error("无法找到ID为fileCount的元素");
      // 尝试用其他方式查找或创建该元素
      const fileCountsInPage = document.querySelectorAll(".file-count");
      console.log(`页面中找到 ${fileCountsInPage.length} 个.file-count元素`);

      if (fileCountsInPage.length > 0) {
        // 如果找到带有file-count类的元素，使用第一个
        fileCountsInPage[0].textContent = `${this.fileList.length} 个文件`;
        console.log("已使用class选择器更新文件计数");
      }
    }
  }

  renderFileList() {
    const fileListElement = document.getElementById("fileList");

    // 打印当前文件列表，检查问题
    console.log(
      "当前文件列表:",
      this.fileList.map((f) => f.name)
    );

    // 清空列表
    fileListElement.innerHTML = "";

    if (this.fileList.length === 0) {
      fileListElement.innerHTML = '<div class="empty-message">未选择文件</div>';
      return;
    }

    // 为每个文件创建列表项
    this.fileList.forEach((fileObj, index) => {
      const fileItem = document.createElement("div");
      fileItem.className = "file-item";
      fileItem.dataset.filename = fileObj.name;

      const fileSize = this.formatFileSize(fileObj.size);

      // 根据文件状态设置不同的显示内容
      let statusText = "等待上传";
      let statusClass = "";

      if (fileObj.status === "duplicate") {
        statusText = "文件已存在";
        statusClass = "status-duplicate";
      } else if (fileObj.status === "uploading") {
        statusText = "上传中...";
        statusClass = "uploading";
      } else if (fileObj.status === "success") {
        statusText = "上传成功";
        statusClass = "status-success";
      } else if (fileObj.status === "error") {
        statusText = fileObj.message || "上传失败";
        statusClass = "status-error";
      } else if (fileObj.status === "cancelled") {
        statusText = "已取消";
        statusClass = "status-cancelled";
      }

      fileItem.innerHTML = `
                <div class="file-info">
                    <div class="file-name">${fileObj.name}</div>
                    <div class="file-meta">
                        <span class="file-size">${fileSize}</span>
                    </div>
                </div>
                <div class="file-status ${statusClass}">${statusText}</div>
                <button class="delete-btn" data-index="${index}">×</button>
            `;

      fileListElement.appendChild(fileItem);

      // 添加删除按钮事件
      const deleteBtn = fileItem.querySelector(".delete-btn");
      deleteBtn.addEventListener("click", (e) => {
        this.removeFile(index);
        e.stopPropagation();
      });
    });
  }

  removeFile(index) {
    // 移除指定索引的文件
    if (index >= 0 && index < this.fileList.length) {
      console.log(`移除文件: ${this.fileList[index].name}`);
      this.fileList.splice(index, 1);
      this.updateFileCount();
      this.renderFileList();
      this.updateUploadButton();
    }
  }

  updateUploadButton() {
    const uploadBtn = document.getElementById("uploadBtn");

    if (this.uploadInProgress) {
      uploadBtn.textContent = "停止上传";
      uploadBtn.classList.add("btn-danger");
      uploadBtn.disabled = false;
    } else {
      uploadBtn.textContent = "开始上传";
      uploadBtn.classList.remove("btn-danger");

      // 检查是否有等待上传的文件
      const hasUploadableFiles = this.fileList.some(
        (file) => file.status === "waiting" || !file.status
      );

      // 如果没有可上传的文件，禁用按钮
      uploadBtn.disabled = !hasUploadableFiles;

      console.log(`开始上传按钮状态: ${!hasUploadableFiles ? "禁用" : "启用"}`);
    }
  }

  findFileItemElement(filename) {
    return document.querySelector(`.file-item[data-filename="${filename}"]`);
  }

  findFileByName(filename) {
    return this.fileList.find((file) => file.name === filename);
  }

  formatFileSize(bytes) {
    const units = ["B", "KB", "MB", "GB"];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  async uploadFiles() {
    // 过滤出可上传的文件
    const filesToUpload = this.fileList.filter(
        (fileObj) => fileObj.status === "waiting" || !fileObj.status
    );

    console.log(`开始上传 ${filesToUpload.length} 个文件`);

    if (filesToUpload.length === 0) {
      this.showMessage("warning", "没有可上传的文件");
      return;
    }

    this.uploadInProgress = true;
    this.abortController = new AbortController();
    this.updateUploadButton();

    // 更新所有要上传的文件状态为"上传中"
    filesToUpload.forEach((fileObj) => {
      fileObj.status = "uploading";
      const fileItem = this.findFileItemElement(fileObj.name);
      if (fileItem) {
        fileItem.querySelector(".file-status").textContent = "上传中...";
        fileItem.querySelector(".file-status").className =
          "file-status uploading";
      }
    });

    this.renderFileList();

    const formData = new FormData();
    filesToUpload.forEach((fileObj) => {
      formData.append("files", fileObj.file);
    });

    try {
        const response = await fetch("/files/upload", {  // 更新上传路径
            method: "POST",
            body: formData,
            signal: this.abortController.signal,
        });

        const result = await response.json();

        if (result.status === "success") {
          console.log("上传结果:", result);
          result.files.forEach((fileResult) => {
            const fileObj = this.findFileByName(fileResult.filename);
            if (fileObj) {
              if (fileResult.message === "文件已存在") {
                fileObj.status = "duplicate";
                fileObj.message = "文件已存在";
              } else {
                fileObj.status = "success";
                fileObj.hash = fileResult.hash;
              }
            }
          });

          // 显示成功消息
          this.showMessage("success", `成功处理 ${result.files.length} 个文件`);

          // 刷新文件列表
          this.renderFileList();
        } else {
          throw new Error(result.message);
        }
    } catch (error) {
        console.error("上传失败:", error);
        this.showMessage("error", `上传失败: ${error.message}`);

        // 更新文件状态为"上传失败"
        filesToUpload.forEach((fileObj) => {
          fileObj.status = "error";
          fileObj.message = error.message;
          const fileItem = this.findFileItemElement(fileObj.name);
          if (fileItem) {
            fileItem.querySelector(".file-status").textContent =
              fileObj.message;
            fileItem.querySelector(".file-status").className =
              "file-status status-error";
          }
        });

        // 刷新文件列表
        this.renderFileList();
    } finally {
        this.uploadInProgress = false;
        this.abortController = null;
        this.updateUploadButton();
    }
  }

  stopUpload() {
    if (this.abortController) {
      this.abortController.abort();
      this.showMessage("info", "正在取消上传...");
    }
  }

  showMessage(type, message) {
    console.log(`显示消息: [${type}] ${message}`);

    const messageElement = document.getElementById("messages");
    const messageItem = document.createElement("div");
    messageItem.className = `message message-${type}`;
    messageItem.textContent = message;

    messageElement.appendChild(messageItem);

    // 5秒后自动移除消息
    setTimeout(() => {
      messageItem.classList.add("fade-out");
      setTimeout(() => {
        messageElement.removeChild(messageItem);
      }, 500);
    }, 5000);
  }
}

// 初始化上传管理器
document.addEventListener("DOMContentLoaded", () => {
  console.log("页面加载完成，初始化上传管理器");
  window.uploadManager = new UploadManager();
});