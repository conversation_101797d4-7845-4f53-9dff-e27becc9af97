"""
管道标准数据管理模块

此模块提供了管道标准相关的数据，包括：
- GB-B标准管道尺寸
- 管道标准查询功能
"""

from dataclasses import dataclass
from typing import Dict, Optional, List


@dataclass
class PipeStandard:
    """管道标准数据类"""
    nom: int  # 公称直径(DN)
    od: float  # 外径(mm)
    description: str  # 描述
    part_number: str  # 零件号
    standard: str  # 标准号


# GB-B标准管道尺寸数据
GB_B_PIPE_SIZES: Dict[int, PipeStandard] = {
    10: PipeStandard(10, 14, "DN10", "0003", "GB-B"),
    15: <PERSON><PERSON><PERSON>tandar<PERSON>(15, 18, "DN15", "0004", "GB-B"),
    20: <PERSON><PERSON><PERSON>tandar<PERSON>(20, 25, "DN20", "0005", "GB-B"),
    25: PipeStandard(25, 32, "DN25", "0006", "GB-B"),
    32: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(32, 38, "DN32", "0007", "GB-B"),
    40: Pipe<PERSON>tandard(40, 45, "DN40", "0008", "GB-B"),
    50: PipeStandard(50, 57, "DN50", "0009", "GB-B"),
    65: PipeStandard(65, 76, "DN65", "0010", "GB-B"),
    80: PipeStandard(80, 89, "DN80", "0011", "GB-B"),
    100: PipeStandard(100, 108, "DN100", "0013", "GB-B"),
    125: PipeStandard(125, 133, "DN125", "0014", "GB-B"),
    150: PipeStandard(150, 159, "DN150", "0015", "GB-B"),
    200: PipeStandard(200, 219, "DN200", "0016", "GB-B"),
    250: PipeStandard(250, 273, "DN250", "0017", "GB-B"),
    300: PipeStandard(300, 325, "DN300", "0018", "GB-B"),
    350: PipeStandard(350, 377, "DN350", "0019", "GB-B"),
    400: PipeStandard(400, 426, "DN400", "0020", "GB-B"),
    450: PipeStandard(450, 480, "DN450", "0021", "GB-B"),
    500: PipeStandard(500, 530, "DN500", "0022", "GB-B"),
    600: PipeStandard(600, 630, "DN600", "0024", "GB-B"),
    700: PipeStandard(700, 720, "DN700", "0025", "GB-B"),
    800: PipeStandard(800, 820, "DN800", "0026", "GB-B"),
    900: PipeStandard(900, 920, "DN900", "0027", "GB-B"),
    1000: PipeStandard(1000, 1020, "DN1000", "0028", "GB-B"),
    1200: PipeStandard(1200, 1220, "DN1200", "0029", "GB-B"),
    1400: PipeStandard(1400, 1420, "DN1400", "0030", "GB-B"),
    1600: PipeStandard(1600, 1620, "DN1600", "0031", "GB-B"),
    1800: PipeStandard(1800, 1820, "DN1800", "0032", "GB-B"),
    2000: PipeStandard(2000, 2020, "DN2000", "0033", "GB-B")
}


def get_pipe_standard(nom: int, standard: str = "GB-B") -> Optional[PipeStandard]:
    """
    根据公称直径和标准获取管道尺寸信息
    
    Args:
        nom: 公称直径(DN)
        standard: 标准号，默认为"GB-B"
        
    Returns:
        PipeStandard: 管道尺寸信息，如果不存在则返回None
    """
    if standard == "GB-B":
        return GB_B_PIPE_SIZES.get(nom)
    return None


def get_standard_od(nom: int, standard: str = "GB-B") -> Optional[float]:
    """
    根据公称直径和标准获取管道外径
    
    Args:
        nom: 公称直径(DN)
        standard: 标准号，默认为"GB-B"
        
    Returns:
        float: 管道外径(mm)，如果不存在则返回None
    """
    pipe_standard = get_pipe_standard(nom, standard)
    return pipe_standard.od if pipe_standard else None


def get_all_standards() -> Dict[str, Dict[int, PipeStandard]]:
    """
    获取所有标准的所有管道尺寸信息
    
    Returns:
        Dict[str, Dict[int, PipeStandard]]: 所有标准的所有管道尺寸信息
    """
    return {
        "GB-B": GB_B_PIPE_SIZES.copy()
    }


def get_available_standards() -> List[str]:
    """
    获取所有可用的标准号
    
    Returns:
        List[str]: 所有可用的标准号
    """
    return ["GB-B"]
