:root {
    --primary-color: #1a73e8;
    --primary-hover: #1765cc;
    --danger-color: #ea4335;
    --success-color: #34a853;
    --warning-color: #fbbc05;
    --info-color: #4285f4;
    --text-color: #3c4043;
    --border-color: #dadce0;
    --hover-color: #f7f9fc;
    --background-color: #f8f9fa;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Google Sans', 'Segoe UI', Roboto, Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.5;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--border-color);
}

.header h1 {
    font-size: 22px;
    font-weight: 500;
    color: var(--primary-color);
}

.nav-links {
    display: flex;
    gap: 16px;
}

.nav-link {
    color: var(--primary-color);
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 4px;
    transition: background-color 0.2s;
    font-weight: 500;
}

.nav-link:hover {
    background-color: var(--hover-color);
}

.upload-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.upload-area {
    position: relative;
    margin-bottom: 20px;
    height: 200px;
}

.file-input {
    display: none;
}

.drop-zone {
    padding: 40px;
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    text-align: center;
    transition: all 0.3s;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: var(--hover-color);
    cursor: pointer;
}

.drop-zone.active {
    border-color: var(--primary-color);
    background-color: rgba(26, 115, 232, 0.05);
}

.drop-zone-prompt {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.drop-zone .icon {
    font-size: 48px;
    margin-bottom: 16px;
    color: var(--primary-color);
}

.drop-zone p {
    margin-bottom: 16px;
    color: #5f6368;
    font-size: 14px;
}

.browse-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background-color 0.2s;
}

.browse-btn:hover {
    background-color: var(--primary-hover);
}

.file-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.file-list-header h2 {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-color);
}

.file-count {
    font-size: 14px;
    color: #5f6368;
    font-weight: 400;
}

.file-list {
    max-height: 300px;
    overflow-y: auto;
    padding: 8px;
    background: white;
    border-radius: 4px;
    border: 1px solid var(--border-color);
}

.empty-message {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100px;
    color: #80868b;
    font-size: 14px;
}

.file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    margin-bottom: 8px;
    border-radius: 4px;
    background-color: var(--hover-color);
    transition: background-color 0.2s;
}

.file-item:hover {
    background-color: #f1f3f4;
}

.file-info {
    flex: 1;
    overflow: hidden;
    margin-right: 16px;
}

.file-name {
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 4px;
    font-size: 14px;
}

.file-meta {
    font-size: 12px;
    color: #5f6368;
}

.file-status {
    font-size: 12px;
    padding: 4px 8px;
    margin-right: 8px;
    border-radius: 16px;
    background-color: #f1f3f4;
    color: #5f6368;
    white-space: nowrap;
}

.status-success {
    color: var(--success-color);
    background-color: rgba(52, 168, 83, 0.1);
}

.status-error {
    color: var(--danger-color);
    background-color: rgba(234, 67, 53, 0.1);
}

.status-duplicate {
    color: var(--warning-color);
    background-color: rgba(251, 188, 5, 0.1);
}

.status-cancelled {
    color: var(--info-color);
    background-color: rgba(66, 133, 244, 0.1);
}

.uploading {
    color: var(--primary-color);
    background-color: rgba(26, 115, 232, 0.1);
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 0.6; }
    50% { opacity: 1; }
    100% { opacity: 0.6; }
}

.delete-btn {
    background: none;
    border: none;
    color: #80868b;
    font-size: 16px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 50%;
    transition: all 0.2s;
}

.delete-btn:hover {
    color: var(--danger-color);
    background-color: rgba(234, 67, 53, 0.1);
}

.upload-actions {
    display: flex;
    justify-content: center;
    margin: 8px 0;
}

.upload-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 24px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background-color 0.2s;
    min-width: 150px;
}

.upload-btn:hover:not(:disabled) {
    background-color: var(--primary-hover);
}

.upload-btn:disabled {
    background-color: #dadce0;
    color: #80868b;
    cursor: not-allowed;
}

.btn-danger {
    background-color: var(--danger-color);
}

.btn-danger:hover {
    background-color: #d93025;
}

.messages-container {
    margin-bottom: 20px;
}

.message {
    padding: 12px 16px;
    margin-bottom: 10px;
    border-radius: 4px;
    position: relative;
    animation: slideIn 0.3s ease-out;
    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.1);
    font-size: 14px;
}

.message-success {
    background-color: rgba(52, 168, 83, 0.1);
    color: var(--success-color);
    border-left: 4px solid var(--success-color);
}

.message-error {
    background-color: rgba(234, 67, 53, 0.1);
    color: var(--danger-color);
    border-left: 4px solid var(--danger-color);
}

.message-info {
    background-color: rgba(66, 133, 244, 0.1);
    color: var(--info-color);
    border-left: 4px solid var(--info-color);
}

.message-warning {
    background-color: rgba(251, 188, 5, 0.1);
    color: var(--warning-color);
    border-left: 4px solid var(--warning-color);
}

.fade-out {
    animation: fadeOut 0.5s ease-out forwards;
}

.upload-info {
    background-color: #f8f9fa;
    padding: 16px;
    border-radius: 4px;
    font-size: 13px;
    color: #5f6368;
    margin-top: 8px;
}

.info-title {
    font-weight: 500;
    margin-bottom: 8px;
    color: var(--text-color);
}

.upload-info ul {
    list-style-type: none;
    padding-left: 8px;
}

.upload-info li {
    margin-bottom: 6px;
    position: relative;
    padding-left: 16px;
}

.upload-info li:before {
    content: "•";
    color: var(--primary-color);
    position: absolute;
    left: 0;
    font-size: 16px;
}

@keyframes slideIn {
    from { transform: translateY(-10px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@media (max-width: 600px) {
    .header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
    
    .nav-links {
        width: 100%;
    }
    
    .upload-area {
        height: 160px;
    }
    
    .drop-zone {
        padding: 20px;
    }
    
    .drop-zone .icon {
        font-size: 36px;
    }
}