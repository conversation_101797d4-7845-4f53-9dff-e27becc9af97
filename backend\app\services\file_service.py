# 标准库导入
import asyncio
import hashlib
import json
import os
import time
from pathlib import Path
from typing import Optional, Tuple, List, Dict, Any, Union

# 第三方库导入
import aiofiles
import chardet
import pandas as pd
from fastapi import UploadFile
from sqlalchemy.orm import Session

# 本地应用导入
from ..models import RawFile, ParquetFile, ProcessStatus
from ..utils.file_utils import FileUtils
from .base_service import BaseService
from .parquet_service import ParquetService
from .timeseries_service import TimeSeriesService
from ..core import logger
from .file_analyzer import FileAnalyzer

# from ..models.parquet_file import ParquetFile
# from fastapi import UploadFile
# from sqlalchemy import func, or_, and_
# from datetime import datetime

class FileService(BaseService):
    """处理文件上传、保存和管理的服务"""
    
    def __init__(self, raw_dir: str, processed_dir: str):
        """初始化文件服务"""
        super().__init__()
        # 确保目录是 Path 对象
        self.raw_dir = Path(raw_dir)
        self.processed_dir = Path(processed_dir)
        self.parquet_service = ParquetService(processed_dir)
        self.timeseries_service = TimeSeriesService(processed_dir)
        self.analyzer = FileAnalyzer()
        
        # 确保目录存在
        self.raw_dir.mkdir(parents=True, exist_ok=True)
        self.processed_dir.mkdir(parents=True, exist_ok=True)
        
        # 不再初始化 logger，使用从 config.py 导入的全局 logger

        logger.info(f"文件服务初始化 - 原始文件目录: {self.raw_dir}, 处理后文件目录: {self.processed_dir}")
        
    async def calculate_file_hash(self, file: Union[UploadFile, Path]) -> str:
        """计算文件的SHA256哈希值"""
        try:
            sha256_hash = hashlib.sha256()
            
            if isinstance(file, Path):
                # 处理文件路径对象
                async with aiofiles.open(file, 'rb') as f:
                    total_bytes = 0
                    while True:
                        chunk = await f.read(8192)
                        if not chunk:
                            break
                        sha256_hash.update(chunk)
                        total_bytes += len(chunk)
                    logger.info(f"已读取 {total_bytes} 字节用于计算哈希值")
            else:
                # 处理 UploadFile 对象
                try:
                    current_position = await file.seek(0)
                except Exception as e:
                    logger.error(f"无法定位文件位置: {str(e)}")
                    current_position = 0

                bytes_read = 0
                try:
                    while True:
                        chunk = await file.read(8192)
                        if not chunk:
                            break
                        sha256_hash.update(chunk)
                        bytes_read += len(chunk)

                    logger.info(f"已读取 {bytes_read} 字节用于计算哈希值")

                    # 恢复文件位置
                    await file.seek(0)
                except Exception as e:
                    logger.error(f"计算哈希值失败: {str(e)}")
                    # 返回一个基于文件名的备用哈希值
                    return hashlib.sha256(file.filename.encode()).hexdigest()
            
            return sha256_hash.hexdigest()

        except Exception as e:
            logger.error(f"哈希值计算过程出错: {str(e)}")
            # 确保总是返回一个字符串
            return f"error-hash-{int(time.time())}"
    
    async def check_file_exists(self, file_hash: str, db: Session) -> Optional[RawFile]:
        """检查是否存在相同哈希值的文件"""
        return db.query(RawFile).filter(RawFile.file_hash == file_hash).first()

    async def save_upload_file(self, file: UploadFile, db: Session) -> Tuple[Optional[RawFile], str]:
        """
        保存上传的文件并记录到数据库
        返回: (文件记录, 消息)
        """
        file_path = None
        file_record = None
        
        try:
            # 确保目录存在
            await self.ensure_directory(self.raw_dir)
            
            # 基本参数检查
            if not file or not file.filename:
                logger.error("文件对象为None或文件名为空")
                return None, "error"
            
            # 构建文件保存路径
            file_path = self.raw_dir / file.filename
            
            # 保存文件
            await file.seek(0)
            save_result = await self.save_file_chunks(file, file_path)
            
            if not save_result["success"]:
                return None, "error"
                
            # 检查是否存在相同哈希值的文件
            existing_file = await self.check_file_exists(save_result["file_hash"], db)
            if existing_file:
                logger.warning(f"文件已存在: {file.filename} (hash: {save_result['file_hash']})")
                return existing_file, "duplicate"
            
            # 分析文件格式 - 使用FileAnalyzer实例
            file_format = await self.analyzer.analyze_file(file_path)
            
            # 准备元数据
            metadata = {
                "columns": file_format.get("columns"),
                "row_count": file_format.get("row_count")
            }
            metadata = {k: v for k, v in metadata.items() if v is not None}
            metadata_json = json.dumps(metadata) if metadata else None
            
            # 创建数据库记录
            file_record = RawFile(
                filename=file.filename,
                file_path=str(file_path.resolve()),
                file_size=save_result["file_size"],
                file_hash=save_result["file_hash"],
                mime_type=file.content_type or "application/octet-stream",
                status=ProcessStatus.PENDING,
                encoding=file_format.get("encoding"),
                delimiter=file_format.get("delimiter"),
                is_binary=file_format.get("is_binary", False),
                file_metadata=metadata_json
            )
            
            db.add(file_record)
            db.commit()
            db.refresh(file_record)
            
            return file_record, "success"
                
        except Exception as e:
            logger.error(f"处理文件时出错: {str(e)}")
            
            # 处理异常情况
            if file_path and file_path.exists():
                try:
                    file_path.unlink()  # 删除失败的文件
                except Exception:
                    pass
            
            if file_record and hasattr(file_record, 'id') and file_record.id:
                # 更新已创建记录的状态
                file_record.status = ProcessStatus.ERROR
                file_record.error_message = str(e)
                db.commit()
                return file_record, "error"
            
            raise

    async def soft_delete_file(self, file_id: int, reason: str = None, deleted_by: str = None, db: Session = None) -> bool:
        """
        软删除文件记录
        :param file_id: 文件ID
        :param reason: 删除原因
        :param deleted_by: 删除操作者
        :param db: 数据库会话
        :return: 是否成功删除
        """
        try:
            # 查找文件记录
            file_record = await self.get_file_by_id(file_id, db)
            if not file_record:
                logger.warning(f"尝试删除不存在的文件，ID: {file_id}")
                return False

            # 如果已经是删除状态，不再处理
            if file_record.is_deleted:
                logger.warning(f"文件已经是删除状态，ID: {file_id}")
                return True

            # 调用 ParquetService 进行软删除处理
            # 这将处理文件关联并处理相关的 Parquet 文件
            affected_parquet_files = await self.parquet_service.delete_rawfile_data(file_record, db)
            
            # 更新文件记录的软删除状态
            file_record.is_deleted = True
            file_record.deleted_at = datetime.now()
            file_record.deletion_reason = reason
            file_record.deleted_by = deleted_by
            db.commit()
            
            logger.info(
                f"已软删除文件记录，ID: {file_id}，影响了 {len(affected_parquet_files)} 个Parquet文件")
            return True
        except Exception as e:
            logger.error(f"软删除文件过程中出错: {str(e)}")
            db.rollback()
            raise

    async def list_files(self, db: Session, status: Optional[str] = None, limit: int = 100, offset: int = 0) -> Tuple[List[RawFile], int]:
        """
        获取文件列表，支持状态过滤和分页
        :param db: 数据库会话
        :param status: 可选的状态过滤
        :param limit: 返回的最大记录数
        :param offset: 起始偏移量
        :return: (文件列表, 总数)
        """
        query = db.query(RawFile)
        
        # 应用状态过滤
        if status:
            query = query.filter(RawFile.status == status)
        
        # 获取总数
        total = query.count()
        
        # 应用分页并获取结果
        files = query.order_by(RawFile.id.desc()).offset(offset).limit(limit).all()
        
        return files, total
    
    async def get_file_by_id(self, file_id: int, db: Session) -> Optional[RawFile]:
        """根据ID获取文件"""
        return db.query(RawFile).filter(RawFile.id == file_id).first()
    
    async def get_files_by_status(self, status: str, db: Session) -> List[RawFile]:
        """获取指定状态的文件"""
        return db.query(RawFile).filter(RawFile.status == status).all()
    
    async def update_file_status(self, file_id: int, status: str, error_message: Optional[str], db: Session) -> Optional[RawFile]:
        """更新文件状态"""
        file = await self.get_file_by_id(file_id, db)
        if not file:
            return None
            
        file.status = status
        if error_message:
            file.error_message = error_message
            
        db.commit()
        db.refresh(file)
        return file
    
    async def get_file_download_info(self, file_id: int, db: Session) -> Tuple[Optional[str], Optional[str]]:
        """
        获取文件下载信息
        :param file_id: 文件ID
        :param db: 数据库会话
        :return: (文件路径, 文件名)
        """
        file = await self.get_file_by_id(file_id, db)
        if not file:
            return None, None
            
        return file.file_path, file.filename
    
    async def search_files(self, db: Session, query: str, limit: int = 100) -> List[RawFile]:
        """
        搜索文件
        :param db: 数据库会话
        :param query: 搜索关键词
        :param limit: 返回的最大记录数
        :return: 文件列表
        """
        search_term = f"%{query}%"
        
        files = db.query(RawFile).filter(
            or_(
                RawFile.filename.ilike(search_term),
                RawFile.file_path.ilike(search_term)
            )
        ).order_by(RawFile.id.desc()).limit(limit).all()
        
        return files 

    # 增加一个查询已删除文件的方法
    async def list_deleted_files(self, db: Session, limit: int = 100, offset: int = 0) -> Tuple[List[RawFile], int]:
        """
        获取已删除的文件列表
        :param db: 数据库会话
        :param limit: 限制返回的记录数
        :param offset: 偏移量
        :return: 已删除文件列表和总数
        """
        try:
            # 查询已软删除的文件
            query = db.query(RawFile).filter(RawFile.is_deleted == True)

            # 获取总数
            total = query.count()

            # 获取分页数据
            files = query.order_by(RawFile.deleted_at.desc()).offset(
                offset).limit(limit).all()

            return files, total
        except Exception as e:
            logger.error(f"获取已删除文件列表失败: {str(e)}")
            raise
