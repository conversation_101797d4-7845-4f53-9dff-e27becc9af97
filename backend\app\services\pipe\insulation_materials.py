import yaml
import os
from pathlib import Path
from typing import Dict, Any
from .data_models import InsulationLayer_material


class InsulationMaterial:
    """保温材料加载器"""

    def __init__(self, materials_file: str = None):
        """
        初始化保温材料加载器
        
        Args:
            materials_file: 材料数据文件路径，默认为当前目录下的dataset/insulation_materials.yaml
        """
        if materials_file is None:
            materials_file = os.path.join(os.path.dirname(
                __file__), 'dataset', 'insulation_materials.yaml')
        self.materials_file = Path(materials_file)
        self._materials: Dict[str, InsulationLayer_material] = {}
        self._load_all_materials()

    def _load_all_materials(self) -> None:
        """加载所有保温材料"""
        if not self.materials_file.exists():
            raise FileNotFoundError(f"材料文件不存在: {self.materials_file}")

        with open(self.materials_file, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)

        if 'materials' not in data:
            raise ValueError("材料文件格式错误：缺少'materials'字段")

        for material_id, material_data in data['materials'].items():
            # 验证数据格式
            required_fields = ['name', 'sub_name', 'density', 'max_temp',
                               'recommended_temp', 'k_70', 'k_eq']
            for field in required_fields:
                if field not in material_data:
                    raise ValueError(f"材料 {material_id} 数据缺少必要字段: {field}")

            # 创建材料对象
            material = InsulationLayer_material(
                material_name=material_data['name'],
                sub_material_name=material_data['sub_name'],
                density=float(material_data['density']),
                max_temperature=float(material_data['max_temp']),
                recommended_temperature=float(
                    material_data['recommended_temp']),
                thermal_conductivity_70=float(material_data['k_70']),
                thermal_conductivity_eq=material_data['k_eq'],
                compressive_strength=material_data.get('compressive_strength')
            )

            self._materials[material_id] = material

    def get_material(self, material_id: str) -> InsulationLayer_material:
        """
        获取指定的保温材料
        
        Args:
            material_id: 材料ID，对应yaml文件中的键名
            
        Returns:
            InsulationLayer_material: 保温材料对象
            
        Raises:
            KeyError: 材料ID不存在
        """
        if material_id not in self._materials:
            raise KeyError(f"材料ID不存在: {material_id}")
        return self._materials[material_id]

    def get_all_materials(self) -> Dict[str, InsulationLayer_material]:
        """获取所有保温材料"""
        return self._materials.copy()

    def to_dict(self, material_id: str = None) -> Dict[str, Dict[str, Any]]:
        """
        将材料数据转换为字典格式
        
        Args:
            material_id: 材料ID，如果为None则转换所有材料
            
        Returns:
            Dict[str, Dict[str, Any]]: 材料数据字典
        """
        if material_id is not None:
            if material_id not in self._materials:
                raise KeyError(f"材料ID不存在: {material_id}")
            materials = {material_id: self._materials[material_id]}
        else:
            materials = self._materials

        result = {}
        for mid, material in materials.items():
            material_dict = {
                "材料名称": material.material_name,
                "子材料名称": material.sub_material_name,
                "密度": f"{material.density} kg/m³",
                "最高使用温度": f"{material.max_temperature} ℃",
                "推荐使用温度": f"{material.recommended_temperature} ℃",
                "70℃导热系数": f"{material.thermal_conductivity_70} W/m·K",
                "导热系数方程": material.thermal_conductivity_eq.split('\n')
            }
            if material.compressive_strength is not None:
                material_dict["抗压强度"] = f"{material.compressive_strength} MPa"
            result[mid] = material_dict
        return result

    def print_material_info(self, material_id: str = None) -> None:
        """
        打印保温材料信息
        
        Args:
            material_id: 材料ID，如果为None则打印所有材料信息
        """
        material_dict = self.to_dict(material_id)

        for mid, data in material_dict.items():
            print(f"\n材料ID: {mid}")
            for key, value in data.items():
                if isinstance(value, list):
                    print(f"{key}:")
                    for eq in value:
                        print(f"  {eq}")
                else:
                    print(f"{key}: {value}")
            print("-" * 50)

    def get_material_summary(self) -> str:
        """
        获取所有材料的简要信息
        
        Returns:
            str: 材料简要信息
        """
        material_dict = self.to_dict()
        summary = []
        for mid, data in material_dict.items():
            summary.append(
                f"{mid}: {data['材料名称']} ({data['子材料名称']}) - "
                f"密度: {data['密度']}, "
                f"最高温度: {data['最高使用温度']}"
            )
        return "\n".join(summary)
