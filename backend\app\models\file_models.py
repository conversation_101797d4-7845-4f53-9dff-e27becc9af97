"""
文件相关的数据模型定义
包含 RawFile、ParquetFile 以及它们之间的关联表
"""
from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, JSON, ForeignKey, Table, Text, Date
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from typing import Dict, Any, Optional
from datetime import datetime

from .base import Base
from .enums import ProcessStatus


# 原始文件和Parquet文件的多对多关联表
raw_parquet_association = Table(
    'raw_parquet_association',
    Base.metadata,
    Column('raw_file_id', Integer, ForeignKey('raw_files.id'), primary_key=True),
    Column('parquet_file_id', Integer, ForeignKey('parquet_files.id'), primary_key=True)
)

# 基础文件模型 - 只作为基类，不会创建实际表
class BaseFile(Base):
    """基础文件模型，定义共享属性"""
    __abstract__ = True
    
    id = Column(Integer, primary_key=True, index=True)
    filename = Column(String, index=True)
    file_path = Column(String, unique=True)
    file_size = Column(Float)
    file_hash = Column(String, index=True, unique=True)
    
    # 处理状态
    status = Column(String)
    error_message = Column(String, nullable=True)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 软删除相关字段
    is_deleted = Column(Boolean, default=False, index=True)
    deleted_at = Column(DateTime(timezone=True), nullable=True)
    deleted_by = Column(String(100), nullable=True)
    deletion_reason = Column(Text, nullable=True)

    def to_dict(self) -> Dict[str, Any]:
        """基础的字典转换方法"""
        return {
            "id": self.id,
            "filename": self.filename,
            "file_path": self.file_path,
            "file_size": self.file_size,
            "file_hash": self.file_hash,
            "status": self.status,
            "error_message": self.error_message,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "is_deleted": self.is_deleted,
            "deleted_at": self.deleted_at.isoformat() if self.deleted_at else None,
            "deleted_by": self.deleted_by,
            "deletion_reason": self.deletion_reason
        }
    
    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}(id={self.id}, filename='{self.filename}')>"

class RawFile(BaseFile):
    """原始文件记录"""
    __tablename__ = "raw_files"
    
    # 设置默认状态
    status = Column(String, default=ProcessStatus.PENDING)
    
    # 类型信息
    mime_type = Column(String)
    upload_time = Column(DateTime(timezone=True), server_default=func.now())
    
    # 数据分析相关
    encoding = Column(String(20), nullable=True)
    delimiter = Column(String(5), nullable=True)
    is_binary = Column(Boolean, default=False)
    file_metadata = Column(Text, nullable=True)  # JSON格式存储
    
    # 数据时间范围
    time_range_start = Column(DateTime, nullable=True)
    time_range_end = Column(DateTime, nullable=True)
    
    # 关联的Parquet文件
    parquet_files = relationship(
        "ParquetFile",
        secondary=raw_parquet_association,
        back_populates="raw_files"
    )
    
    # 处理记录
    last_processed_at = Column(DateTime(timezone=True), nullable=True)
    total_records = Column(Integer, nullable=True)

    # Parquet文件计数
    parquet_count = Column(Integer, nullable=True, default=0)
    
    def to_dict(self) -> Dict[str, Any]:
        """将记录转换为字典"""
        base_dict = super().to_dict()
        base_dict.update({
            "mime_type": self.mime_type,
            "upload_time": self.upload_time.isoformat() if self.upload_time else None,
            "encoding": self.encoding,
            "delimiter": self.delimiter,
            "is_binary": self.is_binary,
            "file_metadata": self.file_metadata,
            "total_records": self.total_records,
            "time_range_start": self.time_range_start.isoformat() if self.time_range_start else None,
            "time_range_end": self.time_range_end.isoformat() if self.time_range_end else None,
            "last_processed_at": self.last_processed_at.isoformat() if self.last_processed_at else None,
            "parquet_count": self.parquet_count
        })
        return base_dict


class ParquetFile(BaseFile):
    __tablename__ = "parquet_files"
    
    # 数据日期
    date = Column(Date, nullable=True, index=True)
    
    # 统计信息
    record_count = Column(Integer, nullable=True)
    
    # 时间范围 - 统一使用time_range_start/time_range_end
    time_range_start = Column(DateTime, nullable=True)
    time_range_end = Column(DateTime, nullable=True)
    
    # 关联关系 - 通过多对多关联表
    raw_files = relationship(
        "RawFile",
        secondary=raw_parquet_association,
        back_populates="parquet_files"
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """将记录转换为字典"""
        base_dict = super().to_dict()
        base_dict.update({
            "time_range_start": self.time_range_start.isoformat() if self.time_range_start else None,
            "time_range_end": self.time_range_end.isoformat() if self.time_range_end else None,
            "record_count": self.record_count,
            "date": str(self.date) if self.date else None
        })
        return base_dict


# 文件相关的辅助函数
def get_file_segments(db_session, raw_file_id=None, parquet_file_id=None):
    """获取文件数据段信息"""
    query = db_session.query(raw_parquet_association)
    
    if raw_file_id:
        query = query.filter_by(raw_file_id=raw_file_id)
    if parquet_file_id:
        query = query.filter_by(parquet_file_id=parquet_file_id)
        
    return query.all() 