from fastapi import APIRouter, HTTPException, UploadFile, File
from typing import List, Optional
import math
from bokeh.plotting import figure
from bokeh.embed import json_item
import json
from pydantic import BaseModel
import csv
from io import StringIO
from fastapi.responses import JSONResponse, PlainTextResponse

pipe_router = APIRouter()


class PipeCalculationRequest(BaseModel):
    data: List[dict]


class PipeCalculationResponse(BaseModel):
    status: str
    data: List[dict]
    message: Optional[str] = None


def calculate_pipe_diameter(flow_rate: float, allowed_velocity: float) -> float:
    """计算管道直径"""
    # 流量从m³/h转换为m³/s
    flow_rate_m3s = flow_rate / 3600
    # 计算直径（m）
    diameter = math.sqrt(4 * flow_rate_m3s / (math.pi * allowed_velocity))
    # 转换为mm
    return diameter * 1000


def calculate_pipe_thickness(pressure: float, diameter: float, material: str) -> float:
    """计算管道壁厚"""
    # 默认许用应力（MPa）
    allowed_stress = 187
    # 压力从MPa转换为Pa
    pressure_pa = pressure * 1e6
    # 直径从mm转换为m
    diameter_m = diameter / 1000
    # 计算壁厚（m）
    thickness = (pressure_pa * diameter_m) / (2 * allowed_stress * 1e6)
    # 转换为mm
    return thickness * 1000


def generate_chart(data: List[float], title: str, ylabel: str) -> str:
    """生成图表并返回JSON字符串"""
    # 创建图表
    p = figure(title=title, width=400, height=300)

    # 添加散点图
    p.scatter(range(1, len(data) + 1), data, size=8)

    # 设置坐标轴标签
    p.xaxis.axis_label = '序号'
    p.yaxis.axis_label = ylabel

    # 将图表转换为JSON
    return json.dumps(json_item(p))


@pipe_router.post("/calculate", response_model=PipeCalculationResponse)
async def calculate_pipe(data: PipeCalculationRequest):
    """计算管道参数"""
    try:
        results = []
        for item in data.data:
            # 获取允许流速（m/s）
            if item["medium"].lower() == "空气":
                allowed_velocity = 50
            else:
                allowed_velocity = 30  # 其他介质的默认值

            # 计算管道直径
            recommended_diameter = calculate_pipe_diameter(
                item["flow"], allowed_velocity)

            # 计算管道壁厚
            recommended_thickness = calculate_pipe_thickness(
                item["pressure"], recommended_diameter, item["material"])

            # 计算实际流速
            actual_velocity = (item["flow"] / 3600) / \
                (math.pi * (recommended_diameter/1000)**2 / 4)

            results.append({
                **item,
                "recommended_diameter": round(recommended_diameter, 2),
                "recommended_thickness": round(recommended_thickness, 2),
                "actual_velocity": round(actual_velocity, 2)
            })

        return {
            "status": "success",
            "data": results
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@pipe_router.post("/import", response_model=PipeCalculationResponse)
async def import_data(file: UploadFile = File(...)):
    """导入CSV数据"""
    try:
        contents = await file.read()
        text = contents.decode("utf-8")
        csv_data = csv.DictReader(StringIO(text))

        data = []
        for row in csv_data:
            data.append({
                "medium": row.get("medium", "空气"),
                "material": row.get("material", "碳钢"),
                "pressure": float(row.get("pressure", 0.6)),
                "temperature": float(row.get("temperature", 25)),
                "flow": float(row.get("flow", 1000))
            })

        return {
            "status": "success",
            "data": data
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@pipe_router.get("/export", response_class=PlainTextResponse)
async def export_data():
    """导出CSV数据"""
    try:
        # 这里应该从数据库或会话中获取数据
        # 目前返回示例数据
        data = [
            {
                "medium": "空气",
                "material": "碳钢",
                "pressure": 0.6,
                "temperature": 25,
                "flow": 1000
            }
        ]

        output = StringIO()
        writer = csv.DictWriter(
            output, fieldnames=["medium", "material", "pressure", "temperature", "flow"])
        writer.writeheader()
        writer.writerows(data)

        return output.getvalue()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
