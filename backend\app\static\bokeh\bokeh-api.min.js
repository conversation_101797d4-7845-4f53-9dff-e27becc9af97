'use strict';
/*!
 * Copyright (c) Anaconda, Inc., and Bokeh Contributors
 * All rights reserved.
 * 
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 * 
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * 
 * Redistributions in binary form must reproduce the above copyright notice,
 * this list of conditions and the following disclaimer in the documentation
 * and/or other materials provided with the distribution.
 * 
 * Neither the name of <PERSON><PERSON><PERSON> nor the names of any contributors
 * may be used to endorse or promote products derived from this software
 * without specific prior written permission.
 * 
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENT<PERSON>, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
 * THE POSSIBILITY OF SUCH DAMAGE.
 */
(function(root, factory) {
  factory(root["Bokeh"], "3.6.3");
})(this, function(Bokeh, version) {
  let define;
  return (function(modules, entry, aliases, externals) {
    const bokeh = typeof Bokeh !== "undefined" ? (version != null ? Bokeh[version] : Bokeh) : null;
    if (bokeh != null) {
      return bokeh.register_plugin(modules, entry, aliases);
    } else {
      throw new Error("Cannot find Bokeh" + (version != null ? " " + version : "") + ". You have to load it prior to loading plugins.");
    }
  })
({
582: function _(t,_,n,o,r){o();t(1).__exportStar(t(583),n)},
583: function _(t,r,o,_,n){_();const i=t(1),s=i.__importStar(t(584));o.LinAlg=s;const a=i.__importStar(t(585));o.Charts=a;const m=i.__importStar(t(588));o.Plotting=m;const p=i.__importStar(t(586));o.Palettes=p,o.Themes=i.__importStar(t(595)),n("Document",t(5).Document),n("sprintf",t(245).sprintf),i.__exportStar(t(587),o),n("f",t(596).f)},
584: function _(r,t,e,n,a){n(),e.is_Numerical=function(r){return(0,u.isNumber)(r)||(0,h.is_Floating)(r)||(0,s.is_NDArray)(r)||(0,u.isArrayable)(r)};const i=r(1);var o=r(9);a("keys",o.keys),a("values",o.values),a("entries",o.entries),a("size",o.size),a("extend",o.extend),i.__exportStar(r(10),e),i.__exportStar(r(40),e),i.__exportStar(r(115),e),i.__exportStar(r(8),e),i.__exportStar(r(26),e);const s=r(30),u=r(8),p=r(10),f=r(13),l=r(115),m=r(26),h=r(11),_=i.__importStar(r(11));!function(r){function t(r){return e(r)/r.length}function e(r){return(0,f.sum)(r)}function n(r){const t=r.length-1,e=new Float64Array(t);for(let n=0;n<t;n++)e[n]=r[n+1]-r[n];return(0,s.ndarray)(e.buffer,{shape:[t],dtype:"float64"})}function a(r,t){const e=(0,h.is_Floating)(r)?r[h.float]():r,n=(0,h.is_Floating)(t)?t[h.float]():t,a=(0,u.isNumber)(e),i=(0,u.isNumber)(n);if(a&&i)return e/n;if(a&&!i)return(0,f.map)(n,(r=>e/r));if(!a&&i)return(0,f.map)(e,(r=>r/n));if((0,s.is_NDArray)(e)&&(0,s.is_NDArray)(n)){if((0,m.is_equal)(e.shape,n.shape)&&e.dtype==n.dtype)return(0,f.map)(e,((r,t)=>r/n[t]));throw new Error("shape or dtype mismatch")}throw new Error("not implemented")}function i(r,t,e){const n=(0,h.is_Floating)(r)?r[h.float]():r,a=(0,h.is_Floating)(t)?t[h.float]():t,i=(0,u.isNumber)(n),o=(0,u.isNumber)(a),p=r=>r?1:0;if(i&&o)return p(n>=a);if(i&&!o)return(0,f.map)(a,(r=>p(e(n,r))));if(!i&&o)return(0,f.map)(n,(r=>p(e(r,a))));if((0,s.is_NDArray)(n)&&(0,s.is_NDArray)(a)){if((0,m.is_equal)(n.shape,a.shape)&&n.dtype==a.dtype)return(0,f.map)(n,((r,t)=>p(e(r,a[t]))));throw new Error("shape or dtype mismatch")}throw new Error("not implemented")}let o;r.pi=Math.PI,r.arange=function(r,t,e=1){const n=(0,p.range)(r,t,e);return(0,s.ndarray)(n,{shape:[n.length],dtype:"float64"})},r.linspace=function(r,t,e=100){const n=(0,p.linspace)(r,t,e);return(0,s.ndarray)(n,{shape:[n.length],dtype:"float64"})},r.mean=t,r.std=function(r){const n=t(r);return Math.sqrt(e((0,f.map)(r,(r=>(r-n)**2)))/r.length)},r.sum=e,r.diff=n,r.sin=function(r){return(0,u.isNumber)(r)?Math.sin(r):(0,h.is_Floating)(r)?Math.sin(r[h.float]()):(0,f.map)(r,(r=>Math.sin(r)))},r.cos=function(r){return(0,u.isNumber)(r)?Math.cos(r):(0,h.is_Floating)(r)?Math.cos(r[h.float]()):(0,f.map)(r,(r=>Math.cos(r)))},r.exp=function(r){return(0,u.isNumber)(r)?Math.exp(r):(0,h.is_Floating)(r)?Math.exp(r[h.float]()):(0,f.map)(r,(r=>Math.exp(r)))},r.sqrt=function(r){return(0,u.isNumber)(r)?Math.sqrt(r):(0,h.is_Floating)(r)?Math.sqrt(r[h.float]()):(0,f.map)(r,(r=>Math.sqrt(r)))},r.factorial=function(r){return(0,u.isNumber)(r)?_.factorial(r):(0,h.is_Floating)(r)?_.factorial(r[h.float]()):(0,f.map)(r,_.factorial)},r.hermite=function(r){const t=_.hermite(r);return r=>(0,u.isNumber)(r)?_.eval_poly(t,r):(0,h.is_Floating)(r)?_.eval_poly(t,r[h.float]()):(0,f.map)(r,(r=>_.eval_poly(t,r)))},r.pos=function(r){return(0,u.isNumber)(r)?+r:(0,h.is_Floating)(r)?+r[h.float]():(0,f.map)(r,(r=>+r))},r.neg=function(r){return(0,u.isNumber)(r)?-r:(0,h.is_Floating)(r)?-r[h.float]():(0,f.map)(r,(r=>-r))},r.add=function(r,t){const e=(0,h.is_Floating)(r)?r[h.float]():r,n=(0,h.is_Floating)(t)?t[h.float]():t,a=(0,u.isNumber)(e),i=(0,u.isNumber)(n);if(a&&i)return e+n;if(a&&!i)return(0,f.map)(n,(r=>e+r));if(!a&&i)return(0,f.map)(e,(r=>r+n));if((0,s.is_NDArray)(e)&&(0,s.is_NDArray)(n)){if((0,m.is_equal)(e.shape,n.shape)&&e.dtype==n.dtype)return(0,f.map)(e,((r,t)=>r+n[t]));throw new Error("shape or dtype mismatch")}throw new Error("not implemented")},r.sub=function(r,t){const e=(0,h.is_Floating)(r)?r[h.float]():r,n=(0,h.is_Floating)(t)?t[h.float]():t,a=(0,u.isNumber)(e),i=(0,u.isNumber)(n);if(a&&i)return e-n;if(a&&!i)return(0,f.map)(n,(r=>e-r));if(!a&&i)return(0,f.map)(e,(r=>r-n));if((0,s.is_NDArray)(e)&&(0,s.is_NDArray)(n)){if((0,m.is_equal)(e.shape,n.shape)&&e.dtype==n.dtype)return(0,f.map)(e,((r,t)=>r-n[t]));throw new Error("shape or dtype mismatch")}throw new Error("not implemented")},r.mul=function(r,t){const e=(0,h.is_Floating)(r)?r[h.float]():r,n=(0,h.is_Floating)(t)?t[h.float]():t,a=(0,u.isNumber)(e),i=(0,u.isNumber)(n);if(a&&i)return e*n;if(a&&!i)return(0,f.map)(n,(r=>e*r));if(!a&&i)return(0,f.map)(e,(r=>r*n));if((0,s.is_NDArray)(e)&&(0,s.is_NDArray)(n)){if((0,m.is_equal)(e.shape,n.shape)&&e.dtype==n.dtype)return(0,f.map)(e,((r,t)=>r*n[t]));throw new Error("shape or dtype mismatch")}throw new Error("not implemented")},r.div=a,r.pow=function(r,t){const e=(0,h.is_Floating)(r)?r[h.float]():r,n=(0,h.is_Floating)(t)?t[h.float]():t,a=(0,u.isNumber)(e),i=(0,u.isNumber)(n);if(a&&i)return e**n;if(a&&!i)return(0,f.map)(n,(r=>e**r));if(!a&&i)return(0,f.map)(e,(r=>r**n));if((0,s.is_NDArray)(e)&&(0,s.is_NDArray)(n)){if((0,m.is_equal)(e.shape,n.shape)&&e.dtype==n.dtype)return(0,f.map)(e,((r,t)=>r**n[t]));throw new Error("shape or dtype mismatch")}throw new Error("not implemented")},r.ge=function(r,t){return i(r,t,((r,t)=>r>=t))},r.le=function(r,t){return i(r,t,((r,t)=>r<=t))},r.gt=function(r,t){return i(r,t,((r,t)=>r>t))},r.lt=function(r,t){return i(r,t,((r,t)=>r<t))},r.where=function(r,t,e){const n=(0,h.is_Floating)(t)?t[h.float]():t,a=(0,h.is_Floating)(e)?e[h.float]():e,i=(0,u.isNumber)(n),o=(0,u.isNumber)(a),p=(()=>{if(i&&o)return r=>0!=r?n:a;if(i&&!o)return(r,t)=>0!=r?n:a[t];if(!i&&o)return(r,t)=>0!=r?n[t]:a;if((0,s.is_NDArray)(n)&&(0,s.is_NDArray)(a)){if((0,m.is_equal)(n.shape,a.shape)&&n.dtype==a.dtype)return(r,t)=>0!=r?n[t]:a[t];throw new Error("shape or dtype mismatch")}throw new Error("not implemented")})();return(0,f.map)(r,p)},r.histogram=function(r,t){const{density:i,bins:o}=t,u=(0,s.ndarray)(o,{dtype:"float64",shape:[o.length]}),p=(0,s.ndarray)((0,f.bin_counts)(r,u),{dtype:"float64",shape:[u.length-1]});if(i){return[a(a(p,n(u)),e(p)),u]}return[p,u]},function(r){class t{constructor(r){this._random=new l.Random(r??Date.now())}normal(r,t,e){const n=this._random.normals(r,t,e);return(0,s.ndarray)(n.buffer,{shape:[e],dtype:"float64"})}}t.__name__="RandomGenerator",r.RandomGenerator=t,r.default_rng=function(r){return new t(r)}}(o=r.random||(r.random={}))}(e.np||(e.np={}))},
585: function _(e,t,l,n,a){n(),l.pie=function(e,t={}){const l=[],n=[];for(let t=0;t<Math.min(e.labels.length,e.values.length);t++)e.values[t]>0&&(l.push(e.labels[t]),n.push(e.values[t]));const a=null!=t.start_angle?t.start_angle:0,o=null!=t.end_angle?t.end_angle:a+2*Math.PI,i=Math.abs(o-a),g=(0,s.sum)(n),p=n.map((e=>e/g)),h=(0,s.cumsum)(p).map((e=>a+i*e)),f=[a].concat(h.slice(0,-1)),m=(0,s.zip)(f,h).map((([e,t])=>(e+t)/2));let b,w;null==t.center?(b=0,w=0):(0,c.isArray)(t.center)?(b=t.center[0],w=t.center[1]):(b=t.center.x,w=t.center.y);const x=null!=t.inner_radius?t.inner_radius:0,y=null!=t.outer_radius?t.outer_radius:1,v=_(t.palette),M=[];for(let e=0;e<p.length;e++)M.push(v[e%v.length]);const R=M.map((e=>(0,r.is_dark)((0,r.color2rgba)(e))?"white":"black"));const S=(x+y)/2;let[k,z]=(0,s.unzip)(m.map((e=>{return l=e,[(t=S)*Math.cos(l),t*Math.sin(l)];var t,l})));k=k.map((e=>e+b)),z=z.map((e=>e+w));const A=m.map((e=>e>=Math.PI/2&&e<=3*Math.PI/2?e+Math.PI:e)),D=new u.ColumnDataSource({data:{labels:l,values:n,percentages:p.map((e=>(0,d.sprintf)("%.2f%%",100*e))),start_angles:f,end_angles:h,text_angles:A,colors:M,text_colors:R,text_cx:k,text_cy:z}}),P=new u.AnnularWedge({x:b,y:w,inner_radius:x,outer_radius:y,start_angle:{field:"start_angles"},end_angle:{field:"end_angles"},line_color:null,line_width:1,fill_color:{field:"colors"}}),C=new u.AnnularWedge({x:b,y:w,inner_radius:x,outer_radius:y,start_angle:{field:"start_angles"},end_angle:{field:"end_angles"},line_color:null,line_width:1,fill_color:{field:"colors"},fill_alpha:.8}),T=new u.GlyphRenderer({data_source:D,glyph:P,hover_glyph:C}),G=new u.Text({x:{field:"text_cx"},y:{field:"text_cy"},text:{field:t.slice_labels??"labels"},angle:{field:"text_angles"},text_align:"center",text_baseline:"middle",text_color:{field:"text_colors"},text_font_size:"12px"}),I=new u.GlyphRenderer({data_source:D,glyph:G}),F=new u.DataRange1d({renderers:[T],range_padding:.2}),H=new u.DataRange1d({renderers:[T],range_padding:.2}),L=new u.Plot({x_range:F,y_range:H});L.add_renderers(T,I);const Q=new u.HoverTool({renderers:[T],tooltips:"<div>@labels</div><div><b>@values</b> (@percentages)</div>"});return L.add_tools(Q),L},l.bar=function(e,t={}){const l=e[0],n=e.slice(1),a=(0,s.transpose)(n),o=a[0].map((e=>e.toString())),r=a.slice(1);let c,d=new u.CategoricalAxis,g=new u.FactorRange({factors:o}),p=new u.CategoricalScale;c=null!=t.axis_number_format?new u.NumeralTickFormatter({format:t.axis_number_format}):new u.BasicTickFormatter;let h=new u.LinearAxis({formatter:c}),f=new u.DataRange1d({start:0}),m=new u.LinearScale;const b=_(t.palette),w=null!=t.stacked&&t.stacked,x=null!=t.orientation?t.orientation:"horizontal",y=[];if(w){const e=[],t=[];for(let n=0;n<r.length;n++){const a=[],i=[];for(let l=0;l<o.length;l++){const s=o[l];0==n?(e.push(0),t.push(r[n][l])):(e[l]+=r[n-1][l],t[l]+=r[n][l]),a.push([s,-.5]),i.push([s,.5])}const c=new u.ColumnDataSource({data:{left:(0,s.copy)(e),right:(0,s.copy)(t),top:i,bottom:a,labels:o,values:r[n],columns:r[n].map((e=>l[n+1]))}}),d=new u.Quad({left:{field:"left"},bottom:{field:"bottom"},right:{field:"right"},top:{field:"top"},line_color:null,fill_color:b[n%b.length]}),_=new u.GlyphRenderer({data_source:c,glyph:d});y.push(_)}}else{const e=1/r.length;for(let t=0;t<r.length;t++){const n=[],a=[],s=[],i=[];for(let l=0;l<o.length;l++){const c=o[l];n.push(0),a.push(r[t][l]),s.push([c,t*e-.5]),i.push([c,(t+1)*e-.5])}const c=new u.ColumnDataSource({data:{left:n,right:a,top:i,bottom:s,labels:o,values:r[t],columns:r[t].map((e=>l[t+1]))}}),d=new u.Quad({left:{field:"left"},bottom:{field:"bottom"},right:{field:"right"},top:{field:"top"},line_color:null,fill_color:b[t%b.length]}),_=new u.GlyphRenderer({data_source:c,glyph:d});y.push(_)}}if("vertical"==x){[f,g]=[g,f],[h,d]=[d,h],[m,p]=[p,m];for(const e of y){const t=(0,i.dict)(e.data_source.data),l=t.get("left"),n=t.get("right"),a=t.get("top"),o=t.get("bottom");t.set("left",o),t.set("bottom",l),t.set("right",a),t.set("top",n)}}const v=new u.Plot({x_range:f,y_range:g,x_scale:m,y_scale:p});v.add_renderers(...y),v.add_layout(d,"left"),v.add_layout(h,"below");let M,R;"horizontal"==x?(M="center_right",R="horizontal"):(M="top_center",R="vertical");const S=new u.HoverTool({renderers:y,tooltips:"<div>@labels</div><div>@columns:&nbsp<b>@values</b></div>",point_policy:"snap_to_data",anchor:M,attachment:R});return v.add_tools(S),v};const o=e(1).__importStar(e(586)),r=e(22),s=e(10),i=e(9),c=e(8),d=e(245),u=e(587);function _(e="Spectral11"){return(0,c.isArray)(e)?e:o[e]}},
586: function _(e,r,t,a,o){a(),t.Greens4=t.Greens3=t.Blues9=t.Blues8=t.Blues7=t.Blues6=t.Blues5=t.Blues4=t.Blues3=t.Purples9=t.Purples8=t.Purples7=t.Purples6=t.Purples5=t.Purples4=t.Purples3=t.YlOrBr9=t.YlOrBr8=t.YlOrBr7=t.YlOrBr6=t.YlOrBr5=t.YlOrBr4=t.YlOrBr3=t.YlOrRd9=t.YlOrRd8=t.YlOrRd7=t.YlOrRd6=t.YlOrRd5=t.YlOrRd4=t.YlOrRd3=t.OrRd9=t.OrRd8=t.OrRd7=t.OrRd6=t.OrRd5=t.OrRd4=t.OrRd3=t.PuRd9=t.PuRd8=t.PuRd7=t.PuRd6=t.PuRd5=t.PuRd4=t.PuRd3=t.RdPu9=t.RdPu8=t.RdPu7=t.RdPu6=t.RdPu5=t.RdPu4=void 0,t.PRGn5=t.PRGn4=t.PRGn3=t.BrBG11=t.BrBG10=t.BrBG9=t.BrBG8=t.BrBG7=t.BrBG6=t.BrBG5=t.BrBG4=t.BrBG3=t.PuOr11=t.PuOr10=t.PuOr9=t.PuOr8=t.PuOr7=t.PuOr6=t.PuOr5=t.PuOr4=t.PuOr3=t.Greys256=t.Greys11=t.Greys10=t.Greys9=t.Greys8=t.Greys7=t.Greys6=t.Greys5=t.Greys4=t.Greys3=t.Reds9=t.Reds8=t.Reds7=t.Reds6=t.Reds5=t.Reds4=t.Reds3=t.Oranges9=t.Oranges8=t.Oranges7=t.Oranges6=t.Oranges5=t.Oranges4=t.Oranges3=t.Greens9=t.Greens8=t.Greens7=t.Greens6=t.Greens5=void 0,t.Spectral10=t.Spectral9=t.Spectral8=t.Spectral7=t.Spectral6=t.Spectral5=t.Spectral4=t.Spectral3=t.RdYlBu11=t.RdYlBu10=t.RdYlBu9=t.RdYlBu8=t.RdYlBu7=t.RdYlBu6=t.RdYlBu5=t.RdYlBu4=t.RdYlBu3=t.RdGy11=t.RdGy10=t.RdGy9=t.RdGy8=t.RdGy7=t.RdGy6=t.RdGy5=t.RdGy4=t.RdGy3=t.RdBu11=t.RdBu10=t.RdBu9=t.RdBu8=t.RdBu7=t.RdBu6=t.RdBu5=t.RdBu4=t.RdBu3=t.PiYG11=t.PiYG10=t.PiYG9=t.PiYG8=t.PiYG7=t.PiYG6=t.PiYG5=t.PiYG4=t.PiYG3=t.PRGn11=t.PRGn10=t.PRGn9=t.PRGn8=t.PRGn7=t.PRGn6=void 0,t.Viridis6=t.Viridis5=t.Viridis4=t.Viridis3=t.Plasma256=t.Plasma11=t.Plasma10=t.Plasma9=t.Plasma8=t.Plasma7=t.Plasma6=t.Plasma5=t.Plasma4=t.Plasma3=t.Magma256=t.Magma11=t.Magma10=t.Magma9=t.Magma8=t.Magma7=t.Magma6=t.Magma5=t.Magma4=t.Magma3=t.Inferno256=t.Inferno11=t.Inferno10=t.Inferno9=t.Inferno8=t.Inferno7=t.Inferno6=t.Inferno5=t.Inferno4=t.Inferno3=t.Bokeh8=t.Bokeh7=t.Bokeh6=t.Bokeh5=t.Bokeh4=t.Bokeh3=t.RdYlGn11=t.RdYlGn10=t.RdYlGn9=t.RdYlGn8=t.RdYlGn7=t.RdYlGn6=t.RdYlGn5=t.RdYlGn4=t.RdYlGn3=t.Spectral11=void 0,t.Pastel1_4=t.Pastel1_3=t.Paired12=t.Paired11=t.Paired10=t.Paired9=t.Paired8=t.Paired7=t.Paired6=t.Paired5=t.Paired4=t.Paired3=t.Dark2_8=t.Dark2_7=t.Dark2_6=t.Dark2_5=t.Dark2_4=t.Dark2_3=t.Accent8=t.Accent7=t.Accent6=t.Accent5=t.Accent4=t.Accent3=t.Turbo256=t.Turbo11=t.Turbo10=t.Turbo9=t.Turbo8=t.Turbo7=t.Turbo6=t.Turbo5=t.Turbo4=t.Turbo3=t.Cividis256=t.Cividis11=t.Cividis10=t.Cividis9=t.Cividis8=t.Cividis7=t.Cividis6=t.Cividis5=t.Cividis4=t.Cividis3=t.Viridis256=t.Viridis11=t.Viridis10=t.Viridis9=t.Viridis8=t.Viridis7=void 0,t.Category20_10=t.Category20_9=t.Category20_8=t.Category20_7=t.Category20_6=t.Category20_5=t.Category20_4=t.Category20_3=t.Category10_10=t.Category10_9=t.Category10_8=t.Category10_7=t.Category10_6=t.Category10_5=t.Category10_4=t.Category10_3=t.Set3_12=t.Set3_11=t.Set3_10=t.Set3_9=t.Set3_8=t.Set3_7=t.Set3_6=t.Set3_5=t.Set3_4=t.Set3_3=t.Set2_8=t.Set2_7=t.Set2_6=t.Set2_5=t.Set2_4=t.Set2_3=t.Set1_9=t.Set1_8=t.Set1_7=t.Set1_6=t.Set1_5=t.Set1_4=t.Set1_3=t.Pastel2_8=t.Pastel2_7=t.Pastel2_6=t.Pastel2_5=t.Pastel2_4=t.Pastel2_3=t.Pastel1_9=t.Pastel1_8=t.Pastel1_7=t.Pastel1_6=t.Pastel1_5=void 0,t.Colorblind6=t.Colorblind5=t.Colorblind4=t.Colorblind3=t.Category20c_20=t.Category20c_19=t.Category20c_18=t.Category20c_17=t.Category20c_16=t.Category20c_15=t.Category20c_14=t.Category20c_13=t.Category20c_12=t.Category20c_11=t.Category20c_10=t.Category20c_9=t.Category20c_8=t.Category20c_7=t.Category20c_6=t.Category20c_5=t.Category20c_4=t.Category20c_3=t.Category20b_20=t.Category20b_19=t.Category20b_18=t.Category20b_17=t.Category20b_16=t.Category20b_15=t.Category20b_14=t.Category20b_13=t.Category20b_12=t.Category20b_11=t.Category20b_10=t.Category20b_9=t.Category20b_8=t.Category20b_7=t.Category20b_6=t.Category20b_5=t.Category20b_4=t.Category20b_3=t.Category20_20=t.Category20_19=t.Category20_18=t.Category20_17=t.Category20_16=t.Category20_15=t.Category20_14=t.Category20_13=t.Category20_12=t.Category20_11=void 0,t.TolPRGn3=t.BuRd9=t.BuRd8=t.BuRd7=t.BuRd6=t.BuRd5=t.BuRd4=t.BuRd3=t.Sunset11=t.Sunset10=t.Sunset9=t.Sunset8=t.Sunset7=t.Sunset6=t.Sunset5=t.Sunset4=t.Sunset3=t.Light9=t.Light8=t.Light7=t.Light6=t.Light5=t.Light4=t.Light3=t.DarkText=t.PaleTextBackground=t.MediumContrast6=t.MediumContrast5=t.MediumContrast4=t.MediumContrast3=t.Muted9=t.Muted8=t.Muted7=t.Muted6=t.Muted5=t.Muted4=t.Muted3=t.Vibrant7=t.Vibrant6=t.Vibrant5=t.Vibrant4=t.Vibrant3=t.HighContrast3=t.Bright7=t.Bright6=t.Bright5=t.Bright4=t.Bright3=t.Colorblind8=t.Colorblind7=void 0,t.TolRainbow18=t.TolRainbow17=t.TolRainbow16=t.TolRainbow15=t.TolRainbow14=t.TolRainbow13=t.TolRainbow12=t.TolRainbow11=t.TolRainbow10=t.TolRainbow9=t.TolRainbow8=t.TolRainbow7=t.TolRainbow6=t.TolRainbow5=t.TolRainbow4=t.TolRainbow3=t.Iridescent23=t.Iridescent22=t.Iridescent21=t.Iridescent20=t.Iridescent19=t.Iridescent18=t.Iridescent17=t.Iridescent16=t.Iridescent15=t.Iridescent14=t.Iridescent13=t.Iridescent12=t.Iridescent11=t.Iridescent10=t.Iridescent9=t.Iridescent8=t.Iridescent7=t.Iridescent6=t.Iridescent5=t.Iridescent4=t.Iridescent3=t.TolYlOrBr9=t.TolYlOrBr8=t.TolYlOrBr7=t.TolYlOrBr6=t.TolYlOrBr5=t.TolYlOrBr4=t.TolYlOrBr3=t.TolPRGn9=t.TolPRGn8=t.TolPRGn7=t.TolPRGn6=t.TolPRGn5=t.TolPRGn4=void 0,t.Category20b=t.Category20=t.Category10=t.Set3=t.Set2=t.Set1=t.Pastel2=t.Pastel1=t.Paired=t.Dark2=t.Accent=t.Turbo=t.Cividis=t.Viridis=t.Plasma=t.Magma=t.Inferno=t.Bokeh=t.RdYlGn=t.Spectral=t.RdYlBu=t.RdGy=t.RdBu=t.PiYG=t.PRGn=t.BrBG=t.PuOr=t.Greys=t.Reds=t.Oranges=t.Greens=t.Blues=t.Purples=t.YlOrBr=t.YlOrRd=t.OrRd=t.PuRd=t.RdPu=t.BuPu=t.PuBu=t.PuBuGn=t.BuGn=t.GnBu=t.YlGnBu=t.YlGn=t.TolRainbow23=t.TolRainbow22=t.TolRainbow21=t.TolRainbow20=t.TolRainbow19=void 0,t.colorblind=t.tol=t.mpl=t.bokeh=t.d3=t.brewer=t.TolRainbow=t.Iridescent=t.TolYlOrBr=t.TolPRGn=t.BuRd=t.Sunset=t.Light=t.MediumContrast=t.Muted=t.Vibrant=t.HighContrast=t.Bright=t.Colorblind=t.Category20c=void 0,t.interp_palette=function(e,r){const t=e.length;if(t<1)throw new Error("palette must contain at least one color");if(r<0)throw new Error("requested palette length cannot be negative");const a=new Uint8Array(r),o=new Uint8Array(r),l=new Uint8Array(r),u=new Uint8Array(r);for(let r=0;r<t;r++)[a[r],o[r],l[r],u[r]]=(0,d.color2rgba)(e[r]);const s=(0,n.range)(0,t),_=(0,n.linspace)(0,t-1,r),R=(0,i.interpolate)(_,s,a),B=(0,i.interpolate)(_,s,o),P=(0,i.interpolate)(_,s,l),g=(0,i.interpolate)(_,s,u),G=new Array(r);for(let e=0;e<r;e++)G[e]=[(0,d.byte)(R[e]),(0,d.byte)(B[e]),(0,d.byte)(P[e]),(0,d.byte)(g[e])];return G},t.linear_palette=l,t.varying_alpha_palette=function(e,r=null,t=0,a=255){if(t<0||t>255)throw new Error("start_alpha must be in the range 0 to 255");if(a<0||a>255)throw new Error("end_alpha must be in the range 0 to 255");const o=(0,d.color2rgba)(e);if(o[3]<255){const e=o[3]/255;t*=e,a*=e}const n=null!=r&&r>0?r:Math.round(Math.abs(a-t))+1,i=(a-t)/255;t/=255;const l=new Array(n);for(let e=0;e<n;e++)l[e]=(0,d.color2hex)(o,t+i*e/(n-1));return l},t.magma=function(e){return l(t.Magma256,e)},t.inferno=function(e){return l(t.Inferno256,e)},t.plasma=function(e){return l(t.Plasma256,e)},t.viridis=function(e){return l(t.Viridis256,e)},t.cividis=function(e){return l(t.Cividis256,e)},t.turbo=function(e){return l(t.Turbo256,e)},t.grey=function(e){return l(t.Greys256,e)};const n=e(10),i=e(13),d=e(22);function l(e,r){if(r<=e.length)return(0,n.linspace)(0,e.length-1,r).map((r=>e[0|r]));throw new Error("too many color entries requested")}t.YlGn3=[832787711,2916978431,4160535039],t.YlGn4=[595870719,2026273279,3269892607,4294954239],t.YlGn5=[6830079,832787711,2026273279,3269892607,4294954239],t.YlGn6=[6830079,832787711,2026273279,2916978431,3656426495,4294954239],t.YlGn7=[5911295,595870719,1101749759,2026273279,2916978431,3656426495,4294954239],t.YlGn8=[5911295,595870719,1101749759,2026273279,2916978431,3656426495,4160535039,4294960639],t.YlGn9=[4532735,6830079,595870719,1101749759,2026273279,2916978431,3656426495,4160535039,4294960639],t.YlGnBu3=[746567935,2144189439,3992498687],t.YlGnBu4=[576628991,1102497023,2715464959,4294954239],t.YlGnBu5=[624203007,746567935,1102497023,2715464959,4294954239],t.YlGnBu6=[624203007,746567935,1102497023,2144189439,3353982207,4294954239],t.YlGnBu7=[204244223,576628991,496091391,1102497023,2144189439,3353982207,4294954239],t.YlGnBu8=[204244223,576628991,496091391,1102497023,2144189439,3353982207,3992498687,4294957567],t.YlGnBu9=[136141055,624203007,576628991,496091391,1102497023,2144189439,3353982207,3992498687,4294957567],t.GnBu3=[1134742271,2833102335,3774077951],t.GnBu4=[730644223,2077017343,3135552767,4042909951],t.GnBu5=[141077759,1134742271,2077017343,3135552767,4042909951],t.GnBu6=[141077759,1134742271,2077017343,2833102335,3438003711,4042909951],t.GnBu7=[140025599,730644223,1320408063,2077017343,2833102335,3438003711,4042909951],t.GnBu8=[140025599,730644223,1320408063,2077017343,2833102335,3438003711,3774077951,4160549119],t.GnBu9=[138445311,141077759,730644223,1320408063,2077017343,2833102335,3438003711,3774077951,4160549119],t.BuGn3=[748838911,2581121535,3858102783],t.BuGn4=[596329983,1724032255,3001213695,3992517631],t.BuGn5=[7154943,748838911,1724032255,3001213695,3992517631],t.BuGn6=[7154943,748838911,1724032255,2581121535,3438077695,3992517631],t.BuGn7=[5776639,596329983,1101952767,1724032255,2581121535,3438077695,3992517631],t.BuGn8=[5776639,596329983,1101952767,1724032255,2581121535,3438077695,3858102783,4160552447],t.BuGn9=[4463615,7154943,596329983,1101952767,1724032255,2581121535,3438077695,3858102783,4160552447],t.PuBuGn3=[479238655,2797460479,3974295807],t.PuBuGn4=[42044159,1739182079,3184124415,4142921727],t.PuBuGn5=[23878143,479238655,1739182079,3184124415,4142921727],t.PuBuGn6=[23878143,479238655,1739182079,2797460479,3503417087,4142921727],t.PuBuGn7=[23351551,42044159,915456255,1739182079,2797460479,3503417087,4142921727],t.PuBuGn8=[23351551,42044159,915456255,1739182079,2797460479,3503417087,3974295807,4294441983],t.PuBuGn9=[21378815,23878143,42044159,915456255,1739182079,2797460479,3503417087,3974295807,4294441983],t.PuBu3=[730644223,2797460479,3974623999],t.PuBu4=[91271423,1957285887,3184124415,4058969855],t.PuBu5=[73043455,730644223,1957285887,3184124415,4058969855],t.PuBu6=[73043455,730644223,1957285887,2797460479,3503417087,4058969855],t.PuBu7=[55475199,91271423,915456255,1957285887,2797460479,3503417087,4058969855],t.PuBu8=[55475199,91271423,915456255,1957285887,2797460479,3503417087,3974623999,4294441983],t.PuBu9=[37247231,73043455,91271423,915456255,1957285887,2797460479,3503417087,3974623999,4294441983],t.BuPu3=[2287380479,2663176959,3773625599],t.BuPu4=[2286001663,2358691583,3016614911,3992517631],t.BuPu5=[2165275903,2287380479,2358691583,3016614911,3992517631],t.BuPu6=[2165275903,2287380479,2358691583,2663176959,3218335487,3992517631],t.BuPu7=[1845586943,2286001663,2355868159,2358691583,2663176959,3218335487,3992517631],t.BuPu8=[1845586943,2286001663,2355868159,2358691583,2663176959,3218335487,3773625599,4160552447],t.BuPu9=[1291865087,2165275903,2286001663,2355868159,2358691583,2663176959,3218335487,3773625599,4160552447],t.RdPu3=[3306916607,4204770815,4259372543],t.RdPu4=[2919333631,4150829567,4222925311,4276871935],t.RdPu5=[2046916607,3306916607,4150829567,4222925311,4276871935],t.RdPu6=[2046916607,3306916607,4150829567,4204770815,4240818431,4276871935],t.RdPu7=[2046916607,2919333631,3711211519,4150829567,4204770815,4240818431,4276871935],t.RdPu8=[2046916607,2919333631,3711211519,4150829567,4204770815,4240818431,4259372543,4294439935],t.RdPu9=[1224764159,2046916607,2919333631,3711211519,4150829567,4204770815,4240818431,4259372543,4294439935],t.PuRd3=[3709630463,3381970943,3890343935],t.PuRd4=[3457308415,3747983615,3619019007,4058969855],t.PuRd5=[2550154239,3709630463,3747983615,3619019007,4058969855],t.PuRd6=[2550154239,3709630463,3747983615,3381970943,3568950015,4058969855],t.PuRd7=[2432712703,3457308415,3878259455,3747983615,3381970943,3568950015,4058969855],t.PuRd8=[2432712703,3457308415,3878259455,3747983615,3381970943,3568950015,3890343935,4160027135],t.PuRd9=[1728061439,2550154239,3457308415,3878259455,3747983615,3381970943,3568950015,3890343935,4160027135],t.OrRd3=[3813291007,4256924927,4276668671],t.OrRd4=[3610255359,4237122047,4258040575,4277197311],t.OrRd5=[3003121919,3813291007,4237122047,4258040575,4277197311],t.OrRd6=[3003121919,3813291007,4237122047,4256924927,4258569983,4277197311],t.OrRd7=[2566914303,3610255359,4016392447,4237122047,4256924927,4258569983,4277197311],t.OrRd8=[2566914303,3610255359,4016392447,4237122047,4256924927,4258569983,4276668671,4294438143],t.OrRd9=[2130706687,3003121919,3610255359,4016392447,4237122047,4256924927,4258569983,4276668671,4294438143],t.YlOrRd3=[4030406911,4273097983,4293763327],t.YlOrRd4=[3810139391,4253891839,4274806015,4294947583],t.YlOrRd5=[3170903807,4030406911,4253891839,4274806015,4294947583],t.YlOrRd6=[3170903807,4030406911,4253891839,4273097983,4275664639,4294947583],t.YlOrRd7=[2969577215,3810139391,4232981247,4253891839,4273097983,4275664639,4294947583],t.YlOrRd8=[2969577215,3810139391,4232981247,4253891839,4273097983,4275664639,4293763327,4294954239],t.YlOrRd9=[2147493631,3170903807,3810139391,4232981247,4253891839,4273097983,4275664639,4293763327,4294954239],t.YlOrBr3=[3646885631,4274278399,4294425855],t.YlOrBr4=[3427533567,4271450623,4275670783,4294956287],t.YlOrBr5=[2570323199,3646885631,4271450623,4275670783,4294956287],t.YlOrBr6=[2570323199,3646885631,4271450623,4274278399,4276326911,4294956287],t.YlOrBr7=[2351760639,3427533567,3966768383,4271450623,4274278399,4276326911,4294956287],t.YlOrBr8=[2351760639,3427533567,3966768383,4271450623,4274278399,4276326911,4294425855,4294960639],t.YlOrBr9=[1713702655,2570323199,3427533567,3966768383,4271450623,4274278399,4276326911,4294425855,4294960639],t.Purples3=[1969992191,3166559487,4025349631],t.Purples4=[1783735295,2660944127,3419005695,4075878399],t.Purples5=[1411878911,1969992191,2660944127,3419005695,4075878399],t.Purples6=[1411878911,1969992191,2660944127,3166559487,3671780351,4075878399],t.Purples7=[1242859263,1783735295,2155723519,2660944127,3166559487,3671780351,4075878399],t.Purples8=[1242859263,1783735295,2155723519,2660944127,3166559487,3671780351,4025349631,4244372991],t.Purples9=[1056996863,1411878911,1783735295,2155723519,2660944127,3166559487,3671780351,4025349631,4244372991],t.Blues3=[830651903,2664096255,3740006399],t.Blues4=[561100287,1806620415,3185043455,4025745407],t.Blues5=[139566335,830651903,1806620415,3185043455,4025745407],t.Blues6=[139566335,830651903,1806620415,2664096255,3336302591,4025745407],t.Blues7=[138777855,561100287,1116915455,1806620415,2664096255,3336302591,4025745407],t.Blues8=[138777855,561100287,1116915455,1806620415,2664096255,3336302591,3740006399,4160487423],t.Blues9=[137391103,139566335,561100287,1116915455,1806620415,2664096255,3336302591,3740006399,4160487423],t.Greens3=[832787711,2715393023,3858096383],t.Greens4=[596329983,1959032575,3135550463,3992513023],t.Greens5=[7154943,832787711,1959032575,3135550463,3992513023],t.Greens6=[7154943,832787711,1959032575,2715393023,3353985279,3992513023],t.Greens7=[5911295,596329983,1101749759,1959032575,2715393023,3353985279,3992513023],t.Greens8=[5911295,596329983,1101749759,1959032575,2715393023,3353985279,3858096383,4160550399],t.Greens9=[4463615,7154943,596329983,1101749759,1959032575,2715393023,3353985279,3858096383,4160550399],t.Oranges3=[3864333823,4256066559,4276539135],t.Oranges4=[3645309439,4253891839,4257121791,4277001983],t.Oranges5=[2788557823,3864333823,4253891839,4257121791,4277001983],t.Oranges6=[2788557823,3864333823,4253891839,4256066559,4258308863,4277001983],t.Oranges7=[2351760639,3645374975,4050195455,4253891839,4256066559,4258308863,4277001983],t.Oranges8=[2351760639,3645374975,4050195455,4253891839,4256066559,4258308863,4276539135,4294306815],t.Oranges9=[2133263615,2788557823,3645374975,4050195455,4253891839,4256066559,4258308863,4276539135,4294306815],t.Reds3=[3727501055,4237456127,4276146943],t.Reds4=[3407355391,4218047231,4239299071,4276476415],t.Reds5=[2769229311,3727501055,4218047231,4239299071,4276476415],t.Reds6=[2769229311,3727501055,4218047231,4237456127,4240155135,4276476415],t.Reds7=[2566917631,3407355391,4013632767,4218047231,4237456127,4240155135,4276476415],t.Reds8=[2566917631,3407355391,4013632767,4218047231,4237456127,4240155135,4276146943,4294308095],t.Reds9=[1728056831,2769229311,3407355391,4013632767,4218047231,4237456127,4240155135,4276146943,4294308095],t.Greys3=[1667458047,3183328767,4042322175],t.Greys4=[1381126911,2526451455,3435973887,4160223231],t.Greys5=[623191551,1667458047,2526451455,3435973887,4160223231],t.Greys6=[623191551,1667458047,2526451455,3183328767,3654932991,4160223231],t.Greys7=[623191551,1381126911,1936946175,2526451455,3183328767,3654932991,4160223231],t.Greys8=[623191551,1381126911,1936946175,2526451455,3183328767,3654932991,4042322175,4294967295],t.Greys9=[255,623191551,1381126911,1936946175,2526451455,3183328767,3654932991,4042322175,4294967295],t.Greys10=[255,471604479,943208703,1431655935,1903260159,2374864383,2863311615,3334915839,3806520063,4294967295],t.Greys11=[255,421075455,858993663,1280068863,1717987071,2139062271,2576980479,2998055679,3435973887,3857049087,4294967295],t.Greys256=[255,16843263,33686271,50529279,67372287,84215295,101058303,117901311,134744319,151587327,168430335,185273343,202116351,218959359,235802367,252645375,269488383,286331391,303174399,320017407,336860415,353703423,370546431,387389439,404232447,421075455,437918463,454761471,471604479,488447487,505290495,522133503,538976511,555819519,572662527,589505535,606348543,623191551,640034559,656877567,673720575,690563583,707406591,724249599,741092607,757935615,774778623,791621631,808464639,825307647,842150655,858993663,875836671,892679679,909522687,926365695,943208703,960051711,976894719,993737727,1010580735,1027423743,1044266751,1061109759,1077952767,1094795775,1111638783,1128481791,1145324799,1162167807,1179010815,1195853823,1212696831,1229539839,1246382847,1263225855,1280068863,1296911871,1313754879,1330597887,1347440895,1364283903,1381126911,1397969919,1414812927,1431655935,1448498943,1465341951,1482184959,1499027967,1515870975,1532713983,1549556991,1566399999,1583243007,1600086015,1616929023,1633772031,1650615039,1667458047,1684301055,1701144063,1717987071,1734830079,1751673087,1768516095,1785359103,1802202111,1819045119,1835888127,1852731135,1869574143,1886417151,1903260159,1920103167,1936946175,1953789183,1970632191,1987475199,2004318207,2021161215,2038004223,2054847231,2071690239,2088533247,2105376255,2122219263,2139062271,2155905279,2172748287,2189591295,2206434303,2223277311,2240120319,2256963327,2273806335,2290649343,2307492351,2324335359,2341178367,2358021375,2374864383,2391707391,2408550399,2425393407,2442236415,2459079423,2475922431,2492765439,2509608447,2526451455,2543294463,2560137471,2576980479,2593823487,2610666495,2627509503,2644352511,2661195519,2678038527,2694881535,2711724543,2728567551,2745410559,2762253567,2779096575,2795939583,2812782591,2829625599,2846468607,2863311615,2880154623,2896997631,2913840639,2930683647,2947526655,2964369663,2981212671,2998055679,3014898687,3031741695,3048584703,3065427711,3082270719,3099113727,3115956735,3132799743,3149642751,3166485759,3183328767,3200171775,3217014783,3233857791,3250700799,3267543807,3284386815,3301229823,3318072831,3334915839,3351758847,3368601855,3385444863,3402287871,3419130879,3435973887,3452816895,3469659903,3486502911,3503345919,3520188927,3537031935,3553874943,3570717951,3587560959,3604403967,3621246975,3638089983,3654932991,3671775999,3688619007,3705462015,3722305023,3739148031,3755991039,3772834047,3789677055,3806520063,3823363071,3840206079,3857049087,3873892095,3890735103,3907578111,3924421119,3941264127,3958107135,3974950143,3991793151,4008636159,4025479167,4042322175,4059165183,4076008191,4092851199,4109694207,4126537215,4143380223,4160223231,4177066239,4193909247,4210752255,4227595263,4244438271,4261281279,4278124287,4294967295],t.PuOr3=[2576270335,4160223231,4054008063],t.PuOr4=[1581029887,2997605119,4256719871,3865117183],t.PuOr5=[1581029887,2997605119,4160223231,4256719871,3865117183],t.PuOr6=[1411877119,2576270335,3638225919,4276139775,4054008063,3008890623],t.PuOr7=[1411877119,2576270335,3638225919,4160223231,4276139775,4054008063,3008890623],t.PuOr8=[1411877119,2155064575,2997605119,3638225919,4276139775,4256719871,3766621439,3008890623],t.PuOr9=[1411877119,2155064575,2997605119,3638225919,4160223231,4276139775,4256719871,3766621439,3008890623],t.PuOr10=[754994175,1411877119,2155064575,2997605119,3638225919,4276139775,4256719871,3766621439,3008890623,2134575359],t.PuOr11=[754994175,1411877119,2155064575,2997605119,3638225919,4160223231,4276139775,4256719871,3766621439,3008890623,2134575359],t.BrBG3=[1521790207,4126537215,3635635711],t.BrBG4=[25522687,2160968191,3754065407,2791381759],t.BrBG5=[25522687,2160968191,4126537215,3754065407,2791381759],t.BrBG6=[23486207,1521790207,3354060287,4142449663,3635635711,2354121471],t.BrBG7=[23486207,1521790207,3354060287,4126537215,4142449663,3635635711,2354121471],t.BrBG8=[23486207,899125247,2160968191,3354060287,4142449663,3754065407,3212914175,2354121471],t.BrBG9=[23486207,899125247,2160968191,3354060287,4126537215,4142449663,3754065407,3212914175,2354121471],t.BrBG10=[3944703,23486207,899125247,2160968191,3354060287,4142449663,3754065407,3212914175,2354121471,1412433407],t.BrBG11=[3944703,23486207,899125247,2160968191,3354060287,4126537215,4142449663,3754065407,3212914175,2354121471,1412433407],t.PRGn3=[2143255551,4160223231,2945303551],t.PRGn4=[8927231,2799411455,3265646591,2066912511],t.PRGn5=[8927231,2799411455,4160223231,3265646591,2066912511],t.PRGn6=[460863487,2143255551,3656438783,3889490175,2945303551,1982497791],t.PRGn7=[460863487,2143255551,3656438783,4160223231,3889490175,2945303551,1982497791],t.PRGn8=[460863487,1521377791,2799411455,3656438783,3889490175,3265646591,2574298111,1982497791],t.PRGn9=[460863487,1521377791,2799411455,3656438783,4160223231,3889490175,3265646591,2574298111,1982497791],t.PRGn10=[4463615,460863487,1521377791,2799411455,3656438783,3889490175,3265646591,2574298111,1982497791,1073761279],t.PRGn11=[4463615,460863487,1521377791,2799411455,3656438783,4160223231,3889490175,3265646591,2574298111,1982497791,1073761279],t.PiYG3=[2715249407,4160223231,3919825407],t.PiYG4=[1303127807,3101787903,4055292671,3491531775],t.PiYG5=[1303127807,3101787903,4160223231,4055292671,3491531775],t.PiYG6=[1301422591,2715249407,3874869503,4259377151,3919825407,3306913279],t.PiYG7=[1301422591,2715249407,3874869503,4160223231,4259377151,3919825407,3306913279],t.PiYG8=[1301422591,2143044095,3101787903,3874869503,4259377151,4055292671,3732385535,3306913279],t.PiYG9=[1301422591,2143044095,3101787903,3874869503,4160223231,4259377151,4055292671,3732385535,3306913279],t.PiYG10=[660871679,1301422591,2143044095,3101787903,3874869503,4259377151,4055292671,3732385535,3306913279,2382451455],t.PiYG11=[660871679,1301422591,2143044095,3101787903,3874869503,4160223231,4259377151,4055292671,3732385535,3306913279,2382451455],t.RdBu3=[1739182079,4160223231,4018823935],t.RdBu4=[91336959,2462441215,4104487679,3389006079],t.RdBu5=[91336959,2462441215,4160223231,4104487679,3389006079],t.RdBu6=[560377087,1739182079,3521507583,4259039231,4018823935,2987928575],t.RdBu7=[560377087,1739182079,3521507583,4160223231,4259039231,4018823935,2987928575],t.RdBu8=[560377087,1133757439,2462441215,3521507583,4259039231,4104487679,3596635647,2987928575],t.RdBu9=[560377087,1133757439,2462441215,3521507583,4160223231,4259039231,4104487679,3596635647,2987928575],t.RdBu10=[87056895,560377087,1133757439,2462441215,3521507583,4259039231,4104487679,3596635647,2987928575,1728061439],t.RdBu11=[87056895,560377087,1133757439,2462441215,3521507583,4160223231,4259039231,4104487679,3596635647,2987928575,1728061439],t.RdGy3=[2576980479,4294967295,4018823935],t.RdGy4=[1077952767,3132799743,4104487679,3389006079],t.RdGy5=[1077952767,3132799743,4294967295,4104487679,3389006079],t.RdGy6=[1296911871,2576980479,3772834047,4259039231,4018823935,2987928575],t.RdGy7=[1296911871,2576980479,3772834047,4294967295,4259039231,4018823935,2987928575],t.RdGy8=[1296911871,2273806335,3132799743,3772834047,4259039231,4104487679,3596635647,2987928575],t.RdGy9=[1296911871,2273806335,3132799743,3772834047,4294967295,4259039231,4104487679,3596635647,2987928575],t.RdGy10=[437918463,1296911871,2273806335,3132799743,3772834047,4259039231,4104487679,3596635647,2987928575,1728061439],t.RdGy11=[437918463,1296911871,2273806335,3132799743,3772834047,4294967295,4259039231,4104487679,3596635647,2987928575,1728061439],t.RdYlBu3=[2445270015,4294950911,4237122047],t.RdYlBu4=[746305279,2883185151,4256063999,3608747263],t.RdYlBu5=[746305279,2883185151,4294950911,4256063999,3608747263],t.RdYlBu6=[1165341951,2445270015,3774085375,4276130047,4237122047,3610257407],t.RdYlBu7=[1165341951,2445270015,3774085375,4294950911,4276130047,4237122047,3610257407],t.RdYlBu8=[1165341951,1957548543,2883185151,3774085375,4276130047,4256063999,4100801535,3610257407],t.RdYlBu9=[1165341951,1957548543,2883185151,3774085375,4294950911,4276130047,4256063999,4100801535,3610257407],t.RdYlBu10=[825660927,1165341951,1957548543,2883185151,3774085375,4276130047,4256063999,4100801535,3610257407,2768250623],t.RdYlBu11=[825660927,1165341951,1957548543,2883185151,3774085375,4294950911,4276130047,4256063999,4100801535,3610257407,2768250623],t.Spectral3=[2580911359,4294950911,4237122047],t.Spectral4=[730053375,2883429631,4256063999,3608747263],t.Spectral5=[730053375,2883429631,4294950911,4256063999,3608747263],t.Spectral6=[847822335,2580911359,3874855167,4276128767,4237122047,3577630719],t.Spectral7=[847822335,2580911359,3874855167,4294950911,4276128767,4237122047,3577630719],t.Spectral8=[847822335,1724032511,2883429631,3874855167,4276128767,4256063999,4100801535,3577630719],t.Spectral9=[847822335,1724032511,2883429631,3874855167,4294950911,4276128767,4256063999,4100801535,3577630719],t.Spectral10=[1582277375,847822335,1724032511,2883429631,3874855167,4276128767,4256063999,4100801535,3577630719,2650882815],t.Spectral11=[1582277375,847822335,1724032511,2883429631,3874855167,4294950911,4276128767,4256063999,4100801535,3577630719,2650882815],t.RdYlGn3=[2446287103,4294950911,4237122047],t.RdYlGn4=[446054911,2799266559,4256063999,3608747263],t.RdYlGn5=[446054911,2799266559,4294950911,4256063999,3608747263],t.RdYlGn6=[446189823,2446287103,3656354815,4276128767,4237122047,3610257407],t.RdYlGn7=[446189823,2446287103,3656354815,4294950911,4276128767,4237122047,3610257407],t.RdYlGn8=[446189823,1723687935,2799266559,3656354815,4276128767,4256063999,4100801535,3610257407],t.RdYlGn9=[446189823,1723687935,2799266559,3656354815,4294950911,4276128767,4256063999,4100801535,3610257407],t.RdYlGn10=[6830079,446189823,1723687935,2799266559,3656354815,4276128767,4256063999,4100801535,3610257407,2768250623],t.RdYlGn11=[6830079,446189823,1723687935,2799266559,3656354815,4294950911,4276128767,4256063999,4100801535,3610257407,2768250623],t.Bokeh3=[3960821759,4031915007,4138277887],t.Bokeh4=[...t.Bokeh3,2781690367],t.Bokeh5=[...t.Bokeh4,548558079],t.Bokeh6=[...t.Bokeh5,11185919],t.Bokeh7=[...t.Bokeh6,2301135359],t.Bokeh8=[...t.Bokeh6,1234752511,2301135359],t.Inferno3=[1023,3124123135,4244546815],t.Inferno4=[1023,2015129087,3983025663,4244546815],t.Inferno5=[1023,1427074559,3124123135,4186704383,4244546815],t.Inferno6=[1023,1091135487,2468702207,3696245247,4221831935,4244546815],t.Inferno7=[1023,839474687,2015129087,3124123135,3983025663,4222818559,4244546815],t.Inferno8=[1023,655053567,1662283519,2653512703,3527885567,4118550015,4206831103,4244546815],t.Inferno9=[1023,520898559,1427074559,2283891455,3124123135,3814208255,4186704383,4173935103,4244546815],t.Inferno10=[1023,436945151,1242262271,2015129087,2754371839,3443673087,3983025663,4221110015,4157553407,4244546815],t.Inferno11=[1023,369834495,1091135487,1779920639,2468702207,3124123135,3696245247,4067760895,4221831935,4141171455,4244546815],t.Inferno256=[1023,1279,1791,16779263,16845311,16845823,33623807,33689855,50467583,67310847,67311359,84154623,100932607,117775871,134619135,151396863,168240127,185018111,218638591,235416319,252259839,269037567,302658303,319436031,336279295,369834495,386612223,420167423,436945151,470565887,487343615,520898559,537676543,571165951,604720895,638275839,655053567,688608511,722097919,755652863,772430591,805985535,839474687,873029631,889807103,923361791,956916479,990471423,1007248895,1040803583,1074358015,1091135487,1124755711,1158310399,1175087615,1208707839,1242262271,1259105279,1292659711,1326279935,1343057151,1376677119,1393454591,1427074559,1460628991,1477471743,1511091711,1527869183,1561489151,1595043583,1611886335,1645506303,1662283519,1695903487,1712680703,1746300671,1779920639,1796697855,1830317823,1847095039,1880715007,1914269183,1931111935,1964731903,1981509119,2015129087,2048683519,2065526015,2099080447,2115923199,2149542911,2166320127,2199940095,2233494271,2250337023,2283891455,2300733951,2334288383,2367908351,2384750847,2418305279,2435147775,2468702207,2502321919,2519099135,2552718847,2569561343,2603115775,2619958271,2653512703,2687132415,2703974911,2737529343,2754371839,2787926015,2804768767,2838388479,2871942655,2888785151,2922404863,2939247615,2972801791,2989644287,3023263999,3040040959,3073660671,3090503423,3124123135,3140965631,3174519807,3191362303,3208204799,3241824511,3258667007,3292286719,3309129215,3342748927,3359525887,3376368383,3409988095,3426830591,3443673087,3477358335,3494200575,3511043071,3527885567,3561505279,3578347775,3595190271,3612032767,3645718015,3662560255,3679402751,3696245247,3713153279,3729995775,3746838271,3763746047,3797365759,3814208255,3831116287,3847958783,3864801023,3864931839,3881774335,3898682367,3915524863,3932432639,3949275135,3966183167,3983025663,3983156223,4000064255,4016906751,4033814527,4033879807,4050787839,4067695871,4067760895,4084668927,4084799743,4101641983,4118550015,4118680831,4135588607,4135653887,4152561919,4152692479,4169600511,4169665791,4169796607,4186704383,4186835199,4186966271,4203808767,4203939583,4204070655,4204201727,4221110015,4221241087,4221372159,4221437951,4221569023,4221700351,4221831935,4221963263,4222094847,4222226175,4222357759,4222489343,4222620927,4222686975,4222818559,4222950143,4223081727,4223213311,4206567935,4206699519,4206831103,4206962943,4190317311,4190448895,4190580735,4173935103,4174066943,4174198783,4157553407,4157684991,4141039615,4141171455,4124526079,4124657919,4108012543,4108079103,4091433727,4091565823,4091697663,4075052543,4075184383,4058539263,4058605823,4058737919,4058870015,4058936575,4059068927,4059201023,4076044799,4076111359,4093020671,4109864447,4126708223,4143617535,4160461311,4194082303,4210925823,4244546815],t.Magma3=[1023,3040246271,4227645439],t.Magma4=[1023,1897890303,4032847359,4227645439],t.Magma5=[1023,1326545919,3040246271,4219887871,4227645439],t.Magma6=[1023,990867455,2351530239,3712575743,4255083775,4227645439],t.Magma7=[1023,722558719,1897890303,3040246271,4032847359,4272846591,4227645439],t.Magma8=[1023,571560191,1561820927,2536275967,3510791935,4168441087,4273569791,4227645439],t.Magma9=[1023,454051071,1326545919,2166718975,3040246271,3847250943,4219887871,4274161407,4227645439],t.Magma10=[1023,386874623,1125086719,1897890303,2653847295,3409867263,4032847359,4237518591,4274490367,4227645439],t.Magma11=[1023,336410111,990867455,1662615551,2351530239,3040246271,3712575743,4134427647,4255083775,4258107647,4227645439],t.Magma256=[1023,1279,1791,16779263,16845311,16845823,33689087,33689599,50532863,67310591,67376639,84154367,100997631,117775359,134618623,151461887,168239871,185083135,201926399,218769663,235547391,252390655,269234175,286011903,302855167,336410111,353253631,370031359,386874623,403652607,437273087,454051071,470828799,504383999,521227263,538004991,571560191,588337919,621893119,638670847,672225791,705780991,722558719,756048127,789603071,806381055,839935999,873490687,890202879,923757823,957312767,990867455,1007645183,1041199871,1074754559,1108309247,1125086719,1158641407,1192196095,1209039103,1242593791,1259371007,1292991231,1326545919,1343388671,1376943359,1393786111,1427340799,1460960767,1477803775,1511358207,1528200959,1561820927,1578598399,1612218367,1628995583,1662615551,1696235775,1713012991,1746632959,1763475711,1797030143,1813872895,1847493119,1864270335,1897890303,1931444735,1948287487,1981907455,1998684671,2032304639,2049081855,2082701823,2116321791,2133099007,2166718975,2183496191,2217116159,2233893375,2267513343,2301133311,2317910527,2351530239,2368307455,2401927423,2435481855,2452324607,2485879039,2502721791,2536275967,2569895935,2586673151,2620293119,2653847295,2670690047,2704244479,2737864447,2754641407,2788261375,2805038591,2838658303,2872278271,2889055231,2922675199,2956229631,2973072127,3006626559,3040246271,3057023487,3090643199,3107420415,3141040127,3174660095,3191437055,3225056767,3258611199,3275453695,3309073663,3325850623,3359470335,3393090303,3409867263,3443486975,3460329727,3493949439,3510791935,3544346111,3561188863,3594808575,3611651071,3645270783,3662113279,3695733247,3712575743,3729418239,3763037951,3779880703,3796723199,3830342911,3847250943,3864093439,3880936191,3897844223,3931463935,3948306687,3965214719,3982057471,3998965503,3999096319,4015939071,4032847359,4049689855,4066598143,4083506431,4083637247,4100479999,4117388287,4117519359,4134427647,4134558719,4151401471,4151532799,4168441087,4168572159,4185480447,4185611775,4185742847,4202651391,4202716927,4202848255,4219756799,4219887871,4220019199,4220150527,4237059071,4237190143,4237321471,4237452799,4237518591,4254427135,4254558463,4254689791,4254821119,4254952447,4255083775,4255215359,4255281151,4255412479,4272321023,4272452607,4272583935,4272715263,4272846591,4272912639,4273043967,4273175551,4273306879,4273438207,4273569791,4273701119,4273767167,4273898495,4274030079,4274161407,4274292991,4274424319,4274490367,4274621951,4274753279,4258107647,4258239231,4258370559,4258436607,4258568191,4258699519,4258831103,4258962687,4259094015,4259160063,4259291647,4259423231,4242777599,4242908927,4242974975,4243106559,4243238143,4243369727,4243501311,4243632639,4243698687,4243830271,4243961855,4227316223,4227447807,4227513855,4227645439],t.Plasma3=[201819903,3393616127,4026016255],t.Plasma4=[201819903,2602016511,3967308799,4026016255],t.Plasma5=[201819903,2080548863,3393616127,4153623039,4026016255],t.Plasma6=[201819903,1778427903,2955579391,3764675071,4238751231,4026016255],t.Plasma7=[201819903,1543546367,2602016511,3393616127,3967308799,4256313343,4026016255],t.Plasma8=[201819903,1375839231,2299045375,3090319871,3663358207,4085729535,4256902143,4026016255],t.Plasma9=[201819903,1241686271,2080548863,2820839167,3393616127,3849018623,4153623039,4257425663,4026016255],t.Plasma10=[201819903,1157865215,1912645887,2602016511,3157689855,3612765439,3967308799,4204608255,4240910079,4026016255],t.Plasma11=[201819903,1073978623,1778427903,2400035839,2955579391,3393616127,3764675071,4051848447,4238751231,4241237503,4026016255],t.Plasma256=[201819903,268929023,319195647,352750335,403082239,453414143,486968831,520457983,554012671,587567359,621122047,654676735,688231423,721786111,755275007,788829695,822384383,855939071,872716543,906270975,939825663,973380351,990092031,1023646719,1057201407,1073978623,1107533311,1141087999,1157865215,1191354367,1224908799,1241686271,1275240959,1308795391,1325572863,1359061759,1375839231,1409393663,1442948095,1459725567,1493279999,1509991935,1543546367,1577100799,1593878271,1627432703,1644209919,1677764607,1694541823,1728096255,1744873471,1778427903,1811982591,1828759807,1862314239,1879091455,1912645887,1929423103,1962977535,1979820287,2013374719,2030151935,2063771903,2080548863,2114168831,2130946047,2164566015,2181343231,2214962943,2231805695,2248648447,2282202623,2299045375,2332665087,2349507839,2383193343,2400035839,2416878591,2450498303,2467340799,2500960767,2517803263,2534646015,2568265727,2585108223,2602016511,2635636223,2652478719,2669321215,2686163967,2719783679,2736626175,2753468671,2770311167,2803996671,2820839167,2837681663,2854524159,2888143871,2904986367,2921828863,2938671359,2955579391,2972422143,2989264639,3022884351,3039726847,3056569343,3073411839,3090319871,3107162367,3124004863,3140847359,3157689855,3174532351,3191374847,3208217343,3225125375,3241967871,3258810623,3275653119,3292495615,3309338111,3326180607,3343023103,3359931135,3376773631,3393616127,3410458623,3427301119,3444143615,3460986367,3477828863,3494736895,3511579391,3511644671,3528487167,3545329663,3562172159,3579014655,3595922943,3612765439,3612830719,3629673215,3646515711,3663358207,3680200703,3697108735,3697174271,3714016767,3730859263,3747701759,3747767039,3764675071,3781517567,3798360319,3815202815,3815268095,3832176127,3849018623,3849083903,3865926399,3882769151,3899677183,3899742463,3916584959,3933427455,3933492735,3950400767,3967243519,3967308799,3984151295,3984282111,4001124607,4017967103,4018032383,4034940415,4035005951,4051848447,4068756479,4068821759,4085664255,4085729535,4102637567,4102703103,4119545599,4119676415,4136518911,4136584191,4136715007,4153557503,4153623039,4170531071,4170596351,4170727167,4187569663,4187634943,4204542975,4204608255,4204739327,4204804607,4221712639,4221777919,4221843199,4238751231,4238816767,4238947583,4239012863,4239143679,4239208959,4256117247,4256182527,4256313343,4256378623,4256509439,4256574975,4256705791,4256771071,4256902143,4256967423,4257098239,4257229311,4257294591,4257425663,4257490943,4257621759,4240910079,4241041151,4241171967,4241237503,4241368575,4224722175,4224787711,4224918783,4208272639,4208338175,4208469247,4191757567,4191888639,4175242495,4175373567,4158661887,4158793215,4142147071,4142212607,4125566719,4125697791,4108986111,4092339967,4092471039,4075824895,4075890431,4059244287,4042597887,4042662911,4026016255],t.Viridis3=[1140937983,546278655,4259783935],t.Viridis4=[1140937983,812092927,901216511,4259783935],t.Viridis5=[1140937983,995199743,546278655,1539859199,4259783935],t.Viridis6=[1140937983,1078167551,695766783,581403903,2043761151,4259783935],t.Viridis7=[1140937983,1144619775,812092927,546278655,901216511,2379629823,4259783935],t.Viridis8=[1140937983,1177648895,911903999,662605567,513837055,1237413375,2648259327,4259783935],t.Viridis9=[1140937983,1194031871,995199743,745639679,546278655,665682175,1539859199,2866492159,4259783935],t.Viridis10=[1140937983,1193768959,1045006847,812092927,629313279,513575423,901216511,1808620031,3000839423,4259783935],t.Viridis11=[1140937983,1210283263,1078167551,878611967,695766783,546278655,581403903,1119777279,2043761151,3135121407,4259783935],t.Viridis256=[1140937983,1141003775,1141069823,1157978367,1158044415,1158175743,1175018751,1175150335,1175216127,1175347711,1192190719,1192322047,1192388095,1192519423,1192585215,1192651263,1192782591,1209625599,1209691391,1209822975,1209888767,1209954559,1210085887,1210151679,1210217471,1210283263,1193637375,1193703167,1193768959,1193834751,1193966079,1194031871,1194097663,1177386239,1177517311,1177583103,1177648895,1160937471,1161068543,1161134335,1161200127,1144488447,1144619775,1127908351,1127973887,1128039679,1111327999,1111393791,1111524863,1094813439,1094878975,1078167551,1078233087,1061521407,1061652735,1044941055,1045006847,1028295167,1028360703,1028426239,1011714815,1011780351,995134207,995199743,978488319,978553855,961842175,961907711,945196031,945261823,928550143,928615679,911903999,911969535,895257855,895323391,878611967,878677503,861965823,862031359,845319679,845385215,828673535,828739071,828804607,812092927,812158463,795446783,795512319,778800895,778866431,778931967,762220287,762285823,745574143,745639679,745705215,728993535,729059071,712347391,712412927,712478463,695766783,695832319,679120639,679120639,679186175,662474495,662540031,662605567,645893887,645959423,646024959,629313279,629378559,612666879,612732415,612797951,596086271,596151807,596217343,579440127,579505663,579571199,562859519,562924799,562990335,546278655,546344191,546409727,529698047,529763327,529828863,529894399,529959935,513247999,513313535,513379071,513379071,513444351,513509887,513575423,513640703,513706239,513771775,513837055,530679807,530745087,530810623,547653119,547718655,564561407,564626687,581403903,598246399,598311679,615154431,631996927,648839679,665682175,682524671,699367423,716209919,733052415,749829631,783449343,800291839,817134335,850754303,867596799,901216511,918059007,951678719,968455935,1002075647,1035695359,1052537855,1086157567,1119777279,1153331455,1170173951,1203793663,1237413375,1271033087,1304587263,1338206719,1371826431,1405446143,1439065855,1472620031,1506239743,1539859199,1590256127,1623810303,1657430015,1691049471,1741446399,1775000575,1808620031,1842239743,1892570879,1926190591,1959810303,2010141439,2043761151,2094157823,2127711999,2178108671,2211662847,2262059519,2295678975,2346010367,2379629823,2429961215,2463580671,2513911807,2547531519,2597862655,2648259327,2681813247,2732210175,2782541311,2816160767,2866492159,2916888831,2950442751,3000839423,3051170815,3084724735,3135121407,3185452799,3219072255,3269403391,3319734783,3353354239,3403685631,3454017023,3487636735,3537968127,3571522303,3621919231,3672250623,3705805055,3756202239,3789756671,3840088319,3890485759,3924040191,3974372095,4007992319,4058324223,4091879167,4142276607,4175831551,4209386239,4259783935],t.Cividis3=[2116863,2071689471,4293477887],t.Cividis4=[2116863,1448897791,2795271679,4293477887],t.Cividis5=[2116863,1078750207,2071689471,3165548287,4293477887],t.Cividis6=[2116863,826567679,1718120703,2509207807,3401148927,4293477887],t.Cividis7=[2116863,574450943,1448897791,2071689471,2795271679,3552601599,4293477887],t.Cividis8=[2116863,356085247,1230203903,1819112191,2374531327,2997252863,3653657087,4293477887],t.Cividis9=[2116863,20279039,1078750207,1600351999,2071689471,2610132991,3165548287,3754647039,4293477887],t.Cividis10=[2116863,3370751,927427583,1448897791,1869640703,2307225855,2795271679,3283381503,3821886463,4293477887],t.Cividis11=[2116863,3239935,826567679,1297443839,1718120703,2071689471,2509207807,2929947647,3401148927,3872414207,4293477887],t.Cividis256=[2116863,2117375,2183423,2249215,2315263,2315775,2381567,2447615,2513663,2513919,2579967,2646015,2646527,2712575,2778367,2778879,2844927,2910975,2977023,2977279,3043071,3043327,3108863,3108863,3174399,3239935,3239935,3305215,3370751,3436287,3436287,20279039,104230655,171404799,238513663,305688063,356085247,389639679,440036607,473656575,507276543,540830975,574450943,608070911,641625343,658468095,692087807,725707775,742484991,776104959,792947711,826567679,843344895,860187647,893807615,910584831,927427583,944270335,977890303,994667519,1011510271,1028353023,1045130239,1078750207,1095592959,1112435711,1129212927,1146055679,1162898431,1179675647,1196518399,1213361151,1230203903,1246981119,1263823871,1280666623,1297443839,1314286591,1331129599,1347972351,1364749567,1381592319,1398435071,1415212287,1432055039,1448897791,1465740799,1482518015,1499360767,1516203519,1532980735,1549823487,1566666495,1583509247,1600286463,1600351999,1617194751,1634037759,1650814975,1667657727,1684500479,1701277695,1718120703,1734963455,1751806207,1751806207,1768649215,1785491967,1802334719,1819112191,1835954943,1852797695,1869574911,1869640703,1886483455,1903326207,1920103679,1936946431,1953789439,1970632191,1970632191,1987475199,2004317951,2021160703,2037938175,2054780927,2071623679,2071689471,2088466687,2105309439,2122152191,2138994943,2155772159,2172614911,2189457663,2206300415,2223077631,2239920383,2256763135,2273605887,2290448639,2307225855,2324068607,2340911359,2357754111,2374531327,2391374079,2408216831,2425059583,2441902335,2458679551,2475522303,2492365055,2509207807,2525984767,2542827519,2559670271,2576513023,2593355775,2610132991,2626975743,2643818495,2660660991,2677503743,2694346495,2711123711,2727966463,2744808959,2761651711,2778494463,2795271679,2812114431,2828956927,2845799679,2862642431,2879485183,2896262143,2913104895,2929947647,2946790399,2963632895,2980475647,2997252863,3030872575,3047715327,3064558079,3081400575,3098243327,3115020543,3131863039,3148705791,3165548287,3182391039,3199233791,3216076287,3232853503,3249695999,3266538751,3283381503,3317001215,3333843967,3350686463,3367529215,3384306175,3401148927,3417991423,3434834175,3451676671,3468519423,3502139135,3518981887,3535824383,3552601599,3569444095,3586286591,3603129343,3619971839,3636814335,3653657087,3687276799,3704119551,3720962047,3737804543,3754647039,3771424255,3788266751,3821886463,3838729215,3855571711,3872414207,3889256703,3906099199,3922941695,3956561663,3973404159,3990246655,4007089151,4023931647,4040774143,4057616639,4091236351,4108078847,4124921343,4141763839,4158606335,4192225791,4209002751,4225845247,4242687743,4259530239,4293149695,4293214975,4293280511,4293346303,4293412095,4293477887],t.Turbo3=[806501375,2717662719,7996418],t.Turbo4=[806501375,451196671,4189731071,7996418],t.Turbo5=[806501375,716828159,2717662719,4219478783,7996418],t.Turbo6=[806501375,1050410751,1190626303,3789305855,4015657471,7996418],t.Turbo7=[806501375,1166342655,451196671,2717662719,4189731071,3846572799,7996418],t.Turbo8=[806501375,1182133759,483252223,1643932927,3488232703,4271582719,3678013439,7996418],t.Turbo9=[806501375,1181278463,716828159,804297471,2717662719,3973134847,4219478783,3526362623,7996418],t.Turbo10=[806501375,1163974399,917043711,451196671,1912430591,3320787967,4189731071,4134213887,3408593919,7996418],t.Turbo11=[806501375,1146670079,1050410751,416664831,1190626303,2717662719,3789305855,4255527423,4015657471,3307602687,7996418],t.Turbo256=[806501375,823476991,840452863,874205695,891181311,908156927,925066751,942042367,959017727,975993343,992968703,1009944063,1010142207,1027052031,1044027135,1061002495,1077977599,1078175487,1095085055,1095282943,1112258047,1129233151,1129365247,1129562879,1146537983,1146670079,1163644671,1163842303,1163974399,1164171775,1181146623,1181278463,1181475839,1181607679,1181804799,1182002175,1182133759,1182331135,1182462719,1182659839,1182791423,1182988543,1166342655,1166539775,1166671103,1150090751,1133444607,1116864255,1100218111,1083637503,1066991359,1050410751,1016987135,1000406271,966982911,950402047,917043711,883620095,866973439,833615359,800191487,766833151,733409279,716828159,683404287,649980415,633399039,599975167,566551039,549904383,516545791,499899135,483252223,466605311,449958655,433311743,416664831,416795391,400148479,400213247,400343807,400474111,417381887,417446655,434354431,451196671,468038911,501723647,518565887,552185087,585869823,619489023,669885439,703504639,753901055,804297471,854693887,905155839,955552255,1005882879,1073056511,1123452927,1190626303,1257799935,1308196095,1375304191,1442477823,1509651199,1576759295,1643932927,1711040767,1778214399,1845322495,1912430591,1962827007,2029935103,2097043199,2164151295,2231259391,2281590271,2348698623,2399029503,2466137855,2516468991,2566800127,2617065727,2667396863,2717662719,2767993855,2801482495,2851813887,2902079487,2935568383,2985834239,3019388415,3069654527,3119854847,3153343743,3203609855,3237098495,3287364607,3320787967,3371054079,3404542975,3454743807,3488232703,3521656063,3571922431,3605345791,3638769151,3672258303,3722458879,3755882239,3789305855,3822729215,3856152831,3889641727,3906287871,3939711487,3973134847,3989780991,4023204351,4039850751,4073208575,4089854719,4106500863,4139924223,4156570111,4173216255,4189862399,4189731071,4206311423,4222957567,4222826239,4239406591,4239275263,4255855615,4255724287,4255527423,4255330559,4271976447,4271779583,4271582719,4271385855,4254411775,4254214655,4254017791,4253820927,4236846847,4236649727,4219675647,4219478783,4202504447,4202307583,4185333503,4168359167,4151385087,4151188223,4134213887,4117239807,4100265727,4083357183,4066383103,4049409023,4015657471,3998748927,3981774847,3964800767,3931115007,3914141183,3897232639,3863546879,3846572799,3812887295,3795978751,3762292991,3728607487,3711698943,3678013439,3644327679,3610642175,3593733631,3560048127,3526362623,3492742399,3459056895,3408593919,3374908415,3341288447,3307602687,3273917183,3223519999,3189834495,3139371519,3105751551,3055288831,3021668863,2971206143,2920808959,2887123455,2836726271,2786329087,2735866367,2685469183,2635071999,2584609279,2534212095,2483814911,2433417727,2383020543,2332623359,2265448959,2215051775,2164654847,2097480447,2047083263],t.Accent3=[2143911935,3199128831,4257253119],t.Accent4=[...t.Accent3,4294941183],t.Accent5=[...t.Accent4,946647295],t.Accent6=[...t.Accent5,4026695679],t.Accent7=[...t.Accent6,3210418175],t.Accent8=[...t.Accent7,1717987071],t.Dark2_3=[463370239,3646882559,1970320383],t.Dark2_4=[...t.Dark2_3,3878259455],t.Dark2_5=[...t.Dark2_4,1722162943],t.Dark2_6=[...t.Dark2_5,3869967103],t.Dark2_7=[...t.Dark2_6,2792758783],t.Dark2_8=[...t.Dark2_7,1717987071],t.Paired3=[2798576639,528004351,3000994559],t.Paired4=[...t.Paired3,866135295],t.Paired5=[...t.Paired4,4221213183],t.Paired6=[...t.Paired5,3810139391],t.Paired7=[...t.Paired6,4257181695],t.Paired8=[...t.Paired7,4286513407],t.Paired9=[...t.Paired8,3400718079],t.Paired10=[...t.Paired9,1782422271],t.Paired11=[...t.Paired10,4294941183],t.Paired12=[...t.Paired11,2975410431],t.Pastel1_3=[4222922495,3016614911,3438003711],t.Pastel1_4=[...t.Pastel1_3,3737904383],t.Pastel1_5=[...t.Pastel1_4,4275676927],t.Pastel1_6=[...t.Pastel1_5,4294954239],t.Pastel1_7=[...t.Pastel1_6,3856186879],t.Pastel1_8=[...t.Pastel1_7,4258983167],t.Pastel1_9=[...t.Pastel1_8,4076008191],t.Pastel2_3=[3017985535,4258114815,3419793663],t.Pastel2_4=[...t.Pastel2_3,4106937599],t.Pastel2_5=[...t.Pastel2_4,3874867711],t.Pastel2_6=[...t.Pastel2_5,4294094591],t.Pastel2_7=[...t.Pastel2_6,4058172671],t.Pastel2_8=[...t.Pastel2_7,3435973887],t.Set1_3=[3826916607,931051775,1303333631],t.Set1_4=[...t.Set1_3,2555290623],t.Set1_5=[...t.Set1_4,4286513407],t.Set1_6=[...t.Set1_5,4294915071],t.Set1_7=[...t.Set1_6,2790664447],t.Set1_8=[...t.Set1_7,4152475647],t.Set1_9=[...t.Set1_8,2576980479],t.Set2_3=[1724032511,4237124351,2376125439],t.Set2_4=[...t.Set2_3,3884631039],t.Set2_5=[...t.Set2_4,2799195391],t.Set2_6=[...t.Set2_5,4292423679],t.Set2_7=[...t.Set2_6,3854865663],t.Set2_8=[...t.Set2_7,3014898687],t.Set3_3=[2379466751,4294947839,3199916799],t.Set3_4=[...t.Set3_3,4219499263],t.Set3_5=[...t.Set3_4,2159137791],t.Set3_6=[...t.Set3_5,4256457471],t.Set3_7=[...t.Set3_6,3017697791],t.Set3_8=[...t.Set3_7,4241352191],t.Set3_9=[...t.Set3_8,3654932991],t.Set3_10=[...t.Set3_9,3162553855],t.Set3_11=[...t.Set3_10,3438003711],t.Set3_12=[...t.Set3_11,4293750783],t.Category10_3=[527938815,4286516991,748694783],t.Category10_4=[...t.Category10_3,3592890623],t.Category10_5=[...t.Category10_4,2489826815],t.Category10_6=[...t.Category10_5,2354465791],t.Category10_7=[...t.Category10_6,3816276735],t.Category10_8=[...t.Category10_7,2139062271],t.Category10_9=[...t.Category10_8,3166511871],t.Category10_10=[...t.Category10_9,398381055],t.Category20_3=[527938815,2932336895,4286516991],t.Category20_4=[...t.Category20_3,4290476287],t.Category20_5=[...t.Category20_4,748694783],t.Category20_6=[...t.Category20_5,2564786943],t.Category20_7=[...t.Category20_6,3592890623],t.Category20_8=[...t.Category20_7,4288190207],t.Category20_9=[...t.Category20_8,2489826815],t.Category20_10=[...t.Category20_9,3316700671],t.Category20_11=[...t.Category20_10,2354465791],t.Category20_12=[...t.Category20_11,3298596095],t.Category20_13=[...t.Category20_12,3816276735],t.Category20_14=[...t.Category20_13,4155953919],t.Category20_15=[...t.Category20_14,2139062271],t.Category20_16=[...t.Category20_15,3351758847],t.Category20_17=[...t.Category20_16,3166511871],t.Category20_18=[...t.Category20_17,3688599039],t.Category20_19=[...t.Category20_18,398381055],t.Category20_20=[...t.Category20_19,2665145855],t.Category20b_3=[960199167,1381278719,1802424319],t.Category20b_4=[...t.Category20b_3,2627657471],t.Category20b_5=[...t.Category20b_4,1668889087],t.Category20b_6=[...t.Category20b_5,2359448319],t.Category20b_7=[...t.Category20b_6,3050269695],t.Category20b_8=[...t.Category20b_7,3470499071],t.Category20b_9=[...t.Category20b_8,2355966463],t.Category20b_10=[...t.Category20b_9,3181263359],t.Category20b_11=[...t.Category20b_10,3887747839],t.Category20b_12=[...t.Category20b_11,3888878847],t.Category20b_13=[...t.Category20b_12,2218539519],t.Category20b_14=[...t.Category20b_13,2907261695],t.Category20b_15=[...t.Category20b_14,3596708863],t.Category20b_16=[...t.Category20b_15,3885407487],t.Category20b_17=[...t.Category20b_16,2067887103],t.Category20b_18=[...t.Category20b_17,2773587199],t.Category20b_19=[...t.Category20b_18,3463298559],t.Category20b_20=[...t.Category20b_19,3734951679],t.Category20c_3=[830651903,1806620415,2664096255],t.Category20c_4=[...t.Category20c_3,3336302591],t.Category20c_5=[...t.Category20c_4,3864333823],t.Category20c_6=[...t.Category20c_5,4253891839],t.Category20c_7=[...t.Category20c_6,4256066559],t.Category20c_8=[...t.Category20c_7,4258308863],t.Category20c_9=[...t.Category20c_8,832787711],t.Category20c_10=[...t.Category20c_9,1959032575],t.Category20c_11=[...t.Category20c_10,2715393023],t.Category20c_12=[...t.Category20c_11,3353985279],t.Category20c_13=[...t.Category20c_12,1969992191],t.Category20c_14=[...t.Category20c_13,2660944127],t.Category20c_15=[...t.Category20c_14,3166559487],t.Category20c_16=[...t.Category20c_15,3671780351],t.Category20c_17=[...t.Category20c_16,1667458047],t.Category20c_18=[...t.Category20c_17,2526451455],t.Category20c_19=[...t.Category20c_18,3183328767],t.Category20c_20=[...t.Category20c_19,3654932991],t.Colorblind3=[7516927,3869180159,4041491199],t.Colorblind4=[...t.Colorblind3,10384383],t.Colorblind5=[...t.Colorblind4,1454696959],t.Colorblind6=[...t.Colorblind5,3579707647],t.Colorblind7=[...t.Colorblind6,3430524927],t.Colorblind8=[...t.Colorblind7,255],t.Bright3=[1148693247,3999692799,579351551],t.Bright4=[...t.Bright3,3434824959],t.Bright5=[...t.Bright4,1724706559],t.Bright6=[...t.Bright5,2855499775],t.Bright7=[...t.Bright6,3149642751],t.HighContrast3=[4491519,3718919167,3142936319],t.Vibrant3=[4000789503,7846911,867954431],t.Vibrant4=[...t.Vibrant3,3996350463],t.Vibrant5=[...t.Vibrant4,3425899007],t.Vibrant6=[...t.Vibrant5,10062079],t.Vibrant7=[...t.Vibrant6,3149642751],t.Muted3=[3429267455,857901311,3721164799],t.Muted4=[...t.Muted3,293024767],t.Muted5=[...t.Muted4,2295131903],t.Muted6=[...t.Muted5,2283951615],t.Muted7=[...t.Muted6,1152031231],t.Muted8=[...t.Muted7,2576954367],t.Muted9=[...t.Muted8,2856622591],t.MediumContrast3=[1721355519,4491519,4006373119],t.MediumContrast4=[...t.MediumContrast3,2571392511],t.MediumContrast5=[...t.MediumContrast4,2574713087],t.MediumContrast6=[...t.MediumContrast5,4003048191],t.PaleTextBackground=[3150769919,3438215167,3437079295,4008623103,4291611903,3722305023],t.DarkText=[572675583,576017919,576004863,1717974015,1714631679,1431655935],t.Light3=[2007686655,4001916671,4007495935],t.Light4=[...t.Light3,4289379327],t.Light5=[...t.Light4,2581463039],t.Light6=[...t.Light5,1153145343],t.Light7=[...t.Light6,3150722047],t.Light8=[...t.Light7,2863268095],t.Light9=[...t.Light8,3722305023],t.Sunset3=[910924543,3941387519,2768250623],t.Sunset4=[910924543,2782062079,4257247999,2768250623],t.Sunset5=[910924543,2209929215,3941387519,4187511039,2768250623],t.Sunset6=[910924543,1856425471,3269783551,4275735551,4135472127,2768250623],t.Sunset7=[910924543,1637336575,2782062079,3941387519,4257247999,3983032831,2768250623],t.Sunset8=[910924543,1502462207,2462375679,3454461439,4175404543,4239090431,3881318911,2768250623],t.Sunset9=[910924543,1401208063,2209929215,2279468031,3941387519,4258300415,4187511039,3813487871,2768250623],t.Sunset10=[910924543,1316993535,2007945727,2782062079,3555188735,4125272063,4257247999,4152971775,3745788159,2768250623],t.Sunset11=[910924543,1249622015,1856425471,2563432959,3269783551,3941387519,4275735551,4256392959,4135472127,3711774207,2768250623],t.BuRd3=[560377087,4160223231,2987928575],t.BuRd4=[560377087,3151686399,4207522047,2987928575],t.BuRd5=[560377087,2462441215,4160223231,4104487679,2987928575],t.BuRd6=[560377087,1924256767,3639144959,4225814783,3901320447,2987928575],t.BuRd7=[560377087,1571015679,3151686399,4160223231,4207522047,3765919487,2987928575],t.BuRd8=[560377087,1318766335,2748244991,3790402559,4209499135,4139030015,3664336127,2987928575],t.BuRd9=[560377087,1133757439,2462441215,3521507583,4160223231,4259039231,4104487679,3596635647,2987928575],t.TolPRGn3=[1982497791,4160223231,460863487],t.TolPRGn4=[1982497791,3670335487,3404120575,460863487],t.TolPRGn5=[1982497791,3265646591,4160223231,2899549951,460863487],t.TolPRGn6=[1982497791,2978988287,3940281343,3740392191,2344912383,460863487],t.TolPRGn7=[1982497791,2793519103,3670335487,4160223231,3404120575,1975154175,460863487],t.TolPRGn8=[1982497791,2658644223,3434272511,3991138047,3857965823,3101404671,1706256895,460863487],t.TolPRGn9=[1982497791,2574298111,3265646591,3889490175,4160223231,3656438783,2899549951,1521377791,460863487],t.TolYlOrBr3=[4294960639,4221184511,1713702655],t.TolYlOrBr4=[4294960639,4274939391,3781431039,1713702655],t.TolYlOrBr5=[4294960639,4276326911,4221184511,3427533567,1713702655],t.TolYlOrBr6=[4294960639,4276855551,4256909311,4017625343,3074556671,1713702655],t.TolYlOrBr7=[4294960639,4277186047,4274939391,4221184511,3781431039,2856059903,1713702655],t.TolYlOrBr8=[4294960639,4277450239,4275732223,4239539967,4068613631,3579185151,2687960063,1713702655],t.TolYlOrBr9=[4294960639,4294425855,4276326911,4274278399,4221184511,3966768383,3427533567,2570323199,1713702655],t.Iridescent3=[4277922303,4244100607,4126392831],t.Iridescent4=[...t.Iridescent3,3941643775],t.Iridescent5=[...t.Iridescent4,3723280383],t.Iridescent6=[...t.Iridescent5,3504851711],t.Iridescent7=[...t.Iridescent6,3269710591],t.Iridescent8=[...t.Iridescent7,3051215103],t.Iridescent9=[...t.Iridescent8,2832784639],t.Iridescent10=[...t.Iridescent9,2614288895],t.Iridescent11=[...t.Iridescent10,2378949887],t.Iridescent12=[...t.Iridescent11,2177165311],t.Iridescent13=[...t.Iridescent12,2075977727],t.Iridescent14=[...t.Iridescent13,2125653247],t.Iridescent15=[...t.Iridescent14,2292571647],t.Iridescent16=[...t.Iridescent15,2476266239],t.Iridescent17=[...t.Iridescent16,2609562879],t.Iridescent18=[...t.Iridescent17,2642260735],t.Iridescent19=[...t.Iridescent18,2591071999],t.Iridescent20=[...t.Iridescent19,2422442239],t.Iridescent21=[...t.Iridescent20,2153214207],t.Iridescent22=[...t.Iridescent21,1749637119],t.Iridescent23=[...t.Iridescent22,1177893631],t.TolRainbow3=[426094847,4159723263,3691318527],t.TolRainbow4=[426094847,1320314367,4159723263,3691318527],t.TolRainbow5=[426094847,2075123455,1320314367,4159723263,3691318527],t.TolRainbow6=[426094847,2075123455,1320314367,3403721727,4159723263,3691318527],t.TolRainbow7=[2284745471,426094847,2075123455,1320314367,3403721727,4159723263,3691318527],t.TolRainbow8=[2284745471,426094847,2075123455,1320314367,3403721727,4159723263,4001375999,3691318527],t.TolRainbow9=[2284745471,426094847,2075123455,1320314367,3403721727,4159723263,4001375999,3691318527,1914244863],t.TolRainbow10=[2284745471,426094847,2075123455,1320314367,3403721727,4159723263,4104599295,3898612991,3691318527,1914244863],t.TolRainbow11=[2284745471,426094847,1384761343,2075123455,1320314367,3403721727,4159723263,4104599295,3898612991,3691318527,1914244863],t.TolRainbow12=[3518748671,2859441919,2284745471,426094847,1384761343,2075123455,1320314367,3403721727,4159723263,4104599295,3898612991,3691318527],t.TolRainbow13=[3518748671,2859441919,2284745471,426094847,1384761343,2075123455,1320314367,2429126655,3403721727,4159723263,4104599295,3898612991,3691318527],t.TolRainbow14=[3518748671,2859441919,2284745471,426094847,1384761343,2075123455,1320314367,2429126655,3403721727,4159723263,4139860479,4052954623,3898612991,3691318527],t.TolRainbow15=[3518748671,2859441919,2284745471,426094847,1384761343,2075123455,1320314367,2429126655,3403721727,4159723263,4139860479,4052954623,3898612991,3691318527,1914244863],t.TolRainbow16=[3518748671,3129849087,2859441919,2284745471,426094847,1384761343,2075123455,1320314367,2429126655,3403721727,4159723263,4139860479,4052954623,3898612991,3691318527,1914244863],t.TolRainbow17=[3518748671,3129849087,2859441919,2572126463,2284745471,426094847,1384761343,2075123455,1320314367,2429126655,3403721727,4159723263,4139860479,4052954623,3898612991,3691318527,1914244863],t.TolRainbow18=[3518748671,3129849087,2859441919,2572126463,2284745471,426094847,1384761343,2075123455,1320314367,2429126655,3403721727,4159723263,4139860479,4052954623,3898612991,3691318527,2769751807,1914244863],t.TolRainbow19=[3654083583,3400322047,3129849087,2859441919,2572126463,2284745471,426094847,1384761343,2075123455,1320314367,2429126655,3403721727,4159723263,4139860479,4052954623,3898612991,3691318527,2769751807,1914244863],t.TolRainbow20=[3654083583,3400322047,3129849087,2859441919,2572126463,2284745471,426094847,1132314623,1637208063,2075123455,1320314367,2429126655,3403721727,4159723263,4139860479,4052954623,3898612991,3691318527,2769751807,1914244863],t.TolRainbow21=[3654083583,3400322047,3129849087,2859441919,2572126463,2284745471,426094847,1132314623,1637208063,2075123455,1320314367,2429126655,3403721727,4159723263,4157294079,4104599295,4001375999,3864336639,3691318527,2769751807,1914244863],t.TolRainbow22=[3654083583,3400322047,3129849087,2859441919,2572126463,2284745471,426094847,1132314623,1637208063,2075123455,1320314367,2429126655,3403721727,4159723263,4157294079,4104599295,4001375999,3864336639,3691318527,2769751807,1914244863,1108675327],t.TolRainbow23=[3907845119,3654083583,3400322047,3129849087,2859441919,2572126463,2284745471,426094847,1132314623,1637208063,2075123455,1320314367,2429126655,3403721727,4159723263,4157294079,4104599295,4001375999,3864336639,3691318527,2769751807,1914244863,1108675327],t.YlGn={YlGn3:t.YlGn3,YlGn4:t.YlGn4,YlGn5:t.YlGn5,YlGn6:t.YlGn6,YlGn7:t.YlGn7,YlGn8:t.YlGn8,YlGn9:t.YlGn9},t.YlGnBu={YlGnBu3:t.YlGnBu3,YlGnBu4:t.YlGnBu4,YlGnBu5:t.YlGnBu5,YlGnBu6:t.YlGnBu6,YlGnBu7:t.YlGnBu7,YlGnBu8:t.YlGnBu8,YlGnBu9:t.YlGnBu9},t.GnBu={GnBu3:t.GnBu3,GnBu4:t.GnBu4,GnBu5:t.GnBu5,GnBu6:t.GnBu6,GnBu7:t.GnBu7,GnBu8:t.GnBu8,GnBu9:t.GnBu9},t.BuGn={BuGn3:t.BuGn3,BuGn4:t.BuGn4,BuGn5:t.BuGn5,BuGn6:t.BuGn6,BuGn7:t.BuGn7,BuGn8:t.BuGn8,BuGn9:t.BuGn9},t.PuBuGn={PuBuGn3:t.PuBuGn3,PuBuGn4:t.PuBuGn4,PuBuGn5:t.PuBuGn5,PuBuGn6:t.PuBuGn6,PuBuGn7:t.PuBuGn7,PuBuGn8:t.PuBuGn8,PuBuGn9:t.PuBuGn9},t.PuBu={PuBu3:t.PuBu3,PuBu4:t.PuBu4,PuBu5:t.PuBu5,PuBu6:t.PuBu6,PuBu7:t.PuBu7,PuBu8:t.PuBu8,PuBu9:t.PuBu9},t.BuPu={BuPu3:t.BuPu3,BuPu4:t.BuPu4,BuPu5:t.BuPu5,BuPu6:t.BuPu6,BuPu7:t.BuPu7,BuPu8:t.BuPu8,BuPu9:t.BuPu9},t.RdPu={RdPu3:t.RdPu3,RdPu4:t.RdPu4,RdPu5:t.RdPu5,RdPu6:t.RdPu6,RdPu7:t.RdPu7,RdPu8:t.RdPu8,RdPu9:t.RdPu9},t.PuRd={PuRd3:t.PuRd3,PuRd4:t.PuRd4,PuRd5:t.PuRd5,PuRd6:t.PuRd6,PuRd7:t.PuRd7,PuRd8:t.PuRd8,PuRd9:t.PuRd9},t.OrRd={OrRd3:t.OrRd3,OrRd4:t.OrRd4,OrRd5:t.OrRd5,OrRd6:t.OrRd6,OrRd7:t.OrRd7,OrRd8:t.OrRd8,OrRd9:t.OrRd9},t.YlOrRd={YlOrRd3:t.YlOrRd3,YlOrRd4:t.YlOrRd4,YlOrRd5:t.YlOrRd5,YlOrRd6:t.YlOrRd6,YlOrRd7:t.YlOrRd7,YlOrRd8:t.YlOrRd8,YlOrRd9:t.YlOrRd9},t.YlOrBr={YlOrBr3:t.YlOrBr3,YlOrBr4:t.YlOrBr4,YlOrBr5:t.YlOrBr5,YlOrBr6:t.YlOrBr6,YlOrBr7:t.YlOrBr7,YlOrBr8:t.YlOrBr8,YlOrBr9:t.YlOrBr9},t.Purples={Purples3:t.Purples3,Purples4:t.Purples4,Purples5:t.Purples5,Purples6:t.Purples6,Purples7:t.Purples7,Purples8:t.Purples8,Purples9:t.Purples9},t.Blues={Blues3:t.Blues3,Blues4:t.Blues4,Blues5:t.Blues5,Blues6:t.Blues6,Blues7:t.Blues7,Blues8:t.Blues8,Blues9:t.Blues9},t.Greens={Greens3:t.Greens3,Greens4:t.Greens4,Greens5:t.Greens5,Greens6:t.Greens6,Greens7:t.Greens7,Greens8:t.Greens8,Greens9:t.Greens9},t.Oranges={Oranges3:t.Oranges3,Oranges4:t.Oranges4,Oranges5:t.Oranges5,Oranges6:t.Oranges6,Oranges7:t.Oranges7,Oranges8:t.Oranges8,Oranges9:t.Oranges9},t.Reds={Reds3:t.Reds3,Reds4:t.Reds4,Reds5:t.Reds5,Reds6:t.Reds6,Reds7:t.Reds7,Reds8:t.Reds8,Reds9:t.Reds9},t.Greys={Greys3:t.Greys3,Greys4:t.Greys4,Greys5:t.Greys5,Greys6:t.Greys6,Greys7:t.Greys7,Greys8:t.Greys8,Greys9:t.Greys9,Greys10:t.Greys10,Greys11:t.Greys11,Greys256:t.Greys256},t.PuOr={PuOr3:t.PuOr3,PuOr4:t.PuOr4,PuOr5:t.PuOr5,PuOr6:t.PuOr6,PuOr7:t.PuOr7,PuOr8:t.PuOr8,PuOr9:t.PuOr9,PuOr10:t.PuOr10,PuOr11:t.PuOr11},t.BrBG={BrBG3:t.BrBG3,BrBG4:t.BrBG4,BrBG5:t.BrBG5,BrBG6:t.BrBG6,BrBG7:t.BrBG7,BrBG8:t.BrBG8,BrBG9:t.BrBG9,BrBG10:t.BrBG10,BrBG11:t.BrBG11},t.PRGn={PRGn3:t.PRGn3,PRGn4:t.PRGn4,PRGn5:t.PRGn5,PRGn6:t.PRGn6,PRGn7:t.PRGn7,PRGn8:t.PRGn8,PRGn9:t.PRGn9,PRGn10:t.PRGn10,PRGn11:t.PRGn11},t.PiYG={PiYG3:t.PiYG3,PiYG4:t.PiYG4,PiYG5:t.PiYG5,PiYG6:t.PiYG6,PiYG7:t.PiYG7,PiYG8:t.PiYG8,PiYG9:t.PiYG9,PiYG10:t.PiYG10,PiYG11:t.PiYG11},t.RdBu={RdBu3:t.RdBu3,RdBu4:t.RdBu4,RdBu5:t.RdBu5,RdBu6:t.RdBu6,RdBu7:t.RdBu7,RdBu8:t.RdBu8,RdBu9:t.RdBu9,RdBu10:t.RdBu10,RdBu11:t.RdBu11},t.RdGy={RdGy3:t.RdGy3,RdGy4:t.RdGy4,RdGy5:t.RdGy5,RdGy6:t.RdGy6,RdGy7:t.RdGy7,RdGy8:t.RdGy8,RdGy9:t.RdGy9,RdGy10:t.RdGy10,RdGy11:t.RdGy11},t.RdYlBu={RdYlBu3:t.RdYlBu3,RdYlBu4:t.RdYlBu4,RdYlBu5:t.RdYlBu5,RdYlBu6:t.RdYlBu6,RdYlBu7:t.RdYlBu7,RdYlBu8:t.RdYlBu8,RdYlBu9:t.RdYlBu9,RdYlBu10:t.RdYlBu10,RdYlBu11:t.RdYlBu11},t.Spectral={Spectral3:t.Spectral3,Spectral4:t.Spectral4,Spectral5:t.Spectral5,Spectral6:t.Spectral6,Spectral7:t.Spectral7,Spectral8:t.Spectral8,Spectral9:t.Spectral9,Spectral10:t.Spectral10,Spectral11:t.Spectral11},t.RdYlGn={RdYlGn3:t.RdYlGn3,RdYlGn4:t.RdYlGn4,RdYlGn5:t.RdYlGn5,RdYlGn6:t.RdYlGn6,RdYlGn7:t.RdYlGn7,RdYlGn8:t.RdYlGn8,RdYlGn9:t.RdYlGn9,RdYlGn10:t.RdYlGn10,RdYlGn11:t.RdYlGn11},t.Bokeh={Bokeh3:t.Bokeh3,Bokeh4:t.Bokeh4,Bokeh5:t.Bokeh5,Bokeh6:t.Bokeh6,Bokeh7:t.Bokeh7,Bokeh8:t.Bokeh8},t.Inferno={Inferno3:t.Inferno3,Inferno4:t.Inferno4,Inferno5:t.Inferno5,Inferno6:t.Inferno6,Inferno7:t.Inferno7,Inferno8:t.Inferno8,Inferno9:t.Inferno9,Inferno10:t.Inferno10,Inferno11:t.Inferno11,Inferno256:t.Inferno256},t.Magma={Magma3:t.Magma3,Magma4:t.Magma4,Magma5:t.Magma5,Magma6:t.Magma6,Magma7:t.Magma7,Magma8:t.Magma8,Magma9:t.Magma9,Magma10:t.Magma10,Magma11:t.Magma11,Magma256:t.Magma256},t.Plasma={Plasma3:t.Plasma3,Plasma4:t.Plasma4,Plasma5:t.Plasma5,Plasma6:t.Plasma6,Plasma7:t.Plasma7,Plasma8:t.Plasma8,Plasma9:t.Plasma9,Plasma10:t.Plasma10,Plasma11:t.Plasma11,Plasma256:t.Plasma256},t.Viridis={Viridis3:t.Viridis3,Viridis4:t.Viridis4,Viridis5:t.Viridis5,Viridis6:t.Viridis6,Viridis7:t.Viridis7,Viridis8:t.Viridis8,Viridis9:t.Viridis9,Viridis10:t.Viridis10,Viridis11:t.Viridis11,Viridis256:t.Viridis256},t.Cividis={Cividis3:t.Cividis3,Cividis4:t.Cividis4,Cividis5:t.Cividis5,Cividis6:t.Cividis6,Cividis7:t.Cividis7,Cividis8:t.Cividis8,Cividis9:t.Cividis9,Cividis10:t.Cividis10,Cividis11:t.Cividis11,Cividis256:t.Cividis256},t.Turbo={Turbo3:t.Turbo3,Turbo4:t.Turbo4,Turbo5:t.Turbo5,Turbo6:t.Turbo6,Turbo7:t.Turbo7,Turbo8:t.Turbo8,Turbo9:t.Turbo9,Turbo10:t.Turbo10,Turbo11:t.Turbo11,Turbo256:t.Turbo256},t.Accent={Accent3:t.Accent3,Accent4:t.Accent4,Accent5:t.Accent5,Accent6:t.Accent6,Accent7:t.Accent7,Accent8:t.Accent8},t.Dark2={Dark2_3:t.Dark2_3,Dark2_4:t.Dark2_4,Dark2_5:t.Dark2_5,Dark2_6:t.Dark2_6,Dark2_7:t.Dark2_7,Dark2_8:t.Dark2_8},t.Paired={Paired3:t.Paired3,Paired4:t.Paired4,Paired5:t.Paired5,Paired6:t.Paired6,Paired7:t.Paired7,Paired8:t.Paired8,Paired9:t.Paired9,Paired10:t.Paired10,Paired11:t.Paired11,Paired12:t.Paired12},t.Pastel1={Pastel1_3:t.Pastel1_3,Pastel1_4:t.Pastel1_4,Pastel1_5:t.Pastel1_5,Pastel1_6:t.Pastel1_6,Pastel1_7:t.Pastel1_7,Pastel1_8:t.Pastel1_8,Pastel1_9:t.Pastel1_9},t.Pastel2={Pastel2_3:t.Pastel2_3,Pastel2_4:t.Pastel2_4,Pastel2_5:t.Pastel2_5,Pastel2_6:t.Pastel2_6,Pastel2_7:t.Pastel2_7,Pastel2_8:t.Pastel2_8},t.Set1={Set1_3:t.Set1_3,Set1_4:t.Set1_4,Set1_5:t.Set1_5,Set1_6:t.Set1_6,Set1_7:t.Set1_7,Set1_8:t.Set1_8,Set1_9:t.Set1_9},t.Set2={Set2_3:t.Set2_3,Set2_4:t.Set2_4,Set2_5:t.Set2_5,Set2_6:t.Set2_6,Set2_7:t.Set2_7,Set2_8:t.Set2_8},t.Set3={Set3_3:t.Set3_3,Set3_4:t.Set3_4,Set3_5:t.Set3_5,Set3_6:t.Set3_6,Set3_7:t.Set3_7,Set3_8:t.Set3_8,Set3_9:t.Set3_9,Set3_10:t.Set3_10,Set3_11:t.Set3_11,Set3_12:t.Set3_12},t.Category10={Category10_3:t.Category10_3,Category10_4:t.Category10_4,Category10_5:t.Category10_5,Category10_6:t.Category10_6,Category10_7:t.Category10_7,Category10_8:t.Category10_8,Category10_9:t.Category10_9,Category10_10:t.Category10_10},t.Category20={Category20_3:t.Category20_3,Category20_4:t.Category20_4,Category20_5:t.Category20_5,Category20_6:t.Category20_6,Category20_7:t.Category20_7,Category20_8:t.Category20_8,Category20_9:t.Category20_9,Category20_10:t.Category20_10,Category20_11:t.Category20_11,Category20_12:t.Category20_12,Category20_13:t.Category20_13,Category20_14:t.Category20_14,Category20_15:t.Category20_15,Category20_16:t.Category20_16,Category20_17:t.Category20_17,Category20_18:t.Category20_18,Category20_19:t.Category20_19,Category20_20:t.Category20_20},t.Category20b={Category20b_3:t.Category20b_3,Category20b_4:t.Category20b_4,Category20b_5:t.Category20b_5,Category20b_6:t.Category20b_6,Category20b_7:t.Category20b_7,Category20b_8:t.Category20b_8,Category20b_9:t.Category20b_9,Category20b_10:t.Category20b_10,Category20b_11:t.Category20b_11,Category20b_12:t.Category20b_12,Category20b_13:t.Category20b_13,Category20b_14:t.Category20b_14,Category20b_15:t.Category20b_15,Category20b_16:t.Category20b_16,Category20b_17:t.Category20b_17,Category20b_18:t.Category20b_18,Category20b_19:t.Category20b_19,Category20b_20:t.Category20b_20},t.Category20c={Category20c_3:t.Category20c_3,Category20c_4:t.Category20c_4,Category20c_5:t.Category20c_5,Category20c_6:t.Category20c_6,Category20c_7:t.Category20c_7,Category20c_8:t.Category20c_8,Category20c_9:t.Category20c_9,Category20c_10:t.Category20c_10,Category20c_11:t.Category20c_11,Category20c_12:t.Category20c_12,Category20c_13:t.Category20c_13,Category20c_14:t.Category20c_14,Category20c_15:t.Category20c_15,Category20c_16:t.Category20c_16,Category20c_17:t.Category20c_17,Category20c_18:t.Category20c_18,Category20c_19:t.Category20c_19,Category20c_20:t.Category20c_20},t.Colorblind={Colorblind3:t.Colorblind3,Colorblind4:t.Colorblind4,Colorblind5:t.Colorblind5,Colorblind6:t.Colorblind6,Colorblind7:t.Colorblind7,Colorblind8:t.Colorblind8},t.Bright={Bright3:t.Bright3,Bright4:t.Bright4,Bright5:t.Bright5,Bright6:t.Bright6,Bright7:t.Bright7},t.HighContrast={HighContrast3:t.HighContrast3},t.Vibrant={Vibrant3:t.Vibrant3,Vibrant4:t.Vibrant4,Vibrant5:t.Vibrant5,Vibrant6:t.Vibrant6,Vibrant7:t.Vibrant7},t.Muted={Muted3:t.Muted3,Muted4:t.Muted4,Muted5:t.Muted5,Muted6:t.Muted6,Muted7:t.Muted7,Muted8:t.Muted8},t.MediumContrast={MediumContrast3:t.MediumContrast3,MediumContrast4:t.MediumContrast4,MediumContrast5:t.MediumContrast5,MediumContrast6:t.MediumContrast6},t.Light={Light3:t.Light3,Light4:t.Light4,Light5:t.Light5,Light6:t.Light6,Light7:t.Light7,Light8:t.Light8,Light9:t.Light9},t.Sunset={Sunset3:t.Sunset3,Sunset4:t.Sunset4,Sunset5:t.Sunset5,Sunset6:t.Sunset6,Sunset7:t.Sunset7,Sunset8:t.Sunset8,Sunset9:t.Sunset9,Sunset10:t.Sunset10,Sunset11:t.Sunset11},t.BuRd={BuRd3:t.BuRd3,BuRd4:t.BuRd4,BuRd5:t.BuRd5,BuRd6:t.BuRd6,BuRd7:t.BuRd7,BuRd8:t.BuRd8,BuRd9:t.BuRd9},t.TolPRGn={TolPRGn3:t.TolPRGn3,TolPRGn4:t.TolPRGn4,TolPRGn5:t.TolPRGn5,TolPRGn6:t.TolPRGn6,TolPRGn7:t.TolPRGn7,TolPRGn8:t.TolPRGn8,TolPRGn9:t.TolPRGn9},t.TolYlOrBr={TolYlOrBr3:t.TolYlOrBr3,TolYlOrBr4:t.TolYlOrBr4,TolYlOrBr5:t.TolYlOrBr5,TolYlOrBr6:t.TolYlOrBr6,TolYlOrBr7:t.TolYlOrBr7,TolYlOrBr8:t.TolYlOrBr8,TolYlOrBr9:t.TolYlOrBr9},t.Iridescent={Iridescent3:t.Iridescent3,Iridescent4:t.Iridescent4,Iridescent5:t.Iridescent5,Iridescent6:t.Iridescent6,Iridescent7:t.Iridescent7,Iridescent8:t.Iridescent8,Iridescent9:t.Iridescent9,Iridescent10:t.Iridescent10,Iridescent11:t.Iridescent11,Iridescent12:t.Iridescent12,Iridescent13:t.Iridescent13,Iridescent14:t.Iridescent14,Iridescent15:t.Iridescent15,Iridescent16:t.Iridescent16,Iridescent17:t.Iridescent17,Iridescent18:t.Iridescent18,Iridescent19:t.Iridescent19,Iridescent20:t.Iridescent20,Iridescent21:t.Iridescent21,Iridescent22:t.Iridescent22,Iridescent23:t.Iridescent23},t.TolRainbow={TolRainbow3:t.TolRainbow3,TolRainbow4:t.TolRainbow4,TolRainbow5:t.TolRainbow5,TolRainbow6:t.TolRainbow6,TolRainbow7:t.TolRainbow7,TolRainbow8:t.TolRainbow8,TolRainbow9:t.TolRainbow9,TolRainbow10:t.TolRainbow10,TolRainbow11:t.TolRainbow11,TolRainbow12:t.TolRainbow12,TolRainbow13:t.TolRainbow13,TolRainbow14:t.TolRainbow14,TolRainbow15:t.TolRainbow15,TolRainbow16:t.TolRainbow16,TolRainbow17:t.TolRainbow17,TolRainbow18:t.TolRainbow18,TolRainbow19:t.TolRainbow19,TolRainbow20:t.TolRainbow20,TolRainbow21:t.TolRainbow21,TolRainbow22:t.TolRainbow22,TolRainbow23:t.TolRainbow23},t.brewer={YlGn:t.YlGn,YlGnBu:t.YlGnBu,GnBu:t.GnBu,BuGn:t.BuGn,PuBuGn:t.PuBuGn,PuBu:t.PuBu,BuPu:t.BuPu,RdPu:t.RdPu,PuRd:t.PuRd,OrRd:t.OrRd,YlOrRd:t.YlOrRd,YlOrBr:t.YlOrBr,Purples:t.Purples,Blues:t.Blues,Greens:t.Greens,Oranges:t.Oranges,Reds:t.Reds,Greys:t.Greys,PuOr:t.PuOr,BrBG:t.BrBG,PRGn:t.PRGn,PiYG:t.PiYG,RdBu:t.RdBu,RdGy:t.RdGy,RdYlBu:t.RdYlBu,Spectral:t.Spectral,RdYlGn:t.RdYlGn,Accent:t.Accent,Dark2:t.Dark2,Paired:t.Paired,Pastel1:t.Pastel1,Pastel2:t.Pastel2,Set1:t.Set1,Set2:t.Set2,Set3:t.Set3},t.d3={Category10:t.Category10,Category20:t.Category20,Category20b:t.Category20b,Category20c:t.Category20c},t.bokeh={Bokeh:t.Bokeh},t.mpl={Magma:t.Magma,Inferno:t.Inferno,Plasma:t.Plasma,Viridis:t.Viridis,Cividis:t.Cividis,Turbo:t.Turbo},t.tol={Bright:t.Bright,HighContrast:t.HighContrast,Vibrant:t.Vibrant,Muted:t.Muted,MediumContrast:t.MediumContrast,Light:t.Light,Sunset:t.Sunset,BuRd:t.BuRd,TolPRGn:t.TolPRGn,TolYlOrBr:t.TolYlOrBr,Iridescent:t.Iridescent,TolRainbow:t.TolRainbow},t.colorblind={Colorblind:t.Colorblind}},
587: function _(t,_,n,o,r){o();t(1).__exportStar(t(77),n)},
588: function _(o,r,i,g,u){g();var c=o(589);u("figure",c.figure),u("Figure",c.Figure),u("show",o(592).show),u("gridplot",o(593).gridplot),u("color",o(22).color2css)},
589: function _(e,t,n,s,i){var o;s(),n.figure=function(e){return new A(e)};const r=e(1),a=e(28),l=e(18),c=e(590),_=e(26),d=e(10),u=e(9),g=e(8),h=e(34),f=r.__importStar(e(30)),p=e(587),x=e(284),m=e(285),y=e(453),w=e(300),v=e(591),b=["pan","wheel_zoom","box_zoom","save","reset","help"];class S{constructor(e){this.models=e;const t=new Map;for(const n of e)for(const e of n){const{attr:n}=e;t.has(n)||t.set(n,[]),t.get(n).push(e)}for(const[e,n]of t)Object.defineProperty(this,e,{get(){throw new Error("only setting values is supported")},set(t){for(const s of n)s.obj.setv({[e]:t});return this}})}each(e){let t=0;for(const n of this.models)e(n,t++)}*[Symbol.iterator](){yield*this.models}}S.__name__="ModelProxy";class k extends v.GlyphAPI{constructor(e,t){super(),this.coordinates=e,this.parent=t}_glyph(e,t,n,s,i){const{coordinates:o}=this;return this.parent._glyph(e,t,n,s,{coordinates:o,...i})}}n.SubFigure=k,k.__name__="SubFigure";class A extends y.Figure{get xaxes(){return[...this.below,...this.above].filter((e=>e instanceof p.Axis))}get yaxes(){return[...this.left,...this.right].filter((e=>e instanceof p.Axis))}get axes(){return[...this.below,...this.above,...this.left,...this.right].filter((e=>e instanceof p.Axis))}get xaxis(){return new S(this.xaxes)}get yaxis(){return new S(this.yaxes)}get axis(){return new S(this.axes)}get xgrids(){return this.center.filter((e=>e instanceof p.Grid)).filter((e=>0==e.dimension))}get ygrids(){return this.center.filter((e=>e instanceof p.Grid)).filter((e=>1==e.dimension))}get grids(){return this.center.filter((e=>e instanceof p.Grid))}get xgrid(){return new S(this.xgrids)}get ygrid(){return new S(this.ygrids)}get grid(){return new S(this.grids)}get legend(){const e=this.panels.filter((e=>e instanceof x.Legend));if(0==e.length){const e=new x.Legend;return this.add_layout(e),e}{const[t]=e;return t}}constructor(e={}){const t=void 0===(e={...e}).x_axis_type?"auto":e.x_axis_type,n=void 0===e.y_axis_type?"auto":e.y_axis_type;delete e.x_axis_type,delete e.y_axis_type;const s=e.x_minor_ticks??"auto",i=e.y_minor_ticks??"auto";delete e.x_minor_ticks,delete e.y_minor_ticks;const r=void 0===e.x_axis_location?"below":e.x_axis_location,a=void 0===e.y_axis_location?"left":e.y_axis_location;delete e.x_axis_location,delete e.y_axis_location;const l=e.x_axis_label??"",c=e.y_axis_label??"";delete e.x_axis_label,delete e.y_axis_label;const _=o._get_range(e.x_range),d=o._get_range(e.y_range);delete e.x_range,delete e.y_range;const u=e.x_scale??o._get_scale(_,t),h=e.y_scale??o._get_scale(d,n);delete e.x_scale,delete e.y_scale;const{active_drag:f,active_inspect:x,active_scroll:m,active_tap:y,active_multi:v}=e;delete e.active_drag,delete e.active_inspect,delete e.active_scroll,delete e.active_tap,delete e.active_multi;const S=(()=>{const{tools:t,toolbar:n}=e;if(null!=t){if(null!=n)throw new Error("'tools' and 'toolbar' can't be used together");return delete e.tools,(0,g.isString)(t)?t.split(",").map((e=>e.trim())).filter((e=>e.length>0)):t}return null!=n?null:b})();super({...e,x_range:_,y_range:d,x_scale:u,y_scale:h}),this._process_axis_and_grid(t,r,s,l,_,0),this._process_axis_and_grid(n,a,i,c,d,1);const k=new Map;if(null!=S){const e=S.map((e=>{if(e instanceof p.Tool)return e;{const t=p.Tool.from_string(e);return k.set(e,t),t}}));this.add_tools(...e)}if((0,g.isString)(f)&&"auto"!=f){const e=k.get(f);(e instanceof w.GestureTool||e instanceof p.ToolProxy)&&(this.toolbar.active_drag=e)}else void 0!==f&&(this.toolbar.active_drag=f);if((0,g.isString)(x)&&"auto"!=x){const e=k.get(x);null!=e&&(this.toolbar.active_inspect=e)}else void 0!==x&&(this.toolbar.active_inspect=x);if((0,g.isString)(m)&&"auto"!=m){const e=k.get(m);(e instanceof w.GestureTool||e instanceof p.ToolProxy)&&(this.toolbar.active_scroll=e)}else void 0!==m&&(this.toolbar.active_scroll=m);if((0,g.isString)(y)&&"auto"!=y){const e=k.get(y);(e instanceof w.GestureTool||e instanceof p.ToolProxy)&&(this.toolbar.active_tap=e)}else void 0!==y&&(this.toolbar.active_tap=y);if((0,g.isString)(v)&&"auto"!=v){const e=k.get(v);(e instanceof w.GestureTool||e instanceof p.ToolProxy)&&(this.toolbar.active_multi=e)}else void 0!==v&&(this.toolbar.active_multi=v)}get coordinates(){return null}subplot(e){const t=new p.CoordinateMapping(e);return new k(t,this)}_pop_visuals(e,t,n="",s={},i={}){const o=function(e){const t=e.split("_",2);return 2==t.length?t:t.concat([""])},r=function(e){const[t,n]=o(e);return(0,d.includes)(["line","fill","hatch","text","global"],t)&&""!==n};s={...s};const a={},l=(0,u.dict)(t),c=(0,u.dict)(e.prototype._props),_=(0,u.dict)(s),g=(0,u.dict)(a),h=(0,u.dict)(i);_.has("text_color")||(s.text_color="black"),_.has("hatch_color")||(s.hatch_color="black"),g.has("color")||(a.color="#1f77b4"),g.has("alpha")||(a.alpha=1);const f={},p=new Set;for(const d of(0,u.keys)(e.prototype._props))if(r(d)){const e=o(d)[1];l.has(n+d)?(f[d]=t[n+d],delete t[n+d]):!c.has(e)&&l.has(n+e)?f[d]=t[n+e]:h.has(e)?f[d]=i[e]:_.has(d)?f[d]=s[d]:g.has(e)&&(f[d]=a[e]),c.has(e)||p.add(e)}for(const e of p)delete t[n+e];return f}_find_uniq_name(e,t){let n=1;for(;;){const s=`${t}__${n}`;if(!e.has(s))return s;n+=1}}_fixup_values(e,t,n){const s=new Set,i=(0,u.dict)(e.prototype._props);for(const[e,o]of(0,u.entries)(n)){const r=i.get(e);if(null!=r){if(r.type.prototype instanceof l.VectorSpec){if(null!=o)if((0,g.isArray)(o)||f.is_NDArray(o)){let s;t.has(e)?t.get(e)!==o?(s=this._find_uniq_name(t,e),t.set(s,o)):s=e:(s=e,t.set(s,o)),n[e]={field:s}}else((0,g.isNumber)(o)||(0,g.isString)(o))&&(n[e]={value:o});if(r.type.prototype instanceof l.UnitsSpec){const t=`${e}_units`,i=n[t];void 0!==i&&(n[e]={...n[e],units:i},s.delete(t),delete n[t])}}}else s.add(e)}return s}_signature(e,t){return`the method signature is ${e}(${t.join(", ")}, args?)`}_glyph(e,t,n,s,i={}){let o;const r=s.length,l=n.length;if(r==l||r==l+1){o={};for(const[[e,i],r]of(0,h.enumerate)((0,d.zip)(n,s))){if((0,g.isPlainObject)(i)&&!(0,a.isVectorized)(i))throw new Error(`invalid value for '${e}' parameter at position ${r}; ${this._signature(t,n)}`);o[e]=i}if(r==l+1){const e=s[r-1];if(!(0,g.isPlainObject)(e)||(0,a.isVectorized)(e))throw new Error(`expected optional arguments; ${this._signature(t,n)}`);o={...o,...s[s.length-1]}}}else if(0==r)o={};else{if(1!=r)throw new Error(`wrong number of arguments; ${this._signature(t,n)}`);o={...s[0]}}o={...o,...i};const c=(()=>{const{source:e}=o;return null==e?new p.ColumnDataSource:e instanceof p.ColumnarDataSource?e:new p.ColumnDataSource({data:e})})(),_=(0,u.clone)(c.data);delete o.source;const{view:f}=o;delete o.view;const x=o.legend;delete o.legend;const m=o.legend_label;delete o.legend_label;const y=o.legend_field;delete o.legend_field;const w=o.legend_group;if(delete o.legend_group,[x,m,y,w].filter((e=>null!=e)).length>1)throw new Error("only one of legend, legend_label, legend_field, legend_group can be specified");const v=o.name;delete o.name;const b=o.level;delete o.level;const S=o.visible;delete o.visible;const k=o.x_range_name;delete o.x_range_name;const A=o.y_range_name;delete o.y_range_name;const L=o.coordinates;delete o.coordinates;const $=this._pop_visuals(e,o),E=this._pop_visuals(e,o,"nonselection_",$,{alpha:.1}),G=this._pop_visuals(e,o,"selection_",$),T=this._pop_visuals(e,o,"hover_",$),P=this._pop_visuals(e,o,"muted_",$,{alpha:.2}),R=(0,u.dict)(_);this._fixup_values(e,R,$),this._fixup_values(e,R,E),this._fixup_values(e,R,G),this._fixup_values(e,R,T),this._fixup_values(e,R,P),this._fixup_values(e,R,o),c.data=_;const C=(e,t,n)=>new e({...t,...n}),F=C(e,o,$),D=(0,u.is_empty)(E)?"auto":C(e,o,E),I=(0,u.is_empty)(G)?"auto":C(e,o,G),j=(0,u.is_empty)(T)?void 0:C(e,o,T),q=(0,u.is_empty)(P)?"auto":C(e,o,P),z=new p.GlyphRenderer({data_source:c,view:f,glyph:F,nonselection_glyph:D,selection_glyph:I,hover_glyph:j,muted_glyph:q,name:v,level:b,visible:S,x_range_name:k,y_range_name:A,coordinates:L});return null!=m&&this._handle_legend_label(m,this.legend,z),null!=y&&this._handle_legend_field(y,this.legend,z),null!=w&&this._handle_legend_group(w,this.legend,z),this.add_renderers(z),z}static _get_range(e){if(null==e)return new p.DataRange1d;if(e instanceof p.Range)return e;if((0,g.isArray)(e)){if((0,g.isArrayOf)(e,g.isString)){const t=e;return new p.FactorRange({factors:t})}{const[t,n]=e;return new p.Range1d({start:t,end:n})}}throw new Error(`unable to determine proper range for: '${e}'`)}static _get_scale(e,t){if(e instanceof p.DataRange1d||e instanceof p.Range1d)switch(t){case null:case"auto":case"linear":case"datetime":case"mercator":return new p.LinearScale;case"log":return new p.LogScale}if(e instanceof p.FactorRange)return new p.CategoricalScale;throw new Error(`unable to determine proper scale for: '${e}'`)}_process_axis_and_grid(e,t,n,s,i,o){const r=this._get_axis(e,i,o);if(null!=r){r instanceof p.LogAxis&&(0==o?this.x_scale=new p.LogScale:this.y_scale=new p.LogScale),r.ticker instanceof p.ContinuousTicker&&(r.ticker.num_minor_ticks=this._get_num_minor_ticks(r,n)),r.axis_label=s,null!=t&&this.add_layout(r,t);const e=new p.Grid({dimension:o,ticker:r.ticker});this.add_layout(e)}}_get_axis(e,t,n){switch(e){case null:return null;case"linear":return new p.LinearAxis;case"log":return new p.LogAxis;case"datetime":return new p.DatetimeAxis;case"mercator":{const e=new p.MercatorAxis,t=0==n?"lon":"lat";return e.ticker.dimension=t,e.formatter.dimension=t,e}case"auto":return t instanceof p.FactorRange?new p.CategoricalAxis:new p.LinearAxis;default:throw new Error("shouldn't have happened")}}_get_num_minor_ticks(e,t){if((0,g.isNumber)(t)){if(t<=1)throw new Error("num_minor_ticks must be > 1");return t}return null==t?0:e instanceof p.LogAxis?10:5}_update_legend(e,t){const{legend:n}=this;let s=!1;for(const i of n.items)if(null!=i.label&&(0,_.is_equal)(i.label,e)){const e=i.label;if("value"in e){i.renderers.push(t),s=!0;break}if("field"in e&&t.data_source==i.renderers[0].data_source){i.renderers.push(t),s=!0;break}}if(!s){const s=new m.LegendItem({label:e,renderers:[t]});n.items.push(s)}}_handle_legend_label(e,t,n){const s={value:e},i=this._find_legend_item(s,t);if(null!=i)i.renderers.push(n);else{const e=new m.LegendItem({label:s,renderers:[n]});t.items.push(e)}}_handle_legend_field(e,t,n){const s={field:e},i=this._find_legend_item(s,t);if(null!=i)i.renderers.push(n);else{const e=new m.LegendItem({label:s,renderers:[n]});t.items.push(e)}}_handle_legend_group(e,t,n){const s=(0,u.dict)(n.data_source.data);if(!s.has(e))throw new Error(`column to be grouped does not exist in glyph data source: ${e}`);const i=s.get(e)??[],o=(0,d.uniq)(i).sort();for(const e of o){const s={value:`${e}`},o=i.indexOf(e),r=new m.LegendItem({label:s,renderers:[n],index:o});t.items.push(r)}}_find_legend_item(e,t){const n=new _.Comparator;for(const s of t.items)if(n.eq(s.label,e))return s;return null}}n.Figure=A,o=A,A.__name__="Figure",(0,c.extend)(o,v.GlyphAPI)},
590: function _(t,e,o,r,c){r(),o.extend=function(t,...e){for(const o of e)for(const e of Object.getOwnPropertyNames(o.prototype))"constructor"!=e&&Object.defineProperty(t.prototype,e,Object.getOwnPropertyDescriptor(o.prototype,e)??Object.create(null))}},
591: function _(t,r,e,s,a){s();const i=t(352);class h{annular_wedge(...t){return this._glyph(i.AnnularWedge,"annular_wedge",["x","y","inner_radius","outer_radius","start_angle","end_angle"],t)}annulus(...t){return this._glyph(i.Annulus,"annulus",["x","y","inner_radius","outer_radius"],t)}arc(...t){return this._glyph(i.Arc,"arc",["x","y","radius","start_angle","end_angle"],t)}bezier(...t){return this._glyph(i.Bezier,"bezier",["x0","y0","x1","y1","cx0","cy0","cx1","cy1"],t)}block(...t){return this._glyph(i.Block,"block",["x","y","width","height"],t)}circle(...t){return this._glyph(i.Circle,"circle",["x","y","radius"],t)}ellipse(...t){return this._glyph(i.Ellipse,"ellipse",["x","y","width","height"],t)}harea(...t){return this._glyph(i.HArea,"harea",["x1","x2","y"],t)}harea_step(...t){return this._glyph(i.HAreaStep,"harea_step",["x1","x2","y","step_mode"],t)}hbar(...t){return this._glyph(i.HBar,"hbar",["y","height","right","left"],t)}hspan(...t){return this._glyph(i.HSpan,"hspan",["y"],t)}hstrip(...t){return this._glyph(i.HStrip,"hstrip",["y0","y1"],t)}hex_tile(...t){return this._glyph(i.HexTile,"hex_tile",["q","r"],t)}image(...t){return this._glyph(i.Image,"image",["color_mapper","image","x","y","dw","dh"],t)}image_stack(...t){return this._glyph(i.ImageStack,"image_stack",["color_mapper","image","x","y","dw","dh"],t)}image_rgba(...t){return this._glyph(i.ImageRGBA,"image_rgba",["image","x","y","dw","dh"],t)}image_url(...t){return this._glyph(i.ImageURL,"image_url",["url","x","y","w","h"],t)}line(...t){return this._glyph(i.Line,"line",["x","y"],t)}mathml(...t){return this._glyph(i.MathMLGlyph,"mathml",["x","y","text"],t)}multi_line(...t){return this._glyph(i.MultiLine,"multi_line",["xs","ys"],t)}multi_polygons(...t){return this._glyph(i.MultiPolygons,"multi_polygons",["xs","ys"],t)}ngon(...t){return this._glyph(i.Ngon,"ngon",["x","y","radius"],t)}patch(...t){return this._glyph(i.Patch,"patch",["x","y"],t)}patches(...t){return this._glyph(i.Patches,"patches",["xs","ys"],t)}quad(...t){return this._glyph(i.Quad,"quad",["left","right","bottom","top"],t)}quadratic(...t){return this._glyph(i.Quadratic,"quadratic",["x0","y0","x1","y1","cx","cy"],t)}ray(...t){return this._glyph(i.Ray,"ray",["x","y","length"],t)}rect(...t){return this._glyph(i.Rect,"rect",["x","y","width","height"],t)}segment(...t){return this._glyph(i.Segment,"segment",["x0","y0","x1","y1"],t)}spline(...t){return this._glyph(i.Spline,"spline",["x","y"],t)}step(...t){return this._glyph(i.Step,"step",["x","y","mode"],t)}tex(...t){return this._glyph(i.TeXGlyph,"tex",["x","y","text"],t)}text(...t){return this._glyph(i.Text,"text",["x","y","text"],t)}varea(...t){return this._glyph(i.VArea,"varea",["x","y1","y2"],t)}varea_step(...t){return this._glyph(i.VAreaStep,"varea_step",["x","y1","y2","step_mode"],t)}vbar(...t){return this._glyph(i.VBar,"vbar",["x","width","top","bottom"],t)}vspan(...t){return this._glyph(i.VSpan,"vspan",["x"],t)}vstrip(...t){return this._glyph(i.VStrip,"vstrip",["x0","x1"],t)}wedge(...t){return this._glyph(i.Wedge,"wedge",["x","y","radius","start_angle","end_angle"],t)}_scatter(t,r){return this._glyph(i.Scatter,r??"scatter",["x","y"],t,null!=r?{marker:r}:void 0)}scatter(...t){return this._scatter(t)}asterisk(...t){return this._scatter(t,"asterisk")}circle_cross(...t){return this._scatter(t,"circle_cross")}circle_dot(...t){return this._scatter(t,"circle_dot")}circle_x(...t){return this._scatter(t,"circle_x")}circle_y(...t){return this._scatter(t,"circle_y")}cross(...t){return this._scatter(t,"cross")}dash(...t){return this._scatter(t,"dash")}diamond(...t){return this._scatter(t,"diamond")}diamond_cross(...t){return this._scatter(t,"diamond_cross")}diamond_dot(...t){return this._scatter(t,"diamond_dot")}dot(...t){return this._scatter(t,"dot")}hex(...t){return this._scatter(t,"hex")}hex_dot(...t){return this._scatter(t,"hex_dot")}inverted_triangle(...t){return this._scatter(t,"inverted_triangle")}plus(...t){return this._scatter(t,"plus")}square(...t){return this._scatter(t,"square")}square_cross(...t){return this._scatter(t,"square_cross")}square_dot(...t){return this._scatter(t,"square_dot")}square_pin(...t){return this._scatter(t,"square_pin")}square_x(...t){return this._scatter(t,"square_x")}star(...t){return this._scatter(t,"star")}star_dot(...t){return this._scatter(t,"star_dot")}triangle(...t){return this._scatter(t,"triangle")}triangle_dot(...t){return this._scatter(t,"triangle_dot")}triangle_pin(...t){return this._scatter(t,"triangle_pin")}x(...t){return this._scatter(t,"x")}y(...t){return this._scatter(t,"y")}}e.GlyphAPI=h,h.__name__="GlyphAPI"},
592: function _(n,t,e,o,r){o(),e.show=async function(n,t){const e=(()=>{if(n instanceof c.Document)return n;{const t=new c.Document;for(const e of(0,a.isArray)(n)?n:[n])t.add_root(e);return t}})(),o=document.currentScript;await(0,s.dom_ready)();const r=(()=>{if(null==t){if(null!=o&&(0,s.contains)(document.body,o)){const n=o.parentNode;if(n instanceof HTMLElement||n instanceof DocumentFragment)return n}return document.body}if((0,a.isString)(t)){const n=document.querySelector(t);if(n instanceof HTMLElement)return null!=n.shadowRoot?n.shadowRoot:n;throw new Error(`'${t}' selector didn't match any elements`)}if(t instanceof HTMLElement)return t;if("undefined"!=typeof $&&t instanceof $)return t[0];throw new Error("target should be a HTMLElement, string selector, $ or null")})(),u=await(0,i.add_document_standalone)(e,r);return new Promise(((t,o)=>{const r=[...u],i=(0,a.isArray)(n)||n instanceof c.Document?r:r[0];e.is_idle?t(i):e.idle.connect((()=>t(i)))}))};const c=n(5),i=n(54),s=n(63),a=n(8)},
593: function _(o,t,e,n,l){n(),e.group_tools=d,e.gridplot=function(o,t={}){const e=t.toolbar_location,n=t.merge_tools??!0,l=t.sizing_mode,s=f.Matrix.from(o),_=[],v=[];for(const[o,e,l]of s)null!=o&&(o instanceof i.Plot&&n&&(v.push(o.toolbar),o.toolbar_location=null),null!=t.width&&(o.width=t.width),null!=t.height&&(o.height=t.height),_.push([o,e,l]));function m(o,t){const e=t[0];return e instanceof a.SaveTool?new a.SaveTool:e instanceof c.CopyTool?new c.CopyTool:e instanceof r.ExamineTool?new r.ExamineTool:e instanceof u.FullscreenTool?new u.FullscreenTool:null}const g=(()=>{const o=[];for(const t of v)o.push(...t.tools);return n?d(o,m):o})(),w=v.map((o=>o.logo)),T=v.map((o=>o.autohide)),b=v.map((o=>o.active_drag)),y=v.map((o=>o.active_inspect)),x=v.map((o=>o.active_scroll)),S=v.map((o=>o.active_tap)),z=v.map((o=>o.active_multi));function P(o,t){const e=new Set(o).size;if(0!=e)return e>1&&console.warn(`found multiple competing values for 'toolbar.${t}' property; using the latest value`),(0,h.last)(o)}const C=P(w,"logo"),E=P(T,"autohide"),F=P(b,"active_drag"),M=P(y,"active_inspect"),q=P(x,"active_scroll"),G=P(S,"active_tap"),$=P(z,"active_multi"),j=new p.Toolbar({tools:g,logo:C,autohide:E,active_drag:F,active_inspect:M,active_scroll:q,active_tap:G,active_multi:$});return new i.GridPlot({children:_,toolbar:j,toolbar_location:e,sizing_mode:l})};const i=o(438),s=o(294),a=o(505),c=o(496),r=o(499),u=o(498),p=o(292),f=o(594),_=o(26),h=o(10);function d(o,t,e=new Set(["overlay","renderers"])){const n=new Map,l=[];for(const t of o)if(t instanceof s.ToolProxy)l.push(t);else{const o=t.attributes;for(const t of e)t in o&&delete o[t];const l=t.constructor.prototype;let i=n.get(l);null==i&&n.set(l,i=new Set),i.add({tool:t,attrs:o})}for(const[o,e]of n.entries()){if(null!=t){const n=t(o,[...e].map((o=>o.tool)));if(null!=n){l.push(n);continue}}for(;0!=e.size;){const[n,...i]=e;e.delete(n);const a=[n.tool];for(const o of i)(0,_.is_equal)(o.attrs,n.attrs)&&(a.push(o.tool),e.delete(o));if(1==a.length)l.push(a[0]);else{const e=t?.(o,a);l.push(e??new s.ToolProxy({tools:a}))}}}return l}},
594: function _(t,r,n,s,i){s();const o=t(10);class e{constructor(t,r,n){this.nrows=t,this.ncols=r,this._matrix=new Array(t);for(let s=0;s<t;s++){this._matrix[s]=new Array(r);for(let t=0;t<r;t++)this._matrix[s][t]=n(s,t)}}at(t,r){return this._matrix[t][r]}*[Symbol.iterator](){for(let t=0;t<this.nrows;t++)for(let r=0;r<this.ncols;r++){const n=this._matrix[t][r];void 0!==n&&(yield[n,t,r])}}*values(){for(const[t]of this)yield t}map(t){return new e(this.nrows,this.ncols,((r,n)=>t(this.at(r,n),r,n)))}apply(t){const r=e.from(t),{nrows:n,ncols:s}=this;if(n==r.nrows&&s==r.ncols)return new e(n,s,((t,n)=>r.at(t,n)(this.at(t,n),t,n)));throw new Error("dimensions don't match")}to_sparse(){return[...this]}static from(t,r){if(t instanceof e)return t;if(null!=r){const n=t,s=Math.ceil(n.length/r);return new e(s,r,((t,s)=>n[t*r+s]))}{const r=t,n=t.length,s=(0,o.min)(r.map((t=>t.length)));return new e(n,s,((t,n)=>r[t][n]))}}}n.Matrix=e,e.__name__="Matrix"},
595: function _(e,_,t,l,o){l();const a=e(14),i=e(9);class n{constructor(e,_){this.type=e,this.attrs=_,this.defaults=new Map((0,i.entries)(_))}}function r(e,_){return new n(e,_)}n.__name__="ThemedAttrs";class b{constructor(e){this.attrs=e}get(e,_){const t=e instanceof a.HasProps?e.constructor:e;for(const{type:e,defaults:l}of this.attrs)if(t==e||t.prototype instanceof e)return l.get(_)}}b.__name__="Theme";const c=e(587);t.dark_minimal=new b([r(c.Plot,{background_fill_color:"#20262b",border_fill_color:"#15191c",outline_line_color:"#e0e0e0",outline_line_alpha:.25}),r(c.Grid,{grid_line_color:"#e0e0e0",grid_line_alpha:.25}),r(c.Axis,{major_tick_line_alpha:0,major_tick_line_color:"#e0e0e0",minor_tick_line_alpha:0,minor_tick_line_color:"#e0e0e0",axis_line_alpha:0,axis_line_color:"#e0e0e0",major_label_text_color:"#e0e0e0",major_label_text_font:"Helvetica",major_label_text_font_size:"1.025em",axis_label_standoff:10,axis_label_text_color:"#e0e0e0",axis_label_text_font:"Helvetica",axis_label_text_font_size:"1.25em",axis_label_text_font_style:"normal"}),r(c.Legend,{spacing:8,glyph_width:15,label_standoff:8,label_text_color:"#e0e0e0",label_text_font:"Helvetica",label_text_font_size:"1.025em",border_line_alpha:0,background_fill_alpha:.25,background_fill_color:"#20262b"}),r(c.BaseColorBar,{title_text_color:"#e0e0e0",title_text_font:"Helvetica",title_text_font_size:"1.025em",title_text_font_style:"normal",major_label_text_color:"#e0e0e0",major_label_text_font:"Helvetica",major_label_text_font_size:"1.025em",background_fill_color:"#15191c",major_tick_line_alpha:0,bar_line_alpha:0}),r(c.Title,{text_color:"#e0e0e0",text_font:"Helvetica",text_font_size:"1.15em"})]),t.light_minimal=new b([r(c.Axis,{major_tick_line_alpha:0,major_tick_line_color:"#5b5b5b",minor_tick_line_alpha:0,minor_tick_line_color:"#5b5b5b",axis_line_alpha:0,axis_line_color:"#5b5b5b",major_label_text_color:"#5b5b5b",major_label_text_font:"Helvetica",major_label_text_font_size:"1.025em",axis_label_standoff:10,axis_label_text_color:"#5b5b5b",axis_label_text_font:"Helvetica",axis_label_text_font_size:"1.25em",axis_label_text_font_style:"normal"}),r(c.Legend,{spacing:8,glyph_width:15,label_standoff:8,label_text_color:"#5b5b5b",label_text_font:"Helvetica",label_text_font_size:"1.025em",border_line_alpha:0,background_fill_alpha:.25}),r(c.BaseColorBar,{title_text_color:"#5b5b5b",title_text_font:"Helvetica",title_text_font_size:"1.025em",title_text_font_style:"normal",major_label_text_color:"#5b5b5b",major_label_text_font:"Helvetica",major_label_text_font_size:"1.025em",major_tick_line_alpha:0,bar_line_alpha:0}),r(c.Title,{text_color:"#5b5b5b",text_font:"Helvetica",text_font_size:"1.15em"})]),t.caliber=new b([r(c.Axis,{major_tick_in:0,major_tick_out:3,major_tick_line_alpha:.25,major_tick_line_color:"#5b5b5b",minor_tick_line_alpha:.25,minor_tick_line_color:"#5b5b5b",axis_line_alpha:1,axis_line_color:"#5b5b5b",major_label_text_color:"#5b5b5b",major_label_text_font:"Calibri Light",major_label_text_font_size:"0.95em",major_label_text_font_style:"bold",axis_label_standoff:10,axis_label_text_color:"#5b5b5b",axis_label_text_font:"Calibri Light",axis_label_text_font_size:"1.15em",axis_label_text_font_style:"bold"}),r(c.Legend,{spacing:8,glyph_width:15,label_standoff:8,label_text_color:"#5b5b5b",label_text_font:"Calibri Light",label_text_font_size:"0.95em",label_text_font_style:"bold",border_line_alpha:0,background_fill_alpha:.25}),r(c.BaseColorBar,{title_text_color:"#5b5b5b",title_text_font:"Calibri Light",title_text_font_size:"1.15em",title_text_font_style:"bold",major_label_text_color:"#5b5b5b",major_label_text_font:"Calibri Light",major_label_text_font_size:"0.95em",major_label_text_font_style:"bold",major_tick_line_alpha:0,bar_line_alpha:0}),r(c.Title,{text_color:"#5b5b5b",text_font:"Calibri Light",text_font_size:"1.25em",text_font_style:"bold"})]),t.constrast=new b([r(c.Plot,{background_fill_color:"#000000",border_fill_color:"#ffffff",outline_line_color:"#000000",outline_line_alpha:.25}),r(c.Grid,{grid_line_color:"#e0e0e0",grid_line_alpha:.25}),r(c.Axis,{major_tick_line_alpha:0,major_tick_line_color:"#000000",minor_tick_line_alpha:0,minor_tick_line_color:"#000000",axis_line_alpha:0,axis_line_color:"#000000",major_label_text_color:"#000000",major_label_text_font:"Helvetica",major_label_text_font_size:"1.025em",axis_label_standoff:10,axis_label_text_color:"#000000",axis_label_text_font:"Helvetica",axis_label_text_font_size:"1.25em",axis_label_text_font_style:"normal"}),r(c.Legend,{spacing:8,glyph_width:15,label_standoff:8,label_text_color:"#ffffff",label_text_font:"Helvetica",label_text_font_size:"1.025em",border_line_alpha:0,background_fill_alpha:.25,background_fill_color:"#000000"}),r(c.BaseColorBar,{title_text_color:"#e0e0e0",title_text_font:"Helvetica",title_text_font_size:"1.025em",title_text_font_style:"normal",major_label_text_color:"#e0e0e0",major_label_text_font:"Helvetica",major_label_text_font_size:"1.025em",background_fill_color:"#15191c",major_tick_line_alpha:0,bar_line_alpha:0}),r(c.Title,{text_color:"#000000",text_font:"Helvetica",text_font_size:"1.15em"})]),t.night_sky=new b([r(c.Plot,{background_fill_color:"#2C001e",border_fill_color:"#15191c",outline_line_color:"#e0e0e0",outline_line_alpha:.25}),r(c.Grid,{grid_line_color:"#e0e0e0",grid_line_alpha:.25}),r(c.Axis,{major_tick_line_alpha:0,major_tick_line_color:"#e0e0e0",minor_tick_line_alpha:0,minor_tick_line_color:"#e0e0e0",axis_line_alpha:0,axis_line_color:"#e0e0e0",major_label_text_color:"#e0e0e0",major_label_text_font:"Helvetica",major_label_text_font_size:"1.025em",axis_label_standoff:10,axis_label_text_color:"#e0e0e0",axis_label_text_font:"Helvetica",axis_label_text_font_size:"1.25em",axis_label_text_font_style:"normal"}),r(c.Legend,{spacing:8,glyph_width:15,label_standoff:8,label_text_color:"#e0e0e0",label_text_font:"Helvetica",label_text_font_size:"1.025em",border_line_alpha:0,background_fill_alpha:.25,background_fill_color:"#2C001e"}),r(c.BaseColorBar,{title_text_color:"#e0e0e0",title_text_font:"Helvetica",title_text_font_size:"1.025em",title_text_font_style:"normal",major_label_text_color:"#e0e0e0",major_label_text_font:"Helvetica",major_label_text_font_size:"1.025em",background_fill_color:"#15191c",major_tick_line_alpha:0,bar_line_alpha:0}),r(c.Title,{text_color:"#e0e0e0",text_font:"Helvetica",text_font_size:"1.15em"})])},
596: function _(r,e,n,t,a){t(),n.f=function(r,...e){const[n,...t]=r,a=n+t.map(((r,e)=>`($${e})${r}`)).join(""),u=new c.Parser(a).parse();if(u.type!=c.FAILURE)return function(r,e){const n=(0,s.dict)(i.np);function t(r){switch(r.type){case c.LITERAL:return r.value;case c.IDENT:if(r.name.startsWith("$")){const n=Number(r.name.slice(1));if(isFinite(n)&&0<=n&&n<e.length)return e[n];throw new Error(`invalid reference: ${r.name}`)}if("np"===r.name)return i.np;throw new Error(`unknown identifier: ${r.name}`);case c.MEMBER:if(t(r.object)===i.np){const{name:e}=r.member,t=n.get(e);if(void 0!==t)return t;throw new Error(`'np.${e}' doesn't exist`)}throw new Error("not an accessible expression");case c.INDEX:throw new Error("not an indexable expression");case c.CALL:const a=t(r.callee);if((0,o.isFunction)(a))return a.apply(void 0,r.args.map((r=>t(r))));throw new Error("not a callable expression");case c.UNARY:{const e=(()=>{switch(r.operator){case"+":return i.np.pos;case"-":return i.np.neg;default:throw new Error(`unsupported operator: ${r.operator}`)}})(),n=t(r.argument);if((0,i.is_Numerical)(n))return e(n);throw new Error("a number or an array was expected")}case c.BINARY:const s=(()=>{switch(r.operator){case"+":return i.np.add;case"-":return i.np.sub;case"*":return i.np.mul;case"/":return i.np.div;case"**":return i.np.pow;case"<=":return i.np.le;case">=":return i.np.ge;case"<":return i.np.lt;case">":return i.np.gt;default:throw new Error(`unsupported operator: ${r.operator}`)}})(),u=t(r.left),p=t(r.right);if((0,i.is_Numerical)(u)&&(0,i.is_Numerical)(p))return s(u,p);throw new Error("a number or an array was expected");case c.COMPOUND:case c.SEQUENCE:case c.ARRAY:default:throw new Error("unsupported")}}return t(r)}(u,e);throw new Error(u.message)};const o=r(8),s=r(9),c=r(597),i=r(584)},
597: function _(e,t,r,i,s){i();const n=e(9),o=46;r.COMPOUND=Symbol("Compound"),r.LITERAL=Symbol("Literal"),r.IDENT=Symbol("Identifier"),r.MEMBER=Symbol("MemberExpression"),r.INDEX=Symbol("IndexExpression"),r.CALL=Symbol("CallExpression"),r.UNARY=Symbol("UnaryExpression"),r.BINARY=Symbol("BinaryExpression"),r.SEQUENCE=Symbol("SequenceExpression"),r.ARRAY=Symbol("ArrayExpression"),r.FAILURE=Symbol("Failure");const h=(0,n.dict)({"-":1,"!":1,"~":1,"+":1}),l=(0,n.dict)({"||":1,"&&":2,"|":3,"^":4,"&":5,"==":6,"!=":6,"<":7,">":7,"<=":7,">=":7,"<<":8,">>":8,"+":9,"-":9,"*":10,"/":10,"%":10,"**":11}),c=new Set(["$","_"]),a=(0,n.dict)({true:!0,false:!1,null:null});function b(e){return Math.max(0,...[...e.keys()].map((e=>e.length)))}const p=b(h),x=b(l);function d(e){return l.get(e)??0}function g(e){return e>=48&&e<=57}function f(e){return e>=65&&e<=90||e>=97&&e<=122||e>=128&&!l.has(String.fromCharCode(e))||c.has(String.fromCharCode(e))}function u(e){return f(e)||g(e)}class E extends Error{}E.__name__="ParseError";class m{constructor(e){this.index=0,this.expr=e}get char(){return this.expr.charAt(this.index)}get code(){return this.expr.charCodeAt(this.index)}error(e){throw new E(`${e} at character ${this.index}`)}gobbleSpaces(){let e=this.code;for(;32==e||9==e||10==e||13==e;)e=this.expr.charCodeAt(++this.index)}parse(){try{const e=this.gobbleExpressions(void 0);return 1==e.length?e[0]:{type:r.COMPOUND,body:e}}catch(e){if(e instanceof E)return{type:r.FAILURE,message:e.message};throw e}}gobbleExpressions(e){const t=[];for(;this.index<this.expr.length;){const r=this.code;if(59==r||44==r)this.index++;else{const i=this.gobbleExpression();if(0!=i)t.push(i);else if(this.index<this.expr.length){if(r==e)break;this.error(`Unexpected '${this.char}'`)}}}return t}gobbleExpression(){const e=this.gobbleBinaryExpression();return this.gobbleSpaces(),e}gobbleBinaryOp(){this.gobbleSpaces();let e=this.expr.substring(this.index,this.index+x),t=e.length;for(;t>0;){if(l.has(e)&&(!f(this.code)||this.index+e.length<this.expr.length&&!u(this.expr.charCodeAt(this.index+e.length))))return this.index+=t,e;e=e.substring(0,--t)}return!1}gobbleBinaryExpression(){const e=this.gobbleToken();if(0==e)return e;let t=this.gobbleBinaryOp();if(0==t)return e;let i={value:t,prec:d(t)};const s=this.gobbleToken();0==s&&this.error(`Expected expression after ${t}`);const n=[e,i,s];let o;for(;0!=(t=this.gobbleBinaryOp());){const e=d(t);if(0==e){this.index-=t.length;break}for(i={value:t,prec:e},o=t;n.length>2&&e<=n[n.length-2].prec;){const e=n.pop(),t=n.pop().value,i=n.pop(),s={type:r.BINARY,operator:t,left:i,right:e};n.push(s)}const s=this.gobbleToken();0==s&&this.error(`Expected expression after ${o}`),n.push(i,s)}let h=n.length-1,l=n[h];for(;h>1;)l={type:r.BINARY,operator:n[h-1].value,left:n[h-2],right:l},h-=2;return l}gobbleToken(){this.gobbleSpaces();const e=this.code;if(g(e)||e==o)return this.gobbleNumericLiteral();let t=!1;if(39==e||34==e)t=this.gobbleStringLiteral();else if(91==e)t=this.gobbleArray();else{let i=this.expr.substring(this.index,this.index+p),s=i.length;for(;s>0;){if(h.has(i)&&(!f(this.code)||this.index+i.length<this.expr.length&&!u(this.expr.charCodeAt(this.index+i.length)))){this.index+=s;const e=this.gobbleToken();return 0==e&&this.error("missing unaryOp argument"),{type:r.UNARY,operator:i,argument:e,prefix:!0}}i=i.substring(0,--s)}if(f(e)){t=this.gobbleIdentifier();const e=a.get(t.name);void 0!==e&&(t={type:r.LITERAL,value:e})}else 40==e&&(t=this.gobbleGroup())}return 0!=t&&(t=this.gobbleTokenProperty(t),t)}gobbleTokenProperty(e){this.gobbleSpaces();let t=this.code;for(;t==o||91==t||40==t;){if(this.index++,t==o)this.gobbleSpaces(),e={type:r.MEMBER,object:e,member:this.gobbleIdentifier()};else if(91==t){const i=this.gobbleExpression();0==i&&this.error("Expected an expression"),e={type:r.INDEX,object:e,index:i},this.gobbleSpaces(),t=this.code,93!==t&&this.error("Unclosed ["),this.index++}else e={type:r.CALL,args:this.gobbleArguments(41),callee:e};this.gobbleSpaces(),t=this.code}return e}gobbleNumericLiteral(){let e="";for(;g(this.code);)e+=this.expr.charAt(this.index++);if(this.code==o)for(e+=this.expr.charAt(this.index++);g(this.code);)e+=this.expr.charAt(this.index++);let t=this.char;if("e"==t||"E"==t){for(e+=this.expr.charAt(this.index++),t=this.char,"+"!=t&&"-"!=t||(e+=this.expr.charAt(this.index++));g(this.code);)e+=this.expr.charAt(this.index++);g(this.expr.charCodeAt(this.index-1))||this.error(`Expected exponent (${e+this.char})`)}const i=this.code;return f(i)?this.error(`Variable names cannot start with a number (${e+this.char})`):(i==o||1==e.length&&e.charCodeAt(0)==o)&&this.error("Unexpected ','"),{type:r.LITERAL,value:parseFloat(e)}}gobbleStringLiteral(){const e=this.expr.charAt(this.index++);let t="",i=!1;for(;this.index<this.expr.length;){let r=this.expr.charAt(this.index++);if(r==e){i=!0;break}if("\\"==r)switch(r=this.expr.charAt(this.index++),r){case"n":t+="\n";break;case"r":t+="\r";break;case"t":t+="\t";break;case"b":t+="\b";break;case"f":t+="\f";break;case"v":t+="\v";break;default:t+=r}else t+=r}return i||this.error(`Unclosed quote after "${t}"`),{type:r.LITERAL,value:t}}gobbleIdentifier(){let e=this.code;const t=this.index;for(f(e)?this.index++:this.error(`Unexpected '${this.char}'`);this.index<this.expr.length&&(e=this.code,u(e));)this.index++;return{type:r.IDENT,name:this.expr.slice(t,this.index)}}gobbleArguments(e){const t=[];let i=!1,s=0;for(;this.index<this.expr.length;){this.gobbleSpaces();const n=this.code;if(n==e){i=!0,this.index++,41==e&&0!=s&&s>=t.length&&this.error(`Unexpected token '${String.fromCharCode(e)}'`);break}if(44==n){if(this.index++,s++,s!==t.length)if(41==e)this.error("Unexpected token ','");else if(93==e)for(let e=t.length;e<s;e++)this.error("Expected an expression")}else if(t.length!==s&&0!==s)this.error("Expected comma");else{const e=this.gobbleExpression();0!=e&&e.type!=r.COMPOUND||this.error("Expected comma"),t.push(e)}}return i||this.error(`Expected ${String.fromCharCode(e)}`),t}gobbleGroup(){this.index++;const e=this.gobbleExpressions(41);if(41==this.code)return this.index++,1==e.length?e[0]:0!=e.length&&{type:r.SEQUENCE,expressions:e};this.error("Unclosed (")}gobbleArray(){return this.index++,{type:r.ARRAY,elements:this.gobbleArguments(93)}}}r.Parser=m,m.__name__="Parser"},
}, 582, {"api/main":582,"api/index":583,"api/linalg":584,"api/charts":585,"api/palettes":586,"api/models":587,"api/plotting":588,"api/figure":589,"core/class":590,"api/glyph_api":591,"api/io":592,"api/gridplot":593,"core/util/matrix":594,"api/themes":595,"api/expr":596,"api/parser":597}, {});});
